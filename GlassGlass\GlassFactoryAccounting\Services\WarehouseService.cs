using System.IO;
using System.Text.Json;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة المخازن
    /// </summary>
    public class WarehouseService
    {
        private readonly string _dataPath;
        private readonly ArchiveService _archiveService;

        public WarehouseService()
        {
            _dataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GlassFactory", "Warehouses");
            _archiveService = new ArchiveService();
            
            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }
        }

        /// <summary>
        /// حفظ مخزن جديد
        /// </summary>
        public async Task<bool> SaveWarehouseAsync(Warehouse warehouse)
        {
            try
            {
                if (warehouse.Id == 0)
                {
                    warehouse.Id = await GetNextIdAsync();

                    // توليد كود تلقائي إذا لم يكن موجود
                    if (string.IsNullOrEmpty(warehouse.Code))
                    {
                        warehouse.Code = await GenerateWarehouseCodeAsync();
                    }
                }

                var fileName = $"Warehouse_{warehouse.Id}.json";
                var filePath = Path.Combine(_dataPath, fileName);

                var json = JsonSerializer.Serialize(warehouse, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving warehouse: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب جميع المخازن
        /// </summary>
        public async Task<List<Warehouse>> GetAllWarehousesAsync()
        {
            try
            {
                var warehouses = new List<Warehouse>();
                
                if (!Directory.Exists(_dataPath))
                    return warehouses;

                var files = Directory.GetFiles(_dataPath, "Warehouse_*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var warehouse = JsonSerializer.Deserialize<Warehouse>(json);
                        if (warehouse != null)
                        {
                            warehouses.Add(warehouse);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error reading warehouse file {file}: {ex.Message}");
                    }
                }

                return warehouses.OrderBy(w => w.Name).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting warehouses: {ex.Message}");
                return new List<Warehouse>();
            }
        }

        /// <summary>
        /// جلب مخزن بالمعرف
        /// </summary>
        public async Task<Warehouse?> GetWarehouseByIdAsync(int id)
        {
            try
            {
                var fileName = $"Warehouse_{id}.json";
                var filePath = Path.Combine(_dataPath, fileName);

                if (!File.Exists(filePath))
                    return null;

                var json = await File.ReadAllTextAsync(filePath);
                return JsonSerializer.Deserialize<Warehouse>(json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting warehouse by id: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// حذف مخزن
        /// </summary>
        public async Task<bool> DeleteWarehouseAsync(int id)
        {
            try
            {
                var fileName = $"Warehouse_{id}.json";
                var filePath = Path.Combine(_dataPath, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting warehouse: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// توليد كود المخزن التلقائي
        /// </summary>
        private async Task<string> GenerateWarehouseCodeAsync()
        {
            try
            {
                var warehouses = await GetAllWarehousesAsync();
                var lastCode = warehouses
                    .Where(w => !string.IsNullOrEmpty(w.Code) && w.Code.StartsWith("WH"))
                    .Select(w => w.Code)
                    .OrderByDescending(c => c)
                    .FirstOrDefault();

                if (lastCode != null && lastCode.Length > 2)
                {
                    var numberPart = lastCode.Substring(2);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        return $"WH{(lastNumber + 1):D4}";
                    }
                }

                return "WH0001";
            }
            catch
            {
                return "WH0001";
            }
        }

        /// <summary>
        /// جلب المعرف التالي
        /// </summary>
        private async Task<int> GetNextIdAsync()
        {
            try
            {
                var warehouses = await GetAllWarehousesAsync();
                return warehouses.Count > 0 ? warehouses.Max(w => w.Id) + 1 : 1;
            }
            catch
            {
                return 1;
            }
        }
    }
}
