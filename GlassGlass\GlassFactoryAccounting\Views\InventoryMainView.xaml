<UserControl x:Class="GlassFactoryAccounting.Views.InventoryMainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Height" Value="120"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#1976D2"/>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📦" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="نظام إدارة المخازن" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                    <TextBlock Text="إدارة شاملة للمخازن والأصناف وحركة المخزون بنظام المتوسط المتحرك" FontSize="14" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- القوائم الرئيسية -->
        <Grid Grid.Row="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- إدارة المخازن -->
            <Border Grid.Row="0" Grid.Column="0" Style="{StaticResource CardStyle}">
                <Button Style="{StaticResource MenuButtonStyle}" Click="BtnWarehouseManagement_Click">
                    <StackPanel>
                        <TextBlock Text="🏠" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="إدارة المخازن" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="إضافة وتعديل المخازن" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- إدارة الأصناف -->
            <Border Grid.Row="0" Grid.Column="1" Style="{StaticResource CardStyle}">
                <Button Style="{StaticResource MenuButtonStyle}" Click="BtnItemManagement_Click">
                    <StackPanel>
                        <TextBlock Text="📋" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="إدارة الأصناف" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="إضافة وتعديل الأصناف" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- حركة المخزون -->
            <Border Grid.Row="0" Grid.Column="2" Style="{StaticResource CardStyle}">
                <Button Style="{StaticResource MenuButtonStyle}" Click="BtnInventoryMovement_Click">
                    <StackPanel>
                        <TextBlock Text="🔄" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="حركة المخزون" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="استلام وتسليم الأصناف" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- تقرير الحركات -->
            <Border Grid.Row="1" Grid.Column="0" Style="{StaticResource CardStyle}">
                <Button Style="{StaticResource MenuButtonStyle}" Click="BtnMovementReport_Click">
                    <StackPanel>
                        <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="تقرير الحركات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="تقرير الاستلام والتسليم" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- الأصناف المتوفرة -->
            <Border Grid.Row="1" Grid.Column="1" Style="{StaticResource CardStyle}">
                <Button Style="{StaticResource MenuButtonStyle}" Click="BtnInventoryBalance_Click">
                    <StackPanel>
                        <TextBlock Text="📈" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="الأصناف المتوفرة" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="أرصدة المخزون والأسعار" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Button>
            </Border>

            <!-- العودة للرئيسية -->
            <Border Grid.Row="1" Grid.Column="2" Style="{StaticResource CardStyle}">
                <Button Style="{StaticResource MenuButtonStyle}" Background="#757575" Click="BtnBack_Click">
                    <StackPanel>
                        <TextBlock Text="🏠" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <TextBlock Text="العودة للرئيسية" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        <TextBlock Text="الرجوع للصفحة الرئيسية" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Button>
            </Border>
        </Grid>
    </Grid>
</UserControl>
