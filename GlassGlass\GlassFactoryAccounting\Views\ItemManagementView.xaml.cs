using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class ItemManagementView : UserControl
    {
        private readonly ItemService _itemService;
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<Item> _items;
        private ObservableCollection<Warehouse> _warehouses;

        public ItemManagementView()
        {
            InitializeComponent();
            _itemService = new ItemService();
            _warehouseService = new WarehouseService();
            _items = new ObservableCollection<Item>();
            _warehouses = new ObservableCollection<Warehouse>();

            ItemsDataGrid.ItemsSource = _items;
            CmbWarehouse.ItemsSource = _warehouses;

            LoadUnitOfMeasures();
            LoadWarehouses();
            LoadItems();
            GenerateNewItemCode();
        }

        private void LoadUnitOfMeasures()
        {
            var units = Enum.GetValues(typeof(UnitOfMeasure)).Cast<UnitOfMeasure>().ToList();
            CmbUnitOfMeasure.ItemsSource = units;
        }

        private async void LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                _warehouses.Clear();
                foreach (var warehouse in warehouses)
                {
                    _warehouses.Add(warehouse);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المخازن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadItems()
        {
            try
            {
                var items = await _itemService.GetAllItemsAsync();
                _items.Clear();
                foreach (var item in items)
                {
                    _items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأصناف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateNewItemCode()
        {
            try
            {
                var items = await _itemService.GetAllItemsAsync();
                var lastCode = items
                    .Where(i => !string.IsNullOrEmpty(i.Code) && i.Code.StartsWith("IT"))
                    .Select(i => i.Code)
                    .OrderByDescending(c => c)
                    .FirstOrDefault();

                if (lastCode != null && lastCode.Length > 2)
                {
                    var numberPart = lastCode.Substring(2);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        TxtItemCode.Text = $"IT{(lastNumber + 1):D4}";
                        return;
                    }
                }

                TxtItemCode.Text = "IT0001";
            }
            catch
            {
                TxtItemCode.Text = "IT0001";
            }
        }

        private void ChkHasDimensions_Checked(object sender, RoutedEventArgs e)
        {
            DimensionsPanel.Visibility = Visibility.Visible;
        }

        private void ChkHasDimensions_Unchecked(object sender, RoutedEventArgs e)
        {
            DimensionsPanel.Visibility = Visibility.Collapsed;
            TxtLength.Clear();
            TxtWidth.Clear();
            TxtArea.Clear();
        }

        private void Dimensions_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(TxtLength.Text, out decimal length) && 
                decimal.TryParse(TxtWidth.Text, out decimal width) &&
                length > 0 && width > 0)
            {
                var area = (length * width) / 1_000_000; // تحويل من ملم² إلى متر²
                TxtArea.Text = area.ToString("F4");
            }
            else
            {
                TxtArea.Clear();
            }
        }

        private async void BtnAddItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(TxtItemName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الصنف", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (CmbWarehouse.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار المخزن", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (CmbUnitOfMeasure.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار وحدة القياس", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var item = new Item
                {
                    Code = TxtItemCode.Text.Trim(),
                    Name = TxtItemName.Text.Trim(),
                    WarehouseId = (int)CmbWarehouse.SelectedValue,
                    UnitOfMeasure = (UnitOfMeasure)CmbUnitOfMeasure.SelectedValue,
                    HasDimensions = ChkHasDimensions.IsChecked == true,
                    Description = TxtDescription.Text.Trim()
                };

                // إضافة محتوى الصندوق
                if (decimal.TryParse(TxtBoxContent.Text, out decimal boxContent))
                    item.BoxContent = boxContent;

                if (item.HasDimensions)
                {
                    if (decimal.TryParse(TxtLength.Text, out decimal length))
                        item.Length = length;
                    if (decimal.TryParse(TxtWidth.Text, out decimal width))
                        item.Width = width;
                }

                var success = await _itemService.SaveItemAsync(item);
                if (success)
                {
                    MessageBox.Show("تم إضافة الصنف بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    ClearForm();
                    LoadItems();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة الصنف", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الصنف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClearForm_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            TxtItemName.Clear();
            TxtBoxContent.Clear();
            CmbWarehouse.SelectedIndex = -1;
            CmbUnitOfMeasure.SelectedIndex = -1;
            ChkHasDimensions.IsChecked = false;
            TxtLength.Clear();
            TxtWidth.Clear();
            TxtArea.Clear();
            TxtDescription.Clear();
            GenerateNewItemCode();
        }

        private async void BtnEditItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Item item)
                {
                    var editWindow = new ItemEditWindow(item, _warehouses.ToList());
                    editWindow.Owner = Window.GetWindow(this);
                    
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadItems();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الصنف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnDeleteItem_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Item item)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف الصنف '{item.Name}'؟", 
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        var success = await _itemService.DeleteItemAsync(item.Id);
                        if (success)
                        {
                            MessageBox.Show("تم حذف الصنف بنجاح", "نجح", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadItems();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الصنف", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الصنف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadWarehouses();
            LoadItems();
        }
    }
}
