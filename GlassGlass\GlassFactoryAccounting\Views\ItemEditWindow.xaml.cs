using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// نافذة تعديل الصنف
    /// </summary>
    public partial class ItemEditWindow : Window
    {
        private readonly ItemService _itemService;
        private readonly Item _item;
        private readonly List<Warehouse> _warehouses;

        public ItemEditWindow(Item item, List<Warehouse> warehouses)
        {
            InitializeComponent();
            _itemService = new ItemService();
            _item = item;
            _warehouses = warehouses;
            
            LoadData();
        }

        private void LoadData()
        {
            // تحميل المخازن
            CmbWarehouse.ItemsSource = _warehouses;
            
            // تحميل وحدات القياس
            var units = Enum.GetValues(typeof(UnitOfMeasure)).Cast<UnitOfMeasure>().ToList();
            CmbUnitOfMeasure.ItemsSource = units;
            
            // تعبئة البيانات
            TxtName.Text = _item.Name;
            CmbWarehouse.SelectedValue = _item.WarehouseId;
            CmbUnitOfMeasure.SelectedValue = _item.UnitOfMeasure;
            ChkHasDimensions.IsChecked = _item.HasDimensions;
            TxtDescription.Text = _item.Description;
            
            if (_item.HasDimensions)
            {
                TxtLength.Text = _item.Length.ToString();
                TxtWidth.Text = _item.Width.ToString();
                TxtArea.Text = _item.Area.ToString("F4");
                DimensionsPanel.Visibility = Visibility.Visible;
            }
        }

        private void ChkHasDimensions_Checked(object sender, RoutedEventArgs e)
        {
            DimensionsPanel.Visibility = Visibility.Visible;
        }

        private void ChkHasDimensions_Unchecked(object sender, RoutedEventArgs e)
        {
            DimensionsPanel.Visibility = Visibility.Collapsed;
            TxtLength.Clear();
            TxtWidth.Clear();
            TxtArea.Clear();
        }

        private void Dimensions_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (decimal.TryParse(TxtLength.Text, out decimal length) && 
                decimal.TryParse(TxtWidth.Text, out decimal width) &&
                length > 0 && width > 0)
            {
                var area = (length * width) / 1_000_000; // تحويل من ملم² إلى متر²
                TxtArea.Text = area.ToString("F4");
            }
            else
            {
                TxtArea.Clear();
            }
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(TxtName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الصنف", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (CmbWarehouse.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار المخزن", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (CmbUnitOfMeasure.SelectedValue == null)
                {
                    MessageBox.Show("يرجى اختيار وحدة القياس", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                _item.Name = TxtName.Text.Trim();
                _item.WarehouseId = (int)CmbWarehouse.SelectedValue;
                _item.UnitOfMeasure = (UnitOfMeasure)CmbUnitOfMeasure.SelectedValue;
                _item.HasDimensions = ChkHasDimensions.IsChecked == true;
                _item.Description = TxtDescription.Text.Trim();

                if (_item.HasDimensions)
                {
                    if (decimal.TryParse(TxtLength.Text, out decimal length))
                        _item.Length = length;
                    if (decimal.TryParse(TxtWidth.Text, out decimal width))
                        _item.Width = width;
                }
                else
                {
                    _item.Length = 0;
                    _item.Width = 0;
                }

                var success = await _itemService.SaveItemAsync(_item);
                if (success)
                {
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التعديلات", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
