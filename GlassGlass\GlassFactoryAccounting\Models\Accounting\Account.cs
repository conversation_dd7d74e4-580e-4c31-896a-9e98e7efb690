using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GlassFactoryAccounting.Models.Accounting
{
    /// <summary>
    /// نموذج الحساب في شجرة الحسابات - محدث ومحسن
    /// </summary>
    public class Account
    {
        public int Id { get; set; }

        [Required]
        [StringLength(20)]
        public string AccountCode { get; set; } = "";

        [Required]
        [StringLength(200)]
        public string AccountName { get; set; } = "";

        [Required]
        public AccountType AccountType { get; set; }

        public bool IsParent { get; set; }

        public int? ParentAccountId { get; set; }

        /// <summary>
        /// مستوى الحساب في الشجرة (1 = رئيسي، 2 = فرعي، 3 = فرعي من الثاني)
        /// </summary>
        public int AccountLevel { get; set; } = 1;

        public Account? ParentAccount { get; set; }

        public List<Account> SubAccounts { get; set; } = new List<Account>();

        public decimal Balance { get; set; }

        public decimal DebitBalance { get; set; }

        public decimal CreditBalance { get; set; }

        public bool IsActive { get; set; } = true;

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public string CreatedBy { get; set; } = "";

        public DateTime? ModifiedDate { get; set; }

        public string? ModifiedBy { get; set; }

        [StringLength(500)]
        public string? Notes { get; set; }

        /// <summary>
        /// هل يمكن استخدام هذا الحساب في القيود (الحسابات الفرعية فقط)
        /// </summary>
        public bool CanBeUsedInEntries => !IsParent;
        
        /// <summary>
        /// الحصول على الكود الكامل للحساب (مع الحساب الأب)
        /// </summary>
        public string FullAccountCode
        {
            get
            {
                if (ParentAccount != null)
                    return $"{ParentAccount.FullAccountCode}.{AccountCode}";
                return AccountCode;
            }
        }
        
        /// <summary>
        /// الحصول على الاسم الكامل للحساب (مع الحساب الأب)
        /// </summary>
        public string FullAccountName
        {
            get
            {
                if (ParentAccount != null)
                    return $"{ParentAccount.AccountName} - {AccountName}";
                return AccountName;
            }
        }
        
        /// <summary>
        /// حساب الرصيد الإجمالي للحساب وجميع الحسابات الفرعية
        /// </summary>
        public decimal TotalBalance
        {
            get
            {
                decimal total = Balance;
                foreach (var subAccount in SubAccounts)
                {
                    total += subAccount.TotalBalance;
                }
                return total;
            }
        }
    }
    
    /// <summary>
    /// أنواع الحسابات حسب النظام المحاسبي الأمريكي
    /// </summary>
    public enum AccountType
    {
        /// <summary>
        /// الأصول - Assets
        /// </summary>
        Asset = 1,
        
        /// <summary>
        /// الالتزامات - Liabilities
        /// </summary>
        Liability = 2,
        
        /// <summary>
        /// حقوق الملكية - Equity
        /// </summary>
        Equity = 3,
        
        /// <summary>
        /// الإيرادات - Revenue
        /// </summary>
        Revenue = 4,
        
        /// <summary>
        /// المصروفات - Expenses
        /// </summary>
        Expense = 5
    }
}
