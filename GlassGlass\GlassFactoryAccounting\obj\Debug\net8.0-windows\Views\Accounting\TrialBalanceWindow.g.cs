﻿#pragma checksum "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D3688109D2CA222912937C80C94D346088F897BC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views.Accounting {
    
    
    /// <summary>
    /// TrialBalanceWindow
    /// </summary>
    public partial class TrialBalanceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 41 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpAsOfDate;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnGenerate;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExport;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrint;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border borderBalanceStatus;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtBalanceStatus;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgTrialBalance;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalDebit;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalCredit;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/GlassFactoryAccounting;V1.0.0.0;component/views/accounting/trialbalancewindow.xa" +
                    "ml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.dpAsOfDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 2:
            this.btnGenerate = ((System.Windows.Controls.Button)(target));
            
            #line 50 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
            this.btnGenerate.Click += new System.Windows.RoutedEventHandler(this.BtnGenerate_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnExport = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
            this.btnExport.Click += new System.Windows.RoutedEventHandler(this.BtnExport_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnPrint = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\..\Views\Accounting\TrialBalanceWindow.xaml"
            this.btnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.borderBalanceStatus = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.txtBalanceStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.dgTrialBalance = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 8:
            this.txtTotalDebit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.txtTotalCredit = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

