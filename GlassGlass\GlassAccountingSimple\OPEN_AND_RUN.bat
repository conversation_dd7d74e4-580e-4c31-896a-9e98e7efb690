@echo off
title Glass Factory Accounting - Open and Run
color 0A

echo.
echo ===============================================
echo   Glass Factory Accounting System
echo   Simple Windows Forms Edition
echo ===============================================
echo.
echo Framework: .NET Framework 4.7.2
echo Technology: Windows Forms (No external dependencies)
echo Owner: <PERSON><PERSON><PERSON> Ahmed
echo.

echo Opening project in Visual Studio...
echo.
echo INSTRUCTIONS:
echo 1. Visual Studio will open the project
echo 2. Press Ctrl+Shift+B to build
echo 3. Press F5 to run
echo 4. You should see a form with Arabic title and 3 buttons
echo 5. Click "Test Program" button to verify it works
echo.

if exist "GlassAccountingSimple.sln" (
    start "" "GlassAccountingSimple.sln"
    echo Project opened in Visual Studio
    echo.
    echo Expected result after F5:
    echo - Window title: "نظام حسابات مصنع الزجاج"
    echo - 3 colored buttons with Arabic/English text
    echo - Green status text at bottom
    echo - RTL layout for Arabic text
    echo.
) else (
    echo ERROR: Solution file not found!
    echo Make sure you are in the correct directory
)

pause
