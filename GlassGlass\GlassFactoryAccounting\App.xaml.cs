﻿using System.Configuration;
using System.Data;
using System.Windows;
using System.Globalization;
using GlassFactoryAccounting.Helpers;

namespace GlassFactoryAccounting;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        // تطبيق الثقافة الإنجليزية للأرقام من بداية التطبيق
        NumberFormatHelper.SetEnglishCulture();

        // تطبيق الثقافة على الخيط الحالي أيضاً
        System.Threading.Thread.CurrentThread.CurrentCulture = new CultureInfo("en-US");
        System.Threading.Thread.CurrentThread.CurrentUICulture = new CultureInfo("en-US");

        base.OnStartup(e);
    }
}

