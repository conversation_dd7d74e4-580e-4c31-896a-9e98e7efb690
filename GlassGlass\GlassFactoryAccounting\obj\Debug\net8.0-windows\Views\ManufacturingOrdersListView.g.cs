﻿#pragma checksum "..\..\..\..\Views\ManufacturingOrdersListView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3A150B488DBECB053DFACA432BB71E22060AE86E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ManufacturingOrdersListView
    /// </summary>
    public partial class ManufacturingOrdersListView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 21 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBack;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnToggleNavigationTools;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox SearchAndFilterPanel;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchOrderNumber;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearchCustomer;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbSearchStatus;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSearch;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpFromDate;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpToDate;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClearSearch;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefresh;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgManufacturingOrders;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnViewOrder;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEditOrder;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeleteOrder;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrintOrder;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSavePDF;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExportExcel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/manufacturingorderslistview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnBack = ((System.Windows.Controls.Button)(target));
            
            #line 24 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnToggleNavigationTools = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnToggleNavigationTools.Click += new System.Windows.RoutedEventHandler(this.BtnToggleNavigationTools_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.SearchAndFilterPanel = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 4:
            this.txtSearchOrderNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.txtSearchCustomer = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.cmbSearchStatus = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.btnSearch = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnSearch.Click += new System.Windows.RoutedEventHandler(this.BtnSearch_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.dpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 9:
            this.dpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 10:
            this.btnClearSearch = ((System.Windows.Controls.Button)(target));
            
            #line 98 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnClearSearch.Click += new System.Windows.RoutedEventHandler(this.BtnClearSearch_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 105 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.dgManufacturingOrders = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.btnViewOrder = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnViewOrder.Click += new System.Windows.RoutedEventHandler(this.BtnViewOrder_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.btnEditOrder = ((System.Windows.Controls.Button)(target));
            
            #line 143 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnEditOrder.Click += new System.Windows.RoutedEventHandler(this.BtnEditOrder_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.btnDeleteOrder = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnDeleteOrder.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteOrder_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.btnPrintOrder = ((System.Windows.Controls.Button)(target));
            
            #line 153 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnPrintOrder.Click += new System.Windows.RoutedEventHandler(this.BtnPrintOrder_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.btnSavePDF = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnSavePDF.Click += new System.Windows.RoutedEventHandler(this.BtnSavePDF_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.btnExportExcel = ((System.Windows.Controls.Button)(target));
            
            #line 163 "..\..\..\..\Views\ManufacturingOrdersListView.xaml"
            this.btnExportExcel.Click += new System.Windows.RoutedEventHandler(this.BtnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

