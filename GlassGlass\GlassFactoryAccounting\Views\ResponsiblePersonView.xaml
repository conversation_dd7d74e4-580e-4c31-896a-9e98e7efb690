<UserControl x:Class="GlassFactoryAccounting.Views.ResponsiblePersonView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">

    <UserControl.Resources>
        <!-- الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="LightBrush" Color="#F5F5F5"/>

        <!-- نمط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط الأزرار -->
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط زر النجاح -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        </Style>

        <!-- نمط حقول الإدخال -->
        <Style x:Key="InputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
        </Style>

        <!-- نمط التسميات -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="8,8,8,0"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="{StaticResource LightBrush}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="👤" FontSize="28" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <TextBlock Text="تسجيل المسؤولين عن السداد" FontSize="22" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- النموذج -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الاسم -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم المسؤول:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtName" Grid.Row="0" Grid.Column="1" Style="{StaticResource InputStyle}"/>

                <!-- المنصب -->
                <TextBlock Grid.Row="1" Grid.Column="0" Text="المنصب:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="CmbPosition" Grid.Row="1" Grid.Column="1" Margin="8" Height="40" FontSize="14" IsEditable="True"/>

                <!-- القسم -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="القسم:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="CmbDepartment" Grid.Row="2" Grid.Column="1" Margin="8" Height="40" FontSize="14" IsEditable="True"/>

                <!-- رقم الهاتف -->
                <TextBlock Grid.Row="3" Grid.Column="0" Text="رقم الهاتف:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtPhone" Grid.Row="3" Grid.Column="1" Style="{StaticResource InputStyle}"/>

                <!-- البريد الإلكتروني -->
                <TextBlock Grid.Row="4" Grid.Column="0" Text="البريد الإلكتروني:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtEmail" Grid.Row="4" Grid.Column="1" Style="{StaticResource InputStyle}"/>

                <!-- الحالة -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="الحالة:" Style="{StaticResource LabelStyle}"/>
                <CheckBox x:Name="ChkIsActive" Grid.Row="5" Grid.Column="1" Content="نشط" Margin="8" FontSize="14" IsChecked="True"/>

                <!-- ملاحظات -->
                <TextBlock Grid.Row="0" Grid.Column="2" Text="ملاحظات:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtNotes" Grid.Row="1" Grid.Column="2" Grid.RowSpan="5" Style="{StaticResource InputStyle}" 
                         Height="200" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                         VerticalContentAlignment="Top"/>

            </Grid>
        </Border>

        <!-- قائمة المسؤولين -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="📋 قائمة المسؤولين عن السداد" FontSize="18" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,15"/>

                <DataGrid x:Name="ResponsiblePersonsDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الاسم" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="المنصب" Binding="{Binding Position}" Width="120"/>
                        <DataGridTextColumn Header="القسم" Binding="{Binding Department}" Width="120"/>
                        <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="180"/>
                        <DataGridCheckBoxColumn Header="نشط" Binding="{Binding IsActive}" Width="60"/>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="160">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="✏️ تعديل" Background="#2196F3" Foreground="White"
                                                BorderThickness="0" Padding="8,4" Margin="2" FontSize="10"
                                                Cursor="Hand" Click="BtnEditPerson_Click"
                                                Tag="{Binding Id}"/>
                                        <Button Content="🗑️ حذف" Background="#F44336" Foreground="White"
                                                BorderThickness="0" Padding="8,4" Margin="2" FontSize="10"
                                                Cursor="Hand" Click="BtnDeletePerson_Click"
                                                Tag="{Binding Id}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- الأزرار -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ المسؤول" Style="{StaticResource SuccessButtonStyle}" Click="BtnSave_Click"/>
                <Button x:Name="BtnClear" Content="🗑️ مسح الحقول" Style="{StaticResource ButtonStyle}" Click="BtnClear_Click"/>
                <Button x:Name="BtnBack" Content="🔙 العودة" Style="{StaticResource ButtonStyle}" Click="BtnBack_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
