using System.Collections.ObjectModel;
using System.Data.SQLite;
using System.IO;
using System.Text.Json;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة المبيعات
    /// </summary>
    public class SalesService
    {
        private readonly DatabaseContext _databaseContext;
        private readonly ArchiveService _archiveService;
        private readonly ServicesService _servicesService;

        public SalesService()
        {
            try
            {
                _databaseContext = new DatabaseContext();
                _archiveService = new ArchiveService();
                _servicesService = new ServicesService();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"SalesService constructor error: {ex.Message}");
                // إنشاء خدمات افتراضية
                _archiveService = new ArchiveService();
                _servicesService = new ServicesService();
            }
        }

        /// <summary>
        /// حفظ فاتورة مبيعات جديدة
        /// </summary>
        public async Task<bool> SaveSaleAsync(Sale sale)
        {
            try
            {
                // حفظ الفاتورة أولاً
                var saveResult = await _archiveService.SaveSaleAsync(sale);

                if (saveResult)
                {
                    // حفظ الخدمات في جدول الخدمات المفصل
                    await _servicesService.SaveInvoiceServicesAsync(sale);
                }

                return saveResult;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving sale: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب جميع الفواتير
        /// </summary>
        public async Task<List<Sale>> GetAllSalesAsync()
        {
            try
            {
                return await _archiveService.GetAllSalesAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting sales: {ex.Message}");
                return new List<Sale>();
            }
        }

        /// <summary>
        /// حفظ عميل جديد
        /// </summary>
        public async Task<int> SaveCustomerAsync(Customer customer)
        {
            return await _archiveService.SaveCustomerAsync(customer);
        }

        /// <summary>
        /// جلب جميع العملاء
        /// </summary>
        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            try
            {
                return await _archiveService.GetAllCustomersAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting customers: {ex.Message}");
                return new List<Customer>();
            }
        }

        /// <summary>
        /// جلب جميع الموردين
        /// </summary>
        public async Task<List<Supplier>> GetAllSuppliersAsync()
        {
            try
            {
                return await _archiveService.GetAllSuppliersAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting suppliers: {ex.Message}");
                return new List<Supplier>();
            }
        }

        /// <summary>
        /// حذف فاتورة مبيعات
        /// </summary>
        public async Task<bool> DeleteSaleAsync(string invoiceNumber)
        {
            try
            {
                // حذف سجلات الخدمات المرتبطة بالفاتورة أولاً
                await _servicesService.DeleteServiceRecordsByInvoiceAsync(invoiceNumber);

                // ثم حذف الفاتورة
                return await _archiveService.DeleteSaleAsync(invoiceNumber);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting sale: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب جميع الخدمات المستخدمة في الفواتير
        /// </summary>
        public async Task<List<string>> GetUsedServicesAsync()
        {
            try
            {
                var sales = await GetAllSalesAsync();
                var services = new HashSet<string>();

                foreach (var sale in sales)
                {
                    if (sale.SaleItems != null)
                    {
                        foreach (var item in sale.SaleItems)
                        {
                            if (!string.IsNullOrWhiteSpace(item.Service))
                            {
                                services.Add(item.Service);
                            }
                        }
                    }
                }

                return services.OrderBy(s => s).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting used services: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// جلب جميع أنواع الزجاج المستخدمة في الفواتير
        /// </summary>
        public async Task<List<string>> GetUsedGlassTypesAsync()
        {
            try
            {
                var sales = await GetAllSalesAsync();
                var glassTypes = new HashSet<string>();

                foreach (var sale in sales)
                {
                    if (sale.SaleItems != null)
                    {
                        foreach (var item in sale.SaleItems)
                        {
                            if (!string.IsNullOrWhiteSpace(item.GlassType))
                            {
                                glassTypes.Add(item.GlassType);
                            }
                        }
                    }
                }

                return glassTypes.OrderBy(g => g).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting used glass types: {ex.Message}");
                return new List<string>();
            }
        }

        /// <summary>
        /// جلب جميع سماكات الزجاج المستخدمة في الفواتير
        /// </summary>
        public async Task<List<decimal>> GetUsedGlassThicknessesAsync()
        {
            try
            {
                var sales = await GetAllSalesAsync();
                var thicknesses = new HashSet<decimal>();

                foreach (var sale in sales)
                {
                    if (sale.SaleItems != null)
                    {
                        foreach (var item in sale.SaleItems)
                        {
                            if (item.GlassThickness > 0)
                            {
                                thicknesses.Add(item.GlassThickness);
                            }
                        }
                    }
                }

                return thicknesses.OrderBy(t => t).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting used glass thicknesses: {ex.Message}");
                return new List<decimal>();
            }
        }

        /// <summary>
        /// الحصول على جميع العملاء (متزامن)
        /// </summary>
        public List<Customer> GetAllCustomers()
        {
            try
            {
                var task = GetAllCustomersAsync();
                task.Wait();
                return task.Result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting customers sync: {ex.Message}");
                return new List<Customer>();
            }
        }
    }
}
