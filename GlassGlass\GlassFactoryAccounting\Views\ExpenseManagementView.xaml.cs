using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class ExpenseManagementView : UserControl
    {
        private readonly ExpenseService _expenseService;
        private List<ExpenseCategory> _expenseCategories;

        public ExpenseManagementView()
        {
            InitializeComponent();
            _expenseService = new ExpenseService();
            _expenseCategories = new List<ExpenseCategory>();
            LoadExpenseCategories();
            InitializeForm();
        }

        private void LoadExpenseCategories()
        {
            try
            {
                _expenseCategories = _expenseService.GetAllExpenseCategories();
                DgExpenseCategories.ItemsSource = _expenseCategories;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المصروفات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeForm()
        {
            // تعيين التاريخ الحالي للمشروع
            DpProjectStartDate.SelectedDate = DateTime.Now;
        }

        private void ChkIsProjectRelated_Checked(object sender, RoutedEventArgs e)
        {
            ProjectDetailsPanel.Visibility = Visibility.Visible;
        }

        private void ChkIsProjectRelated_Unchecked(object sender, RoutedEventArgs e)
        {
            ProjectDetailsPanel.Visibility = Visibility.Collapsed;
            ClearProjectFields();
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var expenseCategory = new ExpenseCategory
                {
                    MainExpenseName = TxtMainExpenseName.Text.Trim(),
                    SubExpenseName = TxtSubExpenseName.Text.Trim(),
                    Notes = string.IsNullOrWhiteSpace(TxtNotes.Text) ? null : TxtNotes.Text.Trim(),
                    IsProjectRelated = ChkIsProjectRelated.IsChecked == true,
                    ProjectTitle = ChkIsProjectRelated.IsChecked == true ? TxtProjectTitle.Text.Trim() : null,
                    ProjectStartDate = ChkIsProjectRelated.IsChecked == true ? DpProjectStartDate.SelectedDate : null,
                    ProjectDescription = ChkIsProjectRelated.IsChecked == true && !string.IsNullOrWhiteSpace(TxtProjectDescription.Text) ? TxtProjectDescription.Text.Trim() : null,
                    ProjectNotes = ChkIsProjectRelated.IsChecked == true && !string.IsNullOrWhiteSpace(TxtProjectNotes.Text) ? TxtProjectNotes.Text.Trim() : null,
                    CreatedBy = "المستخدم الحالي"
                };

                var result = _expenseService.AddExpenseCategory(expenseCategory);

                if (result)
                {
                    MessageBox.Show("تم حفظ المصروف بنجاح!", "نجح الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                    LoadExpenseCategories();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ المصروف!", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المصروف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtMainExpenseName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المصروف الرئيسي", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtMainExpenseName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtSubExpenseName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المصروف الفرعي", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtSubExpenseName.Focus();
                return false;
            }

            // التحقق من عدم تكرار المصروف
            if (_expenseCategories.Any(ec => ec.MainExpenseName.Equals(TxtMainExpenseName.Text.Trim(), StringComparison.OrdinalIgnoreCase) &&
                                           ec.SubExpenseName.Equals(TxtSubExpenseName.Text.Trim(), StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("هذا المصروف موجود بالفعل", "بيانات مكررة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtMainExpenseName.Focus();
                return false;
            }

            // التحقق من بيانات المشروع إذا كان مرتبط بمشروع
            if (ChkIsProjectRelated.IsChecked == true)
            {
                if (string.IsNullOrWhiteSpace(TxtProjectTitle.Text))
                {
                    MessageBox.Show("يرجى إدخال عنوان المشروع", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtProjectTitle.Focus();
                    return false;
                }

                if (DpProjectStartDate.SelectedDate == null)
                {
                    MessageBox.Show("يرجى اختيار تاريخ بدء المشروع", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    DpProjectStartDate.Focus();
                    return false;
                }
            }

            return true;
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            TxtMainExpenseName.Clear();
            TxtSubExpenseName.Clear();
            TxtNotes.Clear();
            ChkIsProjectRelated.IsChecked = false;
            ProjectDetailsPanel.Visibility = Visibility.Collapsed;
            ClearProjectFields();
        }

        private void ClearProjectFields()
        {
            TxtProjectTitle.Clear();
            TxtProjectDescription.Clear();
            TxtProjectNotes.Clear();
            DpProjectStartDate.SelectedDate = DateTime.Now;
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseCategory category)
            {
                // تعبئة النموذج بالبيانات للتعديل
                TxtMainExpenseName.Text = category.MainExpenseName;
                TxtSubExpenseName.Text = category.SubExpenseName;
                TxtNotes.Text = category.Notes ?? "";
                ChkIsProjectRelated.IsChecked = category.IsProjectRelated;

                if (category.IsProjectRelated)
                {
                    ProjectDetailsPanel.Visibility = Visibility.Visible;
                    TxtProjectTitle.Text = category.ProjectTitle ?? "";
                    DpProjectStartDate.SelectedDate = category.ProjectStartDate;
                    TxtProjectDescription.Text = category.ProjectDescription ?? "";
                    TxtProjectNotes.Text = category.ProjectNotes ?? "";
                }

                MessageBox.Show($"تم تحديد المصروف: {category.MainExpenseName} - {category.SubExpenseName} للتعديل\nيمكنك الآن تعديل البيانات والضغط على حفظ المصروف", 
                    "تعديل المصروف", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseCategory category)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف المصروف: {category.MainExpenseName} - {category.SubExpenseName}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = _expenseService.DeleteExpenseCategory(category.Id);
                        
                        if (deleteResult)
                        {
                            MessageBox.Show("تم حذف المصروف بنجاح!", "نجح الحذف", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadExpenseCategories();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المصروف!", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المصروف: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
