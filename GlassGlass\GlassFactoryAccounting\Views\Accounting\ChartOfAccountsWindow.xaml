<Window x:Class="GlassFactoryAccounting.Views.Accounting.ChartOfAccountsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="شجرة الحسابات" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان وشريط الأدوات -->
        <Border Grid.Row="0" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="#F8F9FA" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="0" Text="🌳 شجرة الحسابات" 
                           FontSize="20" FontWeight="Bold" 
                           Foreground="#2C3E50" 
                           VerticalAlignment="Center"/>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="btnAddAccount" Content="➕ إضافة حساب" 
                            Background="#27AE60" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnAddAccount_Click"/>
                    
                    <Button Name="btnEditAccount" Content="✏️ تعديل" 
                            Background="#3498DB" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnEditAccount_Click"/>
                    
                    <Button Name="btnDeleteAccount" Content="🗑️ حذف" 
                            Background="#E74C3C" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnDeleteAccount_Click"/>
                    
                    <Button Name="btnRefresh" Content="🔄 تحديث" 
                            Background="#95A5A6" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnRefresh_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- شجرة الحسابات -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="White">
            <TreeView Name="tvAccounts" FontSize="14" Margin="10">
                <TreeView.ItemTemplate>
                    <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="{Binding AccountCode}" FontWeight="Bold" 
                                       Foreground="#3498DB" Margin="0,0,10,0"/>
                            <TextBlock Text="{Binding AccountName}" Margin="0,0,10,0"/>
                            <TextBlock Text="{Binding AccountTypeDisplay}" 
                                       Foreground="#7F8C8D" FontStyle="Italic"/>
                            <TextBlock Text="{Binding BalanceDisplay}" 
                                       Foreground="#E74C3C" FontWeight="Bold" 
                                       Margin="20,0,0,0"/>
                        </StackPanel>
                    </HierarchicalDataTemplate>
                </TreeView.ItemTemplate>
            </TreeView>
        </Border>
        
        <!-- معلومات الحساب المحدد -->
        <Border Grid.Row="2" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="#ECF0F1" Padding="15" Margin="0,15,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="كود الحساب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Name="txtSelectedAccountCode" Text="-" FontSize="14"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="اسم الحساب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Name="txtSelectedAccountName" Text="-" FontSize="14"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="نوع الحساب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Name="txtSelectedAccountType" Text="-" FontSize="14"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3">
                    <TextBlock Text="الرصيد الحالي:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Name="txtSelectedAccountBalance" Text="-" FontSize="14" FontWeight="Bold"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
