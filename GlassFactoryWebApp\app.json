{"name": "Glass Factory Accounting System", "description": "🏭 نظام حسابات مصنع الزجاج - Complete accounting system for glass factories with Arabic RTL support", "repository": "https://github.com/glassfactory/GlassFactoryWebApp", "logo": "https://cdn-icons-png.flaticon.com/512/2910/2910791.png", "keywords": ["accounting", "glass-factory", "arabic", "rtl", "asp.net-core", "react", "postgresql", "sales", "invoicing"], "image": "heroku/dotnet", "stack": "heroku-22", "buildpacks": [{"url": "https://github.com/heroku/heroku-buildpack-nodejs"}, {"url": "https://github.com/heroku/heroku-buildpack-dotnetcore"}], "formation": {"web": {"quantity": 1, "size": "basic"}}, "addons": [{"plan": "heroku-postgresql:mini"}], "env": {"ASPNETCORE_ENVIRONMENT": {"description": "ASP.NET Core environment", "value": "Production"}, "JWT_SECRET_KEY": {"description": "JWT secret key for authentication", "value": "GlassFactorySecretKey2025VeryLongAndSecureForProduction!"}, "DATABASE_URL": {"description": "PostgreSQL database URL (auto-populated by <PERSON><PERSON>)", "required": false}}, "scripts": {"postdeploy": "echo 'Glass Factory Accounting System deployed successfully!'"}, "success_url": "/", "website": "https://glassfactory.herokuapp.com"}