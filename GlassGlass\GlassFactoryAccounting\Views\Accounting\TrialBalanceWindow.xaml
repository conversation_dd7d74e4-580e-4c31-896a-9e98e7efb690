<Window x:Class="GlassFactoryAccounting.Views.Accounting.TrialBalanceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="ميزان المراجعة" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="⚖️ ميزان المراجعة" 
                   FontSize="24" FontWeight="Bold" 
                   Foreground="#2C3E50" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>
        
        <!-- شريط الأدوات -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="#F8F9FA" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- تاريخ الميزان -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,20,0">
                    <Label Content="كما في تاريخ:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <DatePicker Name="dpAsOfDate" FontSize="14" Padding="5" 
                                VerticalAlignment="Center" Width="150"/>
                </StackPanel>
                
                <!-- أزرار التحكم -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Name="btnGenerate" Content="📊 إنشاء الميزان" 
                            Background="#3498DB" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnGenerate_Click"/>
                    
                    <Button Name="btnExport" Content="📤 تصدير Excel" 
                            Background="#27AE60" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnExport_Click"/>
                    
                    <Button Name="btnPrint" Content="🖨️ طباعة" 
                            Background="#E67E22" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnPrint_Click"/>
                </StackPanel>
                
                <!-- حالة التوازن -->
                <Border Grid.Column="3" Name="borderBalanceStatus" 
                        Background="#27AE60" CornerRadius="5" Padding="10">
                    <TextBlock Name="txtBalanceStatus" Text="متوازن ✅" 
                               FontWeight="Bold" Foreground="White" 
                               HorizontalAlignment="Center"/>
                </Border>
            </Grid>
        </Border>
        
        <!-- جدول ميزان المراجعة -->
        <Border Grid.Row="2" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="White">
            <DataGrid Name="dgTrialBalance" 
                      AutoGenerateColumns="False" 
                      IsReadOnly="True"
                      GridLinesVisibility="All" 
                      HeadersVisibility="Column"
                      FontSize="12" 
                      Margin="10"
                      AlternatingRowBackground="#F8F9FA">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود الحساب" Binding="{Binding AccountCode}" 
                                        Width="120" FontWeight="Bold"/>
                    <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" 
                                        Width="300"/>
                    <DataGridTextColumn Header="نوع الحساب" Binding="{Binding AccountTypeDisplay}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="مدين" Binding="{Binding DebitBalance, StringFormat='{}{0:N2}'}" 
                                        Width="120"/>
                    <DataGridTextColumn Header="دائن" Binding="{Binding CreditBalance, StringFormat='{}{0:N2}'}" 
                                        Width="120"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
        
        <!-- الإجماليات -->
        <Border Grid.Row="3" Background="#34495E" Padding="15" Margin="0,10,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Grid.Column="1" Text="إجمالي المدين:" 
                           FontSize="16" FontWeight="Bold" 
                           Foreground="White" VerticalAlignment="Center" Margin="10,0"/>
                
                <TextBlock Grid.Column="2" Name="txtTotalDebit" Text="0.00" 
                           FontSize="16" FontWeight="Bold" 
                           Foreground="#27AE60" VerticalAlignment="Center" Margin="10,0"/>
                
                <TextBlock Grid.Column="3" Text="إجمالي الدائن:" 
                           FontSize="16" FontWeight="Bold" 
                           Foreground="White" VerticalAlignment="Center" Margin="10,0"/>
                
                <TextBlock Grid.Column="4" Name="txtTotalCredit" Text="0.00" 
                           FontSize="16" FontWeight="Bold" 
                           Foreground="#E74C3C" VerticalAlignment="Center" Margin="10,0"/>
            </Grid>
        </Border>
    </Grid>
</Window>
