using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة التقرير الكامل للرواتب والأجور
    /// </summary>
    public partial class PayrollFullReportView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;
        private List<EmployeeFullReport> _fullReportData;

        public PayrollFullReportView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadFullReport();
        }

        private void InitializeForm()
        {
            // تعيين فترة افتراضية (أول الشهر الحالي إلى اليوم)
            DpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            DpToDate.SelectedDate = DateTime.Now;
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.Items.Add(new { Id = 0, Name = "جميع الموظفين" });
                foreach (var employee in employees)
                {
                    CmbEmployee.Items.Add(employee);
                }
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
                CmbEmployee.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadFullReport()
        {
            try
            {
                _fullReportData = _payrollService.GenerateFullReport();
                FullReportDataGrid.ItemsSource = _fullReportData;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnFilter_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!DpFromDate.SelectedDate.HasValue || !DpToDate.SelectedDate.HasValue)
                {
                    MessageBox.Show("يرجى اختيار فترة التقرير", "بيانات ناقصة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (DpFromDate.SelectedDate > DpToDate.SelectedDate)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون أقل من تاريخ النهاية", "خطأ في التاريخ",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تطبيق الفلترة
                var filteredData = _fullReportData.AsEnumerable();

                // فلترة حسب الموظف إذا تم اختيار موظف محدد
                if (CmbEmployee.SelectedValue != null && (int)CmbEmployee.SelectedValue != 0)
                {
                    int selectedEmployeeId = (int)CmbEmployee.SelectedValue;
                    filteredData = filteredData.Where(r => r.EmployeeId == selectedEmployeeId);
                }

                FullReportDataGrid.ItemsSource = filteredData.ToList();

                MessageBox.Show($"تم تطبيق الفلترة للفترة من {DpFromDate.SelectedDate:yyyy/MM/dd} إلى {DpToDate.SelectedDate:yyyy/MM/dd}",
                    "تم تطبيق الفلترة", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلترة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnReset_Click(object sender, RoutedEventArgs e)
        {
            InitializeForm();
            CmbEmployee.SelectedIndex = 0;
            FullReportDataGrid.ItemsSource = _fullReportData;
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadFullReport();
        }

        private void FullReportDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // يمكن إضافة وظائف إضافية عند تحديد موظف
        }

        private void BtnSettle_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int employeeId)
                {
                    var employee = _fullReportData.FirstOrDefault(r => r.EmployeeId == employeeId);
                    if (employee != null)
                    {
                        var result = MessageBox.Show(
                            $"هل تريد إجراء تسوية لحساب الموظف: {employee.EmployeeName}؟\n" +
                            $"المتبقي الحالي: {employee.RemainingBalance:N2}",
                            "تأكيد التسوية",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            // هنا سيتم تطوير وظيفة التسوية
                            MessageBox.Show("سيتم تطوير وظيفة التسوية قريباً", "قيد التطوير",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إجراء التسوية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExportPDF_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int employeeId)
                {
                    var employee = _fullReportData.FirstOrDefault(r => r.EmployeeId == employeeId);
                    if (employee != null)
                    {
                        // هنا سيتم تطوير وظيفة تصدير PDF
                        MessageBox.Show($"سيتم تصدير كشف حساب الموظف: {employee.EmployeeName} إلى PDF قريباً",
                            "قيد التطوير", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnExportAll_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // هنا سيتم تطوير وظيفة تصدير التقرير الشامل
                MessageBox.Show("سيتم تطوير وظيفة تصدير التقرير الشامل قريباً", "قيد التطوير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
