using System;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using GlassFactoryAccounting.Models.Accounting;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views.Accounting
{
    /// <summary>
    /// نافذة قيد اليومية
    /// </summary>
    public partial class JournalEntryWindow : Window
    {
        private readonly AccountingService _accountingService;
        private readonly ObservableCollection<JournalEntryDetailViewModel> _details;
        
        public JournalEntryWindow()
        {
            InitializeComponent();
            _accountingService = new AccountingService();
            _details = new ObservableCollection<JournalEntryDetailViewModel>();
            
            InitializeWindow();
            AddInitialRows();
        }
        
        /// <summary>
        /// تهيئة النافذة
        /// </summary>
        private void InitializeWindow()
        {
            // تعيين التاريخ الحالي
            dpEntryDate.SelectedDate = DateTime.Now;
            
            // تعيين رقم القيد التلقائي
            txtEntryNumber.Text = GenerateEntryNumber();
            
            // ربط البيانات
            dgDetails.ItemsSource = _details;
            
            // ربط أحداث التغيير
            _details.CollectionChanged += (s, e) => UpdateTotals();
        }
        
        /// <summary>
        /// إضافة صفوف أولية
        /// </summary>
        private void AddInitialRows()
        {
            for (int i = 0; i < 4; i++)
            {
                AddNewDetail();
            }
        }
        
        /// <summary>
        /// توليد رقم قيد تلقائي
        /// </summary>
        private string GenerateEntryNumber()
        {
            return $"JE{DateTime.Now:yyyyMMddHHmmss}";
        }
        
        /// <summary>
        /// إضافة سطر جديد
        /// </summary>
        private void BtnAddDetail_Click(object sender, RoutedEventArgs e)
        {
            AddNewDetail();
        }
        
        /// <summary>
        /// إضافة تفصيل جديد
        /// </summary>
        private void AddNewDetail()
        {
            var detail = new JournalEntryDetailViewModel
            {
                LineNumber = _details.Count + 1
            };
            
            detail.PropertyChanged += Detail_PropertyChanged;
            _details.Add(detail);
        }
        
        /// <summary>
        /// حذف سطر محدد
        /// </summary>
        private void BtnRemoveDetail_Click(object sender, RoutedEventArgs e)
        {
            if (dgDetails.SelectedItem is JournalEntryDetailViewModel selectedDetail)
            {
                _details.Remove(selectedDetail);
                UpdateLineNumbers();
                UpdateTotals();
            }
        }
        
        /// <summary>
        /// تحديث أرقام الأسطر
        /// </summary>
        private void UpdateLineNumbers()
        {
            for (int i = 0; i < _details.Count; i++)
            {
                _details[i].LineNumber = i + 1;
            }
        }
        
        /// <summary>
        /// معالج تغيير خصائص التفاصيل
        /// </summary>
        private void Detail_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            if (e.PropertyName == nameof(JournalEntryDetailViewModel.DebitAmount) ||
                e.PropertyName == nameof(JournalEntryDetailViewModel.CreditAmount))
            {
                UpdateTotals();
            }
        }
        
        /// <summary>
        /// تحديث الإجماليات
        /// </summary>
        private void UpdateTotals()
        {
            var totalDebit = _details.Sum(d => d.DebitAmount);
            var totalCredit = _details.Sum(d => d.CreditAmount);
            
            txtTotalDebit.Text = totalDebit.ToString("N2");
            txtTotalCredit.Text = totalCredit.ToString("N2");
            
            // تحديث حالة التوازن
            UpdateBalanceStatus(totalDebit, totalCredit);
        }
        
        /// <summary>
        /// تحديث حالة التوازن
        /// </summary>
        private void UpdateBalanceStatus(decimal totalDebit, decimal totalCredit)
        {
            if (totalDebit == totalCredit && totalDebit > 0)
            {
                borderBalance.Background = new SolidColorBrush(Color.FromRgb(39, 174, 96)); // أخضر
                txtBalanceStatus.Text = "✅ القيد متوازن - جاهز للحفظ والترحيل";
                btnSave.IsEnabled = true;
                btnPost.IsEnabled = true;
            }
            else
            {
                borderBalance.Background = new SolidColorBrush(Color.FromRgb(231, 76, 60)); // أحمر
                txtBalanceStatus.Text = "⚠️ القيد غير متوازن - يجب أن يكون إجمالي المدين = إجمالي الدائن";
                btnSave.IsEnabled = false;
                btnPost.IsEnabled = false;
            }
        }
        
        /// <summary>
        /// حفظ القيد مع التحقق المحسن
        /// </summary>
        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateEntry())
                    return;

                // إنشاء قيد اليومية
                var entry = CreateJournalEntry(false); // حفظ بدون ترحيل

                if (_accountingService.CreateJournalEntry(entry))
                {
                    MessageBox.Show("تم حفظ القيد بنجاح!", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ القيد. يرجى التحقق من البيانات.", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ القيد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// ترحيل القيد مع التحقق المحسن
        /// </summary>
        private void BtnPost_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateEntry())
                    return;

                var result = MessageBox.Show(
                    "هل أنت متأكد من ترحيل القيد؟\n\nبعد الترحيل لن يمكن تعديل القيد وسيتم تحديث أرصدة الحسابات.",
                    "تأكيد الترحيل",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // إنشاء قيد اليومية مع الترحيل
                    var entry = CreateJournalEntry(true); // حفظ مع ترحيل

                    if (_accountingService.CreateJournalEntry(entry))
                    {
                        MessageBox.Show("تم ترحيل القيد بنجاح!\n\nتم تحديث أرصدة الحسابات المرتبطة.",
                            "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                        ClearForm();
                    }
                    else
                    {
                        MessageBox.Show("فشل في ترحيل القيد. يرجى التحقق من البيانات.", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ترحيل القيد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من مسح جميع البيانات؟",
                "تأكيد المسح",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                ClearForm();
            }
        }
        
        /// <summary>
        /// مسح النموذج
        /// </summary>
        private void ClearForm()
        {
            txtEntryNumber.Text = GenerateEntryNumber();
            dpEntryDate.SelectedDate = DateTime.Now;
            txtDescription.Clear();
            txtReferenceNumber.Clear();
            txtReferenceType.Clear();

            _details.Clear();
            AddInitialRows();
            UpdateTotals();
        }

        /// <summary>
        /// التحقق من صحة البيانات
        /// </summary>
        private bool ValidateEntry()
        {
            // التحقق من وجود وصف
            if (string.IsNullOrWhiteSpace(txtDescription.Text))
            {
                MessageBox.Show("يرجى إدخال وصف القيد.", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtDescription.Focus();
                return false;
            }

            // التحقق من وجود تفاصيل صحيحة
            var validDetails = _details.Where(d =>
                !string.IsNullOrWhiteSpace(d.AccountCode) &&
                (d.DebitAmount > 0 || d.CreditAmount > 0)).ToList();

            if (validDetails.Count < 2)
            {
                MessageBox.Show("يجب أن يحتوي القيد على سطرين صحيحين على الأقل.", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // التحقق من التوازن
            var totalDebit = validDetails.Sum(d => d.DebitAmount);
            var totalCredit = validDetails.Sum(d => d.CreditAmount);

            if (totalDebit != totalCredit)
            {
                MessageBox.Show("القيد غير متوازن. يجب أن يكون إجمالي المدين = إجمالي الدائن.", "قيد غير متوازن",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            // التحقق من صحة الحسابات
            foreach (var detail in validDetails)
            {
                var account = _accountingService.GetAccountByCode(detail.AccountCode);
                if (account == null)
                {
                    MessageBox.Show($"الحساب '{detail.AccountCode}' غير موجود.", "حساب غير صحيح",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }

                if (account.IsParent)
                {
                    MessageBox.Show($"لا يمكن استخدام الحساب الرئيسي '{account.AccountName}' في القيود.\nيرجى استخدام الحسابات الفرعية فقط.",
                        "حساب غير صالح", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// إنشاء قيد اليومية
        /// </summary>
        private JournalEntry CreateJournalEntry(bool isPosted)
        {
            var entry = new JournalEntry
            {
                EntryNumber = txtEntryNumber.Text,
                EntryDate = dpEntryDate.SelectedDate ?? DateTime.Now,
                Description = txtDescription.Text,
                ReferenceNumber = txtReferenceNumber.Text,
                ReferenceType = txtReferenceType.Text,
                IsPosted = isPosted,
                CreatedBy = "المستخدم الحالي", // يمكن تحسينه لاحقاً
                Details = new List<JournalEntryDetail>()
            };

            // إضافة التفاصيل الصحيحة فقط
            var validDetails = _details.Where(d =>
                !string.IsNullOrWhiteSpace(d.AccountCode) &&
                (d.DebitAmount > 0 || d.CreditAmount > 0)).ToList();

            foreach (var detail in validDetails)
            {
                var account = _accountingService.GetAccountByCode(detail.AccountCode);
                if (account != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = account.Id,
                        DebitAmount = detail.DebitAmount,
                        CreditAmount = detail.CreditAmount,
                        Description = detail.Description
                    });
                }
            }

            return entry;
        }
    }
    
    /// <summary>
    /// نموذج عرض تفاصيل قيد اليومية
    /// </summary>
    public class JournalEntryDetailViewModel : INotifyPropertyChanged
    {
        private int _lineNumber;
        private string _accountCode = "";
        private string _accountName = "";
        private string _description = "";
        private decimal _debitAmount;
        private decimal _creditAmount;
        
        public int LineNumber
        {
            get => _lineNumber;
            set { _lineNumber = value; OnPropertyChanged(); }
        }
        
        public string AccountCode
        {
            get => _accountCode;
            set { _accountCode = value ?? ""; OnPropertyChanged(); }
        }
        
        public string AccountName
        {
            get => _accountName;
            set { _accountName = value ?? ""; OnPropertyChanged(); }
        }
        
        public string Description
        {
            get => _description;
            set { _description = value ?? ""; OnPropertyChanged(); }
        }
        
        public decimal DebitAmount
        {
            get => _debitAmount;
            set { _debitAmount = value; OnPropertyChanged(); }
        }
        
        public decimal CreditAmount
        {
            get => _creditAmount;
            set { _creditAmount = value; OnPropertyChanged(); }
        }
        
        public event PropertyChangedEventHandler? PropertyChanged;
        
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
