<Window x:Class="GlassFactoryAccounting.Views.AddCustomerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة عميل جديد" Height="500" Width="600"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14"
        WindowStartupLocation="CenterOwner"
        ResizeMode="NoResize">
    
    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <TextBlock Text="👤 إضافة عميل جديد" FontSize="20" FontWeight="Bold" 
                     Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- نموذج البيانات -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- اسم العميل -->
                <StackPanel Grid.Row="0" Margin="0,0,0,15">
                    <TextBlock Text="اسم العميل: *" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtCustomerName" Height="35" VerticalContentAlignment="Center"
                             BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="2"/>
                </StackPanel>
                
                <!-- رقم الهاتف -->
                <StackPanel Grid.Row="1" Margin="0,0,0,15">
                    <TextBlock Text="رقم الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtCustomerPhone" Height="35" VerticalContentAlignment="Center"
                             BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="1"/>
                </StackPanel>
                
                <!-- البريد الإلكتروني -->
                <StackPanel Grid.Row="2" Margin="0,0,0,15">
                    <TextBlock Text="البريد الإلكتروني:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtCustomerEmail" Height="35" VerticalContentAlignment="Center"
                             BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="1"/>
                </StackPanel>
                
                <!-- العنوان -->
                <StackPanel Grid.Row="3" Margin="0,0,0,15">
                    <TextBlock Text="العنوان:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtCustomerAddress" Height="35" VerticalContentAlignment="Center"
                             BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="1"/>
                </StackPanel>
                
                <!-- الشخص المسؤول -->
                <StackPanel Grid.Row="4" Margin="0,0,0,15">
                    <TextBlock Text="الشخص المسؤول:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtContactPerson" Height="35" VerticalContentAlignment="Center"
                             BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="1"/>
                </StackPanel>
                
                <!-- ملاحظات -->
                <StackPanel Grid.Row="5">
                    <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtCustomerNotes" Height="80" TextWrapping="Wrap" AcceptsReturn="True"
                             VerticalScrollBarVisibility="Auto"
                             BorderBrush="{StaticResource PrimaryBrush}" BorderThickness="1"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ العميل" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,15,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSave_Click"/>
                
                <Button x:Name="BtnCancel" Content="❌ إلغاء" 
                        Background="{StaticResource DangerBrush}" Foreground="White"
                        Padding="20,10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
