﻿#pragma checksum "..\..\..\..\Views\ManufacturingOrderView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "FC31FEBC133AE8FAD88AD2C4E03A6415B3D957CC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ManufacturingOrderView
    /// </summary>
    public partial class ManufacturingOrderView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 29 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnBack;
        
        #line default
        #line hidden
        
        
        #line 38 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnToggleNavigationTools;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox BasicDataPanel;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtOrderNumber;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtCustomerName;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtInvoiceNumber;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpOrderDate;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbOrderStatus;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtNotes;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddGlassPanel;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgGlassPanels;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalGlassSquareMeters;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalGlassValue;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox ServicesPanel;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkFilmService;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkDoubleGlassService;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkBevelService;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkThermalService;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkSGPService;
        
        #line default
        #line hidden
        
        
        #line 178 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkPrintService;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkCNCService;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkSprayService;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkDrawingService;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel spServiceDetails;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddRequiredSize;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgRequiredSizes;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalRequiredMeters;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalLinearMeters;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox ServiceCostsPanel;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCalculateServiceCosts;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel spServiceCosts;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddServiceCost;
        
        #line default
        #line hidden
        
        
        #line 267 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgServiceCosts;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalServiceCosts;
        
        #line default
        #line hidden
        
        
        #line 307 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox AdditionalCostsPanel;
        
        #line default
        #line hidden
        
        
        #line 309 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddAdditionalCost;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgAdditionalCosts;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalAdditionalCosts;
        
        #line default
        #line hidden
        
        
        #line 351 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox CostSummaryPanel;
        
        #line default
        #line hidden
        
        
        #line 369 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryServiceCosts;
        
        #line default
        #line hidden
        
        
        #line 372 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryAdditionalCosts;
        
        #line default
        #line hidden
        
        
        #line 375 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryGlassCosts;
        
        #line default
        #line hidden
        
        
        #line 380 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryTotalCost;
        
        #line default
        #line hidden
        
        
        #line 383 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryTotalMeters;
        
        #line default
        #line hidden
        
        
        #line 386 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryPricePerMeter;
        
        #line default
        #line hidden
        
        
        #line 391 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.GroupBox WasteCalculationPanel;
        
        #line default
        #line hidden
        
        
        #line 408 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtGlassWaste;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtGlassWastePercentage;
        
        #line default
        #line hidden
        
        
        #line 417 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtFilmWaste;
        
        #line default
        #line hidden
        
        
        #line 420 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtFilmWastePercentage;
        
        #line default
        #line hidden
        
        
        #line 426 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSaveOrder;
        
        #line default
        #line hidden
        
        
        #line 431 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnViewOrder;
        
        #line default
        #line hidden
        
        
        #line 436 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrintPDF;
        
        #line default
        #line hidden
        
        
        #line 441 "..\..\..\..\Views\ManufacturingOrderView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnTransferToDelivery;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/manufacturingorderview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnBack = ((System.Windows.Controls.Button)(target));
            
            #line 32 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnToggleNavigationTools = ((System.Windows.Controls.Button)(target));
            
            #line 45 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnToggleNavigationTools.Click += new System.Windows.RoutedEventHandler(this.BtnToggleNavigationTools_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BasicDataPanel = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 4:
            this.txtOrderNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.txtCustomerName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.txtInvoiceNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.dpOrderDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 8:
            this.cmbOrderStatus = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.txtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.btnAddGlassPanel = ((System.Windows.Controls.Button)(target));
            
            #line 107 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnAddGlassPanel.Click += new System.Windows.RoutedEventHandler(this.BtnAddGlassPanel_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.dgGlassPanels = ((System.Windows.Controls.DataGrid)(target));
            
            #line 113 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.dgGlassPanels.CellEditEnding += new System.EventHandler<System.Windows.Controls.DataGridCellEditEndingEventArgs>(this.DgGlassPanels_CellEditEnding);
            
            #line default
            #line hidden
            return;
            case 14:
            this.txtTotalGlassSquareMeters = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.txtTotalGlassValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.ServicesPanel = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 17:
            this.chkFilmService = ((System.Windows.Controls.CheckBox)(target));
            
            #line 167 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.chkFilmService.Checked += new System.Windows.RoutedEventHandler(this.ServiceCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 167 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.chkFilmService.Unchecked += new System.Windows.RoutedEventHandler(this.ServiceCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 18:
            this.chkDoubleGlassService = ((System.Windows.Controls.CheckBox)(target));
            
            #line 169 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.chkDoubleGlassService.Checked += new System.Windows.RoutedEventHandler(this.ServiceCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 169 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.chkDoubleGlassService.Unchecked += new System.Windows.RoutedEventHandler(this.ServiceCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 19:
            this.chkBevelService = ((System.Windows.Controls.CheckBox)(target));
            
            #line 171 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.chkBevelService.Checked += new System.Windows.RoutedEventHandler(this.ServiceCheckBox_Checked);
            
            #line default
            #line hidden
            
            #line 171 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.chkBevelService.Unchecked += new System.Windows.RoutedEventHandler(this.ServiceCheckBox_Unchecked);
            
            #line default
            #line hidden
            return;
            case 20:
            this.chkThermalService = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.chkSGPService = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.chkPrintService = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 23:
            this.chkCNCService = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 24:
            this.chkSprayService = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 25:
            this.chkDrawingService = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.spServiceDetails = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 27:
            this.btnAddRequiredSize = ((System.Windows.Controls.Button)(target));
            
            #line 195 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnAddRequiredSize.Click += new System.Windows.RoutedEventHandler(this.BtnAddRequiredSize_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.dgRequiredSizes = ((System.Windows.Controls.DataGrid)(target));
            
            #line 201 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.dgRequiredSizes.CellEditEnding += new System.EventHandler<System.Windows.Controls.DataGridCellEditEndingEventArgs>(this.DgRequiredSizes_CellEditEnding);
            
            #line default
            #line hidden
            return;
            case 31:
            this.txtTotalRequiredMeters = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 32:
            this.txtTotalLinearMeters = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 33:
            this.ServiceCostsPanel = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 34:
            this.btnCalculateServiceCosts = ((System.Windows.Controls.Button)(target));
            
            #line 259 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnCalculateServiceCosts.Click += new System.Windows.RoutedEventHandler(this.BtnCalculateServiceCosts_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.spServiceCosts = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 36:
            this.btnAddServiceCost = ((System.Windows.Controls.Button)(target));
            
            #line 265 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnAddServiceCost.Click += new System.Windows.RoutedEventHandler(this.BtnAddServiceCost_Click);
            
            #line default
            #line hidden
            return;
            case 37:
            this.dgServiceCosts = ((System.Windows.Controls.DataGrid)(target));
            
            #line 271 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.dgServiceCosts.CellEditEnding += new System.EventHandler<System.Windows.Controls.DataGridCellEditEndingEventArgs>(this.DgServiceCosts_CellEditEnding);
            
            #line default
            #line hidden
            return;
            case 40:
            this.txtTotalServiceCosts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 41:
            this.AdditionalCostsPanel = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 42:
            this.btnAddAdditionalCost = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnAddAdditionalCost.Click += new System.Windows.RoutedEventHandler(this.BtnAddAdditionalCost_Click);
            
            #line default
            #line hidden
            return;
            case 43:
            this.dgAdditionalCosts = ((System.Windows.Controls.DataGrid)(target));
            
            #line 318 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.dgAdditionalCosts.CellEditEnding += new System.EventHandler<System.Windows.Controls.DataGridCellEditEndingEventArgs>(this.DgAdditionalCosts_CellEditEnding);
            
            #line default
            #line hidden
            return;
            case 46:
            this.txtTotalAdditionalCosts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 47:
            this.CostSummaryPanel = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 48:
            this.txtSummaryServiceCosts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 49:
            this.txtSummaryAdditionalCosts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 50:
            this.txtSummaryGlassCosts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 51:
            this.txtSummaryTotalCost = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 52:
            this.txtSummaryTotalMeters = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 53:
            this.txtSummaryPricePerMeter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 54:
            this.WasteCalculationPanel = ((System.Windows.Controls.GroupBox)(target));
            return;
            case 55:
            this.txtGlassWaste = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 56:
            this.txtGlassWastePercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 57:
            this.txtFilmWaste = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 58:
            this.txtFilmWastePercentage = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 59:
            this.btnSaveOrder = ((System.Windows.Controls.Button)(target));
            
            #line 429 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnSaveOrder.Click += new System.Windows.RoutedEventHandler(this.BtnSaveOrder_Click);
            
            #line default
            #line hidden
            return;
            case 60:
            this.btnViewOrder = ((System.Windows.Controls.Button)(target));
            
            #line 434 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnViewOrder.Click += new System.Windows.RoutedEventHandler(this.BtnViewOrder_Click);
            
            #line default
            #line hidden
            return;
            case 61:
            this.btnPrintPDF = ((System.Windows.Controls.Button)(target));
            
            #line 439 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnPrintPDF.Click += new System.Windows.RoutedEventHandler(this.BtnPrintPDF_Click);
            
            #line default
            #line hidden
            return;
            case 62:
            this.btnTransferToDelivery = ((System.Windows.Controls.Button)(target));
            
            #line 444 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            this.btnTransferToDelivery.Click += new System.Windows.RoutedEventHandler(this.BtnTransferToDelivery_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 12:
            
            #line 134 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditGlassPanel_Click);
            
            #line default
            #line hidden
            break;
            case 13:
            
            #line 137 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteGlassPanel_Click);
            
            #line default
            #line hidden
            break;
            case 29:
            
            #line 222 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditRequiredSize_Click);
            
            #line default
            #line hidden
            break;
            case 30:
            
            #line 225 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteRequiredSize_Click);
            
            #line default
            #line hidden
            break;
            case 38:
            
            #line 287 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditServiceCost_Click);
            
            #line default
            #line hidden
            break;
            case 39:
            
            #line 290 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteServiceCost_Click);
            
            #line default
            #line hidden
            break;
            case 44:
            
            #line 332 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditAdditionalCost_Click);
            
            #line default
            #line hidden
            break;
            case 45:
            
            #line 335 "..\..\..\..\Views\ManufacturingOrderView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteAdditionalCost_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

