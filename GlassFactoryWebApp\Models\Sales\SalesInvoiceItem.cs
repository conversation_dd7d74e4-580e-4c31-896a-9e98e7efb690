using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج عنصر فاتورة المبيعات
    /// </summary>
    public class SalesInvoiceItem : BaseEntity
    {
        [Required]
        public int SalesInvoiceId { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Required]
        [MaxLength(20)]
        public string ProductCode { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string ProductName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string ProductDescription { get; set; } = string.Empty;

        [MaxLength(50)]
        public string Unit { get; set; } = "قطعة";

        [Column(TypeName = "decimal(18,3)")]
        public decimal Quantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal UnitPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DiscountAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxPercentage { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TaxAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal Profit { get; set; } = 0;

        public int LineNumber { get; set; } = 1;

        [MaxLength(200)]
        public string? ItemNotes { get; set; }

        // Glass-specific properties
        [Column(TypeName = "decimal(18,2)")]
        public decimal? Width { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Height { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? Thickness { get; set; }

        [MaxLength(50)]
        public string? GlassType { get; set; }

        [MaxLength(50)]
        public string? GlassColor { get; set; }

        [MaxLength(100)]
        public string? FinishingType { get; set; }

        // Foreign Keys
        [ForeignKey("SalesInvoiceId")]
        public virtual SalesInvoice SalesInvoice { get; set; } = null!;

        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;
    }
}
