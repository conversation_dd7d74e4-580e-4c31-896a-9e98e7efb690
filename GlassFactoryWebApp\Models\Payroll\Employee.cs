using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج الموظف
    /// </summary>
    public class Employee : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string EmployeeCode { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string FirstName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string LastName { get; set; } = string.Empty;

        [MaxLength(200)]
        public string FullName => $"{FirstName} {LastName}";

        [MaxLength(20)]
        public string? NationalId { get; set; }

        [MaxLength(20)]
        public string? PassportNumber { get; set; }

        public DateTime? BirthDate { get; set; }

        [MaxLength(10)]
        public string? Gender { get; set; } // ذكر، أنثى

        [MaxLength(20)]
        public string? MaritalStatus { get; set; } // أعزب، متزوج، مطلق، أرمل

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(20)]
        public string? Mobile { get; set; }

        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(500)]
        public string? Address { get; set; }

        [MaxLength(100)]
        public string? City { get; set; }

        [MaxLength(100)]
        public string? Country { get; set; }

        // Employment Information
        [Required]
        public int DepartmentId { get; set; }

        [Required]
        [MaxLength(100)]
        public string Position { get; set; } = string.Empty;

        [Required]
        public DateTime HireDate { get; set; }

        public DateTime? TerminationDate { get; set; }

        [MaxLength(50)]
        public string EmploymentStatus { get; set; } = "نشط"; // نشط، معلق، منتهي

        [MaxLength(50)]
        public string EmploymentType { get; set; } = "دوام كامل"; // دوام كامل، دوام جزئي، مؤقت

        // Salary Information
        [Column(TypeName = "decimal(18,2)")]
        public decimal BasicSalary { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal HousingAllowance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TransportationAllowance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal FoodAllowance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OtherAllowances { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalSalary { get; set; } = 0;

        // Working Hours
        public int WorkingHoursPerDay { get; set; } = 8;

        public int WorkingDaysPerWeek { get; set; } = 6;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OvertimeRate { get; set; } = 1.5m;

        // Benefits
        public bool HasMedicalInsurance { get; set; } = false;

        public bool HasSocialInsurance { get; set; } = false;

        public int VacationDaysPerYear { get; set; } = 21;

        public int SickLeaveDaysPerYear { get; set; } = 30;

        // Bank Information
        [MaxLength(100)]
        public string? BankName { get; set; }

        [MaxLength(50)]
        public string? BankAccountNumber { get; set; }

        [MaxLength(50)]
        public string? IBAN { get; set; }

        // Emergency Contact
        [MaxLength(100)]
        public string? EmergencyContactName { get; set; }

        [MaxLength(20)]
        public string? EmergencyContactPhone { get; set; }

        [MaxLength(100)]
        public string? EmergencyContactRelation { get; set; }

        public bool IsActive { get; set; } = true;

        [MaxLength(200)]
        public string? PhotoPath { get; set; }

        // Foreign Keys
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; } = null!;

        // Navigation Properties
        public virtual ICollection<PayrollRecord> PayrollRecords { get; set; } = new List<PayrollRecord>();
        public virtual ICollection<Attendance> AttendanceRecords { get; set; } = new List<Attendance>();
        public virtual ICollection<LeaveRequest> LeaveRequests { get; set; } = new List<LeaveRequest>();
        public virtual ICollection<EmployeeAdvance> Advances { get; set; } = new List<EmployeeAdvance>();
    }
}
