<UserControl x:Class="GlassFactoryAccounting.Views.ReportsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#17A2B8"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ReportButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Padding" Value="20,15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Height" Value="120"/>
            <Setter Property="Width" Value="200"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="10" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <Border.Effect>
                                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.4" BlurRadius="8"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📊 موديول التقارير" FontSize="28" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                
                <Button x:Name="BtnRefreshReports" Content="🔄 تحديث" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="15,8" Margin="30,0,0,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnRefreshReports_Click"/>
            </StackPanel>
        </Border>
        
        <!-- إحصائيات سريعة -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="📈 إجمالي المبيعات" FontWeight="Bold" FontSize="14" 
                             Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtTotalSales" Text="0.00 ج.م" FontSize="20" FontWeight="Bold" 
                             Foreground="{StaticResource SuccessBrush}" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="🧾 عدد الفواتير" FontWeight="Bold" FontSize="14" 
                             Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtTotalInvoices" Text="0" FontSize="20" FontWeight="Bold" 
                             Foreground="{StaticResource InfoBrush}" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="👥 عدد العملاء" FontWeight="Bold" FontSize="14" 
                             Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtTotalCustomers" Text="0" FontSize="20" FontWeight="Bold" 
                             Foreground="{StaticResource WarningBrush}" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="📅 آخر فاتورة" FontWeight="Bold" FontSize="14" 
                             Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtLastInvoiceDate" Text="لا توجد" FontSize="16" FontWeight="Bold" 
                             Foreground="{StaticResource DangerBrush}" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- أنواع التقارير -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="📋 أنواع التقارير المتاحة" FontSize="20" FontWeight="Bold" 
                         Margin="0,0,0,20" Foreground="{StaticResource PrimaryBrush}"/>
                
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                    <WrapPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        
                        <!-- تقرير المبيعات -->
                        <Button x:Name="BtnSalesReport" Style="{StaticResource ReportButtonStyle}"
                                Background="{StaticResource SuccessBrush}" Click="BtnSalesReport_Click">
                            <StackPanel>
                                <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تقرير المبيعات" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                <TextBlock Text="عرض جميع فواتير المبيعات" FontSize="12" 
                                         HorizontalAlignment="Center" Opacity="0.8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <!-- تقرير التصنيع -->
                        <Button x:Name="BtnManufacturingReport" Style="{StaticResource ReportButtonStyle}"
                                Background="{StaticResource WarningBrush}" Click="BtnManufacturingReport_Click">
                            <StackPanel>
                                <TextBlock Text="🏭" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تقرير التصنيع" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                <TextBlock Text="تقارير الإنتاج والتصنيع" FontSize="12" 
                                         HorizontalAlignment="Center" Opacity="0.8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <!-- تقرير خدمات المبيعات -->
                        <Button x:Name="BtnServicesReport" Style="{StaticResource ReportButtonStyle}"
                                Background="{StaticResource InfoBrush}" Click="BtnServicesReport_Click">
                            <StackPanel>
                                <TextBlock Text="🔧" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تقرير خدمات المبيعات" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                <TextBlock Text="تحليل الخدمات المقدمة" FontSize="12" 
                                         HorizontalAlignment="Center" Opacity="0.8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <!-- تقرير العملاء -->
                        <Button x:Name="BtnCustomersReport" Style="{StaticResource ReportButtonStyle}"
                                Background="{StaticResource PrimaryBrush}" Click="BtnCustomersReport_Click">
                            <StackPanel>
                                <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تقرير العملاء" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                <TextBlock Text="تحليل بيانات العملاء" FontSize="12" 
                                         HorizontalAlignment="Center" Opacity="0.8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <!-- تقرير مخصص -->
                        <Button x:Name="BtnCustomReport" Style="{StaticResource ReportButtonStyle}"
                                Background="{StaticResource DangerBrush}" Click="BtnCustomReport_Click">
                            <StackPanel>
                                <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تقرير مخصص" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                <TextBlock Text="إنشاء تقارير مخصصة" FontSize="12" 
                                         HorizontalAlignment="Center" Opacity="0.8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                        <!-- إضافة تقرير جديد -->
                        <Button x:Name="BtnAddNewReport" Style="{StaticResource ReportButtonStyle}"
                                Background="#6C757D" Click="BtnAddNewReport_Click">
                            <StackPanel>
                                <TextBlock Text="➕" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="إضافة تقرير جديد" FontSize="16" FontWeight="Bold" 
                                         HorizontalAlignment="Center" TextWrapping="Wrap"/>
                                <TextBlock Text="توسيع أنواع التقارير" FontSize="12" 
                                         HorizontalAlignment="Center" Opacity="0.8" Margin="0,5,0,0"/>
                            </StackPanel>
                        </Button>
                        
                    </WrapPanel>
                </ScrollViewer>
            </Grid>
        </Border>
    </Grid>
</UserControl>
