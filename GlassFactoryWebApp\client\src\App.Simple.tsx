import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Container, Typography, Box, Card, CardContent, Button, Grid } from '@mui/material';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Create RTL theme for Arabic
const theme = createTheme({
  direction: 'rtl',
  typography: {
    fontFamily: '"Segoe UI", "Tahoma", "Arial", sans-serif',
  },
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Create React Query client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Home Page Component
const HomePage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 4 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          🏭 نظام حسابات مصنع الزجاج
        </Typography>
        <Typography variant="h5" color="text.secondary" gutterBottom>
          Glass Factory Accounting System
        </Typography>
        <Typography variant="body1" sx={{ mt: 2 }}>
          نظام حسابات متكامل مصمم خصيصاً لمصانع الزجاج
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                💰 موديول المبيعات
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                إدارة شاملة للمبيعات والعملاء والفواتير
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  ✅ إدارة العملاء
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  ✅ فواتير المبيعات
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  ✅ مدفوعات العملاء
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  ✅ التقارير والإحصائيات
                </Typography>
              </Box>
              <Button 
                variant="contained" 
                fullWidth 
                sx={{ mt: 2 }}
                href="/sales"
              >
                دخول موديول المبيعات
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                📋 API Documentation
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                واجهة برمجة التطبيقات الموثقة
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  📊 جميع APIs موثقة
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  🔐 أمان متقدم مع JWT
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  🧪 اختبار مباشر للـ APIs
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  📖 أمثلة شاملة
                </Typography>
              </Box>
              <Button 
                variant="outlined" 
                fullWidth 
                sx={{ mt: 2 }}
                href="/swagger"
                target="_blank"
              >
                عرض API Documentation
              </Button>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h5" component="h2" gutterBottom>
                🚀 حالة النظام
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="success.main">
                      ✅ Backend API
                    </Typography>
                    <Typography variant="body2">
                      يعمل بنجاح
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="success.main">
                      ✅ قاعدة البيانات
                    </Typography>
                    <Typography variant="body2">
                      PostgreSQL متصلة
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="success.main">
                      ✅ Frontend
                    </Typography>
                    <Typography variant="body2">
                      React يعمل بنجاح
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <Box sx={{ textAlign: 'center', p: 2 }}>
                    <Typography variant="h6" color="success.main">
                      ✅ النشر
                    </Typography>
                    <Typography variant="body2">
                      Railway.app
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
              <Box sx={{ mt: 2, textAlign: 'center' }}>
                <Button 
                  variant="text" 
                  href="/health"
                  target="_blank"
                >
                  فحص صحة النظام
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mt: 4, textAlign: 'center' }}>
        <Typography variant="body2" color="text.secondary">
          تم تطوير النظام بواسطة: حسام محمد حسان أحمد
        </Typography>
        <Typography variant="body2" color="text.secondary">
          الإصدار: 2.0.0 | البيئة: Production | المنصة: Railway.app
        </Typography>
      </Box>
    </Container>
  );
};

// Sales Module Page
const SalesPage: React.FC = () => {
  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        💰 موديول المبيعات
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        مرحباً بك في موديول المبيعات - قريباً ستكون جميع الوظائف متاحة
      </Typography>
      
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                👥 إدارة العملاء
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                إضافة وتعديل وحذف العملاء
              </Typography>
              <Button variant="contained" fullWidth disabled>
                قريباً
              </Button>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                📋 فواتير المبيعات
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                إنشاء وطباعة وترحيل الفواتير
              </Typography>
              <Button variant="contained" fullWidth disabled>
                قريباً
              </Button>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                💳 المدفوعات
              </Typography>
              <Typography variant="body2" sx={{ mb: 2 }}>
                تسجيل ومتابعة مدفوعات العملاء
              </Typography>
              <Button variant="contained" fullWidth disabled>
                قريباً
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      <Box sx={{ mt: 3 }}>
        <Button variant="outlined" href="/">
          العودة للصفحة الرئيسية
        </Button>
      </Box>
    </Container>
  );
};

// Main App Component
const App: React.FC = () => {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/sales" element={<SalesPage />} />
            <Route path="/sales/*" element={<SalesPage />} />
          </Routes>
        </Router>
        <ToastContainer
          position="top-right"
          autoClose={5000}
          hideProgressBar={false}
          newestOnTop={false}
          closeOnClick
          rtl={true}
          pauseOnFocusLoss
          draggable
          pauseOnHover
        />
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
