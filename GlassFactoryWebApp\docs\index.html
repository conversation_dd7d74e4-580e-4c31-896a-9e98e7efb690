<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 نظام حسابات مصنع الزجاج - Glass Factory Accounting System</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header h2 {
            font-size: 1.5rem;
            color: #7f8c8d;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .status {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .live-demo {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .live-demo h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2rem;
        }
        
        .demo-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: block;
        }
        
        .demo-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
        
        .demo-link h4 {
            font-size: 1.2rem;
            margin-bottom: 10px;
        }
        
        .demo-link p {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .modules {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .module {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .module:hover {
            transform: translateY(-10px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        }
        
        .module h3 {
            font-size: 1.8rem;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .module p {
            color: #7f8c8d;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .features {
            list-style: none;
            margin-bottom: 25px;
        }
        
        .features li {
            padding: 8px 0;
            color: #27ae60;
            font-weight: 500;
        }
        
        .features li:before {
            content: "✅ ";
            margin-left: 10px;
        }
        
        .footer {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .footer p {
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        
        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .tech-item {
            background: #f8f9fa;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #2c3e50;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .modules {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏭 نظام حسابات مصنع الزجاج</h1>
            <h2>Glass Factory Accounting System</h2>
            <div class="status">
                <i class="fas fa-check-circle"></i>
                النظام متاح الآن - System Live Now!
            </div>
        </div>

        <div class="live-demo">
            <h3>🚀 النسخة التجريبية المباشرة</h3>
            <p>يمكنك الآن تجربة النظام مباشرة من خلال الروابط التالية:</p>
            
            <div class="demo-links">
                <a href="https://glassfactory-demo.netlify.app" class="demo-link" target="_blank">
                    <h4>🏠 التطبيق الرئيسي</h4>
                    <p>الصفحة الرئيسية للنظام</p>
                </a>
                
                <a href="https://glassfactory-demo.netlify.app/sales" class="demo-link" target="_blank">
                    <h4>💰 موديول المبيعات</h4>
                    <p>إدارة المبيعات والعملاء</p>
                </a>
                
                <a href="https://glassfactory-api.herokuapp.com/swagger" class="demo-link" target="_blank">
                    <h4>📋 API Documentation</h4>
                    <p>توثيق واجهة برمجة التطبيقات</p>
                </a>
                
                <a href="https://glassfactory-api.herokuapp.com/health" class="demo-link" target="_blank">
                    <h4>🔍 Health Check</h4>
                    <p>فحص صحة النظام</p>
                </a>
            </div>
        </div>

        <div class="modules">
            <div class="module">
                <h3>
                    <i class="fas fa-chart-line"></i>
                    💰 موديول المبيعات
                </h3>
                <p>إدارة شاملة للمبيعات والعملاء والفواتير مع دعم كامل للغة العربية</p>
                <ul class="features">
                    <li>إدارة العملاء</li>
                    <li>فواتير المبيعات</li>
                    <li>مدفوعات العملاء</li>
                    <li>التقارير والإحصائيات</li>
                    <li>البحث والفلترة</li>
                    <li>طباعة PDF</li>
                    <li>تصدير Excel</li>
                </ul>
            </div>

            <div class="module">
                <h3>
                    <i class="fas fa-code"></i>
                    📋 API Documentation
                </h3>
                <p>واجهة برمجة التطبيقات الموثقة مع Swagger UI</p>
                <ul class="features">
                    <li>جميع APIs موثقة</li>
                    <li>أمان متقدم مع JWT</li>
                    <li>اختبار مباشر للـ APIs</li>
                    <li>أمثلة شاملة</li>
                    <li>دعم OpenAPI 3.0</li>
                </ul>
            </div>
        </div>

        <div class="footer">
            <p><strong>تم تطوير النظام بواسطة:</strong> حسام محمد حسان أحمد</p>
            <p><strong>الإصدار:</strong> 2.0.0 | <strong>البيئة:</strong> Production</p>
            <p><strong>تاريخ النشر:</strong> 6 ديسمبر 2024</p>
            
            <div class="tech-stack">
                <div class="tech-item">ASP.NET Core 8.0</div>
                <div class="tech-item">React 18</div>
                <div class="tech-item">TypeScript</div>
                <div class="tech-item">PostgreSQL</div>
                <div class="tech-item">Material-UI</div>
                <div class="tech-item">JWT Auth</div>
                <div class="tech-item">Netlify + Heroku</div>
            </div>
        </div>
    </div>

    <script>
        // إضافة تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير الظهور التدريجي
            const elements = document.querySelectorAll('.live-demo, .module, .footer');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
