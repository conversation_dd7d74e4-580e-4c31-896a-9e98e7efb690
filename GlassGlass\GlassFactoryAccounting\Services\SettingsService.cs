using System.IO;
using System.Text.Json;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة إعدادات التطبيق
    /// </summary>
    public class SettingsService
    {
        private readonly string _settingsPath;
        private AppSettings _settings;

        public SettingsService()
        {
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GlassFactoryAccounting");
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }
            
            _settingsPath = Path.Combine(appDataPath, "settings.json");
            _settings = LoadSettings();
        }

        public AppSettings GetSettings()
        {
            return _settings;
        }

        public void SaveSettings(AppSettings settings)
        {
            _settings = settings;
            var json = JsonSerializer.Serialize(_settings, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(_settingsPath, json);
        }

        private AppSettings LoadSettings()
        {
            if (File.Exists(_settingsPath))
            {
                try
                {
                    var json = File.ReadAllText(_settingsPath);
                    return JsonSerializer.Deserialize<AppSettings>(json) ?? new AppSettings();
                }
                catch
                {
                    return new AppSettings();
                }
            }
            
            return new AppSettings();
        }
    }

    /// <summary>
    /// إعدادات التطبيق
    /// </summary>
    public class AppSettings
    {
        public string CompanyName { get; set; } = "مصنع الزجاج";
        public string CompanyAddress { get; set; } = "المنطقة الصناعية";
        public string CompanyPhone { get; set; } = "+*********";
        public string CompanyEmail { get; set; } = "<EMAIL>";
        public string TaxNumber { get; set; } = "*********";
        public decimal DefaultTaxRate { get; set; } = 14.0m;
        public string Currency { get; set; } = "ج.م";
        public bool AutoBackup { get; set; } = true;
        public int BackupIntervalDays { get; set; } = 7;
        public string BackupPath { get; set; } = "";
        public string DatabasePath { get; set; } = "";
        public DateTime LastBackupDate { get; set; } = DateTime.Now;
        public string Theme { get; set; } = "Light";
        public string Language { get; set; } = "ar";
        public string DefaultPrinter { get; set; } = "";
        public bool ShowWelcomeScreen { get; set; } = true;
    }
}
