using System.ComponentModel;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج فاتورة المشتريات
    /// </summary>
    public class Purchase : INotifyPropertyChanged
    {
        private string _invoiceNumber = "";
        private DateTime _purchaseDate;
        private int _supplierId;
        private decimal _totalAmount;
        private decimal _discount;
        private decimal _tax;
        private decimal _netAmount;
        private PaymentStatus _paymentStatus;
        private string _notes = "";
        private List<PurchaseItem> _purchaseItems;
        private Supplier? _supplier;
        private string _servicesText = "";

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set
            {
                _invoiceNumber = value;
                OnPropertyChanged();
            }
        }

        public DateTime PurchaseDate
        {
            get => _purchaseDate;
            set
            {
                _purchaseDate = value;
                OnPropertyChanged();
            }
        }

        public int SupplierId
        {
            get => _supplierId;
            set
            {
                _supplierId = value;
                OnPropertyChanged();
            }
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set
            {
                _totalAmount = value;
                OnPropertyChanged();
            }
        }

        public decimal Discount
        {
            get => _discount;
            set
            {
                _discount = value;
                OnPropertyChanged();
            }
        }

        public decimal Tax
        {
            get => _tax;
            set
            {
                _tax = value;
                OnPropertyChanged();
            }
        }

        public decimal NetAmount
        {
            get => _netAmount;
            set
            {
                _netAmount = value;
                OnPropertyChanged();
            }
        }

        public PaymentStatus PaymentStatus
        {
            get => _paymentStatus;
            set
            {
                _paymentStatus = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public List<PurchaseItem> PurchaseItems
        {
            get => _purchaseItems;
            set
            {
                _purchaseItems = value;
                OnPropertyChanged();
            }
        }

        public Supplier? Supplier
        {
            get => _supplier;
            set
            {
                _supplier = value;
                OnPropertyChanged();
            }
        }

        public string ServicesText
        {
            get => _servicesText;
            set
            {
                _servicesText = value;
                OnPropertyChanged();
            }
        }

        public Purchase()
        {
            _purchaseItems = new List<PurchaseItem>();
            PurchaseDate = DateTime.Now;
            PaymentStatus = PaymentStatus.غير_مدفوع;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// عنصر فاتورة المشتريات
    /// </summary>
    public class PurchaseItem : INotifyPropertyChanged
    {
        private int _id;
        private string _service = "";
        private string _glassType = "";
        private decimal _glassThickness;
        private string _details = "";
        private decimal _length;
        private decimal _width;
        private decimal _area;
        private int _count;
        private decimal _totalArea;
        private decimal _unitPrice;
        private decimal _totalPrice;
        private bool _isManualRow;
        private string _notes = "";

        public int Id
        {
            get => _id;
            set
            {
                _id = value;
                OnPropertyChanged();
            }
        }

        public string Service
        {
            get => _service;
            set
            {
                _service = value;
                OnPropertyChanged();
            }
        }

        public string GlassType
        {
            get => _glassType;
            set
            {
                _glassType = value;
                OnPropertyChanged();
            }
        }

        public decimal GlassThickness
        {
            get => _glassThickness;
            set
            {
                _glassThickness = value;
                OnPropertyChanged();
            }
        }

        public string Details
        {
            get => _details;
            set
            {
                _details = value;
                OnPropertyChanged();
            }
        }

        public decimal Length
        {
            get => _length;
            set
            {
                _length = value;
                OnPropertyChanged();
                if (!IsManualRow)
                    CalculateArea();
            }
        }

        public decimal Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged();
                if (!IsManualRow)
                    CalculateArea();
            }
        }

        public decimal Area
        {
            get => _area;
            set
            {
                _area = value;
                OnPropertyChanged();
                if (!IsManualRow)
                    CalculateTotalArea();
            }
        }

        public int Count
        {
            get => _count;
            set
            {
                _count = value;
                OnPropertyChanged();
                if (!IsManualRow)
                    CalculateTotalArea();
            }
        }

        public decimal TotalArea
        {
            get => _totalArea;
            set
            {
                _totalArea = value;
                OnPropertyChanged();
                CalculateTotalPrice();
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                _unitPrice = value;
                OnPropertyChanged();
                CalculateTotalPrice();
            }
        }

        public decimal TotalPrice
        {
            get => _totalPrice;
            set
            {
                _totalPrice = value;
                OnPropertyChanged();
            }
        }

        public bool IsManualRow
        {
            get => _isManualRow;
            set
            {
                _isManualRow = value;
                OnPropertyChanged();
            }
        }

        public string Notes
        {
            get => _notes;
            set
            {
                _notes = value;
                OnPropertyChanged();
            }
        }

        public void CalculateArea()
        {
            if (Length > 0 && Width > 0)
            {
                Area = (Length * Width) / 1000000; // تحويل من ملم مربع إلى متر مربع
            }
        }

        public void CalculateTotalArea()
        {
            TotalArea = Area * Count;
        }

        public void CalculateTotalPrice()
        {
            TotalPrice = TotalArea * UnitPrice;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
