<UserControl x:Class="GlassFactoryAccounting.Views.MovementReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📊" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="تقرير الاستلام والتسليم" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                    <TextBlock Text="تقرير شامل لجميع حركات المخزون" FontSize="14" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- الفلاتر -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول -->
                <Grid Grid.Row="0" Margin="0,0,0,15">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="DpFromDate" Height="35" VerticalContentAlignment="Center" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="0,0,15,0">
                        <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="DpToDate" Height="35" VerticalContentAlignment="Center" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Margin="0,0,15,0">
                        <TextBlock Text="نوع الحركة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CmbMovementType" Height="35" VerticalContentAlignment="Center" FontSize="14" Padding="10"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3">
                        <TextBlock Text="الصنف:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CmbItem" Height="35" VerticalContentAlignment="Center" FontSize="14" Padding="10"
                                  DisplayMemberPath="Name" SelectedValuePath="Id"/>
                    </StackPanel>
                </Grid>

                <!-- الأزرار -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="BtnFilter" Content="🔍 تطبيق الفلتر" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Click="BtnFilter_Click"/>
                    <Button x:Name="BtnClearFilter" Content="🗑️ مسح الفلتر" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Background="#757575" Margin="10,0,0,0" Click="BtnClearFilter_Click"/>
                    <Button x:Name="BtnExportExcel" Content="📊 تصدير Excel" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Background="#4CAF50" Margin="10,0,0,0" Click="BtnExportExcel_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- التقرير -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 تقرير الحركات" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBlock x:Name="TxtRecordCount" Text="(0 حركة)" FontSize="14" Foreground="#666" 
                               VerticalAlignment="Center" Margin="10,0,0,0"/>
                </StackPanel>

                <DataGrid Grid.Row="1" x:Name="MovementsDataGrid" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="35">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="60"/>
                        <DataGridTextColumn Header="رقم الأمر" Binding="{Binding OrderNumber}" Width="70"/>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="70"/>
                        <DataGridTextColumn Header="نوع الحركة" Binding="{Binding MovementType}" Width="60"/>
                        <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="70"/>
                        <DataGridTextColumn Header="العميل/المورد" Width="90">
                            <DataGridTextColumn.Binding>
                                <MultiBinding StringFormat="{}{0}{1}">
                                    <Binding Path="CustomerName"/>
                                    <Binding Path="SupplierName"/>
                                </MultiBinding>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الصنف" Binding="{Binding ItemName}" Width="90"/>
                        <DataGridTextColumn Header="وحدة القياس" Binding="{Binding UnitOfMeasure}" Width="70"/>
                        <DataGridTextColumn Header="محتوى الصندوق" Binding="{Binding BoxContent, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="الطول (مم)" Binding="{Binding Length}" Width="70"/>
                        <DataGridTextColumn Header="العرض (مم)" Binding="{Binding Width}" Width="70"/>
                        <DataGridTextColumn Header="المتر المربع" Binding="{Binding Area, StringFormat=F4}" Width="80"/>
                        <DataGridTextColumn Header="العدد" Binding="{Binding Units, StringFormat=F2}" Width="60"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, StringFormat=F2}" Width="60"/>
                        <DataGridTextColumn Header="عدد الصناديق" Binding="{Binding BoxCount, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat=F2}" Width="60"/>
                        <DataGridTextColumn Header="إجمالي القيمة" Binding="{Binding TotalValue, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- الإحصائيات -->
                <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="5" Padding="15" Margin="0,15,0,0">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي الاستلام" FontWeight="Bold" FontSize="14"/>
                            <TextBlock x:Name="TxtTotalReceived" Text="0.00" FontSize="16" Foreground="#4CAF50"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي التسليم" FontWeight="Bold" FontSize="14"/>
                            <TextBlock x:Name="TxtTotalIssued" Text="0.00" FontSize="16" Foreground="#FF9800"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="عدد حركات الاستلام" FontWeight="Bold" FontSize="14"/>
                            <TextBlock x:Name="TxtReceiveCount" Text="0" FontSize="16" Foreground="#2196F3"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                            <TextBlock Text="عدد حركات التسليم" FontWeight="Bold" FontSize="14"/>
                            <TextBlock x:Name="TxtIssueCount" Text="0" FontSize="16" Foreground="#2196F3"/>
                        </StackPanel>
                    </Grid>
                </Border>
            </Grid>
        </Border>
    </Grid>
</UserControl>
