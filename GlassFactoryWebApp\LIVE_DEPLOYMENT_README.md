# 🚀 نظام حسابات مصنع الزجاج - النشر المباشر

## 🌐 **الروابط المباشرة (بعد النشر)**

### 🎯 **الروابط الرئيسية:**
- **🏠 الصفحة الرئيسية:** http://glass-factory-demo.ddns.net
- **💰 موديول المبيعات:** http://glass-factory-demo.ddns.net/sales
- **📋 API Documentation:** http://glass-factory-demo.ddns.net/swagger
- **🔍 Health Check:** http://glass-factory-demo.ddns.net/health

### 🔗 **روابط موديول المبيعات:**
- **📋 قائمة الفواتير:** http://glass-factory-demo.ddns.net/sales/invoices
- **➕ فاتورة جديدة:** http://glass-factory-demo.ddns.net/sales/invoices/new
- **👥 قائمة العملاء:** http://glass-factory-demo.ddns.net/sales/customers
- **👤 عميل جديد:** http://glass-factory-demo.ddns.net/sales/customers/new
- **💳 المدفوعات:** http://glass-factory-demo.ddns.net/sales/payments
- **📊 التقارير:** http://glass-factory-demo.ddns.net/sales/reports

---

## 🚀 **خطوات النشر السريع**

### **الطريقة الأولى: النشر التلقائي**

```bash
# 1. تشغيل ملف النشر السريع
double-click: QUICK_DEPLOY.bat

# 2. رفع الملفات للخادم
scp deployment-package.zip ubuntu@YOUR_SERVER_IP:~/

# 3. الاتصال بالخادم
ssh ubuntu@YOUR_SERVER_IP

# 4. تشغيل النشر
unzip deployment-package.zip
chmod +x DEPLOY_NOW.sh
sudo ./DEPLOY_NOW.sh
```

### **الطريقة الثانية: النشر اليدوي**

```bash
# 1. تحديث النظام
sudo apt update && sudo apt upgrade -y

# 2. تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 3. تثبيت Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 4. نشر التطبيق
docker-compose -f docker-compose.production.yml up -d

# 5. إعداد Nginx
sudo apt install nginx
# إعداد ملف التكوين (موجود في السكريبت)
```

---

## 🔧 **متطلبات النشر**

### **Oracle Cloud Instance:**
- **نوع الخادم:** VM.Standard.E2.1.Micro (Always Free)
- **نظام التشغيل:** Ubuntu 22.04 LTS
- **الذاكرة:** 1GB RAM
- **التخزين:** 47GB
- **الشبكة:** 10TB/month

### **المنافذ المطلوبة:**
- **80:** HTTP
- **443:** HTTPS
- **22:** SSH
- **5432:** PostgreSQL (داخلي)
- **5000:** Backend API (داخلي)
- **3000:** Frontend (داخلي)

### **Domain Name:**
- **المجال:** glass-factory-demo.ddns.net
- **DNS:** يجب توجيهه لـ IP الخادم
- **SSL:** سيتم إعداده تلقائياً

---

## 📊 **مراقبة النظام**

### **فحص حالة الخدمات:**
```bash
# حالة الحاويات
docker-compose -f docker-compose.production.yml ps

# مراقبة السجلات
docker-compose -f docker-compose.production.yml logs -f

# فحص استخدام الموارد
docker stats

# فحص مساحة القرص
df -h

# فحص الذاكرة
free -h
```

### **إعادة تشغيل الخدمات:**
```bash
# إعادة تشغيل جميع الخدمات
docker-compose -f docker-compose.production.yml restart

# إعادة تشغيل خدمة محددة
docker-compose -f docker-compose.production.yml restart api
docker-compose -f docker-compose.production.yml restart web
docker-compose -f docker-compose.production.yml restart postgres
```

### **إيقاف النظام:**
```bash
# إيقاف مؤقت
docker-compose -f docker-compose.production.yml stop

# إيقاف وحذف الحاويات
docker-compose -f docker-compose.production.yml down

# إيقاف وحذف كل شيء (بما في ذلك البيانات)
docker-compose -f docker-compose.production.yml down -v
```

---

## 🔍 **اختبار النظام**

### **اختبارات أساسية:**
```bash
# فحص Frontend
curl -f http://glass-factory-demo.ddns.net

# فحص Backend API
curl -f http://glass-factory-demo.ddns.net/api/health

# فحص قاعدة البيانات
docker exec -it glass-factory-db psql -U glass_factory_user -d glass_factory_db -c "SELECT version();"
```

### **اختبار موديول المبيعات:**
1. **افتح:** http://glass-factory-demo.ddns.net/sales
2. **اختبر إنشاء عميل جديد**
3. **اختبر إنشاء فاتورة مبيعات**
4. **اختبر طباعة الفاتورة**
5. **اختبر تسجيل دفعة**
6. **اختبر التقارير**

---

## 🛠️ **استكشاف الأخطاء**

### **مشاكل شائعة:**

#### **1. التطبيق لا يعمل:**
```bash
# فحص حالة الحاويات
docker-compose ps

# فحص السجلات
docker-compose logs api
docker-compose logs web
docker-compose logs postgres

# إعادة تشغيل
docker-compose restart
```

#### **2. مشكلة في قاعدة البيانات:**
```bash
# فحص PostgreSQL
docker exec -it glass-factory-db psql -U glass_factory_user -d glass_factory_db

# إعادة إنشاء قاعدة البيانات
docker-compose down
docker volume rm glass-factory_postgres_data
docker-compose up -d
```

#### **3. مشكلة في الشبكة:**
```bash
# فحص المنافذ
sudo netstat -tulpn | grep :80
sudo netstat -tulpn | grep :5000

# فحص جدار الحماية
sudo ufw status

# فحص Nginx
sudo systemctl status nginx
sudo nginx -t
```

---

## 📈 **تحسين الأداء**

### **إعدادات PostgreSQL:**
```sql
-- في ملف postgresql.conf
shared_buffers = 128MB
effective_cache_size = 512MB
maintenance_work_mem = 64MB
```

### **إعدادات Nginx:**
```nginx
# في ملف nginx.conf
worker_processes auto;
worker_connections 1024;
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

---

## 🔐 **الأمان**

### **إعدادات جدار الحماية:**
```bash
sudo ufw enable
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw deny 5432  # منع الوصول المباشر لقاعدة البيانات
```

### **تحديثات الأمان:**
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تحديث Docker images
docker-compose pull
docker-compose up -d
```

---

## 📞 **الدعم والمساعدة**

### **معلومات المطور:**
- **👤 المطور:** حسام محمد حسان أحمد
- **📧 البريد:** <EMAIL>
- **🌐 الموقع:** http://glass-factory-demo.ddns.net

### **ملفات مهمة:**
- **📋 السجلات:** `/var/log/glass-factory/`
- **💾 النسخ الاحتياطية:** `/var/backups/glass-factory/`
- **📁 الملفات المرفوعة:** `/var/www/glass-factory/uploads/`
- **📊 التقارير:** `/var/www/glass-factory/reports/`

---

## 🎉 **النتيجة النهائية**

بعد اكتمال النشر، ستحصل على:

✅ **تطبيق ويب متكامل** يعمل 24/7  
✅ **موديول مبيعات كامل** بجميع الوظائف  
✅ **قاعدة بيانات PostgreSQL** محسنة  
✅ **واجهة عربية RTL** متجاوبة  
✅ **API موثق** مع Swagger  
✅ **نسخ احتياطية تلقائية**  
✅ **مراقبة وتسجيل متقدم**  
✅ **أمان عالي** مع جدار حماية  

**🌐 الوصول المباشر:** http://glass-factory-demo.ddns.net

---

**🚀 النظام جاهز للاستخدام والمراجعة!**
