<UserControl x:Class="GlassFactoryAccounting.Views.ArchiveView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <TextBlock Text="📁 أرشيف الفواتير والتحليلات" FontSize="24" FontWeight="Bold" 
                     Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- فلاتر البحث -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="🔍 فلاتر البحث والتصفية" FontSize="16" FontWeight="Bold" 
                         Margin="0,0,0,10" Foreground="{StaticResource PrimaryBrush}"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="البحث في رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtSearchInvoice" Height="35" VerticalContentAlignment="Center"
                                 TextChanged="TxtSearchInvoice_TextChanged"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="5,0">
                        <TextBlock Text="البحث في اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtSearchCustomer" Height="35" VerticalContentAlignment="Center"
                                 TextChanged="TxtSearchCustomer_TextChanged"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" Margin="5,0">
                        <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="DateFrom" Height="35" SelectedDateChanged="DateFrom_SelectedDateChanged"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="3" Margin="10,0,10,0">
                        <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="DateTo" Height="35" SelectedDateChanged="DateTo_SelectedDateChanged"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="4" VerticalAlignment="Bottom">
                        <Button x:Name="BtnClearFilters" Content="مسح الفلاتر" 
                                Background="{StaticResource WarningBrush}" Foreground="White"
                                Padding="15,8" FontWeight="Bold"
                                BorderThickness="0" Cursor="Hand" Click="BtnClearFilters_Click"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>
        
        <!-- التحليلات السريعة -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="📊 التحليلات السريعة" FontSize="16" FontWeight="Bold" 
                         Margin="0,0,0,10" Foreground="{StaticResource PrimaryBrush}"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <Border Grid.Column="0" Background="{StaticResource SuccessBrush}" CornerRadius="5" Padding="10" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي الفواتير" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TxtTotalInvoices" Text="0" Foreground="White" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Grid.Column="1" Background="{StaticResource PrimaryBrush}" CornerRadius="5" Padding="10" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="إجمالي المبيعات" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TxtTotalSales" Text="0.00 ج.م" Foreground="White" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Grid.Column="2" Background="{StaticResource WarningBrush}" CornerRadius="5" Padding="10" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="متوسط الفاتورة" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TxtAverageInvoice" Text="0.00 ج.م" Foreground="White" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Grid.Column="3" Background="#9C27B0" CornerRadius="5" Padding="10" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="أكثر الخدمات" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TxtTopService" Text="-" Foreground="White" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Grid.Column="4" Background="#607D8B" CornerRadius="5" Padding="10" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="عدد العملاء" Foreground="White" FontWeight="Bold" HorizontalAlignment="Center"/>
                            <TextBlock x:Name="TxtTotalCustomers" Text="0" Foreground="White" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Grid>
        </Border>
        
        <!-- جدول الفواتير -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="📋 قائمة الفواتير المحفوظة" FontSize="16" FontWeight="Bold" 
                             Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                    
                    <Button x:Name="BtnRefresh" Content="🔄 تحديث" 
                            Background="{StaticResource SuccessBrush}" Foreground="White"
                            Padding="10,5" Margin="20,0,0,0" FontWeight="Bold"
                            BorderThickness="0" Cursor="Hand" Click="BtnRefresh_Click"/>
                    
                    <Button x:Name="BtnExportExcel" Content="📊 تصدير Excel" 
                            Background="{StaticResource PrimaryBrush}" Foreground="White"
                            Padding="10,5" Margin="10,0,0,0" FontWeight="Bold"
                            BorderThickness="0" Cursor="Hand" Click="BtnExportExcel_Click"/>
                </StackPanel>
                
                <DataGrid Grid.Row="1" x:Name="InvoicesDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40"
                          FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="اسم العميل" Binding="{Binding Customer.Name}" Width="150"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                        <DataGridTextColumn Header="إجمالي المبلغ" Binding="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="المبلغ النهائي" Binding="{Binding NetAmount, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="عدد العناصر" Binding="{Binding SaleItems.Count}" Width="100"/>
                        <DataGridTextColumn Header="الخدمات" Binding="{Binding ServicesText}" Width="200"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️" Background="{StaticResource PrimaryBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnViewInvoice_Click" ToolTip="عرض الفاتورة"/>
                                        
                                        <Button Content="📄" Background="{StaticResource SuccessBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnPrintInvoice_Click" ToolTip="طباعة PDF"/>
                                        
                                        <Button Content="🗑️" Background="{StaticResource DangerBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnDeleteInvoice_Click" ToolTip="حذف الفاتورة"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
