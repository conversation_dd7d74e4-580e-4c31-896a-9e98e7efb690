#!/bin/bash

# 🚀 Glass Factory Accounting System - Oracle Cloud Deployment
# نشر نظام حسابات مصنع الزجاج على Oracle Cloud Free Tier

set -e

echo "🚀 بدء نشر نظام حسابات مصنع الزجاج على Oracle Cloud"
echo "=================================================="

# متغيرات النشر
DOMAIN="glass-factory-demo.ddns.net"
APP_NAME="glass-factory"
DB_NAME="glass_factory_db"
DB_USER="glass_factory_user"
DB_PASSWORD="GlassFactory2025!"

# تحديث النظام
echo "📦 تحديث النظام..."
sudo apt update && sudo apt upgrade -y

# تثبيت Docker
echo "🐳 تثبيت Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
fi

# تثبيت Docker Compose
echo "🐳 تثبيت Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# تثبيت .NET 8 SDK
echo "⚙️ تثبيت .NET 8 SDK..."
if ! command -v dotnet &> /dev/null; then
    wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
    sudo dpkg -i packages-microsoft-prod.deb
    sudo apt update
    sudo apt install -y dotnet-sdk-8.0
fi

# تثبيت Node.js
echo "📦 تثبيت Node.js..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt install -y nodejs
fi

# تثبيت PostgreSQL
echo "🗄️ تثبيت PostgreSQL..."
if ! command -v psql &> /dev/null; then
    sudo apt install -y postgresql postgresql-contrib
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
fi

# إعداد قاعدة البيانات
echo "🗄️ إعداد قاعدة البيانات..."
sudo -u postgres psql << EOF
DROP DATABASE IF EXISTS $DB_NAME;
DROP USER IF EXISTS $DB_USER;
CREATE DATABASE $DB_NAME;
CREATE USER $DB_USER WITH ENCRYPTED PASSWORD '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
ALTER USER $DB_USER CREATEDB;
\q
EOF

# تثبيت Nginx
echo "🌐 تثبيت Nginx..."
if ! command -v nginx &> /dev/null; then
    sudo apt install -y nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
fi

# تثبيت Certbot
echo "🔒 تثبيت Certbot..."
if ! command -v certbot &> /dev/null; then
    sudo apt install -y certbot python3-certbot-nginx
fi

# إنشاء مجلدات التطبيق
echo "📁 إنشاء مجلدات التطبيق..."
sudo mkdir -p /var/www/$APP_NAME
sudo mkdir -p /var/log/$APP_NAME
sudo chown -R $USER:$USER /var/www/$APP_NAME
sudo chown -R $USER:$USER /var/log/$APP_NAME

# نسخ ملفات التطبيق
echo "📋 نسخ ملفات التطبيق..."
cp -r . /var/www/$APP_NAME/

# بناء Backend
echo "🔨 بناء Backend..."
cd /var/www/$APP_NAME
dotnet restore
dotnet publish -c Release -o ./publish

# بناء Frontend
echo "🔨 بناء Frontend..."
cd client
npm install
REACT_APP_API_URL=https://$DOMAIN/api npm run build
cd ..

# إنشاء ملف خدمة systemd
echo "⚙️ إنشاء خدمة systemd..."
sudo tee /etc/systemd/system/$APP_NAME.service > /dev/null << EOF
[Unit]
Description=Glass Factory Accounting System
After=network.target

[Service]
Type=notify
ExecStart=/usr/bin/dotnet /var/www/$APP_NAME/publish/GlassFactoryWebApp.dll
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=$APP_NAME
User=$USER
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=ASPNETCORE_URLS=http://localhost:5000
Environment=ConnectionStrings__DefaultConnection=Host=localhost;Database=$DB_NAME;Username=$DB_USER;Password=$DB_PASSWORD
WorkingDirectory=/var/www/$APP_NAME/publish

[Install]
WantedBy=multi-user.target
EOF

# تمكين وبدء الخدمة
echo "🚀 تمكين وبدء الخدمة..."
sudo systemctl daemon-reload
sudo systemctl enable $APP_NAME
sudo systemctl start $APP_NAME

# إعداد Nginx
echo "🌐 إعداد Nginx..."
sudo tee /etc/nginx/sites-available/$APP_NAME > /dev/null << EOF
server {
    listen 80;
    server_name $DOMAIN;

    # Static files (React build)
    location / {
        root /var/www/$APP_NAME/client/build;
        try_files \$uri \$uri/ /index.html;

        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }

    # API routes
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # File uploads
    client_max_body_size 50M;
}
EOF

# تمكين الموقع
sudo ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t
sudo systemctl reload nginx

# إعداد جدار الحماية
echo "🔥 إعداد جدار الحماية..."
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

echo "✅ تم نشر النظام بنجاح!"
echo "=================================================="
echo "🌐 الموقع: http://$DOMAIN"
echo "🗄️ قاعدة البيانات: $DB_NAME"
echo "📊 حالة الخدمة: sudo systemctl status $APP_NAME"
echo "📋 سجلات التطبيق: sudo journalctl -u $APP_NAME -f"
echo "=================================================="