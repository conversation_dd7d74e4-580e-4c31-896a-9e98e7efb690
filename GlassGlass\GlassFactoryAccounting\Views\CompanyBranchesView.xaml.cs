using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class CompanyBranchesView : UserControl
    {
        private readonly CompanyManagementService _companyService;
        private List<CompanyBranch> _branches;

        public CompanyBranchesView()
        {
            InitializeComponent();
            _companyService = new CompanyManagementService();
            _branches = new List<CompanyBranch>();
            LoadBranches();
        }

        private void LoadBranches()
        {
            try
            {
                _branches = _companyService.GetAllCompanyBranches();
                DgBranches.ItemsSource = _branches;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الفروع: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddBranch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var branch = new CompanyBranch
                {
                    Name = TxtBranchName.Text.Trim(),
                    Address = TxtAddress.Text.Trim(),
                    Phone = TxtPhone.Text.Trim(),
                    Manager = TxtManager.Text.Trim(),
                    CreatedBy = "المستخدم الحالي"
                };

                var result = _companyService.AddCompanyBranch(branch);

                if (result)
                {
                    MessageBox.Show("تم إضافة الفرع بنجاح!", "نجح الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                    LoadBranches();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة الفرع!", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الفرع: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtBranchName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الفرع", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtBranchName.Focus();
                return false;
            }

            // التحقق من عدم تكرار الاسم
            if (_branches.Any(b => b.Name.Equals(TxtBranchName.Text.Trim(), StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("اسم الفرع موجود بالفعل", "بيانات مكررة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtBranchName.Focus();
                return false;
            }

            return true;
        }

        private void BtnClearForm_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            TxtBranchName.Clear();
            TxtAddress.Clear();
            TxtPhone.Clear();
            TxtManager.Clear();
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is CompanyBranch branch)
            {
                // تعبئة النموذج بالبيانات للتعديل
                TxtBranchName.Text = branch.Name;
                TxtAddress.Text = branch.Address ?? "";
                TxtPhone.Text = branch.Phone ?? "";
                TxtManager.Text = branch.Manager ?? "";

                MessageBox.Show($"تم تحديد الفرع: {branch.Name} للتعديل\nيمكنك الآن تعديل البيانات والضغط على إضافة الفرع", 
                    "تعديل الفرع", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is CompanyBranch branch)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الفرع: {branch.Name}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = _companyService.DeleteCompanyBranch(branch.Id);
                        
                        if (deleteResult)
                        {
                            MessageBox.Show("تم حذف الفرع بنجاح!", "نجح الحذف", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadBranches();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الفرع!", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفرع: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
