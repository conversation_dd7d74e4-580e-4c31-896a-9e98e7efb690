@echo off
chcp 65001 >nul
title 🧪 تشغيل سريع للاختبار - النظام المحاسبي المحسن
color 0A

echo.
echo 🧪 تشغيل سريع للاختبار - النظام المحاسبي المحسن
echo ═══════════════════════════════════════════════════════════
echo.

REM إيقاف أي نسخة تعمل
taskkill /f /im "GlassFactoryAccounting.exe" 2>nul >nul

REM البحث عن الملف التنفيذي
echo 🔍 البحث عن الملف التنفيذي...

if exist "Release\GlassFactoryAccounting.exe" (
    echo ✅ تم العثور على الملف في: Release\
    start "" "Release\GlassFactoryAccounting.exe"
    goto :success
)

if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
    echo ✅ تم العثور على الملف في: bin\Release\net8.0-windows\
    start "" "bin\Release\net8.0-windows\GlassFactoryAccounting.exe"
    goto :success
)

if exist "bin\Debug\net8.0-windows\GlassFactoryAccounting.exe" (
    echo ⚠️  تم العثور على نسخة Debug في: bin\Debug\net8.0-windows\
    start "" "bin\Debug\net8.0-windows\GlassFactoryAccounting.exe"
    goto :success
)

REM إذا لم يتم العثور على الملف
echo ❌ لم يتم العثور على الملف التنفيذي!
echo.
echo 🔧 محاولة بناء المشروع...
dotnet build --configuration Release --verbosity quiet
if %errorlevel% equ 0 (
    echo ✅ تم بناء المشروع بنجاح!
    if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
        start "" "bin\Release\net8.0-windows\GlassFactoryAccounting.exe"
        goto :success
    )
)

echo ❌ فشل في العثور على الملف أو بناء المشروع!
echo يرجى التحقق من:
echo 1. تثبيت .NET 8.0 SDK
echo 2. صحة ملفات المشروع
echo 3. عدم وجود أخطاء في الكود
pause
exit /b 1

:success
echo.
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 🎯 نصائح سريعة للاختبار:
echo ────────────────────────────────
echo 1️⃣  اذهب إلى: الحسابات ← شجرة الحسابات
echo 2️⃣  جرب إضافة حساب فرعي جديد
echo 3️⃣  اذهب إلى: الحسابات ← قيد اليومية
echo 4️⃣  أدخل قيد تجريبي وتحقق من التوازن
echo 5️⃣  جرب نافذة اختيار الحسابات المحسنة
echo.
echo 📋 للمزيد من التفاصيل: اقرأ ملف ACCOUNTING_SYSTEM_ENHANCEMENTS.md
echo.
timeout /t 3 /nobreak >nul
