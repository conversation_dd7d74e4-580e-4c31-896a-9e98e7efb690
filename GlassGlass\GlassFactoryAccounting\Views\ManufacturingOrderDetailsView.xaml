<UserControl x:Class="GlassFactoryAccounting.Views.ManufacturingOrderDetailsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان الرئيسي -->
            <Border Grid.Row="0" Background="#2C3E50" CornerRadius="10" Padding="20" Margin="0,0,0,20">
                <StackPanel HorizontalAlignment="Center">
                    <TextBlock x:Name="txtOrderTitle" Text="📋 تفاصيل أمر التصنيع الشامل"
                              FontSize="28" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="txtOrderNumberHeader" Text="رقم الأمر: A001"
                              FontSize="18" Foreground="#BDC3C7" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock Text="⚠️ مستند للفني - يحتوي على جميع التعليمات والبيانات المطلوبة"
                              FontSize="14" Foreground="#F39C12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                </StackPanel>
            </Border>

            <!-- معلومات الأمر الأساسية -->
            <GroupBox Grid.Row="1" Header="📝 معلومات الأمر الأساسية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم العميل:" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtCustomerName" Text="" Margin="5"/>

                    <TextBlock Grid.Row="0" Grid.Column="2" Text="رقم الفاتورة:" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="0" Grid.Column="3" x:Name="txtInvoiceNumber" Text="" Margin="5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="تاريخ الأمر:" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtOrderDate" Text="" Margin="5"/>

                    <TextBlock Grid.Row="1" Grid.Column="2" Text="حالة الأمر:" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="1" Grid.Column="3" x:Name="txtOrderStatus" Text="" Margin="5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="نوع الزجاج:" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtGlassType" Text="" Margin="5"/>

                    <TextBlock Grid.Row="2" Grid.Column="2" Text="السمك:" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="2" Grid.Column="3" x:Name="txtThickness" Text="" Margin="5"/>
                </Grid>
            </GroupBox>

            <!-- ألواح الزجاج المستخدمة -->
            <GroupBox Grid.Row="2" Header="🔷 ألواح الزجاج المستخدمة" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <DataGrid x:Name="dgGlassPanelsDetails" AutoGenerateColumns="False"
                             CanUserAddRows="False" CanUserDeleteRows="False"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="120" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40"/>
                            <DataGridTextColumn Header="نوع الزجاج" Binding="{Binding GlassType}" Width="120"/>
                            <DataGridTextColumn Header="السمك" Binding="{Binding Thickness}" Width="80"/>
                            <DataGridTextColumn Header="الطول (ملم)" Binding="{Binding Length}" Width="100"/>
                            <DataGridTextColumn Header="العرض (ملم)" Binding="{Binding Width}" Width="100"/>
                            <DataGridTextColumn Header="م²" Binding="{Binding SquareMeters, StringFormat=F3}" Width="80"/>
                            <DataGridTextColumn Header="العدد" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="إجمالي م²" Binding="{Binding TotalSquareMeters, StringFormat=F2}" Width="100"/>
                            <DataGridTextColumn Header="السعر" Binding="{Binding Price, StringFormat=F2}" Width="100"/>
                            <DataGridTextColumn Header="إجمالي القيمة" Binding="{Binding TotalValue, StringFormat=F2}" Width="120"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- مجاميع ألواح الزجاج -->
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#E8F5E8" Padding="10" CornerRadius="5" Margin="0,0,5,0">
                            <TextBlock x:Name="txtTotalGlassMeters" Text="إجمالي الأمتار: 0.00 م²"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>

                        <Border Grid.Column="1" Background="#FFF3CD" Padding="10" CornerRadius="5" Margin="5,0,0,0">
                            <TextBlock x:Name="txtTotalGlassValue" Text="إجمالي القيمة: 0.00 ج.م"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- المقاسات المطلوبة للقص -->
            <GroupBox Grid.Row="3" Header="📏 المقاسات المطلوبة للقص" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"
                      Background="#F8F9FA" BorderBrush="#28A745" BorderThickness="2">
                <StackPanel Margin="10">
                    <TextBlock Text="⚠️ هذا الجدول مخصص للفني - يحتوي على جميع المقاسات المطلوبة للقص بدقة"
                              FontSize="14" FontWeight="Bold" Foreground="#DC3545" HorizontalAlignment="Center" Margin="0,0,0,10"/>

                    <DataGrid x:Name="dgRequiredSizesDetails" AutoGenerateColumns="False"
                             CanUserAddRows="False" CanUserDeleteRows="False"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="150" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40"/>
                            <DataGridTextColumn Header="كود المرجع" Binding="{Binding RefCode}" Width="100"/>
                            <DataGridTextColumn Header="الطول (ملم)" Binding="{Binding Length}" Width="100"/>
                            <DataGridTextColumn Header="العرض (ملم)" Binding="{Binding Width}" Width="100"/>
                            <DataGridTextColumn Header="م²" Binding="{Binding SquareMeters, StringFormat=F3}" Width="80"/>
                            <DataGridTextColumn Header="العدد" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="إجمالي م²" Binding="{Binding TotalSquareMeters, StringFormat=F2}" Width="100"/>
                            <DataGridTextColumn Header="المتر الطولي" Binding="{Binding LinearMeters, StringFormat=F2}" Width="100"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- مجاميع المقاسات المطلوبة -->
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#E9ECEF" Padding="10" CornerRadius="5" Margin="0,0,5,0">
                            <TextBlock x:Name="txtTotalRequiredMetersDetails" Text="إجمالي الأمتار المطلوبة فعلياً: 0.00 م²"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>

                        <Border Grid.Column="1" Background="#D1ECF1" Padding="10" CornerRadius="5" Margin="5,0,0,0">
                            <TextBlock x:Name="txtTotalLinearMetersDetails" Text="إجمالي المتر الطولي: 0.00 م"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- الخدمات المطبقة -->
            <GroupBox Grid.Row="4" Header="🔧 الخدمات المطبقة" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <Grid x:Name="gridServicesDetails">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- سيتم إضافة الخدمات هنا برمجياً -->
                        <Border Grid.Column="0" Background="#E3F2FD" Padding="8" Margin="2" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="🎬 فيلم" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="txtFilmService" Text="غير مطبق" HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="1" Background="#F3E5F5" Padding="8" Margin="2" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="🔗 دبل" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="txtDoubleService" Text="غير مطبق" HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="2" Background="#E8F5E8" Padding="8" Margin="2" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="🖨️ طباعة" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="txtPrintService" Text="غير مطبق" HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                        </Border>

                        <Border Grid.Column="3" Background="#FFF3E0" Padding="8" Margin="2" CornerRadius="5">
                            <StackPanel>
                                <TextBlock Text="⚙️ أخرى" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock x:Name="txtOtherServices" Text="غير مطبق" HorizontalAlignment="Center" FontSize="12"/>
                            </StackPanel>
                        </Border>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- تكلفة الخدمات -->
            <GroupBox Grid.Row="5" Header="💰 تكلفة الخدمات" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <DataGrid x:Name="dgServiceCostsDetails" AutoGenerateColumns="False"
                             CanUserAddRows="False" CanUserDeleteRows="False"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="100" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40"/>
                            <DataGridTextColumn Header="اسم الخدمة" Binding="{Binding ServiceName}" Width="150"/>
                            <DataGridTextColumn Header="التكلفة" Binding="{Binding Cost, StringFormat=F2}" Width="100"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <Border Background="#D1ECF1" Padding="10" Margin="0,10,0,0" CornerRadius="5">
                        <TextBlock x:Name="txtTotalServiceCostsDetails" Text="إجمالي تكلفة الخدمات: 0.00 ج.م"
                                  FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- التكاليف الإضافية -->
            <GroupBox Grid.Row="6" Header="💸 التكاليف الإضافية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <DataGrid x:Name="dgAdditionalCostsDetails" AutoGenerateColumns="False"
                             CanUserAddRows="False" CanUserDeleteRows="False"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="100" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40"/>
                            <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                            <DataGridTextColumn Header="القيمة" Binding="{Binding Value, StringFormat=F2}" Width="100"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>
                        </DataGrid.Columns>
                    </DataGrid>

                    <Border Background="#FFF3CD" Padding="10" Margin="0,10,0,0" CornerRadius="5">
                        <TextBlock x:Name="txtTotalAdditionalCostsDetails" Text="إجمالي التكاليف الإضافية: 0.00 ج.م"
                                  FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- ملخص التكاليف النهائي -->
            <GroupBox Grid.Row="7" Header="📈 ملخص التكاليف النهائي" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"
                      Background="#F8F9FA" BorderBrush="#007BFF" BorderThickness="2">
                <Grid Margin="15">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي تكلفة الخدمات:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtSummaryServiceCosts" Text="0.00 ج.م" FontSize="14" Margin="5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="إجمالي التكاليف الإضافية:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtSummaryAdditionalCosts" Text="0.00 ج.م" FontSize="14" Margin="5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي قيمة الزجاج المستخدم:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtSummaryGlassValue" Text="0.00 ج.م" FontSize="14" Margin="5"/>

                    <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="5"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="المجموع الكلي:" FontSize="16" FontWeight="Bold" Margin="5" Foreground="#007BFF"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" x:Name="txtSummaryTotalCost" Text="0.00 ج.م" FontSize="16" FontWeight="Bold" Margin="5" Foreground="#007BFF"/>

                    <TextBlock Grid.Row="5" Grid.Column="0" Text="إجمالي الأمتار المطلوبة فعلياً:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="5" Grid.Column="1" x:Name="txtSummaryTotalMeters" Text="0.00 م²" FontSize="14" Margin="5"/>

                    <TextBlock Grid.Row="6" Grid.Column="0" Text="سعر المتر:" FontSize="16" FontWeight="Bold" Margin="5" Foreground="#28A745"/>
                    <TextBlock Grid.Row="6" Grid.Column="1" x:Name="txtSummaryPricePerMeter" Text="0.00 ج.م/م²" FontSize="16" FontWeight="Bold" Margin="5" Foreground="#28A745"/>
                </Grid>
            </GroupBox>

            <!-- الأزرار السفلية -->
            <StackPanel Grid.Row="8" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button x:Name="btnPrintDetails" Content="🖨️ طباعة التفاصيل"
                       Background="#6F42C1" Foreground="White" Padding="15,8"
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnPrintDetails_Click"/>

                <Button x:Name="btnSavePDFDetails" Content="📄 حفظ PDF"
                       Background="#FD7E14" Foreground="White" Padding="15,8"
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnSavePDFDetails_Click"/>

                <Button x:Name="btnClose" Content="❌ إغلاق"
                       Background="#6C757D" Foreground="White" Padding="15,8"
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnClose_Click"/>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</UserControl>
