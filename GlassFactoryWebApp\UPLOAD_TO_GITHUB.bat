@echo off
title Upload Glass Factory to GitHub
color 0A

echo.
echo ===============================================
echo   📤 رفع نظام حسابات مصنع الزجاج لـ GitHub
echo ===============================================
echo.

echo 🔧 إعداد Git repository...

REM Initialize Git if not already done
if not exist ".git" (
    git init
    echo ✅ تم إنشاء Git repository
) else (
    echo ✅ Git repository موجود مسبقاً
)

echo.
echo 📁 إضافة جميع الملفات...
git add .

echo.
echo 💾 إنشاء commit...
git commit -m "🚀 Initial commit - Glass Factory Accounting System

✅ موديول المبيعات الكامل
✅ إدارة العملاء والفواتير
✅ مدفوعات العملاء
✅ التقارير والإحصائيات
✅ واجهة عربية RTL
✅ API موثق مع Swagger
✅ جاهز للنشر على Railway

المطور: حسام محمد حسان أحمد"

echo.
echo 🌐 ربط GitHub repository...
set /p GITHUB_URL="أدخل رابط GitHub repository (https://github.com/username/GlassFactoryWebApp.git): "

git remote add origin %GITHUB_URL%

echo.
echo 📤 رفع الملفات لـ GitHub...
git branch -M main
git push -u origin main

echo.
echo ✅ تم رفع المشروع لـ GitHub بنجاح!
echo.
echo 🔗 GitHub Repository: %GITHUB_URL%
echo.
echo ===============================================
echo   🚀 الخطوة التالية: النشر على Railway
echo ===============================================
echo.
echo 1. اذهب إلى: https://railway.app
echo 2. انقر "New Project"
echo 3. اختر "Deploy from GitHub repo"
echo 4. اختر "GlassFactoryWebApp"
echo 5. أضف PostgreSQL database
echo 6. أضف متغيرات البيئة
echo 7. انتظر النشر (5-10 دقائق)
echo.

pause
