using System;

namespace GlassFactoryAccounting.Models
{
    // نموذج إدارة المصروفات الرئيسية والفرعية
    public class ExpenseCategory
    {
        public int Id { get; set; }
        public string MainExpenseName { get; set; } = string.Empty;
        public string SubExpenseName { get; set; } = string.Empty;
        public string? Notes { get; set; }
        public bool IsProjectRelated { get; set; }
        public string? ProjectTitle { get; set; }
        public DateTime? ProjectStartDate { get; set; }
        public string? ProjectDescription { get; set; }
        public string? ProjectNotes { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
    }

    // نموذج تسجيل المصروفات الفعلية
    public class ExpenseRecord
    {
        public int Id { get; set; }
        public int MainExpenseId { get; set; }
        public string MainExpenseName { get; set; } = string.Empty;
        public int SubExpenseId { get; set; }
        public string SubExpenseName { get; set; } = string.Empty;
        public int BranchId { get; set; }
        public string BranchName { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public DateTime DateTime { get; set; } = DateTime.Now;
        public string? Notes { get; set; }
        public bool IsProjectExpense { get; set; }
        public string? ProjectTitle { get; set; }
        public int ResponsiblePersonId { get; set; }
        public string ResponsiblePersonName { get; set; } = string.Empty;
        public string PaymentMethod { get; set; } = string.Empty;
        public string ExpenseStatus { get; set; } = "مستحق"; // مسدد أو مستحق
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
    }

    // نموذج فروع الشركة
    public class CompanyBranch
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Address { get; set; }
        public string? Phone { get; set; }
        public string? Manager { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
    }

    // نموذج طرق السداد
    public class PaymentMethod
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string? Description { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
    }
}
