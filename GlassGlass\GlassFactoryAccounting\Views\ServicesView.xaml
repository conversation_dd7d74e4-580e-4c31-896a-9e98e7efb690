<UserControl x:Class="GlassFactoryAccounting.Views.ServicesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1200"
             Background="White">

    <UserControl.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2E86AB"/>
        <SolidColorBrush x:Key="SecondaryBrush" Color="#A23B72"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#F18F01"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#C73E1D"/>
        <SolidColorBrush x:Key="LightBrush" Color="#F5F5F5"/>
        <SolidColorBrush x:Key="DarkBrush" Color="#333333"/>

        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource SecondaryBrush}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="3">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                        Margin="{TemplateBinding Padding}"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="Background" Value="White"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryBrush}" Padding="20,15" Margin="0,0,0,20">
            <TextBlock Text="موديول الخدمات المفصلة" 
                       FontSize="24" FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- شريط الفلاتر -->
        <Border Grid.Row="1" Background="{StaticResource LightBrush}" Padding="20,15" Margin="0,0,0,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول من الفلاتر -->
                <StackPanel Grid.Column="0" Grid.Row="0" Margin="0,0,10,10">
                    <TextBlock Text="العميل:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="CmbCustomer" Style="{StaticResource ModernComboBox}" 
                              IsEditable="True" IsTextSearchEnabled="True"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Grid.Row="0" Margin="0,0,10,10">
                    <TextBlock Text="نوع الخدمة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="CmbServiceType" Style="{StaticResource ModernComboBox}" 
                              IsEditable="True" IsTextSearchEnabled="True"/>
                </StackPanel>

                <StackPanel Grid.Column="2" Grid.Row="0" Margin="0,0,10,10">
                    <TextBlock Text="من تاريخ:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="DpFromDate" FontSize="14" Padding="8"/>
                </StackPanel>

                <StackPanel Grid.Column="3" Grid.Row="0" Margin="0,0,10,10">
                    <TextBlock Text="إلى تاريخ:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="DpToDate" FontSize="14" Padding="8"/>
                </StackPanel>

                <!-- الصف الثاني من الفلاتر -->
                <StackPanel Grid.Column="0" Grid.Row="1" Margin="0,0,10,0">
                    <TextBlock Text="رقم الفاتورة:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtInvoiceNumber" Style="{StaticResource ModernTextBox}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Grid.Row="1" Margin="0,0,10,0">
                    <TextBlock Text="نوع الزجاج:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtGlassType" Style="{StaticResource ModernTextBox}"/>
                </StackPanel>

                <!-- أزرار التحكم -->
                <StackPanel Grid.Column="4" Grid.Row="0" Grid.RowSpan="2" 
                            Orientation="Vertical" VerticalAlignment="Center" Margin="10,0,0,0">
                    <Button x:Name="BtnSearch" Content="بحث" Style="{StaticResource ModernButton}" 
                            Click="BtnSearch_Click" Width="100"/>
                    <Button x:Name="BtnClearFilters" Content="مسح الفلاتر" Style="{StaticResource ModernButton}" 
                            Background="{StaticResource AccentBrush}" Click="BtnClearFilters_Click" Width="100"/>
                </StackPanel>

                <StackPanel Grid.Column="5" Grid.Row="0" Grid.RowSpan="2" 
                            Orientation="Vertical" VerticalAlignment="Center" Margin="10,0,0,0">
                    <Button x:Name="BtnRefresh" Content="تحديث" Style="{StaticResource ModernButton}" 
                            Background="{StaticResource SuccessBrush}" Click="BtnRefresh_Click" Width="100"/>
                    <Button x:Name="BtnExport" Content="تصدير Excel" Style="{StaticResource ModernButton}" 
                            Background="{StaticResource SecondaryBrush}" Click="BtnExport_Click" Width="100"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- جدول البيانات -->
        <Border Grid.Row="2" BorderBrush="#DDDDDD" BorderThickness="1" Margin="0,0,0,10">
            <DataGrid x:Name="ServicesDataGrid" 
                      AutoGenerateColumns="False" 
                      CanUserAddRows="False" 
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal" 
                      HeadersVisibility="Column"
                      AlternatingRowBackground="#F9F9F9"
                      RowBackground="White"
                      FontSize="12"
                      SelectionMode="Single">
                
                <DataGrid.Columns>
                    <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                    <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                    <DataGridTextColumn Header="العميل" Binding="{Binding CustomerName}" Width="150"/>
                    <DataGridTextColumn Header="الخدمة" Binding="{Binding ServiceName}" Width="120"/>
                    <DataGridTextColumn Header="نوع الزجاج" Binding="{Binding GlassType}" Width="100"/>
                    <DataGridTextColumn Header="السماكة" Binding="{Binding GlassThickness}" Width="80"/>
                    <DataGridTextColumn Header="الطول" Binding="{Binding Length}" Width="80"/>
                    <DataGridTextColumn Header="العرض" Binding="{Binding Width}" Width="80"/>
                    <DataGridTextColumn Header="المساحة" Binding="{Binding Area}" Width="80"/>
                    <DataGridTextColumn Header="العدد" Binding="{Binding Count}" Width="60"/>
                    <DataGridTextColumn Header="إجمالي المساحة" Binding="{Binding TotalArea}" Width="100"/>
                    <DataGridTextColumn Header="سعر الوحدة" Binding="{Binding UnitPrice, StringFormat=C}" Width="100"/>
                    <DataGridTextColumn Header="إجمالي السعر" Binding="{Binding TotalPrice, StringFormat=C}" Width="120"/>
                    <DataGridTextColumn Header="التفاصيل" Binding="{Binding Details}" Width="*"/>
                </DataGrid.Columns>
            </DataGrid>
        </Border>

        <!-- شريط المعلومات السفلي -->
        <Border Grid.Row="3" Background="{StaticResource LightBrush}" Padding="20,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock x:Name="TxtRecordCount" Grid.Column="0" 
                           Text="عدد السجلات: 0" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center"/>

                <TextBlock x:Name="TxtTotalArea" Grid.Column="1" 
                           Text="إجمالي المساحة: 0 م²" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center" 
                           Margin="20,0"/>

                <TextBlock x:Name="TxtTotalValue" Grid.Column="2" 
                           Text="إجمالي القيمة: 0 ر.س" 
                           FontWeight="SemiBold" 
                           VerticalAlignment="Center" 
                           Margin="20,0"/>

                <TextBlock x:Name="TxtLastUpdate" Grid.Column="3" 
                           Text="آخر تحديث: --" 
                           FontStyle="Italic" 
                           VerticalAlignment="Center" 
                           Margin="20,0"/>
            </Grid>
        </Border>
    </Grid>
</UserControl>
