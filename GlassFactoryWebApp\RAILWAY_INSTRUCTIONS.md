# 🚀 تعليمات النشر على Railway - خطوة بخطوة

## 🌐 **الروابط المتوقعة بعد النشر:**

### **🎯 الروابط الرئيسية:**
- **🏠 التطبيق الرئيسي:** https://glassfactorywebapp-production.up.railway.app
- **💰 موديول المبيعات:** https://glassfactorywebapp-production.up.railway.app/sales
- **📋 API Documentation:** https://glassfactorywebapp-production.up.railway.app/swagger
- **🔍 Health Check:** https://glassfactorywebapp-production.up.railway.app/health

### **🔗 روابط موديول المبيعات:**
- **📋 قائمة الفواتير:** https://glassfactorywebapp-production.up.railway.app/sales/invoices
- **➕ فاتورة جديدة:** https://glassfactorywebapp-production.up.railway.app/sales/invoices/new
- **👥 قائمة العملاء:** https://glassfactorywebapp-production.up.railway.app/sales/customers
- **👤 عميل جديد:** https://glassfactorywebapp-production.up.railway.app/sales/customers/new
- **💳 المدفوعات:** https://glassfactorywebapp-production.up.railway.app/sales/payments
- **📊 التقارير:** https://glassfactorywebapp-production.up.railway.app/sales/reports

---

## 📋 **الخطوات التفصيلية:**

### **الخطوة 1: الذهاب لـ Railway**
🔗 **اذهب إلى:** https://railway.app
- تأكد من تسجيل الدخول بحساب GitHub

### **الخطوة 2: إنشاء مشروع جديد**
1. انقر **"New Project"**
2. اختر **"Deploy from GitHub repo"**
3. ابحث عن **"GlassFactoryWebApp"**
4. انقر **"Deploy Now"**

### **الخطوة 3: إضافة قاعدة البيانات**
1. في نفس المشروع، انقر **"+ New"**
2. اختر **"Database"**
3. اختر **"PostgreSQL"**
4. انتظر حتى يتم إنشاء قاعدة البيانات (1-2 دقيقة)

### **الخطوة 4: إعداد متغيرات البيئة**
1. انقر على service الرئيسي (GlassFactoryWebApp)
2. انقر **"Variables"**
3. أضف هذه المتغيرات:

```
JWT_SECRET_KEY=GlassFactorySecretKey2025VeryLongAndSecureForProduction!
ASPNETCORE_ENVIRONMENT=Production
```

### **الخطوة 5: انتظار النشر**
1. Railway سيبدأ النشر تلقائياً
2. انقر **"View Logs"** لمتابعة التقدم
3. انتظر 5-10 دقائق حتى اكتمال النشر
4. ستظهر رسالة "Deployment successful"

### **الخطوة 6: الحصول على رابط التطبيق**
1. انقر **"Settings"** في service الرئيسي
2. انقر **"Networking"**
3. انقر **"Generate Domain"**
4. انسخ الرابط (مثل: glassfactorywebapp-production.up.railway.app)

---

## 🧪 **اختبار النظام:**

### **1. اختبار التطبيق الرئيسي:**
- افتح الرابط الرئيسي
- تأكد من ظهور الصفحة الرئيسية
- تأكد من عمل التنقل

### **2. اختبار موديول المبيعات:**
- انتقل إلى `/sales`
- تأكد من ظهور لوحة المبيعات
- تأكد من عمل جميع الأزرار

### **3. اختبار إدارة العملاء:**
- انقر "قائمة العملاء"
- انقر "عميل جديد"
- أدخل بيانات عميل:
  ```
  اسم العميل: شركة الزجاج المتطور
  الهاتف: 0501234567
  البريد: <EMAIL>
  العنوان: الرياض، المملكة العربية السعودية
  ```
- احفظ العميل
- تأكد من ظهوره في القائمة

### **4. اختبار فواتير المبيعات:**
- انقر "قائمة الفواتير"
- انقر "فاتورة جديدة"
- اختر العميل الذي أضفته
- أضف منتجات للفاتورة
- احفظ الفاتورة
- جرب طباعة الفاتورة PDF

### **5. اختبار المدفوعات:**
- انقر "المدفوعات"
- سجل دفعة جديدة للعميل
- تأكد من تحديث رصيد العميل

### **6. اختبار التقارير:**
- انقر "التقارير"
- اعرض تقرير المبيعات
- جرب تصدير التقرير Excel/PDF

### **7. اختبار API:**
- افتح `/swagger`
- تأكد من ظهور جميع APIs
- جرب استدعاء API للعملاء

---

## 🔍 **مراقبة النظام:**

### **في Railway Dashboard:**
- **Metrics:** مراقبة استخدام الموارد
- **Logs:** عرض سجلات التطبيق
- **Deployments:** تاريخ النشر
- **Settings:** إعدادات المشروع

### **Health Checks:**
- **API Health:** `/health`
- **Database:** يتم فحصها تلقائياً
- **Application:** مراقبة مستمرة

---

## 🎯 **النتيجة المتوقعة:**

### **💰 موديول المبيعات الكامل:**
✅ **إدارة العملاء** - إضافة وتعديل وحذف العملاء  
✅ **فواتير المبيعات** - إنشاء وطباعة وترحيل الفواتير  
✅ **مدفوعات العملاء** - تسجيل ومتابعة المدفوعات  
✅ **التقارير** - تقارير مفصلة وإحصائيات  
✅ **البحث والفلترة** - بحث متقدم في جميع البيانات  
✅ **الطباعة** - طباعة الفواتير PDF  
✅ **التصدير** - تصدير التقارير Excel/PDF  

### **🔧 المميزات التقنية:**
✅ **واجهة عربية RTL** - دعم كامل للغة العربية  
✅ **تصميم متجاوب** - يعمل على جميع الأجهزة  
✅ **API موثق** - Swagger documentation  
✅ **أمان متقدم** - JWT authentication  
✅ **قاعدة بيانات PostgreSQL** - مجانية على Railway  
✅ **نسخ احتياطية تلقائية** - Railway يحفظ البيانات  
✅ **SSL Certificate** - HTTPS مجاني  
✅ **مراقبة وتسجيل** - Logs ومراقبة مدمجة  

---

## 🔧 **استكشاف الأخطاء:**

### **مشكلة 1: التطبيق لا يعمل**
- تحقق من **Logs** في Railway
- تأكد من إعداد متغيرات البيئة
- تأكد من اتصال قاعدة البيانات

### **مشكلة 2: قاعدة البيانات**
- تحقق من **PostgreSQL service**
- تأكد من **DATABASE_URL**
- راجع **connection string**

### **مشكلة 3: Frontend لا يظهر**
- تأكد من بناء **React app**
- تحقق من **Static Files**
- راجع **Dockerfile**

---

## 📞 **بعد النشر:**

**أرسل الروابط التالية للمراجعة:**

1. **🌐 التطبيق الرئيسي:** https://your-app-name.up.railway.app
2. **💰 موديول المبيعات:** https://your-app-name.up.railway.app/sales
3. **📋 Swagger API:** https://your-app-name.up.railway.app/swagger
4. **🔍 Health Check:** https://your-app-name.up.railway.app/health

---

**🎉 النظام جاهز للاستخدام والمراجعة!**  
**⏰ الوقت المتوقع للنشر: 5-10 دقائق**
