<Window x:Class="GlassFactoryAccounting.Views.Accounting.JournalEntryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="قيد اليومية" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="📝 قيد اليومية - النظام الأمريكي" 
                   FontSize="24" FontWeight="Bold" 
                   Foreground="#2C3E50" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>
        
        <!-- معلومات القيد -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1"
                CornerRadius="5" Background="#F8F9FA" Margin="0,0,0,15">
            <Grid Margin="15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- رقم القيد -->
                <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                    <Label Content="رقم القيد:" FontWeight="Bold"/>
                    <TextBox Name="txtEntryNumber" FontSize="14" Margin="5" IsReadOnly="True"
                             Background="#ECF0F1"/>
                </StackPanel>

                <!-- تاريخ القيد -->
                <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                    <Label Content="تاريخ القيد:" FontWeight="Bold"/>
                    <DatePicker Name="dpEntryDate" FontSize="14" Margin="5"/>
                </StackPanel>

                <!-- رقم المرجع -->
                <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                    <Label Content="رقم المرجع:" FontWeight="Bold"/>
                    <TextBox Name="txtReferenceNumber" FontSize="14" Margin="5"/>
                </StackPanel>

                <!-- نوع المرجع -->
                <StackPanel Grid.Row="0" Grid.Column="3" Margin="5">
                    <Label Content="نوع المرجع:" FontWeight="Bold"/>
                    <TextBox Name="txtReferenceType" FontSize="14" Margin="5"/>
                </StackPanel>

                <!-- وصف القيد -->
                <StackPanel Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="4" Margin="5">
                    <Label Content="وصف القيد:" FontWeight="Bold"/>
                    <TextBox Name="txtDescription" FontSize="14" Margin="5" Height="60"
                             TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- تفاصيل القيد -->
        <Border Grid.Row="2" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="White">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <!-- شريط أدوات التفاصيل -->
                <StackPanel Grid.Row="0" Orientation="Horizontal"
                            HorizontalAlignment="Right"
                            Background="#34495E" Margin="10">
                    
                    <Button Name="btnAddDetail" Content="➕ إضافة سطر"
                            Background="#27AE60" Foreground="White"
                            Margin="10,5" FontSize="12" FontWeight="Bold"
                            Click="BtnAddDetail_Click"/>

                    <Button Name="btnRemoveDetail" Content="➖ حذف سطر"
                            Background="#E74C3C" Foreground="White"
                            Margin="10,5" FontSize="12" FontWeight="Bold"
                            Click="BtnRemoveDetail_Click"/>
                </StackPanel>
                
                <!-- جدول التفاصيل -->
                <DataGrid Grid.Row="1" Name="dgDetails" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False" 
                          CanUserDeleteRows="False"
                          GridLinesVisibility="All" 
                          HeadersVisibility="Column"
                          FontSize="12" 
                          Margin="10">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="م" Binding="{Binding LineNumber}" 
                                            Width="40" IsReadOnly="True"/>
                        <DataGridTextColumn Header="كود الحساب" Binding="{Binding AccountCode}" 
                                            Width="120"/>
                        <DataGridTextColumn Header="اسم الحساب" Binding="{Binding AccountName}" 
                                            Width="200"/>
                        <DataGridTextColumn Header="البيان" Binding="{Binding Description}" 
                                            Width="200"/>
                        <DataGridTextColumn Header="مدين" Binding="{Binding DebitAmount, StringFormat='{}{0:N2}'}" 
                                            Width="100"/>
                        <DataGridTextColumn Header="دائن" Binding="{Binding CreditAmount, StringFormat='{}{0:N2}'}" 
                                            Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
                
                <!-- ملخص المبالغ -->
                <Border Grid.Row="2" Background="#ECF0F1" Margin="10">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="1" Text="إجمالي المدين:" 
                                   FontWeight="Bold" VerticalAlignment="Center" Margin="10,0"/>
                        <TextBlock Grid.Column="2" Name="txtTotalDebit" Text="0.00" 
                                   FontWeight="Bold" VerticalAlignment="Center" 
                                   Foreground="#27AE60" Margin="10,0"/>
                        
                        <TextBlock Grid.Column="3" Text="إجمالي الدائن:" 
                                   FontWeight="Bold" VerticalAlignment="Center" Margin="10,0"/>
                        <TextBlock Grid.Column="4" Name="txtTotalCredit" Text="0.00" 
                                   FontWeight="Bold" VerticalAlignment="Center" 
                                   Foreground="#E74C3C" Margin="10,0"/>
                    </Grid>
                </Border>
            </Grid>
        </Border>
        
        <!-- حالة التوازن -->
        <Border Grid.Row="3" Name="borderBalance" Background="#E74C3C"
                CornerRadius="5" Margin="10,10,10,10">
            <TextBlock Name="txtBalanceStatus" Text="⚠️ القيد غير متوازن - يجب أن يكون إجمالي المدين = إجمالي الدائن" 
                       FontSize="14" FontWeight="Bold" 
                       Foreground="White" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="4" Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,10,0,0">
            
            <Button Name="btnSave" Content="💾 حفظ القيد"
                    Background="#27AE60" Foreground="White"
                    Margin="20,10" FontSize="14" FontWeight="Bold"
                    Click="BtnSave_Click"/>

            <Button Name="btnPost" Content="📋 ترحيل القيد"
                    Background="#3498DB" Foreground="White"
                    Margin="20,10" FontSize="14" FontWeight="Bold"
                    Click="BtnPost_Click"/>

            <Button Name="btnClear" Content="🗑️ مسح الكل"
                    Background="#95A5A6" Foreground="White"
                    Margin="20,10" FontSize="14" FontWeight="Bold"
                    Click="BtnClear_Click"/>
            
        </StackPanel>
    </Grid>
</Window>
