﻿#pragma checksum "..\..\..\..\Views\EditSaleInvoiceView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AB810C6D059DD80DCC4D95278AEF6F56FCBA7253"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// EditSaleInvoiceView
    /// </summary>
    public partial class EditSaleInvoiceView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 46 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTitle;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManageServices;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbCustomerName;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManageCustomers;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateInvoice;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtInvoiceNumber;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddNormalRow;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddManualRow;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteRow;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoiceDataGrid;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkEnableDiscount;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PnlDiscount;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDiscountAmount;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkEnableNotes;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSubTotal;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PnlDiscountDisplay;
        
        #line default
        #line hidden
        
        
        #line 213 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtDiscountDisplay;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFinalTotal;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSavePDF;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrint;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/editsaleinvoiceview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 49 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.BtnManageServices = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnManageServices.Click += new System.Windows.RoutedEventHandler(this.BtnManageServices_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CmbCustomerName = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.BtnManageCustomers = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnManageCustomers.Click += new System.Windows.RoutedEventHandler(this.BtnManageCustomers_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.DateInvoice = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 7:
            this.TxtInvoiceNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.BtnAddNormalRow = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnAddNormalRow.Click += new System.Windows.RoutedEventHandler(this.BtnAddNormalRow_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnAddManualRow = ((System.Windows.Controls.Button)(target));
            
            #line 122 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnAddManualRow.Click += new System.Windows.RoutedEventHandler(this.BtnAddManualRow_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnDeleteRow = ((System.Windows.Controls.Button)(target));
            
            #line 127 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnDeleteRow.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteRow_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.InvoiceDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            this.ChkEnableDiscount = ((System.Windows.Controls.CheckBox)(target));
            
            #line 188 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.ChkEnableDiscount.Checked += new System.Windows.RoutedEventHandler(this.ChkEnableDiscount_Checked);
            
            #line default
            #line hidden
            
            #line 188 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.ChkEnableDiscount.Unchecked += new System.Windows.RoutedEventHandler(this.ChkEnableDiscount_Unchecked);
            
            #line default
            #line hidden
            return;
            case 13:
            this.PnlDiscount = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 14:
            this.TxtDiscountAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 193 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.TxtDiscountAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtDiscountAmount_TextChanged);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ChkEnableNotes = ((System.Windows.Controls.CheckBox)(target));
            
            #line 197 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.ChkEnableNotes.Checked += new System.Windows.RoutedEventHandler(this.ChkEnableNotes_Checked);
            
            #line default
            #line hidden
            
            #line 197 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.ChkEnableNotes.Unchecked += new System.Windows.RoutedEventHandler(this.ChkEnableNotes_Unchecked);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.TxtSubTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.PnlDiscountDisplay = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.TxtDiscountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.TxtFinalTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 234 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.BtnSavePDF = ((System.Windows.Controls.Button)(target));
            
            #line 239 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnSavePDF.Click += new System.Windows.RoutedEventHandler(this.BtnSavePDF_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.BtnPrint = ((System.Windows.Controls.Button)(target));
            
            #line 244 "..\..\..\..\Views\EditSaleInvoiceView.xaml"
            this.BtnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

