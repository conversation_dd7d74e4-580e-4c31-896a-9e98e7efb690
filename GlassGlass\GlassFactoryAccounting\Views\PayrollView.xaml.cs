using System;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة موديول الرواتب والأجور الرئيسية
    /// </summary>
    public partial class PayrollView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;

        public PayrollView()
        {
            InitializeComponent();
            _payrollService = new PayrollService();

            // الحصول على النافذة الرئيسية
            _mainWindow = Application.Current.MainWindow as MainWindow ?? throw new InvalidOperationException("MainWindow not found");
        }

        #region Button Click Events

        private void BtnEmployeeData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var employeeDataView = new EmployeeDataView(_mainWindow);
                _mainWindow.MainContentArea.Content = employeeDataView;
                _mainWindow.TxtPageTitle.Text = "بيانات الموظف";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة بيانات الموظف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSalaryDue_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var salaryDueView = new SalaryDueView(_mainWindow);
                _mainWindow.MainContentArea.Content = salaryDueView;
                _mainWindow.TxtPageTitle.Text = "الرواتب المستحقة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة الرواتب المستحقة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAdvance_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var advanceView = new EmployeeAdvanceView(_mainWindow);
                _mainWindow.MainContentArea.Content = advanceView;
                _mainWindow.TxtPageTitle.Text = "تسجيل سلفة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة تسجيل السلفة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnBonus_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var bonusView = new EmployeeBonusView(_mainWindow);
                _mainWindow.MainContentArea.Content = bonusView;
                _mainWindow.TxtPageTitle.Text = "تسجيل مكافأة";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة تسجيل المكافأة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeduction_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var deductionView = new EmployeeDeductionView(_mainWindow);
                _mainWindow.MainContentArea.Content = deductionView;
                _mainWindow.TxtPageTitle.Text = "تسجيل خصم";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة تسجيل الخصم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnOvertime_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var overtimeView = new EmployeeOvertimeView(_mainWindow);
                _mainWindow.MainContentArea.Content = overtimeView;
                _mainWindow.TxtPageTitle.Text = "تسجيل وقت إضافي";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة تسجيل الوقت الإضافي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var paymentView = new SimplifiedSalaryPaymentView(_mainWindow);
                _mainWindow.MainContentArea.Content = paymentView;
                _mainWindow.TxtPageTitle.Text = "تسجيل سداد موظف";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة تسجيل السداد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnFullReport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var reportView = new PayrollFullReportView(_mainWindow);
                _mainWindow.MainContentArea.Content = reportView;
                _mainWindow.TxtPageTitle.Text = "تقرير كامل";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة التقرير الكامل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSimpleSalaryPayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var paymentView = new SimplifiedSalaryPaymentView(_mainWindow);
                _mainWindow.MainContentArea.Content = paymentView;
                _mainWindow.TxtPageTitle.Text = "سداد راتب موظف";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة سداد الراتب: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        #endregion
    }
}
