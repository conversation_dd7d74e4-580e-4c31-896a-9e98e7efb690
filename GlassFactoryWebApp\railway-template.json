{"name": "Glass Factory Accounting System", "description": "🏭 نظام حسابات مصنع الزجاج - Complete accounting system for glass factories with Arabic RTL support", "repository": "https://github.com/glassfactory/GlassFactoryWebApp", "services": [{"name": "glass-factory-app", "source": {"repo": "https://github.com/glassfactory/GlassFactoryWebApp", "branch": "main"}, "build": {"builder": "NIXPACKS", "buildCommand": "npm install"}, "deploy": {"startCommand": "node server.js", "healthcheckPath": "/health", "healthcheckTimeout": 300, "numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3}, "variables": {"NODE_ENV": "production", "PORT": "$PORT"}}], "metadata": {"title": "🏭 نظام حسابات مصنع الزجاج", "description": "نظام حسابات متكامل مصمم خصيصاً لمصانع الزجاج مع دعم كامل للغة العربية", "tags": ["accounting", "glass-factory", "arabic", "nodejs", "express", "javascript"], "author": "حسام محمد حسان أحمد", "version": "2.0.0", "features": ["💰 موديول المبيعات الكامل", "👥 إدارة العملاء", "📋 فواتير المبيعات", "💳 مدفوعات العملاء", "📊 التقارير والإحصائيات", "🌍 دعم اللغة العربية RTL", "📱 تصميم متجاوب", "🔐 أمان متقدم", "📄 طباعة PDF", "📊 تصدير Excel", "🚀 APIs موثقة", "⚡ أداء عالي", "🌐 متاح 24/7"], "demo_url": "https://glass-factory-accounting.surge.sh", "live_urls": ["https://glass-factory-accounting.surge.sh", "https://glass-factory-accounting.netlify.app", "https://glass-factory-accounting.vercel.app", "https://glass-factory-accounting.glitch.me"]}}