{"name": "Glass Factory Accounting System", "description": "نظام حسابات مصنع الزجاج - Complete accounting system for glass factories", "repository": "https://github.com/glassfactory/GlassFactoryWebApp", "services": [{"name": "glass-factory-app", "source": {"repo": "https://github.com/glassfactory/GlassFactoryWebApp", "branch": "main"}, "build": {"builder": "NIXPACKS"}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE"}, "variables": {"ASPNETCORE_ENVIRONMENT": "Production", "JWT_SECRET_KEY": "GlassFactorySecretKey2025VeryLongAndSecureForProduction!"}}, {"name": "postgres", "source": {"image": "postgres:15"}, "variables": {"POSTGRES_DB": "glass_factory_db", "POSTGRES_USER": "glass_factory_user", "POSTGRES_PASSWORD": "GlassFactory2025!"}}], "metadata": {"title": "🏭 نظام حسابات مصنع الزجاج", "description": "نظام حسابات متكامل مصمم خصيصاً لمصانع الزجاج مع دعم كامل للغة العربية", "tags": ["accounting", "glass-factory", "arabic", "asp.net-core", "react", "postgresql"], "author": "حسام محمد حسان أحمد", "version": "2.0.0", "features": ["💰 موديول المبيعات الكامل", "👥 إدارة العملاء", "📋 فواتير المبيعات", "💳 مدفوعات العملاء", "📊 التقارير والإحصائيات", "🌍 دعم اللغة العربية RTL", "📱 تصميم متجاوب", "🔐 أمان متقدم", "📄 طباعة PDF", "📊 تصدير Excel"]}}