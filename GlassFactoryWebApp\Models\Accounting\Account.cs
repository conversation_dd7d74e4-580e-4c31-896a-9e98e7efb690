using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج الحساب المحاسبي
    /// </summary>
    public class Account : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string AccountCode { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string AccountName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? AccountDescription { get; set; }

        [Required]
        public AccountType AccountType { get; set; }

        public int? ParentAccountId { get; set; }

        public int AccountLevel { get; set; } = 1;

        public bool IsParent { get; set; } = false;

        public bool IsActive { get; set; } = true;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OpeningBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal DebitBalance { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CreditBalance { get; set; } = 0;

        public bool AllowManualEntry { get; set; } = true;

        public bool RequiresCostCenter { get; set; } = false;

        public bool RequiresProject { get; set; } = false;

        [MaxLength(50)]
        public string? CurrencyCode { get; set; } = "SAR";

        // Control accounts
        public bool IsControlAccount { get; set; } = false;

        [MaxLength(50)]
        public string? ControlAccountType { get; set; } // عملاء، موردين، موظفين، بنوك

        // Reporting
        public bool ShowInBalanceSheet { get; set; } = true;

        public bool ShowInIncomeStatement { get; set; } = false;

        public int SortOrder { get; set; } = 0;

        // Navigation Properties
        [ForeignKey("ParentAccountId")]
        public virtual Account? ParentAccount { get; set; }

        public virtual ICollection<Account> SubAccounts { get; set; } = new List<Account>();
        public virtual ICollection<JournalEntryDetail> JournalEntryDetails { get; set; } = new List<JournalEntryDetail>();

        // Computed Properties
        public bool CanBeUsedInEntries => !IsParent;

        public string AccountTypeDisplay => AccountType switch
        {
            AccountType.Assets => "الأصول",
            AccountType.Liabilities => "الالتزامات",
            AccountType.Equity => "حقوق الملكية",
            AccountType.Revenue => "الإيرادات",
            AccountType.Expenses => "المصروفات",
            AccountType.OtherIncome => "إيرادات أخرى",
            AccountType.OtherExpenses => "مصروفات أخرى",
            _ => "غير محدد"
        };
    }

    /// <summary>
    /// أنواع الحسابات المحاسبية
    /// </summary>
    public enum AccountType
    {
        Assets = 1,          // الأصول
        Liabilities = 2,     // الالتزامات
        Equity = 3,          // حقوق الملكية
        Revenue = 4,         // الإيرادات
        Expenses = 5,        // المصروفات
        OtherIncome = 6,     // إيرادات أخرى
        OtherExpenses = 7    // مصروفات أخرى
    }
}
