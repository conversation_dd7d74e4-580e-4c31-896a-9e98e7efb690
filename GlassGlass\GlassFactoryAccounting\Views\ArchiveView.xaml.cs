using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة أرشيف الفواتير والتحليلات
/// </summary>
public partial class ArchiveView : UserControl, INotifyPropertyChanged
{
    private readonly SalesService _salesService;
    private ObservableCollection<Sale> _allInvoices;
    private ObservableCollection<Sale> _filteredInvoices;

    public ObservableCollection<Sale> FilteredInvoices
    {
        get => _filteredInvoices;
        set
        {
            _filteredInvoices = value;
            OnPropertyChanged();
        }
    }

    public ArchiveView()
    {
        InitializeComponent();
        DataContext = this;
        
        _salesService = new SalesService();
        _allInvoices = new ObservableCollection<Sale>();
        _filteredInvoices = new ObservableCollection<Sale>();
        
        InvoicesDataGrid.ItemsSource = _filteredInvoices;
        
        LoadInvoices();
    }

    private void LoadInvoices()
    {
        try
        {
            Task.Run(async () =>
            {
                try
                {
                    var invoices = await _salesService.GetAllSalesAsync();

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        _allInvoices.Clear();

                        foreach (var invoice in invoices)
                        {
                            // إضافة نص الخدمات للعرض
                            invoice.ServicesText = string.Join(", ", invoice.SaleItems.Select(i => i.Service).Distinct());
                            _allInvoices.Add(invoice);
                        }

                        ApplyFilters();
                        UpdateAnalytics();
                    });
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الفواتير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ApplyFilters()
    {
        var filtered = _allInvoices.AsEnumerable();

        // فلتر رقم الفاتورة
        if (!string.IsNullOrWhiteSpace(TxtSearchInvoice.Text))
        {
            filtered = filtered.Where(i => i.InvoiceNumber.Contains(TxtSearchInvoice.Text, StringComparison.OrdinalIgnoreCase));
        }

        // فلتر اسم العميل
        if (!string.IsNullOrWhiteSpace(TxtSearchCustomer.Text))
        {
            filtered = filtered.Where(i => i.Customer?.Name?.Contains(TxtSearchCustomer.Text, StringComparison.OrdinalIgnoreCase) == true);
        }

        // فلتر التاريخ من
        if (DateFrom.SelectedDate.HasValue)
        {
            filtered = filtered.Where(i => i.SaleDate >= DateFrom.SelectedDate.Value);
        }

        // فلتر التاريخ إلى
        if (DateTo.SelectedDate.HasValue)
        {
            filtered = filtered.Where(i => i.SaleDate <= DateTo.SelectedDate.Value);
        }

        _filteredInvoices.Clear();
        foreach (var invoice in filtered.OrderByDescending(i => i.SaleDate))
        {
            _filteredInvoices.Add(invoice);
        }

        UpdateAnalytics();
    }

    private void UpdateAnalytics()
    {
        var invoices = _filteredInvoices.ToList();
        
        // إجمالي الفواتير
        TxtTotalInvoices.Text = invoices.Count.ToString();
        
        // إجمالي المبيعات
        var totalSales = invoices.Sum(i => i.NetAmount);
        TxtTotalSales.Text = $"{totalSales:N2} ج.م";
        
        // متوسط الفاتورة
        var averageInvoice = invoices.Count > 0 ? totalSales / invoices.Count : 0;
        TxtAverageInvoice.Text = $"{averageInvoice:N2} ج.م";
        
        // أكثر الخدمات استخداماً
        var services = invoices.SelectMany(i => i.SaleItems.Select(s => s.Service))
                              .GroupBy(s => s)
                              .OrderByDescending(g => g.Count())
                              .FirstOrDefault();
        TxtTopService.Text = services?.Key ?? "-";
        
        // عدد العملاء الفريدين
        var uniqueCustomers = invoices.Select(i => i.Customer?.Name).Distinct().Count();
        TxtTotalCustomers.Text = uniqueCustomers.ToString();
    }

    private void TxtSearchInvoice_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void TxtSearchCustomer_TextChanged(object sender, TextChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void DateFrom_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void DateTo_SelectedDateChanged(object sender, SelectionChangedEventArgs e)
    {
        ApplyFilters();
    }

    private void BtnClearFilters_Click(object sender, RoutedEventArgs e)
    {
        TxtSearchInvoice.Text = "";
        TxtSearchCustomer.Text = "";
        DateFrom.SelectedDate = null;
        DateTo.SelectedDate = null;
        ApplyFilters();
    }

    private void BtnRefresh_Click(object sender, RoutedEventArgs e)
    {
        LoadInvoices();
    }

    private void BtnViewInvoice_Click(object sender, RoutedEventArgs e)
    {
        if (InvoicesDataGrid.SelectedItem is Sale selectedInvoice)
        {
            var viewWindow = new InvoiceViewWindow(selectedInvoice);
            viewWindow.ShowDialog();
        }
    }

    private void BtnPrintInvoice_Click(object sender, RoutedEventArgs e)
    {
        if (InvoicesDataGrid.SelectedItem is Sale selectedInvoice)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PDF Files|*.pdf",
                    FileName = $"Invoice_{selectedInvoice.InvoiceNumber}.pdf",
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var pdfService = new InvoicePdfService();
                    bool success = pdfService.CreateInvoicePdf(
                        selectedInvoice.Customer?.Name ?? "",
                        selectedInvoice.InvoiceNumber,
                        selectedInvoice.SaleDate,
                        new ObservableCollection<SaleItem>(selectedInvoice.SaleItems),
                        selectedInvoice.TotalAmount,
                        selectedInvoice.Discount,
                        selectedInvoice.NetAmount,
                        selectedInvoice.Notes,
                        saveDialog.FileName
                    );

                    if (success)
                    {
                        MessageBox.Show($"تم حفظ الفاتورة بنجاح في:\n{saveDialog.FileName}", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        
                        Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                    }
                    else
                    {
                        MessageBox.Show("حدث خطأ أثناء إنشاء ملف PDF", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnDeleteInvoice_Click(object sender, RoutedEventArgs e)
    {
        if (InvoicesDataGrid.SelectedItem is Sale selectedInvoice)
        {
            var result = MessageBox.Show($"هل تريد حذف الفاتورة '{selectedInvoice.InvoiceNumber}'؟\nهذا الإجراء لا يمكن التراجع عنه.", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
            
            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // حذف الملف من النظام
                    var salesFolder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                        "GlassFactoryAccounting", "Sales");
                    
                    var files = Directory.GetFiles(salesFolder, "*.json");
                    foreach (var file in files)
                    {
                        try
                        {
                            var json = File.ReadAllText(file);
                            var sale = System.Text.Json.JsonSerializer.Deserialize<Sale>(json);
                            if (sale?.InvoiceNumber == selectedInvoice.InvoiceNumber)
                            {
                                File.Delete(file);
                                break;
                            }
                        }
                        catch
                        {
                            // تجاهل الملفات التالفة
                        }
                    }
                    
                    // إزالة من القائمة
                    _allInvoices.Remove(selectedInvoice);
                    _filteredInvoices.Remove(selectedInvoice);
                    
                    UpdateAnalytics();
                    
                    MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }

    private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "CSV Files|*.csv",
                FileName = $"Invoices_Archive_{DateTime.Now:yyyyMMdd}.csv",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (saveDialog.ShowDialog() == true)
            {
                var csv = "رقم الفاتورة,اسم العميل,التاريخ,إجمالي المبلغ,الخصم,المبلغ النهائي,عدد العناصر,الخدمات\n";
                
                foreach (var invoice in _filteredInvoices)
                {
                    csv += $"{invoice.InvoiceNumber},{invoice.Customer?.Name},{invoice.SaleDate:yyyy/MM/dd}," +
                           $"{invoice.TotalAmount:N2},{invoice.Discount:N2},{invoice.NetAmount:N2}," +
                           $"{invoice.SaleItems.Count},{invoice.ServicesText}\n";
                }
                
                File.WriteAllText(saveDialog.FileName, csv, System.Text.Encoding.UTF8);
                
                MessageBox.Show($"تم تصدير البيانات بنجاح إلى:\n{saveDialog.FileName}", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تصدير البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;
    
    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
