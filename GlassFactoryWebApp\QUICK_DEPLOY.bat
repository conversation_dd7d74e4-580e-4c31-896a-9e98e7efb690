@echo off
title Glass Factory - Quick Deploy to Oracle Cloud
color 0A

echo.
echo ===============================================
echo   🚀 Glass Factory Quick Deploy
echo   نشر سريع لنظام حسابات مصنع الزجاج
echo ===============================================
echo.

echo 📋 Deployment Information:
echo ✅ Target: Oracle Cloud Free Tier
echo ✅ Domain: glass-factory-demo.ddns.net
echo ✅ Technology: Docker + Docker Compose
echo ✅ Database: PostgreSQL
echo ✅ Frontend: React + TypeScript
echo ✅ Backend: ASP.NET Core 8.0
echo.

echo 🔧 Prerequisites Check:
echo - Oracle Cloud Instance (Ubuntu 22.04)
echo - Docker and Docker Compose
echo - Domain pointing to server IP
echo - Ports 80, 443, 22 open
echo.

echo ⚠️ IMPORTANT NOTES:
echo 1. Make sure you have Oracle Cloud instance ready
echo 2. Domain should point to your server IP
echo 3. SSH access to the server is required
echo 4. This will deploy the complete application
echo.

set /p CONFIRM="Do you want to proceed with deployment? (y/n): "
if /i "%CONFIRM%" neq "y" (
    echo Deployment cancelled.
    pause
    exit /b
)

echo.
echo 📦 Preparing deployment package...

REM Create deployment package
if exist "deployment-package.zip" del "deployment-package.zip"

echo Creating deployment package...
powershell -Command "Compress-Archive -Path '.\*' -DestinationPath 'deployment-package.zip' -Force"

echo.
echo 📋 Deployment Instructions:
echo.
echo 1. Upload the deployment package to your Oracle Cloud server:
echo    scp deployment-package.zip ubuntu@YOUR_SERVER_IP:~/
echo.
echo 2. SSH to your server:
echo    ssh ubuntu@YOUR_SERVER_IP
echo.
echo 3. Extract and deploy:
echo    unzip deployment-package.zip
echo    chmod +x DEPLOY_NOW.sh
echo    sudo ./DEPLOY_NOW.sh
echo.
echo 4. Wait for deployment to complete (5-10 minutes)
echo.
echo 5. Access your application:
echo    http://glass-factory-demo.ddns.net
echo.

echo ===============================================
echo   📋 Manual Deployment Steps
echo ===============================================
echo.
echo If you prefer manual deployment, follow these steps:
echo.
echo 1. Connect to your Oracle Cloud server
echo 2. Update system: sudo apt update && sudo apt upgrade -y
echo 3. Install Docker: curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh
echo 4. Install Docker Compose: sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
echo 5. Make executable: sudo chmod +x /usr/local/bin/docker-compose
echo 6. Upload project files to server
echo 7. Run: docker-compose -f docker-compose.production.yml up -d
echo 8. Configure Nginx proxy
echo 9. Test the application
echo.

echo ===============================================
echo   🌐 Expected URLs After Deployment
echo ===============================================
echo.
echo 🔗 Main Application: http://glass-factory-demo.ddns.net
echo 🔗 Sales Module: http://glass-factory-demo.ddns.net/sales
echo 🔗 API Documentation: http://glass-factory-demo.ddns.net/swagger
echo 🔗 Health Check: http://glass-factory-demo.ddns.net/health
echo.

echo ===============================================
echo   📊 Monitoring Commands
echo ===============================================
echo.
echo After deployment, use these commands to monitor:
echo.
echo Check containers: docker-compose -f docker-compose.production.yml ps
echo View logs: docker-compose -f docker-compose.production.yml logs -f
echo Restart services: docker-compose -f docker-compose.production.yml restart
echo Stop services: docker-compose -f docker-compose.production.yml down
echo.

echo ===============================================
echo   🔧 Troubleshooting
echo ===============================================
echo.
echo If deployment fails:
echo 1. Check Docker is running: sudo systemctl status docker
echo 2. Check ports are open: sudo ufw status
echo 3. Check logs: docker-compose logs
echo 4. Restart services: docker-compose restart
echo 5. Check disk space: df -h
echo 6. Check memory: free -h
echo.

echo ===============================================
echo   ✅ Deployment Package Ready
echo ===============================================
echo.
echo 📦 Package: deployment-package.zip
echo 📏 Size: 
dir deployment-package.zip
echo.
echo 🚀 Ready to deploy to Oracle Cloud!
echo.
echo Next steps:
echo 1. Upload deployment-package.zip to your server
echo 2. Extract and run DEPLOY_NOW.sh
echo 3. Access http://glass-factory-demo.ddns.net
echo.

pause

echo.
echo 🎯 Opening deployment guide...
start notepad DEPLOYMENT_GUIDE.md

echo.
echo 📞 Need help? Contact the developer:
echo 👤 Developer: حسام محمد حسان أحمد
echo 📧 Email: <EMAIL>
echo.

pause
