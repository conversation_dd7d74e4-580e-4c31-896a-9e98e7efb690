﻿#pragma checksum "..\..\..\..\Views\ExpensesMainView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7F0D9FEF21FF5A82ACCDC960FE00F61200C292A8"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ExpensesMainView
    /// </summary>
    public partial class ExpensesMainView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 99 "..\..\..\..\Views\ExpensesMainView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExpenseManagement;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\Views\ExpensesMainView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExpenseRecord;
        
        #line default
        #line hidden
        
        
        #line 135 "..\..\..\..\Views\ExpensesMainView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExpenseReport;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\ExpensesMainView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnProjectExpenseReport;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/expensesmainview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ExpensesMainView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnExpenseManagement = ((System.Windows.Controls.Button)(target));
            
            #line 102 "..\..\..\..\Views\ExpensesMainView.xaml"
            this.BtnExpenseManagement.Click += new System.Windows.RoutedEventHandler(this.BtnExpenseManagement_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnExpenseRecord = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\..\Views\ExpensesMainView.xaml"
            this.BtnExpenseRecord.Click += new System.Windows.RoutedEventHandler(this.BtnExpenseRecord_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnExpenseReport = ((System.Windows.Controls.Button)(target));
            
            #line 138 "..\..\..\..\Views\ExpensesMainView.xaml"
            this.BtnExpenseReport.Click += new System.Windows.RoutedEventHandler(this.BtnExpenseReport_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnProjectExpenseReport = ((System.Windows.Controls.Button)(target));
            
            #line 152 "..\..\..\..\Views\ExpensesMainView.xaml"
            this.BtnProjectExpenseReport.Click += new System.Windows.RoutedEventHandler(this.BtnProjectExpenseReport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

