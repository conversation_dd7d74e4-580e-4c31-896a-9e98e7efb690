using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class ExpenseReportView : UserControl
    {
        private readonly ExpenseService _expenseService;
        private List<ExpenseRecord> _allExpenseRecords;
        private List<ExpenseRecord> _filteredExpenseRecords;

        public ExpenseReportView()
        {
            InitializeComponent();
            _expenseService = new ExpenseService();
            _allExpenseRecords = new List<ExpenseRecord>();
            _filteredExpenseRecords = new List<ExpenseRecord>();
            LoadData();
            InitializeFilters();
        }

        private void LoadData()
        {
            try
            {
                _allExpenseRecords = _expenseService.GetRegularExpenseRecords();
                _filteredExpenseRecords = _allExpenseRecords.ToList();
                DgExpenseRecords.ItemsSource = _filteredExpenseRecords;
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeFilters()
        {
            try
            {
                // تحميل أنواع المصروفات للفلتر
                var mainExpenses = _expenseService.GetDistinctMainExpenseNames();
                mainExpenses.Insert(0, "الكل");
                CmbExpenseTypeFilter.ItemsSource = mainExpenses;
                CmbExpenseTypeFilter.SelectedIndex = 0;

                // تحميل الفروع للفلتر
                var branches = _expenseService.GetAllCompanyBranches();
                var branchNames = branches.Select(b => b.Name).ToList();
                branchNames.Insert(0, "الكل");
                CmbBranchFilter.ItemsSource = branchNames;
                CmbBranchFilter.SelectedIndex = 0;

                // تحميل المسؤولين للفلتر
                var responsiblePersons = _expenseService.GetAllResponsiblePersons();
                var personNames = responsiblePersons.Select(p => p.Name).ToList();
                personNames.Insert(0, "الكل");
                CmbResponsiblePersonFilter.ItemsSource = personNames;
                CmbResponsiblePersonFilter.SelectedIndex = 0;

                // تحميل طرق السداد للفلتر
                var paymentMethods = new List<string> { "الكل", "كاش", "بنك", "مستحق الدفع" };
                CmbPaymentMethodFilter.ItemsSource = paymentMethods;
                CmbPaymentMethodFilter.SelectedIndex = 0;

                // تحميل حالات المصروف للفلتر
                var expenseStatuses = new List<string> { "الكل", "مسدد", "مستحق" };
                CmbExpenseStatusFilter.ItemsSource = expenseStatuses;
                CmbExpenseStatusFilter.SelectedIndex = 0;

                // تعيين التواريخ الافتراضية
                DpFromDate.SelectedDate = DateTime.Now.AddMonths(-1);
                DpToDate.SelectedDate = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الفلاتر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            if (_filteredExpenseRecords == null || !_filteredExpenseRecords.Any())
            {
                TxtTotalAmount.Text = "0.00";
                TxtTotalCount.Text = "0";
                TxtAverageAmount.Text = "0.00";
                TxtMaxAmount.Text = "0.00";
                return;
            }

            var totalAmount = _filteredExpenseRecords.Sum(e => e.Amount);
            var count = _filteredExpenseRecords.Count;
            var averageAmount = totalAmount / count;
            var maxAmount = _filteredExpenseRecords.Max(e => e.Amount);

            TxtTotalAmount.Text = totalAmount.ToString("N2");
            TxtTotalCount.Text = count.ToString();
            TxtAverageAmount.Text = averageAmount.ToString("N2");
            TxtMaxAmount.Text = maxAmount.ToString("N2");
        }

        private void BtnFilter_Click(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                _filteredExpenseRecords = _allExpenseRecords.ToList();

                // فلتر التاريخ
                if (DpFromDate.SelectedDate.HasValue)
                {
                    _filteredExpenseRecords = _filteredExpenseRecords
                        .Where(e => e.DateTime.Date >= DpFromDate.SelectedDate.Value.Date)
                        .ToList();
                }

                if (DpToDate.SelectedDate.HasValue)
                {
                    _filteredExpenseRecords = _filteredExpenseRecords
                        .Where(e => e.DateTime.Date <= DpToDate.SelectedDate.Value.Date)
                        .ToList();
                }

                // فلتر نوع المصروف
                if (CmbExpenseTypeFilter.SelectedItem != null && 
                    CmbExpenseTypeFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedType = CmbExpenseTypeFilter.SelectedItem.ToString();
                    _filteredExpenseRecords = _filteredExpenseRecords
                        .Where(e => e.MainExpenseName == selectedType)
                        .ToList();
                }

                // فلتر الفرع
                if (CmbBranchFilter.SelectedItem != null &&
                    CmbBranchFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedBranch = CmbBranchFilter.SelectedItem.ToString();
                    _filteredExpenseRecords = _filteredExpenseRecords
                        .Where(e => e.BranchName == selectedBranch)
                        .ToList();
                }

                // فلتر المسؤول
                if (CmbResponsiblePersonFilter.SelectedItem != null &&
                    CmbResponsiblePersonFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedPerson = CmbResponsiblePersonFilter.SelectedItem.ToString();
                    _filteredExpenseRecords = _filteredExpenseRecords
                        .Where(e => e.ResponsiblePersonName == selectedPerson)
                        .ToList();
                }

                // فلتر طريقة السداد
                if (CmbPaymentMethodFilter.SelectedItem != null &&
                    CmbPaymentMethodFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedPaymentMethod = CmbPaymentMethodFilter.SelectedItem.ToString();
                    _filteredExpenseRecords = _filteredExpenseRecords
                        .Where(e => e.PaymentMethod == selectedPaymentMethod)
                        .ToList();
                }

                // فلتر حالة المصروف
                if (CmbExpenseStatusFilter.SelectedItem != null &&
                    CmbExpenseStatusFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedStatus = CmbExpenseStatusFilter.SelectedItem.ToString();
                    _filteredExpenseRecords = _filteredExpenseRecords
                        .Where(e => e.ExpenseStatus == selectedStatus)
                        .ToList();
                }

                DgExpenseRecords.ItemsSource = _filteredExpenseRecords;
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClearFilter_Click(object sender, RoutedEventArgs e)
        {
            ClearFilters();
        }

        private void ClearFilters()
        {
            DpFromDate.SelectedDate = null;
            DpToDate.SelectedDate = null;
            CmbExpenseTypeFilter.SelectedIndex = 0;
            CmbBranchFilter.SelectedIndex = 0;
            CmbResponsiblePersonFilter.SelectedIndex = 0;
            CmbPaymentMethodFilter.SelectedIndex = 0;
            CmbExpenseStatusFilter.SelectedIndex = 0;

            _filteredExpenseRecords = _allExpenseRecords.ToList();
            DgExpenseRecords.ItemsSource = _filteredExpenseRecords;
            UpdateStatistics();
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseRecord record)
            {
                try
                {
                    var editWindow = new EditExpenseRecordWindow(record, _expenseService);
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadData(); // إعادة تحميل البيانات بعد التعديل
                        MessageBox.Show("تم تحديث المصروف بنجاح!", "نجح التحديث",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseRecord record)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف المصروف: {record.MainExpenseName} - {record.SubExpenseName}؟\nالمبلغ: {record.Amount:N2}", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = _expenseService.DeleteExpenseRecord(record.Id);
                        
                        if (deleteResult)
                        {
                            MessageBox.Show("تم حذف المصروف بنجاح!", "نجح الحذف", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadData(); // إعادة تحميل البيانات
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المصروف!", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المصروف: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }

    // نافذة تعديل المصروف
    public partial class EditExpenseRecordWindow : Window
    {
        private readonly ExpenseRecord _expenseRecord;
        private readonly ExpenseService _expenseService;
        private List<ExpenseCategory> _expenseCategories;
        private List<CompanyBranch> _companyBranches;
        private List<ResponsiblePerson> _responsiblePersons;

        public EditExpenseRecordWindow(ExpenseRecord expenseRecord, ExpenseService expenseService)
        {
            _expenseRecord = expenseRecord;
            _expenseService = expenseService;
            InitializeComponent();
            LoadData();
            PopulateForm();
        }

        private void InitializeComponent()
        {
            Title = "تعديل المصروف";
            Width = 500;
            Height = 600;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            FlowDirection = FlowDirection.RightToLeft;

            var scrollViewer = new ScrollViewer();
            var grid = new Grid();
            grid.Margin = new Thickness(20);

            // تعريف الصفوف
            for (int i = 0; i < 20; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            }

            int row = 0;

            // المصروف الرئيسي
            var lblMainExpense = new TextBlock { Text = "المصروف الرئيسي:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblMainExpense, row++);
            grid.Children.Add(lblMainExpense);

            var cmbMainExpense = new ComboBox { Name = "CmbMainExpense", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(cmbMainExpense, row++);
            grid.Children.Add(cmbMainExpense);

            // المصروف الفرعي
            var lblSubExpense = new TextBlock { Text = "المصروف الفرعي:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblSubExpense, row++);
            grid.Children.Add(lblSubExpense);

            var cmbSubExpense = new ComboBox { Name = "CmbSubExpense", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(cmbSubExpense, row++);
            grid.Children.Add(cmbSubExpense);

            // الفرع
            var lblBranch = new TextBlock { Text = "الفرع:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblBranch, row++);
            grid.Children.Add(lblBranch);

            var cmbBranch = new ComboBox { Name = "CmbBranch", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(cmbBranch, row++);
            grid.Children.Add(cmbBranch);

            // المسؤول
            var lblResponsiblePerson = new TextBlock { Text = "المسؤول:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblResponsiblePerson, row++);
            grid.Children.Add(lblResponsiblePerson);

            var cmbResponsiblePerson = new ComboBox { Name = "CmbResponsiblePerson", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(cmbResponsiblePerson, row++);
            grid.Children.Add(cmbResponsiblePerson);

            // المبلغ
            var lblAmount = new TextBlock { Text = "المبلغ:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblAmount, row++);
            grid.Children.Add(lblAmount);

            var txtAmount = new TextBox { Name = "TxtAmount", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(txtAmount, row++);
            grid.Children.Add(txtAmount);

            // رقم الفاتورة
            var lblInvoiceNumber = new TextBlock { Text = "رقم الفاتورة:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblInvoiceNumber, row++);
            grid.Children.Add(lblInvoiceNumber);

            var txtInvoiceNumber = new TextBox { Name = "TxtInvoiceNumber", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(txtInvoiceNumber, row++);
            grid.Children.Add(txtInvoiceNumber);

            // التاريخ
            var lblDateTime = new TextBlock { Text = "التاريخ:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblDateTime, row++);
            grid.Children.Add(lblDateTime);

            var dpDateTime = new DatePicker { Name = "DpDateTime", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(dpDateTime, row++);
            grid.Children.Add(dpDateTime);

            // طريقة السداد
            var lblPaymentMethod = new TextBlock { Text = "طريقة السداد:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblPaymentMethod, row++);
            grid.Children.Add(lblPaymentMethod);

            var cmbPaymentMethod = new ComboBox { Name = "CmbPaymentMethod", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            cmbPaymentMethod.Items.Add("كاش");
            cmbPaymentMethod.Items.Add("بنك");
            cmbPaymentMethod.Items.Add("مستحق الدفع");
            Grid.SetRow(cmbPaymentMethod, row++);
            grid.Children.Add(cmbPaymentMethod);

            // حالة المصروف
            var lblExpenseStatus = new TextBlock { Text = "حالة المصروف:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblExpenseStatus, row++);
            grid.Children.Add(lblExpenseStatus);

            var cmbExpenseStatus = new ComboBox { Name = "CmbExpenseStatus", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            cmbExpenseStatus.Items.Add("مسدد");
            cmbExpenseStatus.Items.Add("مستحق");
            Grid.SetRow(cmbExpenseStatus, row++);
            grid.Children.Add(cmbExpenseStatus);

            // الملاحظات
            var lblNotes = new TextBlock { Text = "الملاحظات:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblNotes, row++);
            grid.Children.Add(lblNotes);

            var txtNotes = new TextBox { Name = "TxtNotes", Height = 60, Margin = new Thickness(0, 0, 0, 15), TextWrapping = TextWrapping.Wrap, AcceptsReturn = true };
            Grid.SetRow(txtNotes, row++);
            grid.Children.Add(txtNotes);

            // الأزرار
            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Center };
            var btnSave = new Button { Content = "حفظ التعديلات", Width = 120, Height = 35, Margin = new Thickness(0, 0, 10, 0), Background = new SolidColorBrush(Colors.Green), Foreground = new SolidColorBrush(Colors.White) };
            var btnCancel = new Button { Content = "إلغاء", Width = 80, Height = 35, Background = new SolidColorBrush(Colors.Gray), Foreground = new SolidColorBrush(Colors.White) };

            btnSave.Click += BtnSave_Click;
            btnCancel.Click += (s, e) => { DialogResult = false; Close(); };

            buttonPanel.Children.Add(btnSave);
            buttonPanel.Children.Add(btnCancel);
            Grid.SetRow(buttonPanel, row++);
            grid.Children.Add(buttonPanel);

            scrollViewer.Content = grid;
            Content = scrollViewer;
        }

        private void LoadData()
        {
            try
            {
                _expenseCategories = _expenseService.GetAllExpenseCategories();
                _companyBranches = _expenseService.GetAllCompanyBranches();
                _responsiblePersons = _expenseService.GetAllResponsiblePersons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PopulateForm()
        {
            try
            {
                var cmbMainExpense = FindName("CmbMainExpense") as ComboBox;
                var cmbSubExpense = FindName("CmbSubExpense") as ComboBox;
                var cmbBranch = FindName("CmbBranch") as ComboBox;
                var cmbResponsiblePerson = FindName("CmbResponsiblePerson") as ComboBox;
                var txtAmount = FindName("TxtAmount") as TextBox;
                var txtInvoiceNumber = FindName("TxtInvoiceNumber") as TextBox;
                var dpDateTime = FindName("DpDateTime") as DatePicker;
                var cmbPaymentMethod = FindName("CmbPaymentMethod") as ComboBox;
                var cmbExpenseStatus = FindName("CmbExpenseStatus") as ComboBox;
                var txtNotes = FindName("TxtNotes") as TextBox;

                // تعبئة القوائم المنسدلة
                var mainExpenses = _expenseCategories.Select(ec => ec.MainExpenseName).Distinct().ToList();
                cmbMainExpense.ItemsSource = mainExpenses;
                cmbMainExpense.SelectedItem = _expenseRecord.MainExpenseName;

                var subExpenses = _expenseCategories.Where(ec => ec.MainExpenseName == _expenseRecord.MainExpenseName).ToList();
                cmbSubExpense.ItemsSource = subExpenses;
                cmbSubExpense.DisplayMemberPath = "SubExpenseName";
                cmbSubExpense.SelectedValuePath = "Id";
                cmbSubExpense.SelectedValue = _expenseRecord.SubExpenseId;

                cmbBranch.ItemsSource = _companyBranches;
                cmbBranch.DisplayMemberPath = "Name";
                cmbBranch.SelectedValuePath = "Id";
                cmbBranch.SelectedValue = _expenseRecord.BranchId;

                cmbResponsiblePerson.ItemsSource = _responsiblePersons;
                cmbResponsiblePerson.DisplayMemberPath = "Name";
                cmbResponsiblePerson.SelectedValuePath = "Id";
                cmbResponsiblePerson.SelectedValue = _expenseRecord.ResponsiblePersonId;

                // تعبئة الحقول
                txtAmount.Text = _expenseRecord.Amount.ToString();
                txtInvoiceNumber.Text = _expenseRecord.InvoiceNumber;
                dpDateTime.SelectedDate = _expenseRecord.DateTime;
                cmbPaymentMethod.SelectedItem = _expenseRecord.PaymentMethod;
                cmbExpenseStatus.SelectedItem = _expenseRecord.ExpenseStatus;
                txtNotes.Text = _expenseRecord.Notes ?? "";

                // ربط تغيير المصروف الرئيسي بالفرعي
                cmbMainExpense.SelectionChanged += (s, e) =>
                {
                    if (cmbMainExpense.SelectedItem != null)
                    {
                        var selectedMain = cmbMainExpense.SelectedItem.ToString();
                        var filteredSubs = _expenseCategories.Where(ec => ec.MainExpenseName == selectedMain).ToList();
                        cmbSubExpense.ItemsSource = filteredSubs;
                    }
                };
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعبئة النموذج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var cmbSubExpense = FindName("CmbSubExpense") as ComboBox;
                var cmbBranch = FindName("CmbBranch") as ComboBox;
                var cmbResponsiblePerson = FindName("CmbResponsiblePerson") as ComboBox;
                var txtAmount = FindName("TxtAmount") as TextBox;
                var txtInvoiceNumber = FindName("TxtInvoiceNumber") as TextBox;
                var dpDateTime = FindName("DpDateTime") as DatePicker;
                var cmbPaymentMethod = FindName("CmbPaymentMethod") as ComboBox;
                var cmbExpenseStatus = FindName("CmbExpenseStatus") as ComboBox;
                var txtNotes = FindName("TxtNotes") as TextBox;

                // التحقق من صحة البيانات
                if (!decimal.TryParse(txtAmount.Text, out decimal amount) || amount <= 0)
                {
                    MessageBox.Show("يرجى إدخال مبلغ صحيح", "بيانات غير صحيحة", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (cmbSubExpense.SelectedItem == null || cmbBranch.SelectedItem == null ||
                    cmbResponsiblePerson.SelectedItem == null || dpDateTime.SelectedDate == null)
                {
                    MessageBox.Show("يرجى تعبئة جميع الحقول المطلوبة", "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تحديث بيانات المصروف
                var selectedSubExpense = cmbSubExpense.SelectedItem as ExpenseCategory;
                var selectedBranch = cmbBranch.SelectedItem as CompanyBranch;
                var selectedResponsiblePerson = cmbResponsiblePerson.SelectedItem as ResponsiblePerson;

                _expenseRecord.MainExpenseId = selectedSubExpense.Id;
                _expenseRecord.MainExpenseName = selectedSubExpense.MainExpenseName;
                _expenseRecord.SubExpenseId = selectedSubExpense.Id;
                _expenseRecord.SubExpenseName = selectedSubExpense.SubExpenseName;
                _expenseRecord.BranchId = selectedBranch.Id;
                _expenseRecord.BranchName = selectedBranch.Name;
                _expenseRecord.ResponsiblePersonId = selectedResponsiblePerson.Id;
                _expenseRecord.ResponsiblePersonName = selectedResponsiblePerson.Name;
                _expenseRecord.Amount = amount;
                _expenseRecord.InvoiceNumber = txtInvoiceNumber.Text.Trim();
                _expenseRecord.DateTime = dpDateTime.SelectedDate.Value;
                _expenseRecord.PaymentMethod = cmbPaymentMethod.SelectedItem?.ToString() ?? "";
                _expenseRecord.ExpenseStatus = cmbExpenseStatus.SelectedItem?.ToString() ?? "";
                _expenseRecord.Notes = string.IsNullOrWhiteSpace(txtNotes.Text) ? null : txtNotes.Text.Trim();
                _expenseRecord.ModifiedBy = "المستخدم الحالي";

                // حفظ التعديلات
                var result = _expenseService.UpdateExpenseRecord(_expenseRecord);
                if (result)
                {
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التعديلات!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
