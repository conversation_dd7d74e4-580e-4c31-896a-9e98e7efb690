﻿#pragma checksum "..\..\..\..\Views\ReportsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "55E68FA264CD7513B528D3E6FF6C2B79010BC846"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ReportsView
    /// </summary>
    public partial class ReportsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 83 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshReports;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalSales;
        
        #line default
        #line hidden
        
        
        #line 110 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalInvoices;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalCustomers;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLastInvoiceDate;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSalesReport;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManufacturingReport;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnServicesReport;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCustomersReport;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCustomReport;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\..\Views\ReportsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddNewReport;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/reportsview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ReportsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnRefreshReports = ((System.Windows.Controls.Button)(target));
            
            #line 86 "..\..\..\..\Views\ReportsView.xaml"
            this.BtnRefreshReports.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshReports_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtTotalSales = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtTotalInvoices = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TxtTotalCustomers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.TxtLastInvoiceDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.BtnSalesReport = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\..\Views\ReportsView.xaml"
            this.BtnSalesReport.Click += new System.Windows.RoutedEventHandler(this.BtnSalesReport_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnManufacturingReport = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\Views\ReportsView.xaml"
            this.BtnManufacturingReport.Click += new System.Windows.RoutedEventHandler(this.BtnManufacturingReport_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnServicesReport = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\..\Views\ReportsView.xaml"
            this.BtnServicesReport.Click += new System.Windows.RoutedEventHandler(this.BtnServicesReport_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnCustomersReport = ((System.Windows.Controls.Button)(target));
            
            #line 182 "..\..\..\..\Views\ReportsView.xaml"
            this.BtnCustomersReport.Click += new System.Windows.RoutedEventHandler(this.BtnCustomersReport_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnCustomReport = ((System.Windows.Controls.Button)(target));
            
            #line 194 "..\..\..\..\Views\ReportsView.xaml"
            this.BtnCustomReport.Click += new System.Windows.RoutedEventHandler(this.BtnCustomReport_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnAddNewReport = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\..\Views\ReportsView.xaml"
            this.BtnAddNewReport.Click += new System.Windows.RoutedEventHandler(this.BtnAddNewReport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

