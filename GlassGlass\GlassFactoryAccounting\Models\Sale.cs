using System.Collections.ObjectModel;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج المبيعات
    /// </summary>
    public class Sale : BaseEntity
    {
        private string _invoiceNumber = string.Empty;
        private DateTime _saleDate;
        private int _customerId;
        private Customer? _customer;
        private decimal _totalAmount;
        private decimal _discount;
        private decimal _tax;
        private decimal _netAmount;
        private PaymentStatus _paymentStatus;
        private string _notes = string.Empty;
        private ObservableCollection<SaleItem> _saleItems;
        private string _servicesText = string.Empty;

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetProperty(ref _invoiceNumber, value);
        }

        public DateTime SaleDate
        {
            get => _saleDate;
            set => SetProperty(ref _saleDate, value);
        }

        public int CustomerId
        {
            get => _customerId;
            set => SetProperty(ref _customerId, value);
        }

        public Customer? Customer
        {
            get => _customer;
            set => SetProperty(ref _customer, value);
        }

        public decimal TotalAmount
        {
            get => _totalAmount;
            set => SetProperty(ref _totalAmount, value);
        }

        public decimal Discount
        {
            get => _discount;
            set => SetProperty(ref _discount, value);
        }

        public decimal Tax
        {
            get => _tax;
            set => SetProperty(ref _tax, value);
        }

        public decimal NetAmount
        {
            get => _netAmount;
            set => SetProperty(ref _netAmount, value);
        }

        public PaymentStatus PaymentStatus
        {
            get => _paymentStatus;
            set => SetProperty(ref _paymentStatus, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public ObservableCollection<SaleItem> SaleItems
        {
            get => _saleItems;
            set => SetProperty(ref _saleItems, value);
        }

        public string ServicesText
        {
            get => _servicesText;
            set => SetProperty(ref _servicesText, value);
        }

        public Sale()
        {
            _saleItems = new ObservableCollection<SaleItem>();
            SaleDate = DateTime.Now;
        }
    }

    /// <summary>
    /// عناصر المبيعات
    /// </summary>
    public class SaleItem : BaseEntity
    {
        private int _saleId;
        private string _service = string.Empty;
        private string _glassType = string.Empty;
        private decimal _glassThickness;
        private string _details = string.Empty;
        private decimal _length;
        private decimal _width;
        private decimal _area;
        private int _count;
        private decimal _totalArea;
        private decimal _unitPrice;
        private decimal _totalPrice;
        private bool _isManualRow;

        public int SaleId
        {
            get => _saleId;
            set => SetProperty(ref _saleId, value);
        }

        public string Service
        {
            get => _service;
            set => SetProperty(ref _service, value);
        }

        public string GlassType
        {
            get => _glassType;
            set => SetProperty(ref _glassType, value);
        }

        public decimal GlassThickness
        {
            get => _glassThickness;
            set => SetProperty(ref _glassThickness, value);
        }

        public string Details
        {
            get => _details;
            set => SetProperty(ref _details, value);
        }

        public decimal Length
        {
            get => _length;
            set
            {
                SetProperty(ref _length, value);
                if (!IsManualRow) CalculateArea();
            }
        }

        public decimal Width
        {
            get => _width;
            set
            {
                SetProperty(ref _width, value);
                if (!IsManualRow) CalculateArea();
            }
        }

        public decimal Area
        {
            get => _area;
            set => SetProperty(ref _area, value);
        }

        public int Count
        {
            get => _count;
            set
            {
                SetProperty(ref _count, value);
                if (!IsManualRow) CalculateTotalArea();
            }
        }

        public decimal TotalArea
        {
            get => _totalArea;
            set
            {
                SetProperty(ref _totalArea, value);
                CalculateTotalPrice();
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                SetProperty(ref _unitPrice, value);
                CalculateTotalPrice();
            }
        }

        public decimal TotalPrice
        {
            get => _totalPrice;
            set => SetProperty(ref _totalPrice, value);
        }

        public bool IsManualRow
        {
            get => _isManualRow;
            set => SetProperty(ref _isManualRow, value);
        }

        /// <summary>
        /// حساب المساحة = (الطول × العرض) ÷ 1,000,000
        /// </summary>
        public void CalculateArea()
        {
            if (!IsManualRow && Length > 0 && Width > 0)
            {
                Area = (Length * Width) / 1000000;
                CalculateTotalArea();
            }
        }

        /// <summary>
        /// حساب إجمالي المساحة = المساحة × العدد
        /// </summary>
        public void CalculateTotalArea()
        {
            if (!IsManualRow)
            {
                TotalArea = Area * Count;
            }
            // في الصف اليدوي، يتم إدخال TotalArea يدوياً
        }

        /// <summary>
        /// حساب إجمالي القيمة = السعر × إجمالي المساحة
        /// </summary>
        public void CalculateTotalPrice()
        {
            // هذه المعادلة تعمل في كلا النوعين (عادي ويدوي)
            TotalPrice = UnitPrice * TotalArea;
        }
    }

    /// <summary>
    /// نموذج الخدمات المتاحة
    /// </summary>
    public class Service : BaseEntity
    {
        private string _name = string.Empty;
        private string _description = string.Empty;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }
    }

    /// <summary>
    /// نموذج سجل الخدمة المفصل
    /// </summary>
    public class ServiceRecord : BaseEntity
    {
        private string _invoiceNumber = string.Empty;
        private DateTime _saleDate;
        private int _customerId;
        private string _customerName = string.Empty;
        private string _serviceName = string.Empty;
        private string _glassType = string.Empty;
        private decimal _glassThickness;
        private string _details = string.Empty;
        private decimal _length;
        private decimal _width;
        private decimal _area;
        private int _count;
        private decimal _totalArea;
        private decimal _unitPrice;
        private decimal _totalPrice;
        private bool _isManualRow;
        private DateTime _createdDate;
        private DateTime _modifiedDate;
        private bool _isActive;

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetProperty(ref _invoiceNumber, value);
        }

        public DateTime SaleDate
        {
            get => _saleDate;
            set => SetProperty(ref _saleDate, value);
        }

        public int CustomerId
        {
            get => _customerId;
            set => SetProperty(ref _customerId, value);
        }

        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }

        public string ServiceName
        {
            get => _serviceName;
            set => SetProperty(ref _serviceName, value);
        }

        public string GlassType
        {
            get => _glassType;
            set => SetProperty(ref _glassType, value);
        }

        public decimal GlassThickness
        {
            get => _glassThickness;
            set => SetProperty(ref _glassThickness, value);
        }

        public string Details
        {
            get => _details;
            set => SetProperty(ref _details, value);
        }

        public decimal Length
        {
            get => _length;
            set => SetProperty(ref _length, value);
        }

        public decimal Width
        {
            get => _width;
            set => SetProperty(ref _width, value);
        }

        public decimal Area
        {
            get => _area;
            set => SetProperty(ref _area, value);
        }

        public int Count
        {
            get => _count;
            set => SetProperty(ref _count, value);
        }

        public decimal TotalArea
        {
            get => _totalArea;
            set => SetProperty(ref _totalArea, value);
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set => SetProperty(ref _unitPrice, value);
        }

        public decimal TotalPrice
        {
            get => _totalPrice;
            set => SetProperty(ref _totalPrice, value);
        }

        public bool IsManualRow
        {
            get => _isManualRow;
            set => SetProperty(ref _isManualRow, value);
        }

        public new DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }

        public new DateTime ModifiedDate
        {
            get => _modifiedDate;
            set => SetProperty(ref _modifiedDate, value);
        }

        public new bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        public ServiceRecord()
        {
            _saleDate = DateTime.Now;
            _createdDate = DateTime.Now;
            _modifiedDate = DateTime.Now;
            _isActive = true;
        }
    }

    /// <summary>
    /// حالة الدفع
    /// </summary>
    public enum PaymentStatus
    {
        مدفوع = 1,
        غير_مدفوع = 2,
        مدفوع_جزئيا = 3,
        ملغي = 4
    }
}
