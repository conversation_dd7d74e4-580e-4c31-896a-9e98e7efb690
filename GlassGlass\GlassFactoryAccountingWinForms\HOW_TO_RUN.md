# 🚀 How to Run the Glass Factory Accounting System

## 📋 Project Information
- **Name:** Glass Factory Accounting System - Windows Forms Edition
- **Owner:** <PERSON><PERSON><PERSON>
- **Framework:** .NET Framework 4.7.2
- **Technology:** Windows Forms + SQLite
- **Language:** C# with Arabic RTL Support

---

## 🔧 Prerequisites

### Required Software:
1. **Windows 7** or later
2. **.NET Framework 4.7.2** or later
3. **Visual Studio 2019** or later (for building)

### Optional (for enhanced UI):
- **Bunifu UI WinForms** components

---

## 🚀 How to Run

### Method 1: Using Visual Studio (Recommended)
1. **Open the solution file:**
   ```
   GlassFactoryAccountingWinForms.sln
   ```

2. **Build the solution:**
   - Press `Ctrl + Shift + B`
   - Or go to `Build → Build Solution`

3. **Run the application:**
   - Press `F5`
   - Or go to `Debug → Start Debugging`

### Method 2: Using Batch Files
1. **First, build the project using Visual Studio (Method 1)**

2. **Then run one of these batch files:**
   ```
   START_HERE.bat          (Main launcher)
   RUN_PROGRAM.bat         (Alternative launcher)
   ```

---

## 🎯 What You'll See

### Main Form Features:
- 🏠 **Professional main interface** with Arabic RTL support
- 🎨 **Color-coded buttons** for easy navigation
- 📊 **Status bar** showing system statistics
- ⚡ **Fast startup** with no delays

### Chart of Accounts Features:
- 🌳 **Hierarchical account display** with multiple levels
- 📋 **Interactive data grid** with all account details
- ➕ **Add new accounts** with simple interface
- 🔍 **View account details** by double-clicking
- 📊 **Comprehensive statistics** for all account types
- 🔄 **Real-time data refresh**

---

## 📁 Project Structure

```
GlassFactoryAccountingWinForms/
├── 📄 GlassFactoryAccountingWinForms.sln     # Solution file
├── 📄 GlassFactoryAccountingWinForms.csproj  # Project file
├── 📄 Program.cs                             # Entry point
├── 📁 Forms/                                 # UI Forms
│   ├── MainForm.cs                           # Main interface
│   └── AccountingForms/
│       └── ChartOfAccountsForm.cs            # Accounts management
├── 📁 Models/                                # Data models
│   ├── Account.cs                            # Account model
│   └── JournalEntry.cs                       # Journal entry model
├── 📁 Services/                              # Business logic
│   ├── DatabaseService.cs                    # Database operations
│   └── AccountingService.cs                  # Accounting operations
└── 📁 Properties/                            # Project properties
```

---

## ✅ Verification Steps

### After building and running, you should see:

1. **Main Window:**
   - Title: "🏭 نظام حسابات مصنع الزجاج"
   - Large colored buttons for navigation
   - Status bar at the bottom

2. **Chart of Accounts (click the first button):**
   - Data grid showing default accounts
   - Statistics at the bottom
   - Working "Add Account" button

3. **Database:**
   - SQLite database created automatically
   - Default accounts populated
   - Data persists between sessions

---

## 🔍 Testing the Application

### Test 1: Basic Functionality
1. Run the application
2. Click "🌳 شجرة الحسابات"
3. Verify accounts are displayed
4. Check statistics at the bottom

### Test 2: Add New Account
1. In Chart of Accounts, click "➕ إضافة حساب"
2. Enter account code (e.g., "1150")
3. Enter account name (e.g., "Current Account")
4. Verify account appears in the grid

### Test 3: View Account Details
1. Double-click any account in the grid
2. Verify details popup appears
3. Check all information is displayed correctly

---

## 🐛 Troubleshooting

### Issue: "Build failed"
**Solution:**
- Ensure .NET Framework 4.7.2 SDK is installed
- Check all files are present
- Try cleaning and rebuilding: `Build → Clean Solution` then `Build → Build Solution`

### Issue: "Database error"
**Solution:**
- Ensure the application has write permissions
- Check if Data folder exists
- Try running as administrator

### Issue: "UI not displaying correctly"
**Solution:**
- Verify .NET Framework 4.7.2 is installed
- Check Windows Forms components are available
- Try running on a different Windows version

---

## 🎉 Success Indicators

### ✅ The project is working correctly if:
1. ✅ Application starts without errors
2. ✅ Main form displays with all buttons
3. ✅ Chart of Accounts opens and shows data
4. ✅ Can add new accounts successfully
5. ✅ Database persists data between sessions
6. ✅ Arabic text displays correctly (RTL)
7. ✅ All buttons and menus respond

---

## 📞 Support

**Developer:** Hossam Mohamed Hassan Ahmed  
**Technology:** .NET Framework 4.7.2 + Windows Forms  
**Database:** SQLite  
**License:** Private Property  

---

## 🎯 Next Steps

1. **Journal Entries:** Add form for accounting transactions
2. **Reports:** Trial balance and income statement
3. **UI Enhancements:** Integrate Bunifu UI components
4. **Integration:** Connect with sales and inventory modules

---

**🎉 The system is ready for use and development!**
