<UserControl x:Class="GlassFactoryAccounting.Views.EmployeeManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#2E86AB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" 
                                BorderThickness="{TemplateBinding BorderThickness}"
                                BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1F5F8B"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}" 
                                BorderThickness="{TemplateBinding BorderThickness}" 
                                CornerRadius="4">
                            <ScrollViewer x:Name="PART_ContentHost" 
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Padding="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#2E86AB"/>
                                <Setter Property="BorderThickness" Value="2"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط موحد لحقول الإدخال (TextBox, ComboBox, DatePicker) -->
        <Style x:Key="UnifiedInputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Background="#F8F9FA">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="#2E86AB" Padding="20,15" Margin="0,0,0,20">
            <TextBlock Text="👥 إدارة الموظفين" 
                       FontSize="24" 
                       FontWeight="Bold" 
                       Foreground="White" 
                       HorizontalAlignment="Center"/>
        </Border>

        <!-- نموذج إضافة موظف جديد -->
        <Border Grid.Row="1" Background="White" Padding="20" Margin="20,0,20,20" CornerRadius="8">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>

            <StackPanel>
                <TextBlock Text="إضافة موظف جديد" FontSize="18" FontWeight="Bold" Margin="0,0,0,10" Foreground="#2E86AB"/>
                <TextBlock Text="🔢 رقم الموظف يتم توليده تلقائياً عند الحفظ" FontSize="12" Foreground="#6C757D" Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- الصف الأول -->
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="اسم الموظف:" FontWeight="SemiBold" Margin="5"/>
                        <TextBox x:Name="TxtEmployeeName" Style="{StaticResource ModernTextBox}" />
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="10,0,10,0">
                        <TextBlock Text="الفرع:" FontWeight="SemiBold" Margin="5"/>
                        <ComboBox x:Name="CmbBranch" Height="35" DisplayMemberPath="Name" SelectedValuePath="Id"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Margin="10,0,0,0">
                        <TextBlock Text="المسمى الوظيفي:" FontWeight="SemiBold" Margin="5"/>
                        <TextBox x:Name="TxtJobTitle" Style="{StaticResource ModernTextBox}" />
                    </StackPanel>

                    <!-- الصف الثاني -->
                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="0,15,10,0">
                        <TextBlock Text="تاريخ بدء العمل:" FontWeight="SemiBold" Margin="5"/>
                        <DatePicker x:Name="DpStartDate" Height="35"/>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="10,15,10,0">
                        <TextBlock Text="بيانات التواصل (اختياري):" FontWeight="SemiBold" Margin="5"/>
                        <TextBox x:Name="TxtPhone" Style="{StaticResource ModernTextBox}" />
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="10,15,0,0">
                        <TextBlock Text="العنوان (اختياري):" FontWeight="SemiBold" Margin="5"/>
                        <TextBox x:Name="TxtAddress" Style="{StaticResource ModernTextBox}" />
                    </StackPanel>

                    <!-- أزرار العمليات -->
                    <StackPanel Grid.Row="2" Grid.ColumnSpan="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button x:Name="BtnSave" Content="💾 حفظ الموظف" Style="{StaticResource ModernButton}" Click="BtnSave_Click"/>
                        <Button x:Name="BtnClear" Content="🗑️ مسح الحقول" Style="{StaticResource ModernButton}" Background="#6C757D" Click="BtnClear_Click"/>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </Border>

        <!-- جدول الموظفين -->
        <Border Grid.Row="2" Background="White" Margin="20,0,20,20" CornerRadius="8">
            <Border.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
            </Border.Effect>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- عنوان الجدول -->
                <Border Grid.Row="0" Background="#2E86AB" Padding="15,10">
                    <TextBlock Text="📋 قائمة الموظفين" FontSize="16" FontWeight="Bold" Foreground="White"/>
                </Border>

                <!-- الجدول -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
                    <DataGrid x:Name="DgEmployees"
                              AutoGenerateColumns="False"
                              IsReadOnly="True"
                              GridLinesVisibility="Horizontal"
                              HeadersVisibility="Column"
                              Background="White"
                              AlternatingRowBackground="#F8F9FA"
                              RowHeight="40"
                              FontSize="13"
                              Margin="10">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الموظف" Binding="{Binding EmployeeNumber}" Width="100">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2E86AB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="اسم الموظف" Binding="{Binding Name}" Width="150">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2E86AB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="SemiBold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="الفرع" Binding="{Binding BranchName}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2E86AB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="المسمى الوظيفي" Binding="{Binding JobTitle}" Width="130">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2E86AB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="تاريخ بدء العمل" Binding="{Binding StartDate, StringFormat=yyyy-MM-dd}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2E86AB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="بيانات التواصل" Binding="{Binding Phone}" Width="120">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2E86AB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTemplateColumn Header="العمليات" Width="100">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#2E86AB"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="🗑️ حذف" 
                                            Background="#DC3545" 
                                            Foreground="White" 
                                            BorderThickness="0" 
                                            Padding="8,4" 
                                            Margin="2"
                                            FontSize="11"
                                            Click="BtnDelete_Click"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                    </DataGrid>
                </ScrollViewer>
            </Grid>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
