using System;
using System.Collections.Generic;
using System.Linq;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models.Accounting;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة التكامل المحاسبي - ربط جميع الموديولات بالنظام المحاسبي
    /// </summary>
    public class AccountingIntegrationService
    {
        private readonly AccountingService _accountingService;
        private readonly DatabaseContext _context;

        public AccountingIntegrationService()
        {
            _accountingService = new AccountingService();
            _context = new DatabaseContext();
        }

        #region Sales Integration - تكامل المبيعات

        /// <summary>
        /// إنشاء قيد محاسبي لفاتورة مبيعات
        /// </summary>
        public bool CreateSalesJournalEntry(int saleId, decimal totalAmount, decimal cashAmount, decimal creditAmount, string customerName, string invoiceNumber)
        {
            try
            {
                var entry = new JournalEntry
                {
                    EntryNumber = "", // سيتم توليده تلقائياً
                    EntryDate = DateTime.Now,
                    Description = $"فاتورة مبيعات رقم {invoiceNumber} - العميل: {customerName}",
                    ReferenceType = "Sales",
                    ReferenceId = saleId,
                    ReferenceNumber = invoiceNumber,
                    CreatedBy = "نظام المبيعات",
                    Details = new List<JournalEntryDetail>()
                };

                // الطرف المدين - العملاء أو النقدية
                if (cashAmount > 0)
                {
                    var cashAccount = _accountingService.GetAccountByCode("1110"); // النقدية
                    if (cashAccount != null)
                    {
                        entry.Details.Add(new JournalEntryDetail
                        {
                            AccountId = cashAccount.Id,
                            DebitAmount = cashAmount,
                            CreditAmount = 0,
                            Description = $"نقدي من فاتورة {invoiceNumber}"
                        });
                    }
                }

                if (creditAmount > 0)
                {
                    var customersAccount = _accountingService.GetAccountByCode("1120"); // العملاء
                    if (customersAccount != null)
                    {
                        entry.Details.Add(new JournalEntryDetail
                        {
                            AccountId = customersAccount.Id,
                            DebitAmount = creditAmount,
                            CreditAmount = 0,
                            Description = $"آجل من العميل {customerName}"
                        });
                    }
                }

                // الطرف الدائن - مبيعات الزجاج
                var salesAccount = _accountingService.GetAccountByCode("4100"); // مبيعات الزجاج
                if (salesAccount != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = salesAccount.Id,
                        DebitAmount = 0,
                        CreditAmount = totalAmount,
                        Description = $"مبيعات زجاج - فاتورة {invoiceNumber}"
                    });
                }

                return _accountingService.CreateJournalEntry(entry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating sales journal entry: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Payroll Integration - تكامل الرواتب

        /// <summary>
        /// إنشاء قيد محاسبي لدفع راتب
        /// </summary>
        public bool CreatePayrollJournalEntry(string employeeName, decimal grossSalary, decimal deductions, decimal netSalary, string payrollPeriod)
        {
            try
            {
                var entry = new JournalEntry
                {
                    EntryNumber = "",
                    EntryDate = DateTime.Now,
                    Description = $"راتب الموظف {employeeName} - فترة {payrollPeriod}",
                    ReferenceType = "Payroll",
                    CreatedBy = "نظام الرواتب",
                    Details = new List<JournalEntryDetail>()
                };

                // الطرف المدين - مصروف الرواتب
                var salaryExpenseAccount = _accountingService.GetAccountByCode("5210"); // الرواتب والأجور
                if (salaryExpenseAccount != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = salaryExpenseAccount.Id,
                        DebitAmount = grossSalary,
                        CreditAmount = 0,
                        Description = $"راتب {employeeName} - إجمالي"
                    });
                }

                // الطرف الدائن - النقدية (الصافي)
                var cashAccount = _accountingService.GetAccountByCode("1110"); // النقدية
                if (cashAccount != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = cashAccount.Id,
                        DebitAmount = 0,
                        CreditAmount = netSalary,
                        Description = $"صافي راتب {employeeName}"
                    });
                }

                // الطرف الدائن - الخصومات (إن وجدت)
                if (deductions > 0)
                {
                    var deductionsAccount = _accountingService.GetAccountByCode("2120"); // الرواتب المستحقة
                    if (deductionsAccount != null)
                    {
                        entry.Details.Add(new JournalEntryDetail
                        {
                            AccountId = deductionsAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = deductions,
                            Description = $"خصومات {employeeName}"
                        });
                    }
                }

                return _accountingService.CreateJournalEntry(entry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating payroll journal entry: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Expenses Integration - تكامل المصروفات

        /// <summary>
        /// إنشاء قيد محاسبي لمصروف
        /// </summary>
        public bool CreateExpenseJournalEntry(string expenseType, decimal amount, string description, string paymentMethod)
        {
            try
            {
                var entry = new JournalEntry
                {
                    EntryNumber = "",
                    EntryDate = DateTime.Now,
                    Description = $"مصروف {expenseType} - {description}",
                    ReferenceType = "Expense",
                    CreatedBy = "نظام المصروفات",
                    Details = new List<JournalEntryDetail>()
                };

                // الطرف المدين - المصروفات الإدارية
                var expenseAccount = _accountingService.GetAccountByCode("5220"); // مصروفات إدارية
                if (expenseAccount != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = expenseAccount.Id,
                        DebitAmount = amount,
                        CreditAmount = 0,
                        Description = description
                    });
                }

                // الطرف الدائن - النقدية
                var cashAccount = _accountingService.GetAccountByCode("1110"); // النقدية
                if (cashAccount != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = cashAccount.Id,
                        DebitAmount = 0,
                        CreditAmount = amount,
                        Description = $"دفع {paymentMethod}"
                    });
                }

                return _accountingService.CreateJournalEntry(entry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating expense journal entry: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Manufacturing Integration - تكامل التصنيع

        /// <summary>
        /// إنشاء قيد محاسبي لتكاليف التصنيع
        /// </summary>
        public bool CreateManufacturingJournalEntry(string orderNumber, decimal materialCost, decimal laborCost, decimal overheadCost)
        {
            try
            {
                var totalCost = materialCost + laborCost + overheadCost;

                var entry = new JournalEntry
                {
                    EntryNumber = "",
                    EntryDate = DateTime.Now,
                    Description = $"تكاليف تصنيع أمر رقم {orderNumber}",
                    ReferenceType = "Manufacturing",
                    ReferenceNumber = orderNumber,
                    CreatedBy = "نظام التصنيع",
                    Details = new List<JournalEntryDetail>()
                };

                // الطرف المدين - تكلفة البضاعة المباعة
                var cogsAccount = _accountingService.GetAccountByCode("5100"); // تكلفة البضاعة المباعة
                if (cogsAccount != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = cogsAccount.Id,
                        DebitAmount = totalCost,
                        CreditAmount = 0,
                        Description = $"تكاليف تصنيع أمر {orderNumber}"
                    });
                }

                // الطرف الدائن - المخزون
                var inventoryAccount = _accountingService.GetAccountByCode("1130"); // المخزون
                if (inventoryAccount != null)
                {
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = inventoryAccount.Id,
                        DebitAmount = 0,
                        CreditAmount = totalCost,
                        Description = $"استهلاك مواد أمر {orderNumber}"
                    });
                }

                return _accountingService.CreateJournalEntry(entry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating manufacturing journal entry: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Inventory Integration - تكامل المخزون

        /// <summary>
        /// إنشاء قيد محاسبي لحركة مخزون
        /// </summary>
        public bool CreateInventoryJournalEntry(string movementType, decimal amount, string itemName, string description)
        {
            try
            {
                var entry = new JournalEntry
                {
                    EntryNumber = "",
                    EntryDate = DateTime.Now,
                    Description = $"حركة مخزون {movementType} - {itemName}",
                    ReferenceType = "Inventory",
                    CreatedBy = "نظام المخزون",
                    Details = new List<JournalEntryDetail>()
                };

                var inventoryAccount = _accountingService.GetAccountByCode("1130"); // المخزون

                if (movementType == "إدخال" || movementType == "شراء")
                {
                    // إدخال للمخزون
                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = inventoryAccount.Id,
                        DebitAmount = amount,
                        CreditAmount = 0,
                        Description = description
                    });

                    // الطرف الدائن - الموردين أو النقدية
                    var suppliersAccount = _accountingService.GetAccountByCode("2110"); // الموردين
                    if (suppliersAccount != null)
                    {
                        entry.Details.Add(new JournalEntryDetail
                        {
                            AccountId = suppliersAccount.Id,
                            DebitAmount = 0,
                            CreditAmount = amount,
                            Description = description
                        });
                    }
                }
                else if (movementType == "إخراج" || movementType == "استهلاك")
                {
                    // إخراج من المخزون
                    var cogsAccount = _accountingService.GetAccountByCode("5100"); // تكلفة البضاعة المباعة
                    if (cogsAccount != null)
                    {
                        entry.Details.Add(new JournalEntryDetail
                        {
                            AccountId = cogsAccount.Id,
                            DebitAmount = amount,
                            CreditAmount = 0,
                            Description = description
                        });
                    }

                    entry.Details.Add(new JournalEntryDetail
                    {
                        AccountId = inventoryAccount.Id,
                        DebitAmount = 0,
                        CreditAmount = amount,
                        Description = description
                    });
                }

                return _accountingService.CreateJournalEntry(entry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating inventory journal entry: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
