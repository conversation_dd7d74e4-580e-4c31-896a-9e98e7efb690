<UserControl x:Class="GlassFactoryAccounting.Views.DeliveryOrderView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             Background="White">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان -->
            <Border Grid.Row="0" Background="#6F42C1" CornerRadius="10" Padding="15" Margin="0,0,0,20">
                <TextBlock Text="📋 أمر تسليم جديد" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White" HorizontalAlignment="Center"/>
            </Border>

            <!-- البيانات الأساسية -->
            <GroupBox Grid.Row="1" Header="📋 بيانات أمر التسليم" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الصف الأول -->
                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                        <TextBlock Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtCustomerName" IsReadOnly="True" Background="#F0F0F0" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                        <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtInvoiceNumber" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                        <TextBlock Text="رقم أمر العمل:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtWorkOrderNumber" IsReadOnly="True" Background="#F0F0F0" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="5">
                        <TextBlock Text="رقم أمر التسليم:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtDeliveryOrderNumber" IsReadOnly="True" Background="#F0F0F0" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <!-- الصف الثاني -->
                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                        <TextBlock Text="اسم المشروع:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtProjectName" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                        <TextBlock Text="تاريخ التسليم:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="dpDeliveryDate" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                        <TextBlock Text="مسؤول التسليم:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtDeliveryResponsible" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="3" Margin="5">
                        <TextBlock Text="توقيع المستلم:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtReceiverSignature" Padding="8" FontSize="14"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- جدول المقاسات المسلمة -->
            <GroupBox Grid.Row="2" Header="📏 المقاسات التي تم تسليمها" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <Button x:Name="btnAddDeliveredSize" Content="➕ إضافة مقاس مسلم" 
                           Background="#28A745" Foreground="White" Padding="10,5" 
                           FontSize="14" FontWeight="Bold" Margin="0,0,0,10"
                           Click="BtnAddDeliveredSize_Click"/>
                    
                    <DataGrid x:Name="dgDeliveredSizes" AutoGenerateColumns="False" 
                             CanUserAddRows="False" CanUserDeleteRows="True"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="200">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Ref كود" Binding="{Binding RefCode}" Width="80"/>
                            <DataGridComboBoxColumn Header="نوع الزجاج" SelectedItemBinding="{Binding GlassType}" Width="120"/>
                            <DataGridComboBoxColumn Header="السمك" SelectedItemBinding="{Binding Thickness}" Width="80"/>
                            <DataGridTextColumn Header="الطول" Binding="{Binding Length}" Width="100"/>
                            <DataGridTextColumn Header="العرض" Binding="{Binding Width}" Width="100"/>
                            <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </StackPanel>
            </GroupBox>

            <!-- ملخص التسليم -->
            <GroupBox Grid.Row="3" Header="📊 ملخص التسليم" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي عدد القطع:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtTotalPieces" Text="0 قطعة" FontSize="14" Margin="5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="إجمالي المساحة:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtTotalSquareMeters" Text="0.00 م²" FontSize="14" Margin="5"/>
                </Grid>
            </GroupBox>

            <!-- التوقيعات -->
            <GroupBox Grid.Row="4" Header="✍️ التوقيعات" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="10">
                        <TextBlock Text="توقيع مسؤول التسليم" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <Border BorderBrush="Gray" BorderThickness="1" Height="80" Background="#F8F9FA">
                            <TextBlock Text="المساحة المخصصة للتوقيع" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="Gray"/>
                        </Border>
                        <TextBlock x:Name="txtDeliveryResponsibleDisplay" Text="اسم مسؤول التسليم" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="10">
                        <TextBlock Text="توقيع المستلم" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                        <Border BorderBrush="Gray" BorderThickness="1" Height="80" Background="#F8F9FA">
                            <TextBlock Text="المساحة المخصصة للتوقيع" VerticalAlignment="Center" HorizontalAlignment="Center" Foreground="Gray"/>
                        </Border>
                        <TextBlock x:Name="txtReceiverSignatureDisplay" Text="اسم المستلم" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- الأزرار الرئيسية -->
            <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button x:Name="btnSaveDeliveryOrder" Content="💾 حفظ أمر التسليم" 
                       Background="#28A745" Foreground="White" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnSaveDeliveryOrder_Click"/>
                
                <Button x:Name="btnCreateManualDelivery" Content="➕ إنشاء أمر تسليم يدوي" 
                       Background="#17A2B8" Foreground="White" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnCreateManualDelivery_Click"/>
                
                <Button x:Name="btnPrintDeliveryOrder" Content="🖨️ طباعة أمر التسليم" 
                       Background="#FFC107" Foreground="Black" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnPrintDeliveryOrder_Click"/>
                
                <Button x:Name="btnViewDeliveredSizes" Content="📋 عرض المقاسات المسلمة" 
                       Background="#6F42C1" Foreground="White" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnViewDeliveredSizes_Click"/>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</UserControl>
