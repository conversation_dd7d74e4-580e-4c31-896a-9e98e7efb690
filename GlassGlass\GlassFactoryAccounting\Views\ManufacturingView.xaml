<UserControl x:Class="GlassFactoryAccounting.Views.ManufacturingView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900"
             FlowDirection="RightToLeft">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- زر إظهار/إخفاء أدوات التنقل -->
        <Border Grid.Row="0" Background="#F8F9FA" CornerRadius="8" Padding="10" Margin="10,10,10,5">
            <Grid>
                <TextBlock Text="🏭 موديول التصنيع المتكامل"
                          FontSize="18" FontWeight="Bold"
                          Foreground="#495057" VerticalAlignment="Center"/>

                <Button x:Name="btnToggleNavigationTools"
                       Content="✅ إظهار/إخفاء أدوات التنقل"
                       Background="#6C757D" Foreground="White"
                       FontSize="12" FontWeight="Bold"
                       Padding="12,6" HorizontalAlignment="Left" Margin="300,0,0,0"
                       BorderThickness="0" Cursor="Hand"
                       ToolTip="إخفاء/إظهار شريط الأزرار وشريط الإحصائيات لتوسيع مساحة العمل"
                       Click="BtnToggleNavigationTools_Click"/>
            </Grid>
        </Border>

        <!-- الأزرار الرئيسية -->
        <Border x:Name="NavigationButtonsPanel" Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- الصف الأول -->
                <Button Grid.Row="0" Grid.Column="0" x:Name="btnNewManufacturingOrder"
                       Content="🆕 أمر تصنيع جديد"
                       Background="#28A745" Foreground="White"
                       FontSize="14" FontWeight="Bold"
                       Padding="15,10" Margin="5"
                       Click="BtnNewManufacturingOrder_Click"/>

                <Button Grid.Row="0" Grid.Column="1" x:Name="btnViewOrders"
                       Content="📋 عرض أوامر التصنيع"
                       Background="#17A2B8" Foreground="White"
                       FontSize="14" FontWeight="Bold"
                       Padding="15,10" Margin="5"
                       Click="BtnViewOrders_Click"/>

                <Button Grid.Row="0" Grid.Column="2" x:Name="btnDeliveryOrders"
                       Content="📦 أوامر التسليم"
                       Background="#6F42C1" Foreground="White"
                       FontSize="14" FontWeight="Bold"
                       Padding="15,10" Margin="5"
                       Click="BtnDeliveryOrders_Click"/>

                <!-- الصف الثاني -->
                <Button Grid.Row="1" Grid.Column="0" x:Name="btnSizeTracking"
                       Content="📏 تتبع المقاسات"
                       Background="#FFC107" Foreground="Black"
                       FontSize="14" FontWeight="Bold"
                       Padding="15,10" Margin="5"
                       Click="BtnSizeTracking_Click"/>

                <Button Grid.Row="1" Grid.Column="1" x:Name="btnManufacturingReports"
                       Content="📊 تقارير التصنيع"
                       Background="#20C997" Foreground="White"
                       FontSize="14" FontWeight="Bold"
                       Padding="15,10" Margin="5"
                       Click="BtnManufacturingReports_Click"/>

                <Button Grid.Row="1" Grid.Column="2" x:Name="btnCostAnalysis"
                       Content="💰 تحليل التكاليف"
                       Background="#DC3545" Foreground="White"
                       FontSize="14" FontWeight="Bold"
                       Padding="15,10" Margin="5"
                       Click="BtnCostAnalysis_Click"/>
            </Grid>
        </Border>
        
        <!-- إحصائيات سريعة -->
        <Border x:Name="StatisticsPanel" Grid.Row="2" Style="{StaticResource CardStyle}">
            <UniformGrid Columns="5">
                <!-- أوامر مجدولة -->
                <StackPanel Margin="10">
                    <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="مجدولة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtScheduledOrders" Text="0" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource PrimaryBrush}"/>
                </StackPanel>
                
                <!-- قيد التنفيذ -->
                <StackPanel Margin="10">
                    <TextBlock Text="⚙️" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="قيد التنفيذ" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtInProgressOrders" Text="0" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource WarningBrush}"/>
                </StackPanel>
                
                <!-- مكتملة -->
                <StackPanel Margin="10">
                    <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="مكتملة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtCompletedOrders" Text="0" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource SuccessBrush}"/>
                </StackPanel>
                
                <!-- متوقفة -->
                <StackPanel Margin="10">
                    <TextBlock Text="⏸️" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="متوقفة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtStoppedOrders" Text="0" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource DangerBrush}"/>
                </StackPanel>
                
                <!-- إجمالي التكلفة -->
                <StackPanel Margin="10">
                    <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="إجمالي التكلفة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtTotalCost" Text="0.00 ج.م" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource PrimaryBrush}"/>
                </StackPanel>
            </UniformGrid>
        </Border>
        
        <!-- منطقة المحتوى الرئيسي -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <ContentControl x:Name="MainContent">
                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                    <TextBlock Text="🏭" FontSize="64" HorizontalAlignment="Center" Margin="0,0,0,20" Opacity="0.3"/>
                    <TextBlock Text="مرحباً بك في موديول التصنيع المتكامل"
                              FontSize="24" FontWeight="Bold"
                              Foreground="#6C757D" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                    <TextBlock Text="اختر إحدى العمليات من الأزرار أعلاه للبدء"
                              FontSize="16"
                              Foreground="#6C757D" HorizontalAlignment="Center"/>

                    <!-- ميزات النظام -->
                    <Border Background="White" CornerRadius="10" Padding="20" Margin="0,30,0,0"
                           BorderBrush="#E0E0E0" BorderThickness="1">
                        <StackPanel>
                            <TextBlock Text="✨ ميزات موديول التصنيع المحدث"
                                      FontSize="18" FontWeight="Bold"
                                      Foreground="#495057" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,20,0">
                                    <TextBlock Text="🎯 إدارة شاملة للأوامر" FontSize="14" FontWeight="Bold" Margin="0,5"/>
                                    <TextBlock Text="🔧 خدمات محدثة (فيلم، دبل جلاس، شطف)" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="📏 تتبع المقاسات بكود فريد" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="💰 حساب التكاليف التلقائي المحدث" FontSize="14" Margin="0,5"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="📦 إدارة أوامر التسليم" FontSize="14" FontWeight="Bold" Margin="0,5"/>
                                    <TextBlock Text="📊 تقارير مفصلة ودقيقة" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="🖨️ طباعة PDF احترافية" FontSize="14" Margin="0,5"/>
                                    <TextBlock Text="📈 حساب هالك الزجاج والفيلم" FontSize="14" Margin="0,5"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ContentControl>
        </Border>
    </Grid>
</UserControl>
