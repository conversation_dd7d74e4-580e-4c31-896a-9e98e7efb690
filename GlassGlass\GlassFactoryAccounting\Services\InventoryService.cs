using System.IO;
using System.Text.Json;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة المخزون
    /// </summary>
    public class InventoryService
    {
        private readonly string _dataPath;
        private readonly ItemService _itemService;

        public InventoryService()
        {
            _dataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GlassFactory", "Inventory");
            _itemService = new ItemService();
            
            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }
        }

        /// <summary>
        /// حفظ حركة مخزون جديدة
        /// </summary>
        public async Task<bool> SaveMovementAsync(InventoryMovement movement)
        {
            try
            {
                if (movement.Id == 0)
                {
                    movement.Id = await GetNextIdAsync();
                }

                if (string.IsNullOrEmpty(movement.OrderNumber))
                {
                    movement.OrderNumber = await GenerateOrderNumberAsync();
                }

                var fileName = $"Movement_{movement.Id}.json";
                var filePath = Path.Combine(_dataPath, fileName);

                var json = JsonSerializer.Serialize(movement, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving movement: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب جميع حركات المخزون
        /// </summary>
        public async Task<List<InventoryMovement>> GetAllMovementsAsync()
        {
            try
            {
                var movements = new List<InventoryMovement>();
                
                if (!Directory.Exists(_dataPath))
                    return movements;

                var files = Directory.GetFiles(_dataPath, "Movement_*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var movement = JsonSerializer.Deserialize<InventoryMovement>(json);
                        if (movement != null)
                        {
                            movements.Add(movement);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error reading movement file {file}: {ex.Message}");
                    }
                }

                return movements.OrderByDescending(m => m.Date).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting movements: {ex.Message}");
                return new List<InventoryMovement>();
            }
        }

        /// <summary>
        /// حساب رصيد المخزون لجميع الأصناف
        /// </summary>
        public async Task<List<InventoryBalance>> GetInventoryBalancesAsync()
        {
            try
            {
                var balances = new List<InventoryBalance>();
                var items = await _itemService.GetAllItemsAsync();
                var movements = await GetAllMovementsAsync();

                foreach (var item in items)
                {
                    var itemMovements = movements.Where(m => m.ItemId == item.Id).ToList();
                    
                    var received = itemMovements.Where(m => m.MovementType == MovementType.استلام).ToList();
                    var issued = itemMovements.Where(m => m.MovementType == MovementType.تسليم).ToList();

                    var totalReceived = received.Sum(m => m.Quantity);
                    var totalIssued = issued.Sum(m => m.Quantity);
                    var totalReceivedValue = received.Sum(m => m.TotalValue);

                    var balance = new InventoryBalance
                    {
                        ItemId = item.Id,
                        ItemName = item.Name,
                        WarehouseName = item.WarehouseName,
                        TotalReceived = totalReceived,
                        TotalIssued = totalIssued,
                        UnitOfMeasure = item.UnitOfMeasure,
                        BoxContent = item.BoxContent,
                        Length = item.Length,
                        Width = item.Width,
                        Area = item.Area
                    };

                    // حساب العدد للأصناف ذات الأبعاد
                    if (item.HasDimensions && item.Area > 0)
                    {
                        balance.Units = balance.CurrentBalance / item.Area;
                    }
                    else
                    {
                        balance.Units = 0;
                    }

                    // حساب المتوسط المتحرك
                    if (totalReceived > 0)
                    {
                        balance.AveragePrice = totalReceivedValue / totalReceived;
                    }

                    balances.Add(balance);
                }

                return balances.Where(b => b.CurrentBalance != 0).OrderBy(b => b.ItemName).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error calculating inventory balances: {ex.Message}");
                return new List<InventoryBalance>();
            }
        }

        /// <summary>
        /// توليد رقم أمر تلقائي
        /// </summary>
        private async Task<string> GenerateOrderNumberAsync()
        {
            try
            {
                var movements = await GetAllMovementsAsync();
                var lastOrderNumber = movements
                    .Where(m => !string.IsNullOrEmpty(m.OrderNumber) && m.OrderNumber.StartsWith("ORD"))
                    .Select(m => m.OrderNumber)
                    .OrderByDescending(o => o)
                    .FirstOrDefault();

                if (lastOrderNumber != null && lastOrderNumber.Length > 3)
                {
                    var numberPart = lastOrderNumber.Substring(3);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        return $"ORD{(lastNumber + 1):D6}";
                    }
                }

                return "ORD000001";
            }
            catch
            {
                return "ORD000001";
            }
        }

        /// <summary>
        /// جلب المعرف التالي
        /// </summary>
        private async Task<int> GetNextIdAsync()
        {
            try
            {
                var movements = await GetAllMovementsAsync();
                return movements.Count > 0 ? movements.Max(m => m.Id) + 1 : 1;
            }
            catch
            {
                return 1;
            }
        }
    }
}
