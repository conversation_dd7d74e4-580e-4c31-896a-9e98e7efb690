{"ConnectionStrings": {"DefaultConnection": "Host=postgres;Database=glass_factory_db;Username=glass_factory_user;Password=*****************", "Redis": "redis:6379"}, "JwtSettings": {"SecretKey": "GlassFactorySecretKey2025VeryLongAndSecureForProduction!", "Issuer": "GlassFactoryApp", "Audience": "GlassFactoryUsers", "ExpiryInHours": 24}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Warning"}}, "AllowedHosts": "*", "ApplicationSettings": {"ApplicationName": "نظام حسابات مصنع الزجاج", "ApplicationNameEn": "Glass Factory Accounting System", "Version": "2.0.0", "CompanyName": "مصنع الزجاج المتطور", "CompanyNameEn": "Advanced Glass Factory", "Owner": "حسام محمد حسان أحمد", "SupportEmail": "<EMAIL>", "SupportPhone": "+966-XX-XXX-XXXX", "BaseUrl": "https://glass-factory-demo.ddns.net"}, "FileUploadSettings": {"MaxFileSize": ********, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".gif", ".pdf", ".doc", ".docx", ".xls", ".xlsx"], "UploadPath": "/app/uploads", "TempPath": "/app/temp"}, "ReportSettings": {"ReportsPath": "/app/reports", "TempReportsPath": "/app/temp/reports", "DefaultPageSize": "A4", "DefaultOrientation": "Portrait", "CompanyLogo": "/app/images/company-logo.png"}, "EmailSettings": {"SmtpServer": "smtp.gmail.com", "SmtpPort": 587, "SmtpUsername": "<EMAIL>", "SmtpPassword": "your-app-password", "FromEmail": "<EMAIL>", "FromName": "نظام حسابات مصنع الزجاج", "EnableSsl": true}, "CacheSettings": {"DefaultExpirationMinutes": 30, "SlidingExpirationMinutes": 10, "EnableDistributedCache": true, "CacheKeyPrefix": "GlassFactory:"}, "SecuritySettings": {"EnableTwoFactorAuth": false, "PasswordRequirements": {"RequireDigit": true, "RequireLowercase": true, "RequireUppercase": true, "RequireNonAlphanumeric": false, "RequiredLength": 6, "RequiredUniqueChars": 1}, "LockoutSettings": {"AllowedForNewUsers": true, "DefaultLockoutTimeSpan": "00:05:00", "MaxFailedAccessAttempts": 5}}, "BusinessSettings": {"DefaultCurrency": "SAR", "CurrencySymbol": "ريال", "TaxRate": 15.0, "FiscalYearStart": "01-01", "WorkingDaysPerWeek": 6, "WorkingHoursPerDay": 8, "OvertimeRate": 1.5, "DefaultPaymentTerms": 30, "DefaultWarrantyPeriod": 365}, "GlassFactorySettings": {"GlassTypes": ["شفاف", "ملون", "<PERSON><PERSON><PERSON><PERSON>", "مص<PERSON><PERSON>", "عاكس", "مزدوج", "أ<PERSON><PERSON>", "ديكوري", "مقاوم للحريق"], "GlassColors": ["شفاف", "أزرق", "أخضر", "رمادي", "برونزي", "أسود", "أ<PERSON><PERSON>ض", "ذهبي", "فضي"], "FinishingTypes": ["صقل", "<PERSON><PERSON><PERSON>", "طباعة", "تشطيب", "تقوية", "تصفيح", "عزل حراري", "عزل صوتي"], "QualityStandards": ["مم<PERSON><PERSON><PERSON>", "جيد جداً", "<PERSON>ي<PERSON>", "مق<PERSON>ول", "يحتاج تحسين"], "DefaultThicknesses": [3, 4, 5, 6, 8, 10, 12, 15, 19, 25], "StandardSizes": [{"Width": 100, "Height": 100}, {"Width": 150, "Height": 150}, {"Width": 200, "Height": 200}, {"Width": 300, "Height": 200}, {"Width": 400, "Height": 300}]}, "NotificationSettings": {"EnableEmailNotifications": true, "EnableSmsNotifications": false, "EnablePushNotifications": true, "LowStockThreshold": 10, "CriticalStockThreshold": 5, "OverdueInvoicesDays": 30, "PayrollReminderDays": 3}, "IntegrationSettings": {"EnableAccountingIntegration": true, "EnableInventoryIntegration": true, "EnablePayrollIntegration": true, "EnableManufacturingIntegration": true, "AutoPostJournalEntries": true, "AutoUpdateInventory": true}, "PerformanceSettings": {"EnableResponseCaching": true, "EnableOutputCaching": true, "EnableCompression": true, "MaxConcurrentRequests": 100, "RequestTimeoutSeconds": 30, "DatabaseCommandTimeoutSeconds": 30}, "AuditSettings": {"EnableAuditLogging": true, "AuditLogRetentionDays": 365, "LogUserActions": true, "LogDataChanges": true, "LogSystemEvents": true}, "BackupSettings": {"EnableAutoBackup": true, "BackupSchedule": "0 2 * * *", "BackupRetentionDays": 30, "BackupPath": "/app/backups", "IncludeUploads": true, "CompressBackups": true}, "MonitoringSettings": {"EnableHealthChecks": true, "EnableMetrics": true, "HealthCheckInterval": 30, "MetricsRetentionDays": 7, "AlertThresholds": {"CpuUsagePercent": 80, "MemoryUsagePercent": 85, "DiskUsagePercent": 90, "ResponseTimeMs": 5000}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}, {"Name": "File", "Args": {"path": "/app/logs/glass-factory-.txt", "rollingInterval": "Day", "retainedFileCountLimit": 30, "outputTemplate": "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} {Level:u3}] {Message:lj} {Properties:j}{NewLine}{Exception}"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}}