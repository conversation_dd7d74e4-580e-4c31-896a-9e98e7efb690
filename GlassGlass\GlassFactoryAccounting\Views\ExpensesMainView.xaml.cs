using System.Windows;
using System.Windows.Controls;

namespace GlassFactoryAccounting.Views
{
    public partial class ExpensesMainView : UserControl
    {
        public ExpensesMainView()
        {
            InitializeComponent();
        }

        private void BtnExpenseManagement_Click(object sender, RoutedEventArgs e)
        {
            // التنقل إلى واجهة إدارة المصروفات
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                var expenseManagementView = new ExpenseManagementView();
                mainWindow.MainContentArea.Content = expenseManagementView;
            }
        }

        private void BtnExpenseRecord_Click(object sender, RoutedEventArgs e)
        {
            // التنقل إلى واجهة تسجيل المصروفات
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                var expenseRecordView = new ExpenseRecordView();
                mainWindow.MainContentArea.Content = expenseRecordView;
            }
        }

        private void BtnExpenseReport_Click(object sender, RoutedEventArgs e)
        {
            // التنقل إلى واجهة تقرير المصروفات
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                var expenseReportView = new ExpenseReportView();
                mainWindow.MainContentArea.Content = expenseReportView;
            }
        }

        private void BtnProjectExpenseReport_Click(object sender, RoutedEventArgs e)
        {
            // التنقل إلى واجهة تقرير مصروفات المشاريع
            var mainWindow = Window.GetWindow(this) as MainWindow;
            if (mainWindow != null)
            {
                var projectExpenseReportView = new ProjectExpenseReportView();
                mainWindow.MainContentArea.Content = projectExpenseReportView;
            }
        }


    }
}
