using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة تسجيل سداد الموظف
    /// </summary>
    public partial class SalaryPaymentView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly ExpenseService _expenseService;
        private readonly MainWindow _mainWindow;
        private Employee? _selectedEmployee;

        public SalaryPaymentView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _expenseService = new ExpenseService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadPayments();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ اليوم كافتراضي
            DpPaymentDate.SelectedDate = DateTime.Now;

            // تحميل قائمة المسؤولين من إدارة المصروفات
            LoadResponsiblePersons();

            // تعيين قيم افتراضية
            TxtSalaryPaid.Text = "0";
            TxtBonusAmount.Text = "0";
            TxtOvertimeAmount.Text = "0";
            TxtAdvanceDeduction.Text = "0";
            TxtTotalPaid.Text = "0";
            TxtNetAmount.Text = "0";
        }

        private void LoadResponsiblePersons()
        {
            try
            {
                var responsiblePersons = _expenseService.GetAllResponsiblePersons();
                CmbResponsiblePerson.ItemsSource = responsiblePersons;
                CmbResponsiblePerson.DisplayMemberPath = "Name";
                CmbResponsiblePerson.SelectedValuePath = "Name";

                if (responsiblePersons.Any())
                {
                    CmbResponsiblePerson.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة المسؤولين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // في حالة الخطأ، استخدم قائمة افتراضية
                CmbResponsiblePerson.Items.Add("مدير الموارد البشرية");
                CmbResponsiblePerson.Items.Add("المدير المالي");
                CmbResponsiblePerson.Items.Add("المدير العام");
                CmbResponsiblePerson.Items.Add("مدير الفرع");
                CmbResponsiblePerson.SelectedIndex = 0;
            }
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.ItemsSource = employees;
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadPayments()
        {
            try
            {
                var payments = _payrollService.GetAllPayments();
                PaymentsDataGrid.ItemsSource = payments;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المدفوعات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbEmployee_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbEmployee.SelectedItem is Employee selectedEmployee)
            {
                _selectedEmployee = selectedEmployee;
                TxtEmployeeCode.Text = selectedEmployee.EmployeeCode;
                TxtPosition.Text = selectedEmployee.Position;
                TxtBranch.Text = selectedEmployee.Branch ?? "الفرع الرئيسي";
                TxtBasicSalary.Text = selectedEmployee.BasicSalary.ToString("N2");

                // تعيين الراتب الأساسي كافتراضي
                TxtSalaryPaid.Text = selectedEmployee.BasicSalary.ToString("N2");
                TxtAmountPaid.Text = selectedEmployee.BasicSalary.ToString("N2");

                // تحميل البيانات التلقائية للموظف
                LoadEmployeeCalculatedData(selectedEmployee.Id);

                CalculateTotal();
            }
            else
            {
                _selectedEmployee = null;
                ClearEmployeeFields();
            }
        }

        /// <summary>
        /// تحميل البيانات المحسوبة للموظف (الوقت الإضافي والخصومات)
        /// </summary>
        private void LoadEmployeeCalculatedData(int employeeId)
        {
            try
            {
                // حساب إجمالي الوقت الإضافي المستحق
                var overtimeTotal = _payrollService.GetEmployeeOvertimeTotal(employeeId);
                TxtOvertimeAmount.Text = overtimeTotal.ToString("N2");

                // حساب إجمالي الخصومات المستحقة
                var deductionTotal = _payrollService.GetEmployeeDeductionTotal(employeeId);
                TxtAdvanceDeduction.Text = deductionTotal.ToString("N2");

                // حساب إجمالي المكافآت المستحقة
                var bonusTotal = _payrollService.GetEmployeeBonusTotal(employeeId);
                TxtBonusAmount.Text = bonusTotal.ToString("N2");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading employee calculated data: {ex.Message}");
                // في حالة الخطأ، استخدم القيم الافتراضية
                TxtOvertimeAmount.Text = "0";
                TxtAdvanceDeduction.Text = "0";
                TxtBonusAmount.Text = "0";
            }
        }

        private void ClearEmployeeFields()
        {
            TxtEmployeeCode.Clear();
            TxtPosition.Clear();
            TxtBranch.Clear();
            TxtBasicSalary.Clear();
            TxtSalaryPaid.Text = "0";
            TxtBonusAmount.Text = "0";
            TxtOvertimeAmount.Text = "0";
            TxtAdvanceDeduction.Text = "0";
            TxtTotalPaid.Text = "0";
            TxtNetAmount.Text = "0";
            TxtAmountPaid.Text = "0";
            TxtRemainingAmount.Text = "0";
        }





        private void TxtSalaryPaid_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
                ValidateNumericInput(textBox);
            CalculateTotal();
        }

        private void TxtBonusAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
                ValidateNumericInput(textBox);
            CalculateTotal();
        }

        private void TxtOvertimeAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
                ValidateNumericInput(textBox);
            CalculateTotal();
        }

        private void TxtAdvanceDeduction_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
                ValidateNumericInput(textBox);
            CalculateTotal();
        }

        private void TxtAmountPaid_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
                ValidateNumericInput(textBox);
            CalculateTotal();
        }

        private void ChkAddBonusOvertime_Checked(object sender, RoutedEventArgs e)
        {
            try
            {
                // إظهار حقول المكافأة والوقت الإضافي
                var lblBonus = this.FindName("LblBonus") as TextBlock;
                var lblOvertime = this.FindName("LblOvertime") as TextBlock;

                if (lblBonus != null) lblBonus.Visibility = Visibility.Visible;
                if (lblOvertime != null) lblOvertime.Visibility = Visibility.Visible;

                TxtBonusAmount.Visibility = Visibility.Visible;
                TxtOvertimeAmount.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ChkAddBonusOvertime_Checked: {ex.Message}");
            }
        }

        private void ChkAddBonusOvertime_Unchecked(object sender, RoutedEventArgs e)
        {
            try
            {
                // إخفاء حقول المكافأة والوقت الإضافي وإعادة تعيين القيم
                var lblBonus = this.FindName("LblBonus") as TextBlock;
                var lblOvertime = this.FindName("LblOvertime") as TextBlock;

                if (lblBonus != null) lblBonus.Visibility = Visibility.Collapsed;
                if (lblOvertime != null) lblOvertime.Visibility = Visibility.Collapsed;

                TxtBonusAmount.Visibility = Visibility.Collapsed;
                TxtOvertimeAmount.Visibility = Visibility.Collapsed;

                TxtBonusAmount.Text = "0";
                TxtOvertimeAmount.Text = "0";
                CalculateTotal();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ChkAddBonusOvertime_Unchecked: {ex.Message}");
            }
        }

        private void ValidateNumericInput(TextBox textBox)
        {
            if (textBox != null && !string.IsNullOrEmpty(textBox.Text))
            {
                if (!decimal.TryParse(textBox.Text, out _))
                {
                    // إزالة الأحرف غير الصحيحة
                    int caretIndex = textBox.CaretIndex;
                    textBox.Text = System.Text.RegularExpressions.Regex.Replace(textBox.Text, @"[^\d.]", "");
                    textBox.CaretIndex = Math.Min(caretIndex, textBox.Text.Length);
                }
            }
        }

        private void CalculateTotal()
        {
            decimal salaryPaid = decimal.TryParse(TxtSalaryPaid.Text, out var salary) ? salary : 0;
            decimal bonusAmount = decimal.TryParse(TxtBonusAmount.Text, out var bonus) ? bonus : 0;
            decimal overtimeAmount = decimal.TryParse(TxtOvertimeAmount.Text, out var overtime) ? overtime : 0;
            decimal advanceDeduction = decimal.TryParse(TxtAdvanceDeduction.Text, out var advance) ? advance : 0;
            decimal amountPaid = decimal.TryParse(TxtAmountPaid.Text, out var paid) ? paid : 0;

            // حساب الإجمالي قبل الخصم (إجمالي المستحقات)
            decimal totalPayable = salaryPaid + bonusAmount + overtimeAmount;
            TxtTotalPaid.Text = totalPayable.ToString("N2");

            // حساب الصافي بعد خصم السلفة (إجمالي المستحق بعد الخصومات)
            decimal netAmount = totalPayable - advanceDeduction;
            TxtNetAmount.Text = netAmount.ToString("N2");

            // حساب المبلغ المتبقي (الفرق بين المستحق والمدفوع)
            decimal remainingAmount = netAmount - amountPaid;
            TxtRemainingAmount.Text = remainingAmount.ToString("N2");

            // تغيير لون المبلغ المتبقي حسب القيمة
            if (remainingAmount > 0)
            {
                TxtRemainingAmount.Foreground = System.Windows.Media.Brushes.Red; // متبقي للدفع
            }
            else if (remainingAmount < 0)
            {
                TxtRemainingAmount.Foreground = System.Windows.Media.Brushes.Blue; // مدفوع زيادة
            }
            else
            {
                TxtRemainingAmount.Foreground = System.Windows.Media.Brushes.Green; // مسدد بالكامل
            }
        }

        private void BtnPrintCheck_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_selectedEmployee == null)
                {
                    MessageBox.Show("يرجى اختيار الموظف أولاً", "تحذير",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء تفاصيل الشيك
                var checkDetails = GenerateCheckDetails();

                // عرض معاينة الشيك
                var checkPreview = $@"
═══════════════════════════════════════════════════════════════
                            شيك سداد راتب
═══════════════════════════════════════════════════════════════

اسم الموظف: {_selectedEmployee.Name}
كود الموظف: {_selectedEmployee.EmployeeCode}
الوظيفة: {_selectedEmployee.Position}
تاريخ السداد: {DateTime.Now:yyyy/MM/dd}

═══════════════════════════════════════════════════════════════
                        تفاصيل المستحقات
═══════════════════════════════════════════════════════════════

الراتب الأساسي المسدد: {checkDetails.SalaryPaid:N2} جنيه
{(checkDetails.BonusAmount > 0 ? $"المكافآت: {checkDetails.BonusAmount:N2} جنيه" : "")}
{(checkDetails.OvertimeAmount > 0 ? $"الوقت الإضافي: {checkDetails.OvertimeAmount:N2} جنيه" : "")}

إجمالي المسدد: {checkDetails.TotalPaid:N2} جنيه

═══════════════════════════════════════════════════════════════
                        المتبقي من المستحقات
═══════════════════════════════════════════════════════════════

الرواتب المستحقة المتبقية: {checkDetails.RemainingSalary:N2} جنيه
السلف المستحقة: {checkDetails.RemainingAdvances:N2} جنيه
المكافآت المتبقية: {checkDetails.RemainingBonuses:N2} جنيه

إجمالي المتبقي: {checkDetails.TotalRemaining:N2} جنيه

═══════════════════════════════════════════════════════════════

المسؤول عن السداد: {CmbResponsiblePerson.SelectedItem}
التوقيع: ___________________

═══════════════════════════════════════════════════════════════
";

                // عرض معاينة الشيك
                var result = MessageBox.Show(
                    $"معاينة شيك السداد:\n\n{checkPreview}\n\nهل تريد طباعة الشيك؟",
                    "معاينة الشيك",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // هنا سيتم تطوير وظيفة الطباعة الفعلية
                    MessageBox.Show("تم إرسال الشيك للطباعة بنجاح!", "تم الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الشيك: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء تفاصيل الشيك مع حساب المستحقات والمتبقي
        /// </summary>
        private CheckDetails GenerateCheckDetails()
        {
            var details = new CheckDetails();

            // البيانات الحالية من النموذج
            details.SalaryPaid = decimal.TryParse(TxtSalaryPaid.Text, out var salary) ? salary : 0;
            details.BonusAmount = decimal.TryParse(TxtBonusAmount.Text, out var bonus) ? bonus : 0;
            details.OvertimeAmount = decimal.TryParse(TxtOvertimeAmount.Text, out var overtime) ? overtime : 0;

            details.TotalPaid = details.SalaryPaid + details.BonusAmount + details.OvertimeAmount;

            // حساب المتبقي من قاعدة البيانات
            if (_selectedEmployee != null)
            {
                try
                {
                    // حساب الرواتب المستحقة المتبقية
                    var unpaidSalaries = _payrollService.GetEmployeeUnpaidSalaryTotal(_selectedEmployee.Id);
                    details.RemainingSalary = unpaidSalaries;

                    // حساب السلف المتبقية
                    var remainingAdvances = _payrollService.GetEmployeeRemainingAdvances(_selectedEmployee.Id);
                    details.RemainingAdvances = remainingAdvances;

                    // حساب المكافآت المتبقية
                    var remainingBonuses = _payrollService.GetEmployeeRemainingBonuses(_selectedEmployee.Id);
                    details.RemainingBonuses = remainingBonuses;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error calculating remaining amounts: {ex.Message}");
                    // استخدام قيم افتراضية في حالة الخطأ
                    details.RemainingSalary = _selectedEmployee.BasicSalary;
                    details.RemainingAdvances = 0;
                    details.RemainingBonuses = 0;
                }
            }

            details.TotalRemaining = details.RemainingSalary + details.RemainingAdvances + details.RemainingBonuses;

            return details;
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedEmployee = CmbEmployee.SelectedItem as Employee;
                if (selectedEmployee == null)
                {
                    MessageBox.Show("يرجى اختيار موظف.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                // استخدم selectedEmployee.Id بأمان
                var responsiblePerson = CmbResponsiblePerson != null ? CmbResponsiblePerson.SelectedItem?.ToString() ?? string.Empty : string.Empty;

                var payment = new SalaryPayment
                {
                    EmployeeId = selectedEmployee.Id,
                    EmployeeName = selectedEmployee.Name,
                    EmployeeCode = selectedEmployee.EmployeeCode,
                    SalaryPaid = decimal.Parse(TxtSalaryPaid.Text),
                    BonusAmount = decimal.Parse(TxtBonusAmount.Text),
                    OvertimeAmount = decimal.Parse(TxtOvertimeAmount.Text),
                    TotalPaid = decimal.Parse(TxtAmountPaid.Text), // المبلغ المدفوع فعلياً
                    ResponsiblePerson = (CmbResponsiblePerson.SelectedItem as GlassFactoryAccounting.Models.ResponsiblePerson)?.Name ?? CmbResponsiblePerson.SelectedValue?.ToString() ?? "",
                    PaymentDate = DpPaymentDate.SelectedDate ?? DateTime.Now,
                    Notes = TxtNotes.Text.Trim()
                };

                if (_payrollService.AddSalaryPayment(payment))
                {
                    MessageBox.Show("تم حفظ السداد بنجاح!", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    ClearForm();
                    LoadPayments();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ السداد!", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السداد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbEmployee.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbEmployee.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtSalaryPaid.Text))
            {
                MessageBox.Show("يرجى إدخال الراتب المسدد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtSalaryPaid.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtSalaryPaid.Text, out decimal salary) || salary < 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للراتب المسدد", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtSalaryPaid.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtBonusAmount.Text, out decimal bonus) || bonus < 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للمكافأة", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtBonusAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtOvertimeAmount.Text, out decimal overtime) || overtime < 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للوقت الإضافي", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtOvertimeAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtAdvanceDeduction.Text, out decimal advance) || advance < 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة لخصم السلفة", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAdvanceDeduction.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtAmountPaid.Text, out decimal amountPaid) || amountPaid < 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للمبلغ المدفوع", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmountPaid.Focus();
                return false;
            }

            if (CmbResponsiblePerson.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المسؤول عن السداد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbResponsiblePerson.Focus();
                return false;
            }

            if (!DpPaymentDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ السداد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpPaymentDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            CmbEmployee.SelectedIndex = -1;
            _selectedEmployee = null;
            ClearEmployeeFields();
            CmbResponsiblePerson.SelectedIndex = 0;
            DpPaymentDate.SelectedDate = DateTime.Now;
            TxtNotes.Clear();
            CmbEmployee.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeletePayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int paymentId)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذا السداد؟", "تأكيد الحذف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (_payrollService.DeleteSalaryPayment(paymentId))
                        {
                            MessageBox.Show("تم حذف السداد بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadPayments();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف السداد!", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السداد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditPayment_Click(object sender, RoutedEventArgs e)
        {
            // TODO: تنفيذ منطق التعديل هنا حسب الحاجة
            MessageBox.Show("ميزة التعديل غير مفعلة حالياً. يرجى استكمال منطق التعديل إذا لزم.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
