{"format": 1, "restore": {"E:\\12-6-2025 البرنامج\\GlassGlass\\GlassFactoryWorking\\GlassFactoryWorking.csproj": {}}, "projects": {"E:\\12-6-2025 البرنامج\\GlassGlass\\GlassFactoryWorking\\GlassFactoryWorking.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\12-6-2025 البرنامج\\GlassGlass\\GlassFactoryWorking\\GlassFactoryWorking.csproj", "projectName": "GlassFactoryWorking", "projectPath": "E:\\12-6-2025 البرنامج\\GlassGlass\\GlassFactoryWorking\\GlassFactoryWorking.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\12-6-2025 البرنامج\\GlassGlass\\GlassFactoryWorking\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net48"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "net48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "10.0.100"}, "frameworks": {"net48": {"targetAlias": "net48", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.3, )", "autoReferenced": true}, "System.Data.SQLite.Core": {"target": "Package", "version": "[1.0.118, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\10.0.100-preview.4.25258.110\\RuntimeIdentifierGraph.json"}}, "runtimes": {"win-x86": {"#import": []}}}}}