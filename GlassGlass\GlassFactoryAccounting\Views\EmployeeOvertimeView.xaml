<UserControl x:Class="GlassFactoryAccounting.Views.EmployeeOvertimeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">

    <UserControl.Resources>
        <!-- الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="LightBrush" Color="#F5F5F5"/>

        <!-- نمط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط الأزرار -->
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="120"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط زر النجاح -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#388E3C"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1B5E20"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط زر التحذير -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F57C00"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#E65100"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط موحد لحقول الإدخال (TextBox, ComboBox, DatePicker) -->
        <Style x:Key="UnifiedInputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>

        <!-- نمط التسميات -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="8,8,8,0"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Background="{StaticResource LightBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="⏰" FontSize="28" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <TextBlock Text="تسجيل وقت إضافي" FontSize="22" FontWeight="Bold"
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- النموذج -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- اسم الموظف -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الموظف:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="CmbEmployee" Grid.Row="0" Grid.Column="1" Style="{StaticResource UnifiedComboBoxStyle}"
                          SelectionChanged="CmbEmployee_SelectionChanged"/>

                <!-- كود الموظف -->
                <TextBlock Grid.Row="1" Grid.Column="0" Text="كود الموظف:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtEmployeeCode" Grid.Row="1" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}"
                         IsReadOnly="True" Background="#F0F0F0"/>

                <!-- الوظيفة -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="الوظيفة:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtPosition" Grid.Row="2" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}"
                         IsReadOnly="True" Background="#F0F0F0"/>

                <!-- الفرع -->
                <TextBlock Grid.Row="3" Grid.Column="0" Text="الفرع:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtBranch" Grid.Row="3" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}"
                         IsReadOnly="True" Background="#F0F0F0"/>

                <!-- أجر الساعة -->
                <TextBlock Grid.Row="4" Grid.Column="0" Text="أجر الساعة:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtHourlyRate" Grid.Row="4" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}"
                         IsReadOnly="True" Background="#F0F0F0"/>

                <!-- عدد الساعات الإضافية -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="عدد الساعات الإضافية:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtOvertimeHours" Grid.Row="5" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}"
                         TextChanged="TxtOvertimeHours_TextChanged"/>

                <!-- قيمة الوقت الإضافي -->
                <TextBlock Grid.Row="6" Grid.Column="0" Text="قيمة الوقت الإضافي:" Style="{StaticResource LabelStyle}"/>
                <StackPanel Grid.Row="6" Grid.Column="1" Orientation="Horizontal">
                    <TextBox x:Name="TxtOvertimeAmount" Style="{StaticResource UnifiedInputStyle}" Width="200"
                             TextChanged="TxtOvertimeAmount_TextChanged"/>
                    <Button x:Name="BtnCalculateAuto" Content="🧮 حساب تلقائي" Style="{StaticResource WarningButtonStyle}"
                            Click="BtnCalculateAuto_Click" Margin="5,8,8,8" Padding="10,8"/>
                </StackPanel>

                <!-- تاريخ التسجيل -->
                <TextBlock Grid.Row="7" Grid.Column="0" Text="تاريخ التسجيل:" Style="{StaticResource LabelStyle}"/>
                <DatePicker x:Name="DpOvertimeDate" Grid.Row="7" Grid.Column="1" Style="{StaticResource UnifiedDatePickerStyle}" Margin="8" Height="40" FontSize="14"/>

                <!-- معادلة الحساب -->
                <TextBlock Grid.Row="8" Grid.Column="0" Text="معادلة الحساب:" Style="{StaticResource LabelStyle}"/>
                <TextBlock x:Name="TxtFormula" Grid.Row="8" Grid.Column="1"
                           Text="(عدد الساعات × أجر الساعة) + (عدد الساعات × 0.5 × أجر الساعة)"
                           Style="{StaticResource LabelStyle}" Foreground="#666666" FontWeight="Normal" FontSize="12"
                           TextWrapping="Wrap" Margin="8"/>

                <!-- ملاحظات -->
                <TextBlock Grid.Row="0" Grid.Column="2" Text="ملاحظات:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtNotes" Grid.Row="1" Grid.Column="2" Grid.RowSpan="8" Style="{StaticResource UnifiedInputStyle}"
                         Height="320" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"
                         VerticalContentAlignment="Top"/>

            </Grid>
        </Border>

        <!-- قائمة الوقت الإضافي -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Height="680">
                <DataGrid x:Name="OvertimeDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود الموظف" Binding="{Binding EmployeeCode}" Width="120"/>
                    <DataGridTextColumn Header="اسم الموظف" Binding="{Binding EmployeeName}" Width="180"/>
                    <DataGridTextColumn Header="عدد الساعات" Binding="{Binding OvertimeHours, StringFormat='{}{0:N1}'}" Width="100"/>
                    <DataGridTextColumn Header="قيمة الوقت الإضافي" Binding="{Binding OvertimeAmount, StringFormat='{}{0:N2}'}" Width="140"/>
                    <DataGridTextColumn Header="تاريخ التسجيل" Binding="{Binding OvertimeDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
                    <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150"/>
                    <DataGridTemplateColumn Header="الإجراءات" Width="160">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="✏️ تعديل" Background="#2196F3" Foreground="White"
                                            BorderThickness="0" Padding="8,4" Margin="2" FontSize="10"
                                            Cursor="Hand" Click="BtnEditOvertime_Click"
                                            Tag="{Binding Id}"/>
                                    <Button Content="🗑️ حذف" Background="#F44336" Foreground="White"
                                            BorderThickness="0" Padding="8,4" Margin="2" FontSize="10"
                                            Cursor="Hand" Click="BtnDeleteOvertime_Click"
                                            Tag="{Binding Id}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
                </DataGrid>
            </ScrollViewer>
        </Border>

        <!-- الأزرار -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ الوقت الإضافي" Style="{StaticResource SuccessButtonStyle}" Click="BtnSave_Click"/>
                <Button x:Name="BtnClear" Content="🗑️ مسح الحقول" Style="{StaticResource ButtonStyle}" Click="BtnClear_Click"/>
                <Button x:Name="BtnBack" Content="🔙 العودة" Style="{StaticResource ButtonStyle}" Click="BtnBack_Click"/>
            </StackPanel>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
