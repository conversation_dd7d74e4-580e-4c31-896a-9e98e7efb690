@echo off
title Glass Factory Accounting System
color 0A

echo.
echo ===============================================
echo   Glass Factory Accounting System
echo   Windows Forms Edition (.NET Framework 4.7.2)
echo ===============================================
echo.
echo Owner: <PERSON><PERSON><PERSON>
echo Version: 1.0.0
echo Technology: Windows Forms + SQLite
echo.

echo Looking for executable file...

if exist "bin\Release\GlassFactoryAccountingWinForms.exe" (
    echo Found Release version - Starting...
    start "" "bin\Release\GlassFactoryAccountingWinForms.exe"
    goto :end
)

if exist "bin\Debug\GlassFactoryAccountingWinForms.exe" (
    echo Found Debug version - Starting...
    start "" "bin\Debug\GlassFactoryAccountingWinForms.exe"
    goto :end
)

echo.
echo ===============================================
echo   FIRST TIME SETUP REQUIRED
echo ===============================================
echo.
echo The project needs to be built first.
echo Please follow these steps:
echo.
echo 1. Install Visual Studio 2019 or later
echo 2. Install .NET Framework 4.7.2 SDK
echo 3. Open: GlassFactoryAccountingWinForms.sln
echo 4. Press Ctrl+Shift+B to build the solution
echo 5. Run this file again
echo.
echo OR use Visual Studio:
echo 1. Open: GlassFactoryAccountingWinForms.sln
echo 2. Press F5 to run directly
echo.
echo ===============================================
echo   PROJECT FEATURES
echo ===============================================
echo.
echo - Complete Windows Forms application
echo - Chart of Accounts management
echo - SQLite database (local, no server needed)
echo - Arabic RTL support
echo - Professional UI design
echo - Account creation and editing
echo - Real-time statistics
echo.
pause

:end
