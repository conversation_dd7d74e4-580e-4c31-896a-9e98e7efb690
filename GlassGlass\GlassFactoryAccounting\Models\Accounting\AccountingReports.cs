using System;
using System.Collections.Generic;

namespace GlassFactoryAccounting.Models.Accounting
{
    /// <summary>
    /// نموذج ميزان المراجعة
    /// </summary>
    public class TrialBalance
    {
        public List<TrialBalanceItem> Items { get; set; } = new List<TrialBalanceItem>();
        public decimal TotalDebits => Items.Sum(i => i.DebitBalance);
        public decimal TotalCredits => Items.Sum(i => i.CreditBalance);
        public bool IsBalanced => TotalDebits == TotalCredits;
        public DateTime AsOfDate { get; set; }
    }
    
    public class TrialBalanceItem
    {
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public AccountType AccountType { get; set; }
        public decimal DebitBalance { get; set; }
        public decimal CreditBalance { get; set; }
    }
    
    /// <summary>
    /// نموذج قائمة الدخل
    /// </summary>
    public class IncomeStatement
    {
        public List<IncomeStatementItem> RevenueItems { get; set; } = new List<IncomeStatementItem>();
        public List<IncomeStatementItem> ExpenseItems { get; set; } = new List<IncomeStatementItem>();
        
        public decimal TotalRevenue => RevenueItems.Sum(i => i.Amount);
        public decimal TotalExpenses => ExpenseItems.Sum(i => i.Amount);
        public decimal NetIncome => TotalRevenue - TotalExpenses;
        
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }
    
    public class IncomeStatementItem
    {
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public decimal Amount { get; set; }
        public AccountType AccountType { get; set; }
    }
    
    /// <summary>
    /// نموذج الميزانية العمومية
    /// </summary>
    public class BalanceSheet
    {
        public List<BalanceSheetItem> Assets { get; set; } = new List<BalanceSheetItem>();
        public List<BalanceSheetItem> Liabilities { get; set; } = new List<BalanceSheetItem>();
        public List<BalanceSheetItem> Equity { get; set; } = new List<BalanceSheetItem>();
        
        public decimal TotalAssets => Assets.Sum(i => i.Amount);
        public decimal TotalLiabilities => Liabilities.Sum(i => i.Amount);
        public decimal TotalEquity => Equity.Sum(i => i.Amount);
        
        public bool IsBalanced => TotalAssets == (TotalLiabilities + TotalEquity);
        
        public DateTime AsOfDate { get; set; }
    }
    
    public class BalanceSheetItem
    {
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public decimal Amount { get; set; }
        public AccountType AccountType { get; set; }
    }
    
    /// <summary>
    /// نموذج كشف حساب
    /// </summary>
    public class AccountStatement
    {
        public Account Account { get; set; } = null!;
        public List<AccountStatementItem> Transactions { get; set; } = new List<AccountStatementItem>();
        public decimal OpeningBalance { get; set; }
        public decimal ClosingBalance { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }
    
    public class AccountStatementItem
    {
        public DateTime Date { get; set; }
        public string EntryNumber { get; set; } = "";
        public string Description { get; set; } = "";
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal Balance { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? ReferenceType { get; set; }
    }
    
    /// <summary>
    /// نموذج دفتر الأستاذ العام
    /// </summary>
    public class GeneralLedger
    {
        public List<GeneralLedgerAccount> Accounts { get; set; } = new List<GeneralLedgerAccount>();
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }
    
    public class GeneralLedgerAccount
    {
        public Account Account { get; set; } = null!;
        public List<GeneralLedgerTransaction> Transactions { get; set; } = new List<GeneralLedgerTransaction>();
        public decimal OpeningBalance { get; set; }
        public decimal ClosingBalance { get; set; }
        public decimal TotalDebits => Transactions.Sum(t => t.DebitAmount);
        public decimal TotalCredits => Transactions.Sum(t => t.CreditAmount);
    }
    
    public class GeneralLedgerTransaction
    {
        public DateTime Date { get; set; }
        public string EntryNumber { get; set; } = "";
        public string Description { get; set; } = "";
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal RunningBalance { get; set; }
    }
}
