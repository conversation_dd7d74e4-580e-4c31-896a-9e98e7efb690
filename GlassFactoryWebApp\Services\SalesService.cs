using Microsoft.EntityFrameworkCore;
using AutoMapper;
using GlassFactoryWebApp.Data;
using GlassFactoryWebApp.Models;
using GlassFactoryWebApp.DTOs;

namespace GlassFactoryWebApp.Services
{
    /// <summary>
    /// خدمة المبيعات
    /// </summary>
    public class SalesService : ISalesService
    {
        private readonly ApplicationDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<SalesService> _logger;
        private readonly IAccountingService _accountingService;
        private readonly IInventoryService _inventoryService;
        private readonly IPdfService _pdfService;

        public SalesService(
            ApplicationDbContext context,
            IMapper mapper,
            ILogger<SalesService> logger,
            IAccountingService accountingService,
            IInventoryService inventoryService,
            IPdfService pdfService)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
            _accountingService = accountingService;
            _inventoryService = inventoryService;
            _pdfService = pdfService;
        }

        #region Sales Invoices

        public async Task<PagedResult<SalesInvoiceDto>> GetSalesInvoicesAsync(int page, int pageSize, string? search = null,
            DateTime? fromDate = null, DateTime? toDate = null, string? status = null)
        {
            try
            {
                var query = _context.SalesInvoices
                    .Include(s => s.Customer)
                    .Include(s => s.Items)
                    .Where(s => !s.IsDeleted);

                // تطبيق الفلاتر
                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(s => s.InvoiceNumber.Contains(search) ||
                                           s.CustomerName.Contains(search) ||
                                           s.Customer.CustomerName.Contains(search));
                }

                if (fromDate.HasValue)
                {
                    query = query.Where(s => s.InvoiceDate >= fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.Where(s => s.InvoiceDate <= toDate.Value);
                }

                if (!string.IsNullOrEmpty(status))
                {
                    query = query.Where(s => s.InvoiceStatus == status);
                }

                var totalCount = await query.CountAsync();

                var invoices = await query
                    .OrderByDescending(s => s.InvoiceDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var invoiceDtos = _mapper.Map<List<SalesInvoiceDto>>(invoices);

                return new PagedResult<SalesInvoiceDto>
                {
                    Items = invoiceDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales invoices");
                throw;
            }
        }

        public async Task<SalesInvoiceDto?> GetSalesInvoiceByIdAsync(int id)
        {
            try
            {
                var invoice = await _context.SalesInvoices
                    .Include(s => s.Customer)
                    .Include(s => s.Items)
                        .ThenInclude(i => i.Product)
                    .Include(s => s.Payments)
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                return invoice != null ? _mapper.Map<SalesInvoiceDto>(invoice) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales invoice {Id}", id);
                throw;
            }
        }

        public async Task<SalesInvoiceDto> CreateSalesInvoiceAsync(CreateSalesInvoiceDto createDto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                // التحقق من العميل
                var customer = await _context.Customers.FindAsync(createDto.CustomerId);
                if (customer == null)
                {
                    throw new ArgumentException("العميل غير موجود");
                }

                // إنشاء الفاتورة
                var invoice = _mapper.Map<SalesInvoice>(createDto);
                invoice.InvoiceNumber = await GenerateInvoiceNumberAsync();
                invoice.CustomerName = customer.CustomerName;
                invoice.CustomerAddress = customer.Address ?? "";
                invoice.CustomerPhone = customer.Phone ?? "";

                // حساب الإجماليات
                await CalculateInvoiceTotalsAsync(invoice, createDto.Items);

                _context.SalesInvoices.Add(invoice);
                await _context.SaveChangesAsync();

                // إضافة العناصر
                foreach (var itemDto in createDto.Items)
                {
                    var item = _mapper.Map<SalesInvoiceItem>(itemDto);
                    item.SalesInvoiceId = invoice.Id;

                    // جلب معلومات المنتج
                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        item.ProductCode = product.ProductCode;
                        item.ProductName = product.ProductName;
                        item.ProductDescription = product.ProductDescription;
                        item.Unit = product.Unit;
                        item.CostPrice = product.CostPrice;
                    }

                    // حساب الربح
                    item.Profit = item.TotalPrice - (item.CostPrice * item.Quantity);

                    _context.SalesInvoiceItems.Add(item);
                }

                await _context.SaveChangesAsync();

                // تحديث رصيد العميل
                customer.CurrentBalance += invoice.RemainingAmount;
                await _context.SaveChangesAsync();

                await transaction.CommitAsync();

                return await GetSalesInvoiceByIdAsync(invoice.Id) ?? throw new Exception("فشل في إنشاء الفاتورة");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating sales invoice");
                throw;
            }
        }

        public async Task<SalesInvoiceDto?> UpdateSalesInvoiceAsync(int id, UpdateSalesInvoiceDto updateDto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var invoice = await _context.SalesInvoices
                    .Include(s => s.Items)
                    .Include(s => s.Customer)
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                if (invoice == null) return null;

                // التحقق من إمكانية التعديل
                if (invoice.IsPosted)
                {
                    throw new InvalidOperationException("لا يمكن تعديل فاتورة مرحلة");
                }

                // حفظ الرصيد القديم
                var oldRemainingAmount = invoice.RemainingAmount;

                // تحديث بيانات الفاتورة
                _mapper.Map(updateDto, invoice);
                invoice.UpdatedAt = DateTime.UtcNow;

                // حذف العناصر القديمة
                _context.SalesInvoiceItems.RemoveRange(invoice.Items);

                // إضافة العناصر الجديدة
                invoice.Items.Clear();
                foreach (var itemDto in updateDto.Items)
                {
                    var item = _mapper.Map<SalesInvoiceItem>(itemDto);
                    item.SalesInvoiceId = invoice.Id;

                    var product = await _context.Products.FindAsync(item.ProductId);
                    if (product != null)
                    {
                        item.ProductCode = product.ProductCode;
                        item.ProductName = product.ProductName;
                        item.ProductDescription = product.ProductDescription;
                        item.Unit = product.Unit;
                        item.CostPrice = product.CostPrice;
                    }

                    item.Profit = item.TotalPrice - (item.CostPrice * item.Quantity);
                    invoice.Items.Add(item);
                }

                // إعادة حساب الإجماليات
                await CalculateInvoiceTotalsAsync(invoice, updateDto.Items);

                // تحديث رصيد العميل
                var customer = invoice.Customer;
                customer.CurrentBalance = customer.CurrentBalance - oldRemainingAmount + invoice.RemainingAmount;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return await GetSalesInvoiceByIdAsync(invoice.Id);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error updating sales invoice {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteSalesInvoiceAsync(int id)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var invoice = await _context.SalesInvoices
                    .Include(s => s.Customer)
                    .Include(s => s.Payments)
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                if (invoice == null) return false;

                // التحقق من إمكانية الحذف
                if (invoice.IsPosted)
                {
                    throw new InvalidOperationException("لا يمكن حذف فاتورة مرحلة");
                }

                if (invoice.Payments.Any())
                {
                    throw new InvalidOperationException("لا يمكن حذف فاتورة تحتوي على دفعات");
                }

                // حذف منطقي
                invoice.IsDeleted = true;
                invoice.DeletedAt = DateTime.UtcNow;

                // تحديث رصيد العميل
                var customer = invoice.Customer;
                customer.CurrentBalance -= invoice.RemainingAmount;

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error deleting sales invoice {Id}", id);
                throw;
            }
        }

        public async Task<bool> PostSalesInvoiceAsync(int id)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var invoice = await _context.SalesInvoices
                    .Include(s => s.Items)
                        .ThenInclude(i => i.Product)
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                if (invoice == null || invoice.IsPosted) return false;

                // ترحيل الفاتورة
                invoice.IsPosted = true;
                invoice.PostedAt = DateTime.UtcNow;

                // تحديث المخزون
                await _inventoryService.UpdateInventoryFromSalesAsync(invoice.Id);

                // إنشاء قيود محاسبية
                await _accountingService.CreateSalesJournalEntryAsync(invoice.Id);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error posting sales invoice {Id}", id);
                throw;
            }
        }

        public async Task<bool> UnpostSalesInvoiceAsync(int id)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var invoice = await _context.SalesInvoices
                    .Include(s => s.Items)
                    .FirstOrDefaultAsync(s => s.Id == id && !s.IsDeleted);

                if (invoice == null || !invoice.IsPosted) return false;

                // إلغاء ترحيل الفاتورة
                invoice.IsPosted = false;
                invoice.PostedAt = null;

                // عكس تحديث المخزون
                await _inventoryService.ReverseInventoryFromSalesAsync(invoice.Id);

                // عكس القيود المحاسبية
                await _accountingService.ReverseSalesJournalEntryAsync(invoice.Id);

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error unposting sales invoice {Id}", id);
                throw;
            }
        }

        public async Task<byte[]?> GenerateInvoicePdfAsync(int id)
        {
            try
            {
                var invoice = await GetSalesInvoiceByIdAsync(id);
                if (invoice == null) return null;

                return await _pdfService.GenerateSalesInvoicePdfAsync(invoice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating PDF for sales invoice {Id}", id);
                throw;
            }
        }

        #endregion

        #region Customers

        public async Task<PagedResult<CustomerDto>> GetCustomersAsync(int page, int pageSize, string? search = null)
        {
            try
            {
                var query = _context.Customers.Where(c => !c.IsDeleted && c.IsActive);

                if (!string.IsNullOrEmpty(search))
                {
                    query = query.Where(c => c.CustomerName.Contains(search) ||
                                           c.CustomerCode.Contains(search) ||
                                           c.Phone.Contains(search));
                }

                var totalCount = await query.CountAsync();

                var customers = await query
                    .OrderBy(c => c.CustomerName)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var customerDtos = _mapper.Map<List<CustomerDto>>(customers);

                return new PagedResult<CustomerDto>
                {
                    Items = customerDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers");
                throw;
            }
        }

        public async Task<CustomerDto?> GetCustomerByIdAsync(int id)
        {
            try
            {
                var customer = await _context.Customers
                    .Include(c => c.SalesInvoices)
                    .Include(c => c.Payments)
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);

                return customer != null ? _mapper.Map<CustomerDto>(customer) : null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer {Id}", id);
                throw;
            }
        }

        public async Task<CustomerDto> CreateCustomerAsync(CreateCustomerDto createDto)
        {
            try
            {
                var customer = _mapper.Map<Customer>(createDto);
                customer.CustomerCode = await GenerateCustomerCodeAsync();

                _context.Customers.Add(customer);
                await _context.SaveChangesAsync();

                return _mapper.Map<CustomerDto>(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating customer");
                throw;
            }
        }

        public async Task<CustomerDto?> UpdateCustomerAsync(int id, UpdateCustomerDto updateDto)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(id);
                if (customer == null || customer.IsDeleted) return null;

                _mapper.Map(updateDto, customer);
                customer.UpdatedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                return _mapper.Map<CustomerDto>(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer {Id}", id);
                throw;
            }
        }

        public async Task<bool> DeleteCustomerAsync(int id)
        {
            try
            {
                var customer = await _context.Customers
                    .Include(c => c.SalesInvoices)
                    .FirstOrDefaultAsync(c => c.Id == id && !c.IsDeleted);

                if (customer == null) return false;

                // التحقق من وجود فواتير
                if (customer.SalesInvoices.Any())
                {
                    throw new InvalidOperationException("لا يمكن حذف عميل له فواتير");
                }

                customer.IsDeleted = true;
                customer.DeletedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer {Id}", id);
                throw;
            }
        }

        public async Task<List<CustomerDto>> SearchCustomersAsync(string searchTerm)
        {
            try
            {
                var customers = await _context.Customers
                    .Where(c => !c.IsDeleted && c.IsActive &&
                               (c.CustomerName.Contains(searchTerm) ||
                                c.CustomerCode.Contains(searchTerm) ||
                                c.Phone.Contains(searchTerm)))
                    .Take(10)
                    .ToListAsync();

                return _mapper.Map<List<CustomerDto>>(customers);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customers");
                throw;
            }
        }

        #endregion

        #region Customer Payments

        public async Task<PagedResult<CustomerPaymentDto>> GetCustomerPaymentsAsync(int customerId, int page, int pageSize)
        {
            try
            {
                var query = _context.CustomerPayments
                    .Include(p => p.Customer)
                    .Include(p => p.SalesInvoice)
                    .Where(p => p.CustomerId == customerId && !p.IsDeleted);

                var totalCount = await query.CountAsync();

                var payments = await query
                    .OrderByDescending(p => p.PaymentDate)
                    .Skip((page - 1) * pageSize)
                    .Take(pageSize)
                    .ToListAsync();

                var paymentDtos = _mapper.Map<List<CustomerPaymentDto>>(payments);

                return new PagedResult<CustomerPaymentDto>
                {
                    Items = paymentDtos,
                    TotalCount = totalCount,
                    Page = page,
                    PageSize = pageSize
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customer payments");
                throw;
            }
        }

        public async Task<CustomerPaymentDto> CreateCustomerPaymentAsync(CreateCustomerPaymentDto createDto)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var payment = _mapper.Map<CustomerPayment>(createDto);
                payment.PaymentNumber = await GeneratePaymentNumberAsync();

                _context.CustomerPayments.Add(payment);
                await _context.SaveChangesAsync();

                // تحديث رصيد العميل
                var customer = await _context.Customers.FindAsync(payment.CustomerId);
                if (customer != null)
                {
                    customer.CurrentBalance -= payment.PaymentAmount;
                }

                // تحديث الفاتورة إذا كانت محددة
                if (payment.SalesInvoiceId.HasValue)
                {
                    var invoice = await _context.SalesInvoices.FindAsync(payment.SalesInvoiceId.Value);
                    if (invoice != null)
                    {
                        invoice.PaidAmount += payment.PaymentAmount;
                        invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;

                        if (invoice.RemainingAmount <= 0)
                        {
                            invoice.InvoiceStatus = "مدفوعة";
                        }
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return _mapper.Map<CustomerPaymentDto>(payment);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error creating customer payment");
                throw;
            }
        }

        public async Task<bool> DeleteCustomerPaymentAsync(int id)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var payment = await _context.CustomerPayments
                    .Include(p => p.Customer)
                    .Include(p => p.SalesInvoice)
                    .FirstOrDefaultAsync(p => p.Id == id && !p.IsDeleted);

                if (payment == null) return false;

                // التحقق من إمكانية الحذف
                if (payment.IsPosted)
                {
                    throw new InvalidOperationException("لا يمكن حذف دفعة مرحلة");
                }

                // حذف منطقي
                payment.IsDeleted = true;
                payment.DeletedAt = DateTime.UtcNow;

                // عكس تأثير الدفعة على رصيد العميل
                payment.Customer.CurrentBalance += payment.PaymentAmount;

                // عكس تأثير الدفعة على الفاتورة
                if (payment.SalesInvoice != null)
                {
                    payment.SalesInvoice.PaidAmount -= payment.PaymentAmount;
                    payment.SalesInvoice.RemainingAmount = payment.SalesInvoice.TotalAmount - payment.SalesInvoice.PaidAmount;

                    if (payment.SalesInvoice.RemainingAmount > 0)
                    {
                        payment.SalesInvoice.InvoiceStatus = "مفتوحة";
                    }
                }

                await _context.SaveChangesAsync();
                await transaction.CommitAsync();

                return true;
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error deleting customer payment {Id}", id);
                throw;
            }
        }

        #endregion

        #region Reports and Statistics

        public async Task<SalesStatisticsDto> GetSalesStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.SalesInvoices.Where(s => !s.IsDeleted);

                if (fromDate.HasValue)
                    query = query.Where(s => s.InvoiceDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(s => s.InvoiceDate <= toDate.Value);

                var invoices = await query.ToListAsync();

                return new SalesStatisticsDto
                {
                    TotalSales = invoices.Sum(s => s.TotalAmount),
                    TotalPaid = invoices.Sum(s => s.PaidAmount),
                    TotalRemaining = invoices.Sum(s => s.RemainingAmount),
                    TotalInvoices = invoices.Count,
                    PaidInvoices = invoices.Count(s => s.RemainingAmount <= 0),
                    PendingInvoices = invoices.Count(s => s.RemainingAmount > 0),
                    AverageInvoiceValue = invoices.Any() ? invoices.Average(s => s.TotalAmount) : 0,
                    TotalProfit = invoices.SelectMany(s => s.Items).Sum(i => i.Profit),
                    ProfitMargin = invoices.Sum(s => s.TotalAmount) > 0 ?
                        (invoices.SelectMany(s => s.Items).Sum(i => i.Profit) / invoices.Sum(s => s.TotalAmount)) * 100 : 0,
                    FromDate = fromDate,
                    ToDate = toDate
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales statistics");
                throw;
            }
        }

        public async Task<byte[]> GenerateSalesReportAsync(DateTime? fromDate = null, DateTime? toDate = null,
            int? customerId = null, string format = "pdf")
        {
            try
            {
                var query = _context.SalesInvoices
                    .Include(s => s.Customer)
                    .Include(s => s.Items)
                    .Where(s => !s.IsDeleted);

                if (fromDate.HasValue)
                    query = query.Where(s => s.InvoiceDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(s => s.InvoiceDate <= toDate.Value);

                if (customerId.HasValue)
                    query = query.Where(s => s.CustomerId == customerId.Value);

                var invoices = await query.OrderByDescending(s => s.InvoiceDate).ToListAsync();
                var invoiceDtos = _mapper.Map<List<SalesInvoiceDto>>(invoices);

                if (format.ToLower() == "excel")
                {
                    return await _pdfService.GenerateSalesReportExcelAsync(invoiceDtos, fromDate, toDate);
                }
                else
                {
                    return await _pdfService.GenerateSalesReportPdfAsync(invoiceDtos, fromDate, toDate);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales report");
                throw;
            }
        }

        public async Task<List<SalesChartDataDto>> GetSalesChartDataAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.SalesInvoices.Where(s => !s.IsDeleted);

                if (fromDate.HasValue)
                    query = query.Where(s => s.InvoiceDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(s => s.InvoiceDate <= toDate.Value);

                var data = await query
                    .GroupBy(s => new { s.InvoiceDate.Year, s.InvoiceDate.Month })
                    .Select(g => new SalesChartDataDto
                    {
                        Label = $"{g.Key.Year}-{g.Key.Month:D2}",
                        Value = g.Sum(s => s.TotalAmount),
                        Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                        Color = "#1976d2"
                    })
                    .OrderBy(d => d.Date)
                    .ToListAsync();

                return data;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales chart data");
                throw;
            }
        }

        public async Task<List<TopCustomerDto>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.SalesInvoices
                    .Include(s => s.Customer)
                    .Where(s => !s.IsDeleted);

                if (fromDate.HasValue)
                    query = query.Where(s => s.InvoiceDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(s => s.InvoiceDate <= toDate.Value);

                var topCustomers = await query
                    .GroupBy(s => new { s.CustomerId, s.Customer.CustomerName })
                    .Select(g => new TopCustomerDto
                    {
                        CustomerId = g.Key.CustomerId,
                        CustomerName = g.Key.CustomerName,
                        TotalSales = g.Sum(s => s.TotalAmount),
                        InvoiceCount = g.Count(),
                        AverageInvoiceValue = g.Average(s => s.TotalAmount)
                    })
                    .OrderByDescending(c => c.TotalSales)
                    .Take(count)
                    .ToListAsync();

                return topCustomers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top customers");
                throw;
            }
        }

        public async Task<List<TopProductDto>> GetTopProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var query = _context.SalesInvoiceItems
                    .Include(i => i.SalesInvoice)
                    .Include(i => i.Product)
                    .Where(i => !i.IsDeleted && !i.SalesInvoice.IsDeleted);

                if (fromDate.HasValue)
                    query = query.Where(i => i.SalesInvoice.InvoiceDate >= fromDate.Value);

                if (toDate.HasValue)
                    query = query.Where(i => i.SalesInvoice.InvoiceDate <= toDate.Value);

                var topProducts = await query
                    .GroupBy(i => new { i.ProductId, i.Product.ProductName })
                    .Select(g => new TopProductDto
                    {
                        ProductId = g.Key.ProductId,
                        ProductName = g.Key.ProductName,
                        TotalSales = g.Sum(i => i.TotalPrice),
                        TotalQuantity = g.Sum(i => i.Quantity),
                        InvoiceCount = g.Select(i => i.SalesInvoiceId).Distinct().Count()
                    })
                    .OrderByDescending(p => p.TotalSales)
                    .Take(count)
                    .ToListAsync();

                return topProducts;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting top products");
                throw;
            }
        }

        #endregion

        #region Invoice Items

        public async Task<List<SalesInvoiceItemDto>> GetInvoiceItemsAsync(int invoiceId)
        {
            try
            {
                var items = await _context.SalesInvoiceItems
                    .Include(i => i.Product)
                    .Where(i => i.SalesInvoiceId == invoiceId && !i.IsDeleted)
                    .OrderBy(i => i.LineNumber)
                    .ToListAsync();

                return _mapper.Map<List<SalesInvoiceItemDto>>(items);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting invoice items");
                throw;
            }
        }

        public async Task<SalesInvoiceItemDto> AddInvoiceItemAsync(int invoiceId, CreateSalesInvoiceItemDto createDto)
        {
            try
            {
                var invoice = await _context.SalesInvoices.FindAsync(invoiceId);
                if (invoice == null || invoice.IsPosted)
                {
                    throw new InvalidOperationException("لا يمكن إضافة عناصر لفاتورة مرحلة");
                }

                var item = _mapper.Map<SalesInvoiceItem>(createDto);
                item.SalesInvoiceId = invoiceId;

                // تحديد رقم السطر
                var maxLineNumber = await _context.SalesInvoiceItems
                    .Where(i => i.SalesInvoiceId == invoiceId)
                    .MaxAsync(i => (int?)i.LineNumber) ?? 0;
                item.LineNumber = maxLineNumber + 1;

                // جلب معلومات المنتج
                var product = await _context.Products.FindAsync(item.ProductId);
                if (product != null)
                {
                    item.ProductCode = product.ProductCode;
                    item.ProductName = product.ProductName;
                    item.ProductDescription = product.ProductDescription;
                    item.Unit = product.Unit;
                    item.CostPrice = product.CostPrice;
                }

                // حساب الإجماليات
                var itemTotal = item.Quantity * item.UnitPrice;
                item.DiscountAmount = itemTotal * (item.DiscountPercentage / 100);
                var itemAfterDiscount = itemTotal - item.DiscountAmount;
                item.TaxAmount = itemAfterDiscount * (item.TaxPercentage / 100);
                item.TotalPrice = itemAfterDiscount + item.TaxAmount;
                item.Profit = item.TotalPrice - (item.CostPrice * item.Quantity);

                _context.SalesInvoiceItems.Add(item);
                await _context.SaveChangesAsync();

                // إعادة حساب إجماليات الفاتورة
                await RecalculateInvoiceTotalsAsync(invoiceId);

                return _mapper.Map<SalesInvoiceItemDto>(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding invoice item");
                throw;
            }
        }

        public async Task<SalesInvoiceItemDto?> UpdateInvoiceItemAsync(int itemId, UpdateSalesInvoiceItemDto updateDto)
        {
            try
            {
                var item = await _context.SalesInvoiceItems
                    .Include(i => i.SalesInvoice)
                    .FirstOrDefaultAsync(i => i.Id == itemId && !i.IsDeleted);

                if (item == null) return null;

                if (item.SalesInvoice.IsPosted)
                {
                    throw new InvalidOperationException("لا يمكن تعديل عناصر فاتورة مرحلة");
                }

                _mapper.Map(updateDto, item);

                // إعادة حساب الإجماليات
                var itemTotal = item.Quantity * item.UnitPrice;
                item.DiscountAmount = itemTotal * (item.DiscountPercentage / 100);
                var itemAfterDiscount = itemTotal - item.DiscountAmount;
                item.TaxAmount = itemAfterDiscount * (item.TaxPercentage / 100);
                item.TotalPrice = itemAfterDiscount + item.TaxAmount;
                item.Profit = item.TotalPrice - (item.CostPrice * item.Quantity);

                await _context.SaveChangesAsync();

                // إعادة حساب إجماليات الفاتورة
                await RecalculateInvoiceTotalsAsync(item.SalesInvoiceId);

                return _mapper.Map<SalesInvoiceItemDto>(item);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating invoice item {Id}", itemId);
                throw;
            }
        }

        public async Task<bool> DeleteInvoiceItemAsync(int itemId)
        {
            try
            {
                var item = await _context.SalesInvoiceItems
                    .Include(i => i.SalesInvoice)
                    .FirstOrDefaultAsync(i => i.Id == itemId && !i.IsDeleted);

                if (item == null) return false;

                if (item.SalesInvoice.IsPosted)
                {
                    throw new InvalidOperationException("لا يمكن حذف عناصر فاتورة مرحلة");
                }

                item.IsDeleted = true;
                item.DeletedAt = DateTime.UtcNow;

                await _context.SaveChangesAsync();

                // إعادة حساب إجماليات الفاتورة
                await RecalculateInvoiceTotalsAsync(item.SalesInvoiceId);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting invoice item {Id}", itemId);
                throw;
            }
        }

        #endregion

        #region Validation and Business Logic

        public async Task<bool> ValidateInvoiceAsync(int invoiceId)
        {
            try
            {
                var invoice = await _context.SalesInvoices
                    .Include(s => s.Items)
                    .FirstOrDefaultAsync(s => s.Id == invoiceId && !s.IsDeleted);

                if (invoice == null) return false;

                // التحقق من وجود عناصر
                if (!invoice.Items.Any())
                    return false;

                // التحقق من صحة الكميات والأسعار
                if (invoice.Items.Any(i => i.Quantity <= 0 || i.UnitPrice < 0))
                    return false;

                // التحقق من صحة الإجماليات
                var calculatedTotal = invoice.Items.Sum(i => i.TotalPrice);
                var invoiceTotal = invoice.SubTotal - invoice.DiscountAmount + invoice.TaxAmount;

                return Math.Abs(calculatedTotal - invoiceTotal) < 0.01m;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating invoice {Id}", invoiceId);
                return false;
            }
        }

        public async Task<decimal> CalculateInvoiceTotalAsync(int invoiceId)
        {
            try
            {
                var items = await _context.SalesInvoiceItems
                    .Where(i => i.SalesInvoiceId == invoiceId && !i.IsDeleted)
                    .ToListAsync();

                return items.Sum(i => i.TotalPrice);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calculating invoice total {Id}", invoiceId);
                throw;
            }
        }

        public async Task<bool> CheckCustomerCreditLimitAsync(int customerId, decimal amount)
        {
            try
            {
                var customer = await _context.Customers.FindAsync(customerId);
                if (customer == null) return false;

                return customer.CurrentBalance + amount <= customer.CreditLimit;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking customer credit limit");
                return false;
            }
        }

        public async Task<string> GenerateInvoiceNumberAsync()
        {
            var today = DateTime.Now;
            var prefix = $"INV-{today:yyyyMMdd}";

            var lastInvoice = await _context.SalesInvoices
                .Where(s => s.InvoiceNumber.StartsWith(prefix))
                .OrderByDescending(s => s.InvoiceNumber)
                .FirstOrDefaultAsync();

            if (lastInvoice == null)
            {
                return $"{prefix}-001";
            }

            var lastNumber = lastInvoice.InvoiceNumber.Substring(prefix.Length + 1);
            if (int.TryParse(lastNumber, out var number))
            {
                return $"{prefix}-{(number + 1):D3}";
            }

            return $"{prefix}-001";
        }

        public async Task<bool> CanDeleteInvoiceAsync(int invoiceId)
        {
            try
            {
                var invoice = await _context.SalesInvoices
                    .Include(s => s.Payments)
                    .FirstOrDefaultAsync(s => s.Id == invoiceId && !s.IsDeleted);

                if (invoice == null) return false;

                return !invoice.IsPosted && !invoice.Payments.Any();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if invoice can be deleted");
                return false;
            }
        }

        public async Task<bool> CanEditInvoiceAsync(int invoiceId)
        {
            try
            {
                var invoice = await _context.SalesInvoices.FindAsync(invoiceId);
                if (invoice == null || invoice.IsDeleted) return false;

                return !invoice.IsPosted;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if invoice can be edited");
                return false;
            }
        }

        #endregion

        #region Integration with other modules

        public async Task<bool> UpdateInventoryFromSalesAsync(int invoiceId)
        {
            try
            {
                return await _inventoryService.UpdateInventoryFromSalesAsync(invoiceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating inventory from sales");
                throw;
            }
        }

        public async Task<bool> CreateAccountingEntriesAsync(int invoiceId)
        {
            try
            {
                return await _accountingService.CreateSalesJournalEntryAsync(invoiceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating accounting entries");
                throw;
            }
        }

        public async Task<bool> ReverseAccountingEntriesAsync(int invoiceId)
        {
            try
            {
                return await _accountingService.ReverseSalesJournalEntryAsync(invoiceId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reversing accounting entries");
                throw;
            }
        }

        #endregion

        #region Helper Methods

        private async Task<string> GenerateCustomerCodeAsync()
        {
            var lastCustomer = await _context.Customers
                .Where(c => c.CustomerCode.StartsWith("CUST"))
                .OrderByDescending(c => c.CustomerCode)
                .FirstOrDefaultAsync();

            if (lastCustomer == null)
            {
                return "CUST-001";
            }

            var lastNumber = lastCustomer.CustomerCode.Substring(5);
            if (int.TryParse(lastNumber, out var number))
            {
                return $"CUST-{(number + 1):D3}";
            }

            return "CUST-001";
        }

        private async Task<string> GeneratePaymentNumberAsync()
        {
            var today = DateTime.Now;
            var prefix = $"PAY-{today:yyyyMMdd}";

            var lastPayment = await _context.CustomerPayments
                .Where(p => p.PaymentNumber.StartsWith(prefix))
                .OrderByDescending(p => p.PaymentNumber)
                .FirstOrDefaultAsync();

            if (lastPayment == null)
            {
                return $"{prefix}-001";
            }

            var lastNumber = lastPayment.PaymentNumber.Substring(prefix.Length + 1);
            if (int.TryParse(lastNumber, out var number))
            {
                return $"{prefix}-{(number + 1):D3}";
            }

            return $"{prefix}-001";
        }

        private async Task CalculateInvoiceTotalsAsync(SalesInvoice invoice, List<CreateSalesInvoiceItemDto> items)
        {
            decimal subTotal = 0;
            decimal totalDiscount = 0;
            decimal totalTax = 0;

            foreach (var item in items)
            {
                var itemTotal = item.Quantity * item.UnitPrice;
                var itemDiscount = itemTotal * (item.DiscountPercentage / 100);
                var itemAfterDiscount = itemTotal - itemDiscount;
                var itemTax = itemAfterDiscount * (item.TaxPercentage / 100);

                subTotal += itemTotal;
                totalDiscount += itemDiscount;
                totalTax += itemTax;
            }

            invoice.SubTotal = subTotal;
            invoice.DiscountAmount = totalDiscount + (subTotal * (invoice.DiscountPercentage / 100));
            invoice.TaxAmount = totalTax + ((subTotal - invoice.DiscountAmount) * (invoice.TaxPercentage / 100));
            invoice.TotalAmount = subTotal - invoice.DiscountAmount + invoice.TaxAmount;
            invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;
        }

        private async Task RecalculateInvoiceTotalsAsync(int invoiceId)
        {
            var invoice = await _context.SalesInvoices
                .Include(s => s.Items.Where(i => !i.IsDeleted))
                .FirstOrDefaultAsync(s => s.Id == invoiceId);

            if (invoice == null) return;

            var subTotal = invoice.Items.Sum(i => i.Quantity * i.UnitPrice);
            var totalItemDiscount = invoice.Items.Sum(i => i.DiscountAmount);
            var totalItemTax = invoice.Items.Sum(i => i.TaxAmount);

            invoice.SubTotal = subTotal;
            invoice.DiscountAmount = totalItemDiscount + (subTotal * (invoice.DiscountPercentage / 100));
            invoice.TaxAmount = totalItemTax + ((subTotal - invoice.DiscountAmount) * (invoice.TaxPercentage / 100));
            invoice.TotalAmount = subTotal - invoice.DiscountAmount + invoice.TaxAmount;
            invoice.RemainingAmount = invoice.TotalAmount - invoice.PaidAmount;

            await _context.SaveChangesAsync();
        }

        #endregion
    }
}