<UserControl x:Class="GlassFactoryAccounting.Views.ItemManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect" Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Auto"
                  PanningMode="VerticalOnly"
                  Background="#F5F5F5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📋" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إدارة الأصناف" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                    <TextBlock Text="إضافة وإدارة أصناف المخزون" FontSize="14" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- نموذج إضافة صنف جديد -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول -->
                <Grid Grid.Row="0" Margin="12,12,12,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" MinWidth="180"/>
                        <ColumnDefinition Width="1*" MinWidth="180"/>
                        <ColumnDefinition Width="1*" MinWidth="180"/>
                        <ColumnDefinition Width="1*" MinWidth="180"/>
                        <ColumnDefinition Width="1*" MinWidth="180"/>
                        <ColumnDefinition Width="140"/>
                    </Grid.ColumnDefinitions>

                    <!-- كود الصنف -->
                    <Border Grid.Column="0" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="كود الصنف:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtItemCode" Height="45"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="15" Padding="12,8" IsReadOnly="True"
                                     Background="#F8F9FA" BorderBrush="#BDC3C7" BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- اسم الصنف -->
                    <Border Grid.Column="1" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="اسم الصنف:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtItemName" Height="45"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="15" Padding="12,8"
                                     BorderBrush="#3498DB" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- المخزن -->
                    <Border Grid.Column="2" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="المخزن:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <ComboBox x:Name="CmbWarehouse" Height="45"
                                      VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                      FontSize="15" Padding="12,8" DisplayMemberPath="Name" SelectedValuePath="Id"
                                      BorderBrush="#3498DB" BorderThickness="3"
                                      Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- وحدة القياس -->
                    <Border Grid.Column="3" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="وحدة القياس:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <ComboBox x:Name="CmbUnitOfMeasure" Height="45"
                                      VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                      FontSize="15" Padding="12,8"
                                      BorderBrush="#3498DB" BorderThickness="3"
                                      Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- محتوى الصندوق -->
                    <Border Grid.Column="4" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="محتوى الصندوق:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtBoxContent" Height="45"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="15" Padding="12,8"
                                     BorderBrush="#3498DB" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- له أبعاد -->
                    <Border Grid.Column="5" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel VerticalAlignment="Center">
                            <TextBlock Text="الأبعاد:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <CheckBox x:Name="ChkHasDimensions" Content="له أبعاد؟" FontWeight="Bold"
                                      FontSize="14" HorizontalAlignment="Center"
                                      Checked="ChkHasDimensions_Checked" Unchecked="ChkHasDimensions_Unchecked"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- الصف الثاني - الأبعاد -->
                <Grid Grid.Row="1" x:Name="DimensionsPanel" Visibility="Collapsed" Margin="12,0,12,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                    </Grid.ColumnDefinitions>

                    <!-- الطول -->
                    <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="الطول (ملم):" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtLength" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" TextChanged="Dimensions_TextChanged"
                                     BorderBrush="#E67E22" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- العرض -->
                    <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="العرض (ملم):" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtWidth" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" TextChanged="Dimensions_TextChanged"
                                     BorderBrush="#E67E22" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- المساحة -->
                    <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="المساحة (م²):" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtArea" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" IsReadOnly="True"
                                     Background="#F8F9FA" BorderBrush="#BDC3C7" BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- الوصف -->
                    <Border Grid.Column="3" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="الوصف:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtDescription" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#9B59B6" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- الصف الثالث - الأزرار -->
                <Border Grid.Row="2" Margin="10,0,10,10" Padding="15" Background="#F8F9FA"
                        BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="BtnAddItem" Content="➕ إضافة صنف"
                                Style="{StaticResource SuccessButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Margin="0,0,15,0" Click="BtnAddItem_Click"
                                Background="#27AE60" Foreground="White" BorderThickness="0"/>
                        <Button x:Name="BtnClearForm" Content="🗑️ مسح النموذج"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Background="#95A5A6" Foreground="White" BorderThickness="0"
                                Click="BtnClearForm_Click"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- قائمة الأصناف -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 قائمة الأصناف" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                    <Button x:Name="BtnRefresh" Content="🔄 تحديث" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="20,0,0,0" Click="BtnRefresh_Click"/>
                </StackPanel>

                <DataGrid Grid.Row="1" x:Name="ItemsDataGrid" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="المعرف" Binding="{Binding Id}" Width="50"/>
                        <DataGridTextColumn Header="كود الصنف" Binding="{Binding Code}" Width="80"/>
                        <DataGridTextColumn Header="اسم الصنف" Binding="{Binding Name}" Width="120"/>
                        <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="100"/>
                        <DataGridTextColumn Header="وحدة القياس" Binding="{Binding UnitOfMeasure}" Width="80"/>
                        <DataGridTextColumn Header="محتوى الصندوق" Binding="{Binding BoxContent}" Width="90"/>
                        <DataGridCheckBoxColumn Header="له أبعاد" Binding="{Binding HasDimensions}" Width="60"/>
                        <DataGridTextColumn Header="الطول (ملم)" Binding="{Binding Length}" Width="80"/>
                        <DataGridTextColumn Header="العرض (ملم)" Binding="{Binding Width}" Width="80"/>
                        <DataGridTextColumn Header="المساحة (م²)" Binding="{Binding Area, StringFormat=F4}" Width="80"/>
                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="120"/>
                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="90"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="✏️" 
                                                Style="{StaticResource PrimaryButtonStyle}"
                                                Margin="0,0,5,0" Padding="8,4"
                                                Click="BtnEditItem_Click"
                                                Tag="{Binding}"/>
                                        <Button Content="🗑️"
                                                Background="#F44336" Foreground="White"
                                                BorderThickness="0" Padding="8,4" Cursor="Hand"
                                                Click="BtnDeleteItem_Click"
                                                Tag="{Binding}">
                                            <Button.Template>
                                                <ControlTemplate TargetType="Button">
                                                    <Border Background="{TemplateBinding Background}" CornerRadius="3">
                                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                    </Border>
                                                </ControlTemplate>
                                            </Button.Template>
                                        </Button>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
