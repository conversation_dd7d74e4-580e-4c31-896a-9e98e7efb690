﻿#pragma checksum "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "86CF11D3D42011B173009AE0322EE7A2AEB23F8F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ManufacturingOrderDetailsView
    /// </summary>
    public partial class ManufacturingOrderDetailsView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtOrderTitle;
        
        #line default
        #line hidden
        
        
        #line 29 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtOrderNumberHeader;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtCustomerName;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtInvoiceNumber;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtOrderDate;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtOrderStatus;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtGlassType;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtThickness;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgGlassPanelsDetails;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalGlassMeters;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalGlassValue;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgRequiredSizesDetails;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalRequiredMetersDetails;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalLinearMetersDetails;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid gridServicesDetails;
        
        #line default
        #line hidden
        
        
        #line 172 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtFilmService;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtDoubleService;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtPrintService;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtOtherServices;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgServiceCostsDetails;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalServiceCostsDetails;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgAdditionalCostsDetails;
        
        #line default
        #line hidden
        
        
        #line 238 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalAdditionalCostsDetails;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryServiceCosts;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryAdditionalCosts;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryGlassValue;
        
        #line default
        #line hidden
        
        
        #line 274 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryTotalCost;
        
        #line default
        #line hidden
        
        
        #line 277 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryTotalMeters;
        
        #line default
        #line hidden
        
        
        #line 280 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSummaryPricePerMeter;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrintDetails;
        
        #line default
        #line hidden
        
        
        #line 291 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSavePDFDetails;
        
        #line default
        #line hidden
        
        
        #line 296 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnClose;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/GlassFactoryAccounting;V1.0.0.0;component/views/manufacturingorderdetailsview.xa" +
                    "ml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtOrderTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.txtOrderNumberHeader = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.txtCustomerName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.txtInvoiceNumber = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.txtOrderDate = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.txtOrderStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.txtGlassType = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.txtThickness = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.dgGlassPanelsDetails = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 10:
            this.txtTotalGlassMeters = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.txtTotalGlassValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.dgRequiredSizesDetails = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 13:
            this.txtTotalRequiredMetersDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.txtTotalLinearMetersDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.gridServicesDetails = ((System.Windows.Controls.Grid)(target));
            return;
            case 16:
            this.txtFilmService = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.txtDoubleService = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.txtPrintService = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 19:
            this.txtOtherServices = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.dgServiceCostsDetails = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 21:
            this.txtTotalServiceCostsDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.dgAdditionalCostsDetails = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 23:
            this.txtTotalAdditionalCostsDetails = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.txtSummaryServiceCosts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.txtSummaryAdditionalCosts = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.txtSummaryGlassValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.txtSummaryTotalCost = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.txtSummaryTotalMeters = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.txtSummaryPricePerMeter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 30:
            this.btnPrintDetails = ((System.Windows.Controls.Button)(target));
            
            #line 289 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
            this.btnPrintDetails.Click += new System.Windows.RoutedEventHandler(this.BtnPrintDetails_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.btnSavePDFDetails = ((System.Windows.Controls.Button)(target));
            
            #line 294 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
            this.btnSavePDFDetails.Click += new System.Windows.RoutedEventHandler(this.BtnSavePDFDetails_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.btnClose = ((System.Windows.Controls.Button)(target));
            
            #line 299 "..\..\..\..\Views\ManufacturingOrderDetailsView.xaml"
            this.btnClose.Click += new System.Windows.RoutedEventHandler(this.BtnClose_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

