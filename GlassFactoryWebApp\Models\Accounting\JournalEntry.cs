using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج قيد اليومية
    /// </summary>
    public class JournalEntry : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string EntryNumber { get; set; } = string.Empty;

        [Required]
        public DateTime EntryDate { get; set; } = DateTime.Now;

        [Required]
        [MaxLength(500)]
        public string Description { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,2)")]
        public decimal TotalAmount { get; set; } = 0;

        [MaxLength(50)]
        public string EntryType { get; set; } = "عادي"; // عادي، افتتاحي، إقفال، تسوية

        [MaxLength(100)]
        public string? ReferenceNumber { get; set; }

        [MaxLength(50)]
        public string? ReferenceType { get; set; } // فاتورة مبيعات، فاتورة مشتريات، سند قبض، سند صرف

        public int? ReferenceId { get; set; }

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedDate { get; set; }

        [MaxLength(100)]
        public string? PostedBy { get; set; }

        public bool IsReversed { get; set; } = false;

        public DateTime? ReversedDate { get; set; }

        [MaxLength(100)]
        public string? ReversedBy { get; set; }

        public int? ReversalEntryId { get; set; }

        [MaxLength(50)]
        public string Status { get; set; } = "مسودة"; // مسودة، مرحل، ملغي، معكوس

        [MaxLength(100)]
        public string? CostCenter { get; set; }

        [MaxLength(100)]
        public string? Project { get; set; }

        [MaxLength(500)]
        public string? AdditionalNotes { get; set; }

        // Approval workflow
        public bool RequiresApproval { get; set; } = false;

        public bool IsApproved { get; set; } = false;

        public DateTime? ApprovedDate { get; set; }

        [MaxLength(100)]
        public string? ApprovedBy { get; set; }

        [MaxLength(500)]
        public string? ApprovalNotes { get; set; }

        // Foreign Keys
        public virtual ApplicationUser CreatedByUser { get; set; } = null!;

        [ForeignKey("ReversalEntryId")]
        public virtual JournalEntry? ReversalEntry { get; set; }

        // Navigation Properties
        public virtual ICollection<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();

        // Computed Properties
        public bool IsBalanced
        {
            get
            {
                var totalDebit = Details.Sum(d => d.DebitAmount);
                var totalCredit = Details.Sum(d => d.CreditAmount);
                return Math.Abs(totalDebit - totalCredit) < 0.01m;
            }
        }

        public decimal TotalDebit => Details.Sum(d => d.DebitAmount);
        public decimal TotalCredit => Details.Sum(d => d.CreditAmount);
    }
}
