namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج المنتجات - مخصص لمنتجات الزجاج
    /// </summary>
    public class Product : BaseEntity
    {
        private string _name = string.Empty;
        private string _code = string.Empty;
        private string _category = string.Empty;
        private decimal _costPrice;
        private decimal _sellingPrice;
        private int _stockQuantity;
        private int _minimumStock;
        private string _unit = string.Empty;
        private string _description = string.Empty;
        private GlassType _glassType;
        private decimal _thickness;
        private string _dimensions = string.Empty;
        private string _color = string.Empty;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        public string Category
        {
            get => _category;
            set => SetProperty(ref _category, value);
        }

        public decimal CostPrice
        {
            get => _costPrice;
            set => SetProperty(ref _costPrice, value);
        }

        public decimal SellingPrice
        {
            get => _sellingPrice;
            set => SetProperty(ref _sellingPrice, value);
        }

        public int StockQuantity
        {
            get => _stockQuantity;
            set => SetProperty(ref _stockQuantity, value);
        }

        public int MinimumStock
        {
            get => _minimumStock;
            set => SetProperty(ref _minimumStock, value);
        }

        public string Unit
        {
            get => _unit;
            set => SetProperty(ref _unit, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public GlassType GlassType
        {
            get => _glassType;
            set => SetProperty(ref _glassType, value);
        }

        public decimal Thickness
        {
            get => _thickness;
            set => SetProperty(ref _thickness, value);
        }

        public string Dimensions
        {
            get => _dimensions;
            set => SetProperty(ref _dimensions, value);
        }

        public string Color
        {
            get => _color;
            set => SetProperty(ref _color, value);
        }
    }

    /// <summary>
    /// أنواع الزجاج
    /// </summary>
    public enum GlassType
    {
        شفاف = 1,
        ملون = 2,
        مقسى = 3,
        مزدوج = 4,
        عاكس = 5,
        أمان = 6,
        ديكوري = 7,
        مقاوم_للحريق = 8
    }
}
