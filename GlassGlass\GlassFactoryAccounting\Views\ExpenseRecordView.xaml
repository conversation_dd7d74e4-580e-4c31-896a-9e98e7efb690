<UserControl x:Class="GlassFactoryAccounting.Views.ExpenseRecordView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect" Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20" Background="#F5F5F5">
        <StackPanel>
            <!-- العنوان -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="💸" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="تسجيل المصروفات" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                        <TextBlock Text="تسجيل كل المصروفات الفعلية مع البيانات التفصيلية" FontSize="14" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- نموذج تسجيل المصروف -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="➕ تسجيل مصروف جديد" FontSize="18" FontWeight="Bold" 
                               Margin="0,0,0,20" Foreground="#2C3E50"/>

                    <!-- الصف الأول: المصروف الرئيسي والفرعي والفرع -->
                    <Grid Grid.Row="1" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- المصروف الرئيسي -->
                        <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="المصروف الرئيسي:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <ComboBox x:Name="CmbMainExpense" Height="50"
                                          VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                          FontSize="16" Padding="15,10"
                                          BorderBrush="#E74C3C" BorderThickness="3"
                                          Background="White" SelectionChanged="CmbMainExpense_SelectionChanged"/>
                            </StackPanel>
                        </Border>

                        <!-- المصروف الفرعي -->
                        <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="المصروف الفرعي:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <ComboBox x:Name="CmbSubExpense" Height="50"
                                          VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                          FontSize="16" Padding="15,10"
                                          BorderBrush="#27AE60" BorderThickness="3"
                                          Background="White"/>
                            </StackPanel>
                        </Border>

                        <!-- فرع الشركة -->
                        <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="فرع الشركة:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <ComboBox x:Name="CmbBranch" Grid.Column="0" Height="50"
                                              VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                              FontSize="16" Padding="15,10"
                                              BorderBrush="#9B59B6" BorderThickness="3"
                                              Background="White" Margin="0,0,5,0"/>
                                    <Button x:Name="BtnAddBranch" Grid.Column="1" Content="➕" Width="50" Height="50"
                                            Background="#17A2B8" Foreground="White" BorderThickness="0"
                                            FontSize="18" FontWeight="Bold" Click="BtnAddBranch_Click"/>
                                </Grid>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- الصف الثاني: قيمة المصروف ورقم الفاتورة والتاريخ -->
                    <Grid Grid.Row="2" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- قيمة المصروف -->
                        <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="قيمة المصروف:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <TextBox x:Name="TxtAmount" Height="50"
                                         VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                         FontSize="16" Padding="15,10"
                                         BorderBrush="#F39C12" BorderThickness="3"
                                         Background="White"/>
                            </StackPanel>
                        </Border>

                        <!-- رقم الفاتورة -->
                        <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <TextBox x:Name="TxtInvoiceNumber" Height="50"
                                         VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                         FontSize="16" Padding="15,10"
                                         BorderBrush="#1ABC9C" BorderThickness="3"
                                         Background="White"/>
                            </StackPanel>
                        </Border>

                        <!-- التاريخ والوقت -->
                        <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="التاريخ والوقت:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <DatePicker x:Name="DpDateTime" Height="50"
                                            VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                            FontSize="16" Padding="15,10"
                                            BorderBrush="#E74C3C" BorderThickness="3"
                                            Background="White"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- الصف الثالث: المسؤول وطريقة السداد وحالة المصروف -->
                    <Grid Grid.Row="3" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- المسؤول عن المصروف -->
                        <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="المسؤول عن المصروف:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <ComboBox x:Name="CmbResponsiblePerson" Grid.Column="0" Height="50"
                                              VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                              FontSize="16" Padding="15,10"
                                              BorderBrush="#6f42c1" BorderThickness="3"
                                              Background="White" Margin="0,0,5,0"
                                              DisplayMemberPath="Name" SelectedValuePath="Id"/>
                                    <Button x:Name="BtnAddResponsiblePerson" Grid.Column="1" Content="👤" Width="50" Height="50"
                                            Background="#6f42c1" Foreground="White" BorderThickness="0"
                                            FontSize="18" FontWeight="Bold" Click="BtnAddResponsiblePerson_Click"/>
                                </Grid>
                            </StackPanel>
                        </Border>

                        <!-- طريقة السداد -->
                        <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="طريقة السداد:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <ComboBox x:Name="CmbPaymentMethod" Height="50"
                                          VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                          FontSize="16" Padding="15,10"
                                          BorderBrush="#28a745" BorderThickness="3"
                                          Background="White">
                                    <ComboBoxItem Content="كاش"/>
                                    <ComboBoxItem Content="بنك"/>
                                    <ComboBoxItem Content="مستحق الدفع"/>
                                </ComboBox>
                            </StackPanel>
                        </Border>

                        <!-- حالة المصروف -->
                        <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="حالة المصروف:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <ComboBox x:Name="CmbExpenseStatus" Height="50"
                                          VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                          FontSize="16" Padding="15,10"
                                          BorderBrush="#dc3545" BorderThickness="3"
                                          Background="White" SelectedIndex="1">
                                    <ComboBoxItem Content="مسدد"/>
                                    <ComboBoxItem Content="مستحق"/>
                                </ComboBox>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- الصف الرابع: الملاحظات -->
                    <Border Grid.Row="4" Margin="8,0,8,20" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="ملاحظات:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtNotes" Height="80"
                                     VerticalContentAlignment="Top" HorizontalContentAlignment="Right"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#8E44AD" BorderThickness="3"
                                     Background="White" TextWrapping="Wrap"
                                     AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Border>

                    <!-- الأزرار -->
                    <StackPanel Grid.Row="5" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button x:Name="BtnSave" Content="💾 حفظ" 
                                Style="{StaticResource SuccessButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Margin="0,0,15,0" Click="BtnSave_Click"/>
                        <Button x:Name="BtnClear" Content="🗑️ مسح النموذج" 
                                Style="{StaticResource PrimaryButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Background="#95A5A6" Click="BtnClear_Click"/>
                    </StackPanel>
                </Grid>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
