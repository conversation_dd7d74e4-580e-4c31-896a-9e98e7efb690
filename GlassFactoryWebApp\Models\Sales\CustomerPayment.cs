using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج دفعة العميل
    /// </summary>
    public class CustomerPayment : BaseEntity
    {
        [Required]
        public int CustomerId { get; set; }

        public int? SalesInvoiceId { get; set; }

        [Required]
        [MaxLength(20)]
        public string PaymentNumber { get; set; } = string.Empty;

        [Required]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [Column(TypeName = "decimal(18,2)")]
        public decimal PaymentAmount { get; set; } = 0;

        [Required]
        [MaxLength(50)]
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، شيك، تحويل، بطاقة ائتمان

        [MaxLength(100)]
        public string? CheckNumber { get; set; }

        [MaxLength(100)]
        public string? BankName { get; set; }

        public DateTime? CheckDate { get; set; }

        [MaxLength(100)]
        public string? TransferReference { get; set; }

        [MaxLength(500)]
        public string? PaymentNotes { get; set; }

        [MaxLength(50)]
        public string PaymentStatus { get; set; } = "مؤكد"; // مؤكد، معلق، مرفوض، ملغي

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedAt { get; set; }

        [MaxLength(100)]
        public string? PostedBy { get; set; }

        [MaxLength(100)]
        public string? ReceivedBy { get; set; }

        // Foreign Keys
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;

        [ForeignKey("SalesInvoiceId")]
        public virtual SalesInvoice? SalesInvoice { get; set; }

        public virtual ApplicationUser CreatedByUser { get; set; } = null!;
    }
}
