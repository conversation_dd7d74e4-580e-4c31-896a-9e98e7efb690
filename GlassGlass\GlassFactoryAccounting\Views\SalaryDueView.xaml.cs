using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة الرواتب المستحقة
    /// </summary>
    public partial class SalaryDueView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;

        public SalaryDueView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadSalaryDue();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ بداية الشهر الحالي كافتراضي
            DpDueDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.ItemsSource = employees;
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadSalaryDue()
        {
            try
            {
                var salaryDues = _payrollService.GetAllSalaryDue();
                SalariesDataGrid.ItemsSource = salaryDues;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الرواتب المستحقة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbEmployee_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbEmployee.SelectedItem is Employee selectedEmployee)
            {
                TxtEmployeeCode.Text = selectedEmployee.EmployeeCode;
                TxtSalaryAmount.Text = selectedEmployee.BasicSalary.ToString("N2");
                TxtHireDate.Text = selectedEmployee.HireDate.ToString("yyyy/MM/dd");
                TxtPosition.Text = selectedEmployee.Position;
                TxtBranch.Text = selectedEmployee.Branch;
            }
            else
            {
                ClearEmployeeFields();
            }
        }

        private void ClearEmployeeFields()
        {
            TxtEmployeeCode.Clear();
            TxtSalaryAmount.Clear();
            TxtHireDate.Clear();
            TxtPosition.Clear();
            TxtBranch.Clear();
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedEmployee = CmbEmployee.SelectedItem as Employee;

                var salaryDue = new SalaryDue
                {
                    EmployeeId = selectedEmployee.Id,
                    EmployeeName = selectedEmployee.Name,
                    EmployeeCode = selectedEmployee.EmployeeCode,
                    SalaryAmount = selectedEmployee.BasicSalary,
                    DueDate = DpDueDate.SelectedDate ?? DateTime.Now,
                    Position = selectedEmployee.Position,
                    Branch = selectedEmployee.Branch,
                    HireDate = selectedEmployee.HireDate,
                    IsPaid = false,
                    Notes = TxtNotes.Text.Trim()
                };

                if (_payrollService.AddSalaryDue(salaryDue))
                {
                    MessageBox.Show("تم حفظ الراتب المستحق بنجاح!", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    ClearForm();
                    LoadSalaryDue();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الراتب المستحق!", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الراتب المستحق: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbEmployee.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbEmployee.Focus();
                return false;
            }

            if (!DpDueDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ استحقاق الراتب", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpDueDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            CmbEmployee.SelectedIndex = -1;
            ClearEmployeeFields();
            DpDueDate.SelectedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            TxtNotes.Clear();
            CmbEmployee.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeleteSalaryDue_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int salaryDueId)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذا الراتب المستحق؟", "تأكيد الحذف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (_payrollService.DeleteSalaryDue(salaryDueId))
                        {
                            MessageBox.Show("تم حذف الراتب المستحق بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadSalaryDue();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الراتب المستحق!", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الراتب المستحق: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditSalaryDue_Click(object sender, RoutedEventArgs e)
        {
            // TODO: تنفيذ منطق التعديل هنا حسب الحاجة
            MessageBox.Show("ميزة التعديل غير مفعلة حالياً. يرجى استكمال منطق التعديل إذا لزم.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
