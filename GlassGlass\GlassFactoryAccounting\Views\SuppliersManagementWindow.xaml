<Window x:Class="GlassFactoryAccounting.Views.SuppliersManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة الموردين" Height="600" Width="800"
        FlowDirection="RightToLeft" WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🏭 إدارة الموردين" FontSize="24" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                
                <Button x:Name="BtnAddSupplier" Content="➕ إضافة مورد جديد" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="15,8" Margin="20,0,0,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnAddSupplier_Click"/>
                
                <Button x:Name="BtnRefresh" Content="🔄 تحديث" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,8" Margin="10,0,0,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnRefresh_Click"/>
            </StackPanel>
        </Border>
        
        <!-- قائمة الموردين -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="📋 قائمة الموردين" FontSize="18" FontWeight="Bold" 
                         Margin="0,0,0,15" Foreground="{StaticResource PrimaryBrush}"/>
                
                <DataGrid x:Name="SuppliersDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80"/>
                        <DataGridTextColumn Header="اسم المورد" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                        <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="200"/>
                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="180"/>
                        <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat='{}{0:F2} ج.م'}" Width="120"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="✏️ تعديل" Background="{StaticResource WarningBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2" FontSize="10"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnEditSupplier_Click" Tag="{Binding}"/>
                                        
                                        <Button Content="🗑️ حذف" Background="{StaticResource DangerBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2" FontSize="10"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnDeleteSupplier_Click" Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        
        <!-- أزرار التحكم -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnClose" Content="إغلاق" 
                        Background="{StaticResource DangerBrush}" Foreground="White"
                        Padding="20,10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnClose_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
