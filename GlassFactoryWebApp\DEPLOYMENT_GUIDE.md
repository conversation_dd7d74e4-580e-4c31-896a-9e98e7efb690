# 🚀 دليل النشر على Oracle Cloud Free Tier

## 📋 نظرة عامة

هذا الدليل يوضح كيفية نشر نظام حسابات مصنع الزجاج على Oracle Cloud Free Tier بشكل مجاني ومتاح 24/7.

---

## 🎯 المتطلبات

### Oracle Cloud Account
- حساب Oracle Cloud مجاني
- VM.Standard.E2.1.Micro (1 OCPU, 1GB RAM)
- 47GB Block Volume
- 10TB Outbound Transfer/month

### Domain Name (اختياري)
- دومين مجاني من Freenom أو No-IP
- أو استخدام IP العام مباشرة

---

## 🔧 خطوات النشر

### 1. إنشاء Oracle Cloud Instance

```bash
# تسجيل الدخول لـ Oracle Cloud Console
# إنشاء Compute Instance جديد
# اختيار Ubuntu 22.04 LTS
# اختيار VM.Standard.E2.1.Micro (Always Free)
# إعداد SSH Key
# فتح Ports: 80, 443, 22
```

### 2. الاتصال بالخادم

```bash
# الاتصال عبر SSH
ssh -i your-key.pem ubuntu@your-server-ip

# تحديث النظام
sudo apt update && sudo apt upgrade -y
```

### 3. تشغيل سكريبت النشر التلقائي

```bash
# تحميل المشروع
git clone https://github.com/your-repo/GlassFactoryWebApp.git
cd GlassFactoryWebApp

# تشغيل سكريبت النشر
chmod +x deployment/oracle-cloud-setup.sh
./deployment/oracle-cloud-setup.sh
```

### 4. إعداد Domain Name (اختياري)

```bash
# إذا كان لديك دومين، قم بتوجيهه للـ IP العام
# ثم قم بتحديث DOMAIN_NAME في السكريبت
export DOMAIN_NAME="your-domain.com"
```

---

## 🐳 النشر باستخدام Docker

### 1. تثبيت Docker

```bash
# تثبيت Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# تثبيت Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. تشغيل التطبيق

```bash
# بناء وتشغيل جميع الخدمات
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f

# إيقاف الخدمات
docker-compose down
```

---

## 🔒 إعداد SSL Certificate

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx

# الحصول على شهادة SSL
sudo certbot --nginx -d your-domain.com

# تجديد تلقائي
sudo crontab -e
# إضافة: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 📊 المراقبة والصيانة

### مراقبة الخدمات

```bash
# حالة التطبيق
sudo systemctl status glass-factory-app

# سجلات التطبيق
sudo journalctl -u glass-factory-app -f

# حالة قاعدة البيانات
sudo systemctl status postgresql

# حالة Nginx
sudo systemctl status nginx
```

### النسخ الاحتياطية

```bash
# نسخ احتياطي يدوي
sudo /usr/local/bin/backup-glass-factory-app.sh

# عرض النسخ الاحتياطية
ls -la /var/backups/glass-factory-app/

# استعادة من نسخة احتياطية
sudo -u postgres psql -d glass_factory_db < /var/backups/glass-factory-app/db_backup_YYYYMMDD_HHMMSS.sql
```

---

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. التطبيق لا يعمل
```bash
# فحص حالة الخدمة
sudo systemctl status glass-factory-app

# إعادة تشغيل
sudo systemctl restart glass-factory-app

# فحص السجلات
sudo journalctl -u glass-factory-app --since "1 hour ago"
```

#### 2. مشكلة في قاعدة البيانات
```bash
# فحص PostgreSQL
sudo systemctl status postgresql

# الاتصال بقاعدة البيانات
sudo -u postgres psql -d glass_factory_db

# فحص الاتصالات
sudo -u postgres psql -c "SELECT * FROM pg_stat_activity;"
```

#### 3. مشكلة في Nginx
```bash
# فحص إعدادات Nginx
sudo nginx -t

# إعادة تحميل الإعدادات
sudo systemctl reload nginx

# فحص السجلات
sudo tail -f /var/log/nginx/glass-factory-app.error.log
```

---

## 📈 تحسين الأداء

### تحسين PostgreSQL
```sql
-- في ملف postgresql.conf
shared_buffers = 128MB
effective_cache_size = 512MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
```

### تحسين Nginx
```nginx
# في ملف nginx.conf
worker_processes auto;
worker_connections 1024;
keepalive_timeout 65;
gzip on;
gzip_types text/plain text/css application/json application/javascript;
```

### تحسين .NET Core
```json
// في appsettings.Production.json
{
  "Kestrel": {
    "Limits": {
      "MaxConcurrentConnections": 100,
      "MaxConcurrentUpgradedConnections": 100
    }
  }
}
```

---

## 🔐 الأمان

### إعدادات جدار الحماية
```bash
# تمكين UFW
sudo ufw enable

# السماح بـ SSH
sudo ufw allow OpenSSH

# السماح بـ HTTP/HTTPS
sudo ufw allow 'Nginx Full'

# فحص الحالة
sudo ufw status
```

### تحديثات الأمان
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تحديث Docker images
docker-compose pull
docker-compose up -d
```

---

## 📞 الدعم والمساعدة

### معلومات مهمة
- **المطور:** حسام محمد حسان أحمد
- **التقنيات:** ASP.NET Core 8.0 + React + PostgreSQL
- **الاستضافة:** Oracle Cloud Free Tier
- **الترخيص:** ملكية خاصة

### ملفات مهمة
```
/var/www/glass-factory-app/          # ملفات التطبيق
/var/log/glass-factory-app/          # سجلات التطبيق
/var/backups/glass-factory-app/      # النسخ الاحتياطية
/etc/nginx/sites-available/          # إعدادات Nginx
/etc/systemd/system/                 # خدمات النظام
```

### أوامر مفيدة
```bash
# إعادة تشغيل جميع الخدمات
sudo systemctl restart glass-factory-app nginx postgresql

# فحص استخدام الموارد
htop
df -h
free -h

# فحص الشبكة
netstat -tulpn
ss -tulpn
```

---

## 🎉 النتيجة النهائية

بعد اكتمال النشر، ستحصل على:

✅ **تطبيق ويب متكامل** يعمل 24/7  
✅ **قاعدة بيانات PostgreSQL** محسنة للأداء  
✅ **شهادة SSL مجانية** من Let's Encrypt  
✅ **نسخ احتياطية تلقائية** يومية  
✅ **مراقبة تلقائية** للخدمات  
✅ **أمان عالي** مع جدار حماية  
✅ **أداء محسن** للاستخدام المكثف  

**🌐 الوصول للتطبيق:** `https://your-domain.com`  
**📊 لوحة المراقبة:** `https://your-domain.com:3001` (Grafana)  
**📋 إدارة السجلات:** `https://your-domain.com:5601` (Kibana)  

---

**🚀 نظام حسابات مصنع الزجاج جاهز للاستخدام على Oracle Cloud!**
