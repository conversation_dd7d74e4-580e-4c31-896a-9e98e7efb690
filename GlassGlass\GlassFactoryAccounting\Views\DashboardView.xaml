<UserControl x:Class="GlassFactoryAccounting.Views.DashboardView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900"
             FlowDirection="RightToLeft">
    
    <UserControl.Resources>
        <!-- تعريف الألوان -->
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        
        <!-- نمط البطاقات -->
        <Style x:Key="DashboardCardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <StackPanel Margin="20">
            <!-- ترحيب -->
            <TextBlock Text="مرحباً بك في نظام حسابات مصنع الزجاج"
                     FontSize="28" FontWeight="Bold"
                     Foreground="{StaticResource PrimaryBrush}"
                     HorizontalAlignment="Center" Margin="0,0,0,20"/>

            <!-- بيانات الشركة -->
            <Border Style="{StaticResource DashboardCardStyle}" Margin="0,0,0,30">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="🏢 بيانات الشركة" FontSize="18" FontWeight="Bold"
                             Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,15"/>

                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal">
                            <StackPanel Margin="0,0,30,0">
                                <TextBlock Text="اسم الشركة:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="TxtCompanyName" Text="مصنع الزجاج" FontSize="16" Foreground="{StaticResource PrimaryBrush}"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,30,0">
                                <TextBlock Text="العنوان:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="TxtCompanyAddress" Text="المنطقة الصناعية" FontSize="14"/>
                            </StackPanel>

                            <StackPanel Margin="0,0,30,0">
                                <TextBlock Text="الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="TxtCompanyPhone" Text="+*********" FontSize="14"/>
                            </StackPanel>

                            <StackPanel>
                                <TextBlock Text="الإيميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBlock x:Name="TxtCompanyEmail" Text="<EMAIL>" FontSize="14"/>
                            </StackPanel>
                        </StackPanel>

                        <Button Grid.Column="1" x:Name="BtnEditCompanyInfo" Content="✏️ تعديل بيانات الشركة"
                                Background="{StaticResource WarningBrush}" Foreground="White"
                                Padding="15,8" FontWeight="Bold"
                                BorderThickness="0" Cursor="Hand" Click="BtnEditCompanyInfo_Click"/>
                    </Grid>
                </Grid>
            </Border>

            <!-- إحصائيات سريعة -->
            <TextBlock Text="الإحصائيات السريعة" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"/>
            
            <UniformGrid Columns="4" Margin="0,0,0,30">
                <!-- إجمالي المبيعات اليوم -->
                <Border Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="💰" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="مبيعات اليوم" FontSize="14" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="TxtTodaySales" Text="0.00 ج.م" FontSize="18" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="{StaticResource SuccessBrush}"/>
                    </StackPanel>
                </Border>
                
                <!-- عدد الفواتير اليوم -->
                <Border Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="📄" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="فواتير اليوم" FontSize="14" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="TxtTodayInvoices" Text="0" FontSize="18" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="{StaticResource PrimaryBrush}"/>
                    </StackPanel>
                </Border>
                
                <!-- المنتجات المنخفضة -->
                <Border Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="⚠️" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="منتجات منخفضة" FontSize="14" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="TxtLowStockProducts" Text="0" FontSize="18" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="{StaticResource WarningBrush}"/>
                    </StackPanel>
                </Border>
                
                <!-- إجمالي العملاء -->
                <Border Style="{StaticResource DashboardCardStyle}">
                    <StackPanel>
                        <TextBlock Text="👥" FontSize="32" HorizontalAlignment="Center"/>
                        <TextBlock Text="إجمالي العملاء" FontSize="14" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                        <TextBlock x:Name="TxtTotalCustomers" Text="0" FontSize="18" FontWeight="Bold" 
                                 HorizontalAlignment="Center" Foreground="{StaticResource PrimaryBrush}"/>
                    </StackPanel>
                </Border>
            </UniformGrid>
            
            <!-- الإجراءات السريعة -->
            <TextBlock Text="الإجراءات السريعة" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"/>
            
            <UniformGrid Columns="3" Margin="0,0,0,30">
                <Button x:Name="BtnNewSale" Content="فاتورة مبيعات جديدة" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="15,10" Margin="10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnNewSale_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="5" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Opacity" Value="0.8"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
                
                <Button x:Name="BtnNewProduct" Content="إضافة منتج جديد" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,10" Margin="10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnNewProduct_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="5" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Opacity" Value="0.8"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
                
                <Button x:Name="BtnViewReports" Content="عرض التقارير" 
                        Background="{StaticResource WarningBrush}" Foreground="White"
                        Padding="15,10" Margin="10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnViewReports_Click">
                    <Button.Style>
                        <Style TargetType="Button">
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="Button">
                                        <Border Background="{TemplateBinding Background}" 
                                                CornerRadius="5" Padding="{TemplateBinding Padding}">
                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                        </Border>
                                        <ControlTemplate.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Opacity" Value="0.8"/>
                                            </Trigger>
                                        </ControlTemplate.Triggers>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </Button.Style>
                </Button>
            </UniformGrid>
            
            <!-- آخر العمليات -->
            <TextBlock Text="آخر العمليات" FontSize="20" FontWeight="Bold" Margin="0,0,0,15"/>
            
            <Border Style="{StaticResource DashboardCardStyle}">
                <StackPanel>
                    <TextBlock Text="سيتم عرض آخر العمليات هنا..." 
                             FontSize="16" HorizontalAlignment="Center" 
                             VerticalAlignment="Center" Margin="20"
                             Foreground="Gray"/>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
