<Window x:Class="GlassFactoryAccounting.Views.DeliveredSizesReportView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        mc:Ignorable="d" 
        Title="📋 المقاسات التي تم تسليمها" 
        Height="700" Width="1200"
        Background="White"
        WindowStartupLocation="CenterScreen">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Background="#17A2B8" CornerRadius="10" Padding="15" Margin="0,0,0,20">
            <TextBlock Text="📋 المقاسات التي تم تسليمها" 
                      FontSize="24" FontWeight="Bold" 
                      Foreground="White" HorizontalAlignment="Center"/>
        </Border>

        <!-- فلاتر البحث -->
        <GroupBox Grid.Row="1" Header="🔍 فلاتر البحث" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <!-- الصف الأول -->
                <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                    <TextBlock Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="cmbFilterCustomer" IsEditable="True" Padding="8" FontSize="14"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                    <TextBlock Text="رقم أمر التصنيع:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="txtFilterOrderNumber" Padding="8" FontSize="14"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                    <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="txtFilterInvoiceNumber" Padding="8" FontSize="14"/>
                </StackPanel>

                <StackPanel Grid.Row="0" Grid.Column="3" Margin="5">
                    <TextBlock Text="Ref كود المقاس:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="txtFilterRefCode" Padding="8" FontSize="14"/>
                </StackPanel>

                <!-- الصف الثاني -->
                <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                    <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="dpFilterFromDate" Padding="8" FontSize="14"/>
                </StackPanel>

                <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                    <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="dpFilterToDate" Padding="8" FontSize="14"/>
                </StackPanel>

                <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                    <TextBlock Text="حالة التسليم:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox x:Name="cmbFilterDeliveryStatus" Padding="8" FontSize="14">
                        <ComboBoxItem Content="الكل" IsSelected="True"/>
                        <ComboBoxItem Content="تم التسليم"/>
                        <ComboBoxItem Content="لم يتم التسليم"/>
                    </ComboBox>
                </StackPanel>

                <StackPanel Grid.Row="1" Grid.Column="3" Margin="5" VerticalAlignment="Bottom">
                    <Button x:Name="btnSearch" Content="🔍 بحث" 
                           Background="#007BFF" Foreground="White" Padding="15,8" 
                           FontSize="14" FontWeight="Bold"
                           Click="BtnSearch_Click"/>
                </StackPanel>
            </Grid>
        </GroupBox>

        <!-- جدول النتائج -->
        <GroupBox Grid.Row="2" Header="📊 نتائج البحث" FontSize="16" FontWeight="Bold">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <DataGrid Grid.Row="0" x:Name="dgDeliveredSizes" AutoGenerateColumns="False" 
                         CanUserAddRows="False" CanUserDeleteRows="False"
                         GridLinesVisibility="All" HeadersVisibility="All"
                         FontSize="12" IsReadOnly="True">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40"/>
                        <DataGridTextColumn Header="اسم العميل" Binding="{Binding CustomerName}" Width="150"/>
                        <DataGridTextColumn Header="رقم أمر التصنيع" Binding="{Binding ManufacturingOrderNumber}" Width="120"/>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="100"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding OrderDate}" Width="100"/>
                        <DataGridTextColumn Header="Ref كود" Binding="{Binding RefCode}" Width="80"/>
                        <DataGridTextColumn Header="المقاس" Binding="{Binding SizeDescription}" Width="150"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity}" Width="80"/>
                        <DataGridTextColumn Header="حالة التسليم" Binding="{Binding DeliveryStatus}" Width="100"/>
                        <DataGridTextColumn Header="تاريخ التسليم" Binding="{Binding DeliveryDate}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>

                <!-- ملخص النتائج -->
                <Border Grid.Row="1" Background="#E9ECEF" Padding="10" Margin="0,10,0,0" CornerRadius="5">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0" x:Name="txtTotalRecords" Text="إجمالي السجلات: 0" 
                                  FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        
                        <TextBlock Grid.Column="1" x:Name="txtDeliveredCount" Text="تم التسليم: 0" 
                                  FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Green"/>
                        
                        <TextBlock Grid.Column="2" x:Name="txtPendingCount" Text="لم يتم التسليم: 0" 
                                  FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="Red"/>
                        
                        <TextBlock Grid.Column="3" x:Name="txtTotalQuantity" Text="إجمالي الكمية: 0" 
                                  FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </Grid>
                </Border>
            </Grid>
        </GroupBox>

        <!-- الأزرار -->
        <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
            <Button x:Name="btnExportExcel" Content="📊 تصدير إلى Excel" 
                   Background="#28A745" Foreground="White" Padding="15,8" 
                   FontSize="14" FontWeight="Bold" Margin="5"
                   Click="BtnExportExcel_Click"/>
            
            <Button x:Name="btnPrintReport" Content="🖨️ طباعة التقرير" 
                   Background="#FFC107" Foreground="Black" Padding="15,8" 
                   FontSize="14" FontWeight="Bold" Margin="5"
                   Click="BtnPrintReport_Click"/>
            
            <Button x:Name="btnRefresh" Content="🔄 تحديث" 
                   Background="#17A2B8" Foreground="White" Padding="15,8" 
                   FontSize="14" FontWeight="Bold" Margin="5"
                   Click="BtnRefresh_Click"/>
            
            <Button x:Name="btnClose" Content="❌ إغلاق" 
                   Background="#DC3545" Foreground="White" Padding="15,8" 
                   FontSize="14" FontWeight="Bold" Margin="5"
                   Click="BtnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>
