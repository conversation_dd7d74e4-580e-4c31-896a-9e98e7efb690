using System.Globalization;
using System.Windows.Data;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// محول لتحويل قيمة Boolean إلى نوع الصف
    /// </summary>
    public class BoolToRowTypeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value is bool isManualRow)
                {
                    return isManualRow ? "يدوي" : "عادي";
                }
                return "عادي";
            }
            catch
            {
                return "عادي";
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            try
            {
                if (value is string rowType)
                {
                    return rowType == "يدوي";
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
