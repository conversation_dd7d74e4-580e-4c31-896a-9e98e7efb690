using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using Microsoft.Win32;
using System.IO;
using System.Text;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة موديول الخدمات المفصلة
    /// </summary>
    public partial class ServicesView : UserControl
    {
        private readonly ServicesService _servicesService;
        private ObservableCollection<ServiceRecord> _serviceRecords;
        private List<ServiceRecord> _allServiceRecords;

        public ServicesView()
        {
            InitializeComponent();
            _servicesService = new ServicesService();
            _serviceRecords = new ObservableCollection<ServiceRecord>();
            _allServiceRecords = new List<ServiceRecord>();
            
            ServicesDataGrid.ItemsSource = _serviceRecords;
            
            _ = LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            try
            {
                // عرض مؤشر التحميل
                this.Cursor = System.Windows.Input.Cursors.Wait;

                // تحميل جميع سجلات الخدمات مع معالجة أفضل للأخطاء
                try
                {
                    _allServiceRecords = await _servicesService.GetAllServiceRecordsAsync();
                }
                catch (Exception serviceEx)
                {
                    System.Diagnostics.Debug.WriteLine($"Error in GetAllServiceRecordsAsync: {serviceEx.Message}");
                    // في حالة الخطأ، استخدم قائمة فارغة
                    _allServiceRecords = new List<ServiceRecord>();
                }

                // تحديث الجدول
                RefreshDataGrid();

                // تحميل قوائم الفلاتر
                await LoadFilterOptionsAsync();

                // تحديث الإحصائيات
                UpdateStatistics();

                TxtLastUpdate.Text = $"آخر تحديث: {DateTime.Now:dd/MM/yyyy HH:mm}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in LoadDataAsync: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // تأكد من وجود قائمة فارغة على الأقل
                if (_allServiceRecords == null)
                    _allServiceRecords = new List<ServiceRecord>();
            }
            finally
            {
                this.Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private async Task LoadFilterOptionsAsync()
        {
            try
            {
                // تحميل قائمة العملاء
                try
                {
                    var customers = await _servicesService.GetDistinctCustomerNamesAsync();
                    CmbCustomer.Items.Clear();
                    CmbCustomer.Items.Add("جميع العملاء");
                    foreach (var customer in customers)
                    {
                        CmbCustomer.Items.Add(customer);
                    }
                    CmbCustomer.SelectedIndex = 0;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error loading customers: {ex.Message}");
                    CmbCustomer.Items.Clear();
                    CmbCustomer.Items.Add("جميع العملاء");
                    CmbCustomer.SelectedIndex = 0;
                }

                // تحميل قائمة أنواع الخدمات
                try
                {
                    var services = await _servicesService.GetDistinctServiceNamesAsync();
                    CmbServiceType.Items.Clear();
                    CmbServiceType.Items.Add("جميع الخدمات");
                    foreach (var service in services)
                    {
                        CmbServiceType.Items.Add(service);
                    }
                    CmbServiceType.SelectedIndex = 0;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error loading services: {ex.Message}");
                    CmbServiceType.Items.Clear();
                    CmbServiceType.Items.Add("جميع الخدمات");
                    CmbServiceType.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading filter options: {ex.Message}");
            }
        }

        private void RefreshDataGrid()
        {
            _serviceRecords.Clear();
            foreach (var record in _allServiceRecords)
            {
                _serviceRecords.Add(record);
            }
        }

        private void UpdateStatistics()
        {
            var recordCount = _serviceRecords.Count;
            var totalArea = _serviceRecords.Sum(r => r.TotalArea);
            var totalValue = _serviceRecords.Sum(r => r.TotalPrice);

            TxtRecordCount.Text = $"عدد السجلات: {recordCount:N0}";
            TxtTotalArea.Text = $"إجمالي المساحة: {totalArea:N2} م²";
            TxtTotalValue.Text = $"إجمالي القيمة: {totalValue:N2} ر.س";
        }

        private async void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                this.Cursor = System.Windows.Input.Cursors.Wait;

                var filteredRecords = _allServiceRecords.AsEnumerable();

                // فلتر العميل
                if (CmbCustomer.SelectedItem != null && CmbCustomer.SelectedItem.ToString() != "جميع العملاء")
                {
                    var selectedCustomer = CmbCustomer.SelectedItem.ToString();
                    filteredRecords = filteredRecords.Where(r => r.CustomerName.Contains(selectedCustomer));
                }

                // فلتر نوع الخدمة
                if (CmbServiceType.SelectedItem != null && CmbServiceType.SelectedItem.ToString() != "جميع الخدمات")
                {
                    var selectedService = CmbServiceType.SelectedItem.ToString();
                    filteredRecords = filteredRecords.Where(r => r.ServiceName.Contains(selectedService));
                }

                // فلتر التاريخ
                if (DpFromDate.SelectedDate.HasValue)
                {
                    filteredRecords = filteredRecords.Where(r => r.SaleDate.Date >= DpFromDate.SelectedDate.Value.Date);
                }

                if (DpToDate.SelectedDate.HasValue)
                {
                    filteredRecords = filteredRecords.Where(r => r.SaleDate.Date <= DpToDate.SelectedDate.Value.Date);
                }

                // فلتر رقم الفاتورة
                if (!string.IsNullOrWhiteSpace(TxtInvoiceNumber.Text))
                {
                    filteredRecords = filteredRecords.Where(r => r.InvoiceNumber.Contains(TxtInvoiceNumber.Text));
                }

                // فلتر نوع الزجاج
                if (!string.IsNullOrWhiteSpace(TxtGlassType.Text))
                {
                    filteredRecords = filteredRecords.Where(r => r.GlassType.Contains(TxtGlassType.Text));
                }

                // تحديث الجدول
                _serviceRecords.Clear();
                foreach (var record in filteredRecords.OrderByDescending(r => r.SaleDate))
                {
                    _serviceRecords.Add(record);
                }

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                this.Cursor = System.Windows.Input.Cursors.Arrow;
            }
        }

        private void BtnClearFilters_Click(object sender, RoutedEventArgs e)
        {
            // مسح جميع الفلاتر
            CmbCustomer.SelectedIndex = 0;
            CmbServiceType.SelectedIndex = 0;
            DpFromDate.SelectedDate = null;
            DpToDate.SelectedDate = null;
            TxtInvoiceNumber.Clear();
            TxtGlassType.Clear();

            // إعادة عرض جميع البيانات
            RefreshDataGrid();
            UpdateStatistics();
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.csv",
                    DefaultExt = "csv",
                    FileName = $"تقرير_الخدمات_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    ExportToCSV(saveFileDialog.FileName);
                    MessageBox.Show("تم تصدير التقرير بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ExportToCSV(string filePath)
        {
            var csv = new StringBuilder();
            
            // إضافة العناوين
            csv.AppendLine("رقم الفاتورة,التاريخ,العميل,الخدمة,نوع الزجاج,السماكة,الطول,العرض,المساحة,العدد,إجمالي المساحة,سعر الوحدة,إجمالي السعر,التفاصيل");
            
            // إضافة البيانات
            foreach (var record in _serviceRecords)
            {
                csv.AppendLine($"{record.InvoiceNumber},{record.SaleDate:dd/MM/yyyy},{record.CustomerName},{record.ServiceName},{record.GlassType},{record.GlassThickness},{record.Length},{record.Width},{record.Area},{record.Count},{record.TotalArea},{record.UnitPrice},{record.TotalPrice},{record.Details}");
            }
            
            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }
    }
}
