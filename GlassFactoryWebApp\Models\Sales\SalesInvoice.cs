using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج فاتورة المبيعات
    /// </summary>
    public class SalesInvoice : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Required]
        public int CustomerId { get; set; }

        [MaxLength(100)]
        public string CustomerName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string CustomerAddress { get; set; } = string.Empty;

        [MaxLength(20)]
        public string CustomerPhone { get; set; } = string.Empty;

        public decimal SubTotal { get; set; } = 0;

        public decimal DiscountPercentage { get; set; } = 0;

        public decimal DiscountAmount { get; set; } = 0;

        public decimal TaxPercentage { get; set; } = 0;

        public decimal TaxAmount { get; set; } = 0;

        public decimal TotalAmount { get; set; } = 0;

        public decimal PaidAmount { get; set; } = 0;

        public decimal RemainingAmount { get; set; } = 0;

        [MaxLength(50)]
        public string PaymentMethod { get; set; } = "نقدي"; // نقدي، آجل، شيك، تحويل

        [MaxLength(50)]
        public string InvoiceStatus { get; set; } = "مفتوحة"; // مفتوحة، مدفوعة، ملغاة، مرتجعة

        [MaxLength(100)]
        public string SalesmanName { get; set; } = string.Empty;

        public DateTime? DueDate { get; set; }

        [MaxLength(100)]
        public string? ReferenceNumber { get; set; }

        [MaxLength(500)]
        public string? InvoiceNotes { get; set; }

        public bool IsPrinted { get; set; } = false;

        public DateTime? PrintedAt { get; set; }

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedAt { get; set; }

        [MaxLength(100)]
        public string? PostedBy { get; set; }

        // Foreign Keys
        [ForeignKey("CustomerId")]
        public virtual Customer Customer { get; set; } = null!;

        public virtual ApplicationUser CreatedByUser { get; set; } = null!;

        // Navigation Properties
        public virtual ICollection<SalesInvoiceItem> Items { get; set; } = new List<SalesInvoiceItem>();
        public virtual ICollection<CustomerPayment> Payments { get; set; } = new List<CustomerPayment>();
    }
}
