version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: glass-factory-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: glass_factory_db
      POSTGRES_USER: glass_factory_user
      POSTGRES_PASSWORD: GlassFactory2025!
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U glass_factory_user -d glass_factory_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: glass-factory-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass GlassFactory2025!
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: glass-factory-api
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=glass_factory_db;Username=glass_factory_user;Password=GlassFactory2025!
      - ConnectionStrings__Redis=redis:6379,password=GlassFactory2025!
      - JwtSettings__SecretKey=GlassFactorySecretKey2025VeryLongAndSecure!
      - JwtSettings__Issuer=GlassFactoryApp
      - JwtSettings__Audience=GlassFactoryUsers
      - JwtSettings__ExpiryInHours=24
    volumes:
      - ./uploads:/app/uploads
      - ./reports:/app/reports
      - ./logs:/app/logs
    ports:
      - "5000:80"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Web App
  web:
    build:
      context: ./client
      dockerfile: Dockerfile
    container_name: glass-factory-web
    restart: unless-stopped
    environment:
      - REACT_APP_API_URL=https://your-domain.com/api
      - REACT_APP_VERSION=2.0.0
      - REACT_APP_ENVIRONMENT=production
    ports:
      - "3000:80"
    depends_on:
      - api
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: glass-factory-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./uploads:/var/www/uploads:ro
      - ./reports:/var/www/reports:ro
    depends_on:
      - api
      - web
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: glass-factory-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - glass-factory-network

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: glass-factory-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=GlassFactory2025!
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - glass-factory-network

  # Log Management with ELK Stack
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: glass-factory-elasticsearch
    restart: unless-stopped
    environment:
      - discovery.type=single-node
      - ES_JAVA_OPTS=-Xms512m -Xmx512m
      - xpack.security.enabled=false
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    networks:
      - glass-factory-network

  logstash:
    image: docker.elastic.co/logstash/logstash:8.8.0
    container_name: glass-factory-logstash
    restart: unless-stopped
    volumes:
      - ./logging/logstash.conf:/usr/share/logstash/pipeline/logstash.conf:ro
      - ./logs:/var/log/app:ro
    depends_on:
      - elasticsearch
    networks:
      - glass-factory-network

  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: glass-factory-kibana
    restart: unless-stopped
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
    networks:
      - glass-factory-network

  # Backup Service
  backup:
    build:
      context: ./backup
      dockerfile: Dockerfile
    container_name: glass-factory-backup
    restart: unless-stopped
    environment:
      - POSTGRES_HOST=postgres
      - POSTGRES_DB=glass_factory_db
      - POSTGRES_USER=glass_factory_user
      - POSTGRES_PASSWORD=GlassFactory2025!
      - BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
      - RETENTION_DAYS=30
    volumes:
      - ./backups:/backups
      - ./uploads:/app/uploads:ro
    depends_on:
      - postgres
    networks:
      - glass-factory-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local

networks:
  glass-factory-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
