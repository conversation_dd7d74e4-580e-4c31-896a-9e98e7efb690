using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class ProjectExpenseReportView : UserControl
    {
        private readonly ExpenseService _expenseService;
        private List<ExpenseRecord> _allProjectExpenseRecords;
        private List<ExpenseRecord> _filteredProjectExpenseRecords;

        public ProjectExpenseReportView()
        {
            InitializeComponent();
            _expenseService = new ExpenseService();
            _allProjectExpenseRecords = new List<ExpenseRecord>();
            _filteredProjectExpenseRecords = new List<ExpenseRecord>();
            LoadData();
            InitializeFilters();
        }

        private void LoadData()
        {
            try
            {
                _allProjectExpenseRecords = _expenseService.GetProjectExpenseRecords();
                _filteredProjectExpenseRecords = _allProjectExpenseRecords.ToList();
                DgProjectExpenseRecords.ItemsSource = _filteredProjectExpenseRecords;
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void InitializeFilters()
        {
            try
            {
                // تحميل المشاريع للفلتر
                var projects = _allProjectExpenseRecords
                    .Where(e => !string.IsNullOrEmpty(e.ProjectTitle))
                    .Select(e => e.ProjectTitle)
                    .Distinct()
                    .ToList();
                projects.Insert(0, "الكل");
                CmbProjectFilter.ItemsSource = projects;
                CmbProjectFilter.SelectedIndex = 0;

                // تحميل أنواع المصروفات للفلتر
                var mainExpenses = _expenseService.GetDistinctMainExpenseNames();
                mainExpenses.Insert(0, "الكل");
                CmbExpenseTypeFilter.ItemsSource = mainExpenses;
                CmbExpenseTypeFilter.SelectedIndex = 0;

                // تحميل الفروع للفلتر
                var branches = _expenseService.GetAllCompanyBranches();
                var branchNames = branches.Select(b => b.Name).ToList();
                branchNames.Insert(0, "الكل");
                CmbBranchFilter.ItemsSource = branchNames;
                CmbBranchFilter.SelectedIndex = 0;

                // تحميل المسؤولين للفلتر
                var responsiblePersons = _expenseService.GetAllResponsiblePersons();
                var personNames = responsiblePersons.Select(p => p.Name).ToList();
                personNames.Insert(0, "الكل");
                CmbResponsiblePersonFilter.ItemsSource = personNames;
                CmbResponsiblePersonFilter.SelectedIndex = 0;

                // تحميل طرق السداد للفلتر
                var paymentMethods = new List<string> { "الكل", "كاش", "بنك", "مستحق الدفع" };
                CmbPaymentMethodFilter.ItemsSource = paymentMethods;
                CmbPaymentMethodFilter.SelectedIndex = 0;

                // تحميل حالات المصروف للفلتر
                var expenseStatuses = new List<string> { "الكل", "مسدد", "مستحق" };
                CmbExpenseStatusFilter.ItemsSource = expenseStatuses;
                CmbExpenseStatusFilter.SelectedIndex = 0;

                // تعيين التواريخ الافتراضية
                DpFromDate.SelectedDate = DateTime.Now.AddMonths(-1);
                DpToDate.SelectedDate = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة الفلاتر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics()
        {
            if (_filteredProjectExpenseRecords == null || !_filteredProjectExpenseRecords.Any())
            {
                TxtTotalAmount.Text = "0.00";
                TxtProjectCount.Text = "0";
                TxtExpenseCount.Text = "0";
                TxtAveragePerProject.Text = "0.00";
                return;
            }

            var totalAmount = _filteredProjectExpenseRecords.Sum(e => e.Amount);
            var projectCount = _filteredProjectExpenseRecords
                .Where(e => !string.IsNullOrEmpty(e.ProjectTitle))
                .Select(e => e.ProjectTitle)
                .Distinct()
                .Count();
            var expenseCount = _filteredProjectExpenseRecords.Count;
            var averagePerProject = projectCount > 0 ? totalAmount / projectCount : 0;

            TxtTotalAmount.Text = totalAmount.ToString("N2");
            TxtProjectCount.Text = projectCount.ToString();
            TxtExpenseCount.Text = expenseCount.ToString();
            TxtAveragePerProject.Text = averagePerProject.ToString("N2");
        }

        private void BtnFilter_Click(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                _filteredProjectExpenseRecords = _allProjectExpenseRecords.ToList();

                // فلتر التاريخ
                if (DpFromDate.SelectedDate.HasValue)
                {
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.DateTime.Date >= DpFromDate.SelectedDate.Value.Date)
                        .ToList();
                }

                if (DpToDate.SelectedDate.HasValue)
                {
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.DateTime.Date <= DpToDate.SelectedDate.Value.Date)
                        .ToList();
                }

                // فلتر المشروع
                if (CmbProjectFilter.SelectedItem != null && 
                    CmbProjectFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedProject = CmbProjectFilter.SelectedItem.ToString();
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.ProjectTitle == selectedProject)
                        .ToList();
                }

                // فلتر نوع المصروف
                if (CmbExpenseTypeFilter.SelectedItem != null &&
                    CmbExpenseTypeFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedType = CmbExpenseTypeFilter.SelectedItem.ToString();
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.MainExpenseName == selectedType)
                        .ToList();
                }

                // فلتر الفرع
                if (CmbBranchFilter.SelectedItem != null &&
                    CmbBranchFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedBranch = CmbBranchFilter.SelectedItem.ToString();
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.BranchName == selectedBranch)
                        .ToList();
                }

                // فلتر المسؤول
                if (CmbResponsiblePersonFilter.SelectedItem != null &&
                    CmbResponsiblePersonFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedPerson = CmbResponsiblePersonFilter.SelectedItem.ToString();
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.ResponsiblePersonName == selectedPerson)
                        .ToList();
                }

                // فلتر طريقة السداد
                if (CmbPaymentMethodFilter.SelectedItem != null &&
                    CmbPaymentMethodFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedPaymentMethod = CmbPaymentMethodFilter.SelectedItem.ToString();
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.PaymentMethod == selectedPaymentMethod)
                        .ToList();
                }

                // فلتر حالة المصروف
                if (CmbExpenseStatusFilter.SelectedItem != null &&
                    CmbExpenseStatusFilter.SelectedItem.ToString() != "الكل")
                {
                    var selectedStatus = CmbExpenseStatusFilter.SelectedItem.ToString();
                    _filteredProjectExpenseRecords = _filteredProjectExpenseRecords
                        .Where(e => e.ExpenseStatus == selectedStatus)
                        .ToList();
                }

                DgProjectExpenseRecords.ItemsSource = _filteredProjectExpenseRecords;
                UpdateStatistics();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClearFilter_Click(object sender, RoutedEventArgs e)
        {
            ClearFilters();
        }

        private void ClearFilters()
        {
            DpFromDate.SelectedDate = null;
            DpToDate.SelectedDate = null;
            CmbProjectFilter.SelectedIndex = 0;
            CmbExpenseTypeFilter.SelectedIndex = 0;
            CmbBranchFilter.SelectedIndex = 0;
            CmbResponsiblePersonFilter.SelectedIndex = 0;
            CmbPaymentMethodFilter.SelectedIndex = 0;
            CmbExpenseStatusFilter.SelectedIndex = 0;

            _filteredProjectExpenseRecords = _allProjectExpenseRecords.ToList();
            DgProjectExpenseRecords.ItemsSource = _filteredProjectExpenseRecords;
            UpdateStatistics();
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseRecord record)
            {
                try
                {
                    var editWindow = new EditExpenseRecordWindow(record, _expenseService);
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadData(); // إعادة تحميل البيانات بعد التعديل
                        MessageBox.Show("تم تحديث مصروف المشروع بنجاح!", "نجح التحديث",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في فتح نافذة التعديل: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseRecord record)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف مصروف المشروع؟\nالمشروع: {record.ProjectTitle}\nالمصروف: {record.MainExpenseName} - {record.SubExpenseName}\nالمبلغ: {record.Amount:N2}", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = _expenseService.DeleteExpenseRecord(record.Id);
                        
                        if (deleteResult)
                        {
                            MessageBox.Show("تم حذف مصروف المشروع بنجاح!", "نجح الحذف", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadData(); // إعادة تحميل البيانات
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف مصروف المشروع!", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف مصروف المشروع: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
