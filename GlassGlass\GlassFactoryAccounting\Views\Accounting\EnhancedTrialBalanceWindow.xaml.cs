using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using Microsoft.Win32;
using OfficeOpenXml;
using GlassFactoryAccounting.Models.Accounting;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views.Accounting
{
    /// <summary>
    /// نافذة ميزان المراجعة المحسنة
    /// </summary>
    public partial class EnhancedTrialBalanceWindow : Window
    {
        private readonly AccountingService _accountingService;
        private List<TrialBalanceItem> _allTrialBalanceItems;
        
        public EnhancedTrialBalanceWindow()
        {
            InitializeComponent();
            _accountingService = new AccountingService();
            _allTrialBalanceItems = new List<TrialBalanceItem>();
            
            InitializeWindow();
            LoadTrialBalance();
        }
        
        /// <summary>
        /// تهيئة النافذة
        /// </summary>
        private void InitializeWindow()
        {
            // تعيين التواريخ الافتراضية
            dpFromDate.SelectedDate = new DateTime(DateTime.Now.Year, 1, 1); // بداية السنة
            dpToDate.SelectedDate = DateTime.Now;
            
            // ربط الأحداث
            dpFromDate.SelectedDateChanged += DateChanged;
            dpToDate.SelectedDateChanged += DateChanged;
            cmbAccountType.SelectionChanged += FilterChanged;
            chkHideZeroBalances.Checked += FilterChanged;
            chkHideZeroBalances.Unchecked += FilterChanged;
        }
        
        /// <summary>
        /// تحميل ميزان المراجعة
        /// </summary>
        private void LoadTrialBalance()
        {
            try
            {
                // تحديث جميع أرصدة الحسابات أولاً
                _accountingService.UpdateAllAccountBalances();
                
                // الحصول على ميزان المراجعة
                _allTrialBalanceItems = _accountingService.GetTrialBalanceAdvanced(
                    dpFromDate.SelectedDate, 
                    dpToDate.SelectedDate);
                
                // تطبيق الفلاتر
                ApplyFilters();
                
                // تحديث نطاق التاريخ في العنوان
                UpdateDateRangeDisplay();
                
                System.Diagnostics.Debug.WriteLine($"Trial balance loaded with {_allTrialBalanceItems.Count} items");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل ميزان المراجعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// تطبيق الفلاتر
        /// </summary>
        private void ApplyFilters()
        {
            var filteredItems = _allTrialBalanceItems.AsEnumerable();
            
            // فلتر نوع الحساب
            var selectedType = (cmbAccountType.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            if (!string.IsNullOrEmpty(selectedType) && Enum.TryParse<AccountType>(selectedType, out var accountType))
            {
                filteredItems = filteredItems.Where(item => item.AccountType == accountType);
            }
            
            // فلتر الأرصدة الصفرية
            if (chkHideZeroBalances.IsChecked == true)
            {
                filteredItems = filteredItems.Where(item => 
                    item.DebitBalance != 0 || item.CreditBalance != 0 || item.Balance != 0);
            }
            
            var finalItems = filteredItems.ToList();
            dgTrialBalance.ItemsSource = finalItems;
            
            // تحديث الملخص
            UpdateSummary(finalItems);
        }
        
        /// <summary>
        /// تحديث ملخص الأرصدة
        /// </summary>
        private void UpdateSummary(List<TrialBalanceItem> items)
        {
            var totalDebit = items.Sum(i => i.DebitBalance);
            var totalCredit = items.Sum(i => i.CreditBalance);
            var difference = totalDebit - totalCredit;
            
            txtTotalDebit.Text = totalDebit.ToString("N2");
            txtTotalCredit.Text = totalCredit.ToString("N2");
            txtDifference.Text = Math.Abs(difference).ToString("N2");
            
            // تحديث حالة التوازن
            if (Math.Abs(difference) < 0.01m) // تسامح صغير للأخطاء العشرية
            {
                txtBalanceStatus.Text = "✅ متوازن";
                txtBalanceStatus.Foreground = System.Windows.Media.Brushes.Green;
                txtDifference.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                txtBalanceStatus.Text = "❌ غير متوازن";
                txtBalanceStatus.Foreground = System.Windows.Media.Brushes.Red;
                txtDifference.Foreground = System.Windows.Media.Brushes.Red;
            }
        }
        
        /// <summary>
        /// تحديث عرض نطاق التاريخ
        /// </summary>
        private void UpdateDateRangeDisplay()
        {
            if (dpFromDate.SelectedDate.HasValue && dpToDate.SelectedDate.HasValue)
            {
                txtDateRange.Text = $"من {dpFromDate.SelectedDate.Value:dd/MM/yyyy} إلى {dpToDate.SelectedDate.Value:dd/MM/yyyy}";
            }
            else
            {
                txtDateRange.Text = "جميع الفترات";
            }
        }
        
        /// <summary>
        /// معالج تغيير التاريخ
        /// </summary>
        private void DateChanged(object sender, SelectionChangedEventArgs e)
        {
            if (IsLoaded)
            {
                LoadTrialBalance();
            }
        }
        
        /// <summary>
        /// معالج تغيير الفلاتر
        /// </summary>
        private void FilterChanged(object sender, RoutedEventArgs e)
        {
            if (IsLoaded)
            {
                ApplyFilters();
            }
        }
        
        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadTrialBalance();
            MessageBox.Show("تم تحديث ميزان المراجعة بنجاح!", "تحديث", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "Excel Files|*.xlsx",
                    FileName = $"ميزان_المراجعة_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx"
                };
                
                if (saveDialog.ShowDialog() == true)
                {
                    ExportToExcel(saveDialog.FileName);
                    MessageBox.Show("تم تصدير ميزان المراجعة بنجاح!", "تصدير", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير ميزان المراجعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void ExportToExcel(string fileName)
        {
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add("ميزان المراجعة");
            
            // العناوين
            worksheet.Cells[1, 1].Value = "كود الحساب";
            worksheet.Cells[1, 2].Value = "اسم الحساب";
            worksheet.Cells[1, 3].Value = "نوع الحساب";
            worksheet.Cells[1, 4].Value = "المدين";
            worksheet.Cells[1, 5].Value = "الدائن";
            worksheet.Cells[1, 6].Value = "الرصيد";
            
            // تنسيق العناوين
            using var headerRange = worksheet.Cells[1, 1, 1, 6];
            headerRange.Style.Font.Bold = true;
            headerRange.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            headerRange.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
            
            // البيانات
            var items = dgTrialBalance.ItemsSource as List<TrialBalanceItem>;
            if (items != null)
            {
                for (int i = 0; i < items.Count; i++)
                {
                    var item = items[i];
                    var row = i + 2;
                    
                    worksheet.Cells[row, 1].Value = item.AccountCode;
                    worksheet.Cells[row, 2].Value = item.AccountName;
                    worksheet.Cells[row, 3].Value = item.AccountTypeDisplay;
                    worksheet.Cells[row, 4].Value = item.DebitBalance;
                    worksheet.Cells[row, 5].Value = item.CreditBalance;
                    worksheet.Cells[row, 6].Value = item.Balance;
                }
                
                // تنسيق الأرقام
                worksheet.Cells[2, 4, items.Count + 1, 6].Style.Numberformat.Format = "#,##0.00";
            }
            
            // ضبط عرض الأعمدة
            worksheet.Cells.AutoFitColumns();
            
            // حفظ الملف
            package.SaveAs(new System.IO.FileInfo(fileName));
        }
        
        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // يمكن تحسين هذا لاحقاً لإنشاء تقرير مطبوع مخصص
                    MessageBox.Show("ميزة الطباعة ستكون متاحة في التحديث القادم.", "طباعة", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
    
    /// <summary>
    /// محول للأرقام الموجبة والسالبة
    /// </summary>
    public class PositiveNegativeConverter : IValueConverter
    {
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is decimal decimalValue)
            {
                return decimalValue >= 0 ? "Positive" : "Negative";
            }
            return "Positive";
        }
        
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
