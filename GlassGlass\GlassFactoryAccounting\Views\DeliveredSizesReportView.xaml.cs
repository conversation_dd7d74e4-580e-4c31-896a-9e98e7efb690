using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// تقرير المقاسات المسلمة
    /// </summary>
    public partial class DeliveredSizesReportView : Window
    {
        private readonly Services.ManufacturingService _manufacturingService;
        private ObservableCollection<DeliveredSizeReportViewModel> _reportData;

        public DeliveredSizesReportView()
        {
            InitializeComponent();
            _manufacturingService = new Services.ManufacturingService();
            
            InitializeCollections();
            LoadData();
        }

        #region Initialization

        /// <summary>
        /// تهيئة المجموعات
        /// </summary>
        private void InitializeCollections()
        {
            _reportData = new ObservableCollection<DeliveredSizeReportViewModel>();
            dgDeliveredSizes.ItemsSource = _reportData;
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                LoadCustomers();
                LoadReportData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل أسماء العملاء
        /// </summary>
        private void LoadCustomers()
        {
            try
            {
                var orders = _manufacturingService.GetAllManufacturingOrders();
                var customers = orders.Select(o => o.CustomerName).Distinct().OrderBy(c => c).ToList();
                
                cmbFilterCustomer.Items.Clear();
                cmbFilterCustomer.Items.Add("الكل");
                
                foreach (var customer in customers)
                {
                    if (!string.IsNullOrWhiteSpace(customer))
                    {
                        cmbFilterCustomer.Items.Add(customer);
                    }
                }
                
                cmbFilterCustomer.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customers: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل بيانات التقرير
        /// </summary>
        private void LoadReportData()
        {
            try
            {
                _reportData.Clear();
                
                var orders = _manufacturingService.GetAllManufacturingOrders();
                int rowNumber = 1;

                foreach (var order in orders)
                {
                    var requiredSizes = _manufacturingService.GetRequiredSizes(order.Id);
                    
                    foreach (var size in requiredSizes)
                    {
                        var reportItem = new DeliveredSizeReportViewModel
                        {
                            RowNumber = rowNumber++,
                            CustomerName = order.CustomerName,
                            ManufacturingOrderNumber = order.OrderNumber,
                            InvoiceNumber = order.InvoiceNumber ?? "",
                            OrderDate = order.OrderDate.ToString("yyyy-MM-dd"),
                            RefCode = size.RefCode,
                            SizeDescription = $"{size.Length} × {size.Width} × {size.Thickness}",
                            Quantity = size.Quantity,
                            DeliveryStatus = size.IsDelivered ? "تم التسليم" : "لم يتم التسليم",
                            DeliveryDate = size.IsDelivered ? "تم التسليم" : "-"
                        };

                        _reportData.Add(reportItem);
                    }
                }

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات التقرير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث الملخص
        /// </summary>
        private void UpdateSummary()
        {
            try
            {
                int totalRecords = _reportData.Count;
                int deliveredCount = _reportData.Count(r => r.DeliveryStatus == "تم التسليم");
                int pendingCount = totalRecords - deliveredCount;
                int totalQuantity = _reportData.Sum(r => r.Quantity);

                txtTotalRecords.Text = $"إجمالي السجلات: {totalRecords}";
                txtDeliveredCount.Text = $"تم التسليم: {deliveredCount}";
                txtPendingCount.Text = $"لم يتم التسليم: {pendingCount}";
                txtTotalQuantity.Text = $"إجمالي الكمية: {totalQuantity}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating summary: {ex.Message}");
            }
        }

        #endregion

        #region Search and Filter

        /// <summary>
        /// البحث والفلترة
        /// </summary>
        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تطبيق الفلاتر
        /// </summary>
        private void ApplyFilters()
        {
            try
            {
                var filteredData = new ObservableCollection<DeliveredSizeReportViewModel>();
                var allData = _manufacturingService.GetAllManufacturingOrders();
                int rowNumber = 1;

                foreach (var order in allData)
                {
                    // فلتر العميل
                    if (cmbFilterCustomer.SelectedItem?.ToString() != "الكل" && 
                        !string.IsNullOrWhiteSpace(cmbFilterCustomer.Text) &&
                        !order.CustomerName.Contains(cmbFilterCustomer.Text))
                        continue;

                    // فلتر رقم أمر التصنيع
                    if (!string.IsNullOrWhiteSpace(txtFilterOrderNumber.Text) &&
                        !order.OrderNumber.Contains(txtFilterOrderNumber.Text))
                        continue;

                    // فلتر رقم الفاتورة
                    if (!string.IsNullOrWhiteSpace(txtFilterInvoiceNumber.Text) &&
                        !order.InvoiceNumber?.Contains(txtFilterInvoiceNumber.Text) == true)
                        continue;

                    // فلتر التاريخ
                    if (dpFilterFromDate.SelectedDate.HasValue && order.OrderDate < dpFilterFromDate.SelectedDate.Value)
                        continue;

                    if (dpFilterToDate.SelectedDate.HasValue && order.OrderDate > dpFilterToDate.SelectedDate.Value)
                        continue;

                    var requiredSizes = _manufacturingService.GetRequiredSizes(order.Id);
                    
                    foreach (var size in requiredSizes)
                    {
                        // فلتر Ref كود
                        if (!string.IsNullOrWhiteSpace(txtFilterRefCode.Text) &&
                            !size.RefCode.Contains(txtFilterRefCode.Text))
                            continue;

                        // فلتر حالة التسليم
                        var deliveryStatusFilter = cmbFilterDeliveryStatus.SelectedItem?.ToString();
                        if (deliveryStatusFilter == "تم التسليم" && !size.IsDelivered)
                            continue;
                        if (deliveryStatusFilter == "لم يتم التسليم" && size.IsDelivered)
                            continue;

                        var reportItem = new DeliveredSizeReportViewModel
                        {
                            RowNumber = rowNumber++,
                            CustomerName = order.CustomerName,
                            ManufacturingOrderNumber = order.OrderNumber,
                            InvoiceNumber = order.InvoiceNumber ?? "",
                            OrderDate = order.OrderDate.ToString("yyyy-MM-dd"),
                            RefCode = size.RefCode,
                            SizeDescription = $"{size.Length} × {size.Width} × {size.Thickness}",
                            Quantity = size.Quantity,
                            DeliveryStatus = size.IsDelivered ? "تم التسليم" : "لم يتم التسليم",
                            DeliveryDate = size.IsDelivered ? "تم التسليم" : "-"
                        };

                        filteredData.Add(reportItem);
                    }
                }

                _reportData.Clear();
                foreach (var item in filteredData)
                {
                    _reportData.Add(item);
                }

                UpdateSummary();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying filters: {ex.Message}");
            }
        }

        #endregion

        #region Actions

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير تصدير Excel قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void BtnPrintReport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير طباعة التقرير قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadReportData();
        }

        /// <summary>
        /// إغلاق النافذة
        /// </summary>
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        #endregion
    }

    #region ViewModels

    /// <summary>
    /// نموذج عرض تقرير المقاسات المسلمة
    /// </summary>
    public class DeliveredSizeReportViewModel
    {
        public int RowNumber { get; set; }
        public string CustomerName { get; set; } = "";
        public string ManufacturingOrderNumber { get; set; } = "";
        public string InvoiceNumber { get; set; } = "";
        public string OrderDate { get; set; } = "";
        public string RefCode { get; set; } = "";
        public string SizeDescription { get; set; } = "";
        public int Quantity { get; set; }
        public string DeliveryStatus { get; set; } = "";
        public string DeliveryDate { get; set; } = "";
    }

    #endregion
}
