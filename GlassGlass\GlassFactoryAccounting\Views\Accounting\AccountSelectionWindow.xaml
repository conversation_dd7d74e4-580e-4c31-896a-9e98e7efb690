<Window x:Class="GlassFactoryAccounting.Views.Accounting.AccountSelectionWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="اختيار الحساب" Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">
    
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="🔍 اختيار الحساب" 
                   FontSize="20" FontWeight="Bold" 
                   Foreground="#2C3E50" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,15"/>
        
        <!-- البحث والفلترة -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1"
                CornerRadius="5" Background="#F8F9FA" Margin="0,0,0,15">
            <Grid Margin="15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- مربع البحث -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <Label Content="البحث:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <TextBox Name="txtSearch" Width="300" FontSize="14" Margin="10,0"
                             TextChanged="TxtSearch_TextChanged"
                             ToolTip="ابحث بكود الحساب أو اسم الحساب"/>
                </StackPanel>
                
                <!-- فلتر نوع الحساب -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="20,0">
                    <Label Content="نوع الحساب:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <ComboBox Name="cmbAccountType" Width="150" FontSize="14"
                              SelectionChanged="CmbAccountType_SelectionChanged">
                        <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                        <ComboBoxItem Content="الأصول" Tag="Asset"/>
                        <ComboBoxItem Content="الالتزامات" Tag="Liability"/>
                        <ComboBoxItem Content="حقوق الملكية" Tag="Equity"/>
                        <ComboBoxItem Content="الإيرادات" Tag="Revenue"/>
                        <ComboBoxItem Content="المصروفات" Tag="Expense"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- فلتر الحسابات القابلة للاستخدام -->
                <CheckBox Grid.Column="2" Name="chkUsableOnly" Content="الحسابات الفرعية فقط"
                          FontWeight="Bold" VerticalAlignment="Center" Margin="20,0"
                          IsChecked="True" Checked="ChkUsableOnly_Changed" Unchecked="ChkUsableOnly_Changed"
                          ToolTip="إظهار الحسابات التي يمكن استخدامها في القيود فقط"/>
            </Grid>
        </Border>
        
        <!-- شجرة الحسابات -->
        <Border Grid.Row="2" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="White">
            <TreeView Name="tvAccounts" FontSize="14" Margin="10"
                      SelectedItemChanged="TvAccounts_SelectedItemChanged">
                <TreeView.ItemTemplate>
                    <HierarchicalDataTemplate ItemsSource="{Binding Children}">
                        <StackPanel Orientation="Horizontal">
                            <!-- أيقونة نوع الحساب -->
                            <TextBlock Text="{Binding TypeIcon}" FontSize="16" Margin="0,0,5,0"/>
                            
                            <!-- كود الحساب -->
                            <TextBlock Text="{Binding AccountCode}" FontWeight="Bold" 
                                       Foreground="#3498DB" Margin="0,0,10,0"/>
                            
                            <!-- اسم الحساب -->
                            <TextBlock Text="{Binding AccountName}" Margin="0,0,10,0"/>
                            
                            <!-- نوع الحساب -->
                            <TextBlock Text="{Binding AccountTypeDisplay}" 
                                       Foreground="#7F8C8D" FontStyle="Italic" Margin="0,0,10,0"/>
                            
                            <!-- الرصيد -->
                            <TextBlock Text="{Binding BalanceDisplay}" 
                                       Foreground="#E74C3C" FontWeight="Bold" 
                                       Margin="10,0,0,0"/>
                            
                            <!-- مؤشر الحساب القابل للاستخدام -->
                            <TextBlock Text="{Binding UsabilityIndicator}" 
                                       Foreground="#27AE60" FontWeight="Bold" 
                                       Margin="10,0,0,0"/>
                        </StackPanel>
                    </HierarchicalDataTemplate>
                </TreeView.ItemTemplate>
            </TreeView>
        </Border>
        
        <!-- معلومات الحساب المحدد -->
        <Border Grid.Row="3" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="#ECF0F1" Padding="15" Margin="0,15,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="الحساب المحدد:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Name="txtSelectedAccount" Text="لم يتم اختيار حساب" FontSize="14"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="نوع الحساب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Name="txtSelectedType" Text="-" FontSize="14"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="الرصيد الحالي:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock Name="txtSelectedBalance" Text="-" FontSize="14" FontWeight="Bold"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" Orientation="Horizontal">
                    <Button Name="btnSelect" Content="✅ اختيار" 
                            Background="#27AE60" Foreground="White" 
                            Padding="20,10" FontSize="14" FontWeight="Bold"
                            Margin="20,0,10,0" Click="BtnSelect_Click"
                            IsEnabled="False"/>
                    
                    <Button Name="btnCancel" Content="❌ إلغاء" 
                            Background="#95A5A6" Foreground="White" 
                            Padding="20,10" FontSize="14" FontWeight="Bold"
                            Margin="10,0" Click="BtnCancel_Click"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
