using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة سداد راتب موظف مبسطة
    /// </summary>
    public partial class SimplifiedSalaryPaymentView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly ExpenseService _expenseService;
        private readonly MainWindow _mainWindow;
        private Employee? _selectedEmployee;

        public SimplifiedSalaryPaymentView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _expenseService = new ExpenseService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadPayments();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ اليوم كافتراضي
            DpPaymentDate.SelectedDate = DateTime.Now;

            // تحميل قائمة المسؤولين من إدارة المصروفات
            LoadResponsiblePersons();

            // تعيين قيم افتراضية
            TxtAmountPaid.Text = "0";
        }

        private void LoadResponsiblePersons()
        {
            try
            {
                var responsiblePersons = _expenseService.GetAllResponsiblePersons();
                CmbPaymentHandler.ItemsSource = responsiblePersons;
                CmbPaymentHandler.DisplayMemberPath = "Name";
                CmbPaymentHandler.SelectedValuePath = "Name";

                if (responsiblePersons.Any())
                {
                    CmbPaymentHandler.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة المسؤولين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // في حالة الخطأ، استخدم قائمة افتراضية
                CmbPaymentHandler.Items.Add("مدير الموارد البشرية");
                CmbPaymentHandler.Items.Add("المدير المالي");
                CmbPaymentHandler.Items.Add("المدير العام");
                CmbPaymentHandler.SelectedIndex = 0;
            }
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.ItemsSource = employees;
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadPayments()
        {
            try
            {
                var payments = _payrollService.GetAllPayments();
                PaymentsDataGrid.ItemsSource = payments;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المدفوعات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbEmployee_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbEmployee.SelectedItem is Employee selectedEmployee)
            {
                _selectedEmployee = selectedEmployee;
                TxtEmployeeCode.Text = selectedEmployee.EmployeeCode;
                TxtJobTitle.Text = selectedEmployee.Position;
                TxtBranch.Text = selectedEmployee.Branch ?? "الفرع الرئيسي";

                // تعيين الراتب الأساسي كافتراضي للمبلغ المدفوع
                TxtAmountPaid.Text = selectedEmployee.BasicSalary.ToString("N2");
            }
            else
            {
                _selectedEmployee = null;
                ClearEmployeeFields();
            }
        }

        private void ClearEmployeeFields()
        {
            TxtEmployeeCode.Clear();
            TxtJobTitle.Clear();
            TxtBranch.Clear();
            TxtAmountPaid.Text = "0";
        }

        private void TxtAmountPaid_TextChanged(object sender, TextChangedEventArgs e)
        {
            if (sender is TextBox textBox)
                ValidateNumericInput(textBox);
        }

        private void ValidateNumericInput(TextBox textBox)
        {
            if (textBox != null && !string.IsNullOrEmpty(textBox.Text))
            {
                if (!decimal.TryParse(textBox.Text, out _))
                {
                    // إزالة الأحرف غير الصحيحة
                    int caretIndex = textBox.CaretIndex;
                    textBox.Text = System.Text.RegularExpressions.Regex.Replace(textBox.Text, @"[^\d.]", "");
                    textBox.CaretIndex = Math.Min(caretIndex, textBox.Text.Length);
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedEmployee = CmbEmployee.SelectedItem as Employee;
                if (selectedEmployee == null)
                {
                    MessageBox.Show("يرجى اختيار موظف.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var payment = new SalaryPayment
                {
                    EmployeeId = selectedEmployee.Id,
                    EmployeeName = selectedEmployee.Name,
                    EmployeeCode = selectedEmployee.EmployeeCode,
                    SalaryPaid = decimal.Parse(TxtAmountPaid.Text),
                    BonusAmount = 0, // مبسط - بدون مكافآت
                    OvertimeAmount = 0, // مبسط - بدون وقت إضافي
                    TotalPaid = decimal.Parse(TxtAmountPaid.Text),
                    ResponsiblePerson = (CmbPaymentHandler.SelectedItem as GlassFactoryAccounting.Models.ResponsiblePerson)?.Name ?? CmbPaymentHandler.SelectedValue?.ToString() ?? "",
                    PaymentDate = DpPaymentDate.SelectedDate ?? DateTime.Now,
                    Notes = TxtNotes.Text.Trim()
                };

                if (_payrollService.AddSalaryPayment(payment))
                {
                    MessageBox.Show("تم حفظ السداد بنجاح!", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    ClearForm();
                    LoadPayments();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ السداد!", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السداد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbEmployee.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbEmployee.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtAmountPaid.Text))
            {
                MessageBox.Show("يرجى إدخال المبلغ المدفوع", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmountPaid.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtAmountPaid.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للمبلغ المدفوع", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmountPaid.Focus();
                return false;
            }

            if (CmbPaymentHandler.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المسؤول عن السداد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbPaymentHandler.Focus();
                return false;
            }

            if (!DpPaymentDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ السداد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpPaymentDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            CmbEmployee.SelectedIndex = -1;
            _selectedEmployee = null;
            ClearEmployeeFields();
            CmbPaymentHandler.SelectedIndex = 0;
            DpPaymentDate.SelectedDate = DateTime.Now;
            TxtNotes.Clear();
            CmbEmployee.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var payrollView = new PayrollView();
                _mainWindow.MainContentArea.Content = payrollView;
                _mainWindow.TxtPageTitle.Text = "موديول الرواتب والأجور";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeletePayment_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int paymentId)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذا السداد؟", "تأكيد الحذف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (_payrollService.DeleteSalaryPayment(paymentId))
                        {
                            MessageBox.Show("تم حذف السداد بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadPayments();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف السداد!", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السداد: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
