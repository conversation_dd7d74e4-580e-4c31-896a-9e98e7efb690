@echo off
chcp 65001 >nul
title 🏭 نظام حسابات مصنع الزجاج - التشغيل السريع
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🏭 نظام حسابات مصنع الزجاج                              ║
echo ║                     Glass Factory Accounting System                          ║
echo ║                                                                              ║
echo ║                    المالك: حسام محمد حسان أحمد                             ║
echo ║                    الإصدار: 1.0.0 - النسخة النهائية 2025                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 تشغيل سريع للنظام...
echo.

REM الانتقال إلى مجلد المشروع
cd "GlassGlass"

REM استدعاء ملف التشغيل الرئيسي
if exist "تشغيل_نظام_مصنع_الزجاج.bat" (
    call "تشغيل_نظام_مصنع_الزجاج.bat"
) else if exist "GlassFactoryAccounting\🚀_تشغيل_البرنامج.bat" (
    cd "GlassFactoryAccounting"
    call "🚀_تشغيل_البرنامج.bat"
) else (
    echo ❌ لم يتم العثور على ملفات التشغيل!
    echo يرجى التحقق من وجود المشروع في المجلد الصحيح
    pause
)
