﻿#pragma checksum "..\..\..\..\Views\CompanySettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "49B29ED69A5A75A0A4779D60AF95733EFC290336"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// CompanySettingsWindow
    /// </summary>
    public partial class CompanySettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCompanyName;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCompanyAddress;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCompanyPhone;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCompanyEmail;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtTaxNumber;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDefaultTaxRate;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCurrency;
        
        #line default
        #line hidden
        
        
        #line 99 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkAutoBackup;
        
        #line default
        #line hidden
        
        
        #line 104 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBackupInterval;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBackupPath;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBrowseBackupPath;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\..\Views\CompanySettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/companysettingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\CompanySettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtCompanyName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TxtCompanyAddress = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtCompanyPhone = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TxtCompanyEmail = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtTaxNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtDefaultTaxRate = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.TxtCurrency = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.ChkAutoBackup = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.TxtBackupInterval = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.TxtBackupPath = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.BtnBrowseBackupPath = ((System.Windows.Controls.Button)(target));
            
            #line 118 "..\..\..\..\Views\CompanySettingsWindow.xaml"
            this.BtnBrowseBackupPath.Click += new System.Windows.RoutedEventHandler(this.BtnBrowseBackupPath_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 131 "..\..\..\..\Views\CompanySettingsWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\..\Views\CompanySettingsWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

