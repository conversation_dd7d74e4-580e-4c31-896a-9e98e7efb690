using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة إدارة الموردين الاحترافية
/// </summary>
public partial class SuppliersManagementWindow : Window
{
    private ObservableCollection<Supplier> _suppliers;
    private readonly ArchiveService _archiveService;

    public ObservableCollection<Supplier> Suppliers => _suppliers;
    public bool SuppliersUpdated { get; private set; } = false;

    public SuppliersManagementWindow()
    {
        InitializeComponent();
        _suppliers = new ObservableCollection<Supplier>();
        _archiveService = new ArchiveService();
        SuppliersDataGrid.ItemsSource = _suppliers;
        LoadSuppliers();
    }

    private async void LoadSuppliers()
    {
        try
        {
            var suppliers = await _archiveService.GetAllSuppliersAsync();
            _suppliers.Clear();
            foreach (var supplier in suppliers)
            {
                _suppliers.Add(supplier);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefresh_Click(object sender, RoutedEventArgs e)
    {
        LoadSuppliers();
        MessageBox.Show("تم تحديث قائمة الموردين", "تحديث", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async void BtnAddSupplier_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addSupplierWindow = new AddSupplierWindow();
            addSupplierWindow.Owner = this;
            
            if (addSupplierWindow.ShowDialog() == true && addSupplierWindow.NewSupplier != null)
            {
                int supplierId = await _archiveService.SaveSupplierAsync(addSupplierWindow.NewSupplier);
                
                if (supplierId > 0)
                {
                    addSupplierWindow.NewSupplier.Id = supplierId;
                    _suppliers.Add(addSupplierWindow.NewSupplier);
                    SuppliersUpdated = true;

                    MessageBox.Show("تم إضافة المورد بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("حدث خطأ أثناء حفظ المورد", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة المورد: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void BtnDeleteSupplier_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var button = sender as Button;
            var selectedSupplier = button?.Tag as Supplier ?? SuppliersDataGrid.SelectedItem as Supplier;
            
            if (selectedSupplier != null)
            {
                var result = MessageBox.Show($"هل تريد حذف المورد '{selectedSupplier.Name}'؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    bool success = await _archiveService.DeleteSupplierAsync(selectedSupplier.Id);
                    if (success)
                    {
                        _suppliers.Remove(selectedSupplier);
                        SuppliersUpdated = true;
                        MessageBox.Show("تم حذف المورد بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("حدث خطأ أثناء حذف المورد", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد للحذف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف المورد: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnEditSupplier_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var button = sender as Button;
            var selectedSupplier = button?.Tag as Supplier ?? SuppliersDataGrid.SelectedItem as Supplier;
            
            if (selectedSupplier != null)
            {
                MessageBox.Show("سيتم إضافة نافذة تعديل المورد قريباً", "قيد التطوير", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("يرجى اختيار مورد للتعديل", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل المورد: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = SuppliersUpdated;
        Close();
    }
}
