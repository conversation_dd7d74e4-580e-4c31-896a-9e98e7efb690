<UserControl x:Class="GlassFactoryAccounting.Views.CustomersReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان مع زر العودة -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="BtnBack" Content="⬅️ عودة" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,8" Margin="0,0,20,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnBack_Click"/>
                
                <TextBlock Text="👥 تقرير العملاء" FontSize="24" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>
        
        <!-- محتوى التقرير -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                <TextBlock Text="👥" FontSize="80" HorizontalAlignment="Center" 
                         Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,20"/>
                
                <TextBlock Text="تقرير العملاء" FontSize="28" FontWeight="Bold" 
                         HorizontalAlignment="Center" Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,15"/>
                
                <TextBlock Text="هذا الموديول قيد التطوير" FontSize="18" 
                         HorizontalAlignment="Center" Foreground="#666" Margin="0,0,0,20"/>
                
                <TextBlock Text="سيتم إضافة تحليلات العملاء قريباً" FontSize="16" 
                         HorizontalAlignment="Center" Foreground="#888" Margin="0,0,0,30"/>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="📊 تحليل سلوك العملاء" FontSize="14" Margin="0,0,20,0"/>
                    <TextBlock Text="💰 أفضل العملاء" FontSize="14" Margin="0,0,20,0"/>
                    <TextBlock Text="📈 نمو قاعدة العملاء" FontSize="14"/>
                </StackPanel>
                
                <Button x:Name="BtnComingSoon" Content="🔔 إشعاري عند الإتاحة" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="20,10" Margin="0,30,0,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnComingSoon_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
