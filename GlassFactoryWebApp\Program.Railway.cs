using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using GlassFactoryWebApp.Data;
using GlassFactoryWebApp.Models;
using GlassFactoryWebApp.Services;
using GlassFactoryWebApp.Mappings;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.OpenApi.Models;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// Determine environment - Railway detection
var isRailway = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("RAILWAY_STATIC_URL"));
if (isRailway)
{
    builder.Configuration.AddJsonFile("appsettings.Railway.json", optional: true, reloadOnChange: true);
}

// Database connection
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

// Configure PostgreSQL for Railway
if (isRailway)
{
    var databaseUrl = Environment.GetEnvironmentVariable("DATABASE_URL");
    if (!string.IsNullOrEmpty(databaseUrl))
    {
        // Parse Railway PostgreSQL URL: postgres://user:password@host:port/database
        var uri = new Uri(databaseUrl);
        var host = uri.Host;
        var port = uri.Port;
        var database = uri.LocalPath.TrimStart('/');
        var userInfo = uri.UserInfo.Split(':');
        var username = userInfo[0];
        var password = userInfo.Length > 1 ? userInfo[1] : "";
        
        connectionString = $"Host={host};Port={port};Database={database};Username={username};Password={password};SSL Mode=Require;Trust Server Certificate=true";
    }
}

// Add Entity Framework
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(connectionString));

// Add Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequiredLength = 6;
    options.Password.RequiredUniqueChars = 1;

    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(5);
    options.Lockout.MaxFailedAccessAttempts = 5;
    options.Lockout.AllowedForNewUsers = true;

    options.User.AllowedUserNameCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-._@+";
    options.User.RequireUniqueEmail = true;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// JWT Configuration
var jwtSecretKey = Environment.GetEnvironmentVariable("JWT_SECRET_KEY") ?? 
                   "GlassFactorySecretKey2025VeryLongAndSecureForProduction!";

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = "GlassFactoryApp",
        ValidAudience = "GlassFactoryUsers",
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecretKey))
    };
});

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(SalesMappingProfile));

// Add application services
builder.Services.AddScoped<ISalesService, SalesService>();
builder.Services.AddScoped<IAccountingService, AccountingService>();
builder.Services.AddScoped<IInventoryService, InventoryService>();
builder.Services.AddScoped<IPdfService, PdfService>();

// Add controllers
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// Add Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Glass Factory Accounting System API",
        Version = "v2.0",
        Description = "نظام حسابات مصنع الزجاج - واجهة برمجة التطبيقات",
        Contact = new OpenApiContact
        {
            Name = "حسام محمد حسان أحمد",
            Email = "<EMAIL>"
        }
    });

    c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
    {
        Description = "JWT Authorization header using the Bearer scheme",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        Scheme = "Bearer"
    });

    c.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference
                {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            new string[] {}
        }
    });
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// Add Health Checks
builder.Services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>();

var app = builder.Build();

// Configure the HTTP request pipeline
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Glass Factory API v2.0");
    c.RoutePrefix = "swagger";
    c.DocumentTitle = "Glass Factory API Documentation";
});

app.UseCors();
app.UseStaticFiles();

app.UseAuthentication();
app.UseRouting();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Serve React app for all other routes
app.MapFallbackToFile("index.html");

// Database initialization
using (var scope = app.Services.CreateScope())
{
    try
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

        // Ensure database is created
        await context.Database.EnsureCreatedAsync();

        // Run migrations if any
        if (context.Database.GetPendingMigrations().Any())
        {
            await context.Database.MigrateAsync();
        }

        // Seed initial data
        await SeedInitialDataAsync(context, userManager, roleManager);
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Database initialization failed");
    }
}

Log.Information("🚀 Glass Factory Accounting System started!");
Log.Information("🌐 Environment: {Environment}", isRailway ? "Railway" : "Development");

app.Run();

// Seed data method
static async Task SeedInitialDataAsync(ApplicationDbContext context, UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
{
    // Create roles
    var roles = new[] { "Admin", "Manager", "User", "Accountant", "SalesManager" };
    foreach (var role in roles)
    {
        if (!await roleManager.RoleExistsAsync(role))
        {
            await roleManager.CreateAsync(new IdentityRole(role));
        }
    }

    // Create admin user
    var adminEmail = "<EMAIL>";
    var adminUser = await userManager.FindByEmailAsync(adminEmail);
    if (adminUser == null)
    {
        adminUser = new ApplicationUser
        {
            UserName = adminEmail,
            Email = adminEmail,
            EmailConfirmed = true,
            FirstName = "مدير",
            LastName = "النظام",
            IsActive = true
        };

        var result = await userManager.CreateAsync(adminUser, "Admin123!");
        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(adminUser, "Admin");
        }
    }

    // Seed sample data if database is empty
    if (!context.Customers.Any())
    {
        // Sample customers
        var customers = new[]
        {
            new Customer
            {
                CustomerCode = "CUST-001",
                CustomerName = "شركة الزجاج المتطور",
                Phone = "0501234567",
                Email = "<EMAIL>",
                Address = "الرياض، المملكة العربية السعودية",
                CreditLimit = 50000,
                PaymentTermDays = 30,
                CustomerType = "شركة",
                IsActive = true
            },
            new Customer
            {
                CustomerCode = "CUST-002", 
                CustomerName = "مؤسسة البناء الحديث",
                Phone = "0507654321",
                Email = "<EMAIL>",
                Address = "جدة، المملكة العربية السعودية",
                CreditLimit = 75000,
                PaymentTermDays = 45,
                CustomerType = "مؤسسة",
                IsActive = true
            }
        };

        context.Customers.AddRange(customers);
        await context.SaveChangesAsync();
    }

    // Sample product categories
    if (!context.ProductCategories.Any())
    {
        var categories = new[]
        {
            new ProductCategory
            {
                CategoryCode = "GLASS",
                CategoryName = "منتجات الزجاج",
                Description = "جميع أنواع الزجاج والمنتجات الزجاجية",
                IsActive = true
            },
            new ProductCategory
            {
                CategoryCode = "TOOLS",
                CategoryName = "أدوات ومعدات",
                Description = "أدوات ومعدات التصنيع والتركيب",
                IsActive = true
            }
        };

        context.ProductCategories.AddRange(categories);
        await context.SaveChangesAsync();
    }

    // Sample products
    if (!context.Products.Any())
    {
        var glassCategory = context.ProductCategories.First(c => c.CategoryCode == "GLASS");
        
        var products = new[]
        {
            new Product
            {
                ProductCode = "GLASS-001",
                ProductName = "زجاج شفاف 6مم",
                ProductDescription = "زجاج شفاف عالي الجودة سماكة 6 مليمتر",
                CategoryId = glassCategory.Id,
                Unit = "متر مربع",
                SalePrice = 150,
                CostPrice = 100,
                MinimumStock = 50,
                CurrentStock = 200,
                IsActive = true
            },
            new Product
            {
                ProductCode = "GLASS-002",
                ProductName = "زجاج مقسى 8مم",
                ProductDescription = "زجاج مقسى للأمان سماكة 8 مليمتر",
                CategoryId = glassCategory.Id,
                Unit = "متر مربع", 
                SalePrice = 250,
                CostPrice = 180,
                MinimumStock = 30,
                CurrentStock = 100,
                IsActive = true
            }
        };

        context.Products.AddRange(products);
        await context.SaveChangesAsync();
    }

    Log.Information("✅ Sample data seeded successfully");
}
