using System.Windows;
using Microsoft.Win32;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة إعدادات بيانات الشركة
/// </summary>
public partial class CompanySettingsWindow : Window
{
    private readonly SettingsService _settingsService;

    public CompanySettingsWindow()
    {
        InitializeComponent();
        _settingsService = new SettingsService();
        LoadSettings();
    }

    private void LoadSettings()
    {
        var settings = _settingsService.GetSettings();
        
        TxtCompanyName.Text = settings.CompanyName;
        TxtCompanyAddress.Text = settings.CompanyAddress;
        TxtCompanyPhone.Text = settings.CompanyPhone;
        TxtCompanyEmail.Text = settings.CompanyEmail;
        TxtTaxNumber.Text = settings.TaxNumber;
        TxtDefaultTaxRate.Text = settings.DefaultTaxRate.ToString();
        TxtCurrency.Text = settings.Currency;
        ChkAutoBackup.IsChecked = settings.AutoBackup;
        TxtBackupInterval.Text = settings.BackupIntervalDays.ToString();
        TxtBackupPath.Text = settings.BackupPath;
    }

    private void BtnBrowseBackupPath_Click(object sender, RoutedEventArgs e)
    {
        var folderDialog = new OpenFolderDialog
        {
            Title = "اختر مجلد النسخ الاحتياطي"
        };

        if (folderDialog.ShowDialog() == true)
        {
            TxtBackupPath.Text = folderDialog.FolderName;
        }
    }

    private void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var settings = _settingsService.GetSettings();
            
            settings.CompanyName = TxtCompanyName.Text;
            settings.CompanyAddress = TxtCompanyAddress.Text;
            settings.CompanyPhone = TxtCompanyPhone.Text;
            settings.CompanyEmail = TxtCompanyEmail.Text;
            settings.TaxNumber = TxtTaxNumber.Text;
            
            if (decimal.TryParse(TxtDefaultTaxRate.Text, out decimal taxRate))
                settings.DefaultTaxRate = taxRate;
            
            settings.Currency = TxtCurrency.Text;
            settings.AutoBackup = ChkAutoBackup.IsChecked ?? false;
            
            if (int.TryParse(TxtBackupInterval.Text, out int interval))
                settings.BackupIntervalDays = interval;
            
            settings.BackupPath = TxtBackupPath.Text;
            
            _settingsService.SaveSettings(settings);
            
            MessageBox.Show("تم حفظ إعدادات الشركة بنجاح", "نجح", 
                MessageBoxButton.OK, MessageBoxImage.Information);
            
            DialogResult = true;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ الإعدادات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}
