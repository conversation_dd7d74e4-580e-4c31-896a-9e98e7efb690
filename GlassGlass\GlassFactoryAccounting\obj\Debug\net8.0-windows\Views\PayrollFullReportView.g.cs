﻿#pragma checksum "..\..\..\..\Views\PayrollFullReportView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7EA5F8C7CCA8D0CABED21CE800AF087626F17C02"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// PayrollFullReportView
    /// </summary>
    public partial class PayrollFullReportView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 152 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpFromDate;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpToDate;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbEmployee;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFilter;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReset;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid FullReportDataGrid;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportAll;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\..\Views\PayrollFullReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/payrollfullreportview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\PayrollFullReportView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 2:
            this.DpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.CmbEmployee = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.BtnFilter = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\..\Views\PayrollFullReportView.xaml"
            this.BtnFilter.Click += new System.Windows.RoutedEventHandler(this.BtnFilter_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnReset = ((System.Windows.Controls.Button)(target));
            
            #line 165 "..\..\..\..\Views\PayrollFullReportView.xaml"
            this.BtnReset.Click += new System.Windows.RoutedEventHandler(this.BtnReset_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FullReportDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 183 "..\..\..\..\Views\PayrollFullReportView.xaml"
            this.FullReportDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.FullReportDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 216 "..\..\..\..\Views\PayrollFullReportView.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnExportAll = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\Views\PayrollFullReportView.xaml"
            this.BtnExportAll.Click += new System.Windows.RoutedEventHandler(this.BtnExportAll_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 218 "..\..\..\..\Views\PayrollFullReportView.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 7:
            
            #line 201 "..\..\..\..\Views\PayrollFullReportView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnSettle_Click);
            
            #line default
            #line hidden
            break;
            case 8:
            
            #line 203 "..\..\..\..\Views\PayrollFullReportView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnExportPDF_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

