using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using System.Linq;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة إدارة الخدمات المتاحة
/// </summary>
public partial class ServicesManagementWindow : Window
{
    private ObservableCollection<Service> _services;

    public ServicesManagementWindow(ObservableCollection<Service> services)
    {
        InitializeComponent();
        _services = services;
        ServicesDataGrid.ItemsSource = _services;
    }

    private void BtnAddService_Click(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(TxtServiceName.Text))
        {
            MessageBox.Show("يرجى إدخال اسم الخدمة", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        // التحقق من عدم تكرار الخدمة
        if (_services.Any(s => s.Name.Equals(TxtServiceName.Text, StringComparison.OrdinalIgnoreCase)))
        {
            MessageBox.Show("هذه الخدمة موجودة بالفعل", "تنبيه", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        var newService = new Service
        {
            Id = _services.Count + 1,
            Name = TxtServiceName.Text.Trim(),
            Description = TxtServiceDescription.Text.Trim()
        };

        _services.Add(newService);

        // مسح الحقول
        TxtServiceName.Text = "";
        TxtServiceDescription.Text = "";

        MessageBox.Show("تم إضافة الخدمة بنجاح", "نجح", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnDeleteService_Click(object sender, RoutedEventArgs e)
    {
        if (ServicesDataGrid.SelectedItem is Service selectedService)
        {
            var result = MessageBox.Show($"هل تريد حذف الخدمة '{selectedService.Name}'؟", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                _services.Remove(selectedService);
                MessageBox.Show("تم حذف الخدمة بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار خدمة للحذف", "تنبيه", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        // حفظ الخدمات (يمكن إضافة حفظ في قاعدة البيانات لاحقاً)
        DialogResult = true;
        Close();
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }
}
