<UserControl x:Class="GlassFactoryAccounting.Views.ExpenseTypesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect" Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
    </UserControl.Resources>

    <Grid Margin="20" Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🏷️" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إدارة أنواع المصروفات" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                    <TextBlock Text="إضافة وتعديل أنواع المصروفات الرئيسية والفرعية" FontSize="14" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- نموذج إضافة نوع جديد -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="➕ إضافة نوع مصروف جديد" FontSize="18" FontWeight="Bold" 
                           Margin="0,0,0,20" Foreground="#2C3E50"/>

                <!-- حقول الإدخال -->
                <Grid Grid.Row="1" Margin="12,0,12,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                    </Grid.ColumnDefinitions>

                    <!-- اسم النوع -->
                    <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="اسم النوع:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtTypeName" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#E74C3C" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- الوصف -->
                    <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="الوصف:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtDescription" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#27AE60" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- النوع الأب -->
                    <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="النوع الأب (اختياري):" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <ComboBox x:Name="CmbParentType" Height="50"
                                      VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                      FontSize="16" Padding="15,10"
                                      BorderBrush="#9B59B6" BorderThickness="3"
                                      Background="White"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- الأزرار -->
                <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="BtnAddType" Content="➕ إضافة النوع" 
                            Style="{StaticResource SuccessButtonStyle}"
                            Width="150" Height="45" FontSize="14" FontWeight="Bold"
                            Margin="0,0,15,0" Click="BtnAddType_Click"/>
                    <Button x:Name="BtnClearForm" Content="🗑️ مسح النموذج" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Width="150" Height="45" FontSize="14" FontWeight="Bold"
                            Background="#95A5A6" Click="BtnClearForm_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- قائمة أنواع المصروفات -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="📋 قائمة أنواع المصروفات" FontSize="18" FontWeight="Bold" 
                           Margin="0,0,0,15" Foreground="#2C3E50"/>

                <DataGrid Grid.Row="1" x:Name="DgExpenseTypes" AutoGenerateColumns="False"
                          CanUserAddRows="False" CanUserDeleteRows="False"
                          GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                          Background="White" RowBackground="#FAFAFA" AlternatingRowBackground="#F0F0F0"
                          BorderBrush="#E0E0E0" BorderThickness="1"
                          FontSize="14" RowHeight="40">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="80" IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#34495E"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="اسم النوع" Binding="{Binding Name}" Width="200" IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#34495E"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*" IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#34495E"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="النوع الأب" Binding="{Binding ParentType}" Width="150" IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#34495E"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120" IsReadOnly="True">
                            <DataGridTextColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#34495E"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10"/>
                                </Style>
                            </DataGridTextColumn.HeaderStyle>
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.HeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background" Value="#34495E"/>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="10"/>
                                </Style>
                            </DataGridTemplateColumn.HeaderStyle>
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="✏️" ToolTip="تعديل" Width="30" Height="30" 
                                                Background="#3498DB" Foreground="White" BorderThickness="0"
                                                Margin="2" Click="BtnEdit_Click"/>
                                        <Button Content="🗑️" ToolTip="حذف" Width="30" Height="30" 
                                                Background="#E74C3C" Foreground="White" BorderThickness="0"
                                                Margin="2" Click="BtnDelete_Click"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
