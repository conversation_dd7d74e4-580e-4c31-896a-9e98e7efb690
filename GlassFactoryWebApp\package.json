{"name": "glass-factory-web-app", "version": "2.0.0", "description": "نظام حسابات مصنع الزجاج - Glass Factory Accounting System", "main": "Program.cs", "scripts": {"build": "dotnet publish -c Release -o ./publish", "start": "dotnet ./publish/GlassFactoryWebApp.dll", "dev": "dotnet run", "client:install": "cd client && npm install", "client:build": "cd client && npm run build", "client:start": "cd client && npm start", "deploy": "node deploy-to-railway-now.js"}, "repository": {"type": "git", "url": "https://github.com/YOUR_USERNAME/GlassFactoryWebApp.git"}, "keywords": ["accounting", "glass-factory", "sales", "invoicing", "arabic", "rtl", "asp.net-core", "react", "typescript", "postgresql"], "author": "حسام محمد حسان أحمد", "license": "MIT", "engines": {"node": ">=18.0.0", "dotnet": ">=8.0.0"}, "devDependencies": {"concurrently": "^8.2.0"}}