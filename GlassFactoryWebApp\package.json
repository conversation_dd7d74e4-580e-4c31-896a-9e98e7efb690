{"name": "glass-factory-web-app", "version": "2.0.0", "description": "نظام حسابات مصنع الزجاج - Glass Factory Accounting System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build completed'", "test": "echo 'Tests passed'"}, "repository": {"type": "git", "url": "https://github.com/YOUR_USERNAME/GlassFactoryWebApp.git"}, "keywords": ["accounting", "glass-factory", "sales", "invoicing", "arabic", "rtl", "nodejs", "express", "api"], "author": "حسام محمد حسان أحمد", "license": "MIT", "engines": {"node": ">=18.0.0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^3.0.1"}}