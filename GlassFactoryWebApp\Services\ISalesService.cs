using GlassFactoryWebApp.DTOs;
using GlassFactoryWebApp.Models;

namespace GlassFactoryWebApp.Services
{
    /// <summary>
    /// واجهة خدمة المبيعات
    /// </summary>
    public interface ISalesService
    {
        // Sales Invoices
        Task<PagedResult<SalesInvoiceDto>> GetSalesInvoicesAsync(int page, int pageSize, string? search = null, 
            DateTime? fromDate = null, DateTime? toDate = null, string? status = null);
        Task<SalesInvoiceDto?> GetSalesInvoiceByIdAsync(int id);
        Task<SalesInvoiceDto> CreateSalesInvoiceAsync(CreateSalesInvoiceDto createDto);
        Task<SalesInvoiceDto?> UpdateSalesInvoiceAsync(int id, UpdateSalesInvoiceDto updateDto);
        Task<bool> DeleteSalesInvoiceAsync(int id);
        Task<bool> PostSalesInvoiceAsync(int id);
        Task<bool> UnpostSalesInvoiceAsync(int id);
        Task<byte[]?> GenerateInvoicePdfAsync(int id);

        // Customers
        Task<PagedResult<CustomerDto>> GetCustomersAsync(int page, int pageSize, string? search = null);
        Task<CustomerDto?> GetCustomerByIdAsync(int id);
        Task<CustomerDto> CreateCustomerAsync(CreateCustomerDto createDto);
        Task<CustomerDto?> UpdateCustomerAsync(int id, UpdateCustomerDto updateDto);
        Task<bool> DeleteCustomerAsync(int id);
        Task<List<CustomerDto>> SearchCustomersAsync(string searchTerm);

        // Customer Payments
        Task<PagedResult<CustomerPaymentDto>> GetCustomerPaymentsAsync(int customerId, int page, int pageSize);
        Task<CustomerPaymentDto> CreateCustomerPaymentAsync(CreateCustomerPaymentDto createDto);
        Task<bool> DeleteCustomerPaymentAsync(int id);

        // Reports and Statistics
        Task<SalesStatisticsDto> GetSalesStatisticsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<byte[]> GenerateSalesReportAsync(DateTime? fromDate = null, DateTime? toDate = null, 
            int? customerId = null, string format = "pdf");
        Task<List<SalesChartDataDto>> GetSalesChartDataAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<List<TopCustomerDto>> GetTopCustomersAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);
        Task<List<TopProductDto>> GetTopProductsAsync(int count = 10, DateTime? fromDate = null, DateTime? toDate = null);

        // Invoice Items
        Task<List<SalesInvoiceItemDto>> GetInvoiceItemsAsync(int invoiceId);
        Task<SalesInvoiceItemDto> AddInvoiceItemAsync(int invoiceId, CreateSalesInvoiceItemDto createDto);
        Task<SalesInvoiceItemDto?> UpdateInvoiceItemAsync(int itemId, UpdateSalesInvoiceItemDto updateDto);
        Task<bool> DeleteInvoiceItemAsync(int itemId);

        // Validation and Business Logic
        Task<bool> ValidateInvoiceAsync(int invoiceId);
        Task<decimal> CalculateInvoiceTotalAsync(int invoiceId);
        Task<bool> CheckCustomerCreditLimitAsync(int customerId, decimal amount);
        Task<string> GenerateInvoiceNumberAsync();
        Task<bool> CanDeleteInvoiceAsync(int invoiceId);
        Task<bool> CanEditInvoiceAsync(int invoiceId);

        // Integration with other modules
        Task<bool> UpdateInventoryFromSalesAsync(int invoiceId);
        Task<bool> CreateAccountingEntriesAsync(int invoiceId);
        Task<bool> ReverseAccountingEntriesAsync(int invoiceId);
    }
}
