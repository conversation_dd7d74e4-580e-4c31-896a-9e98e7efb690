using System.ComponentModel.DataAnnotations;

namespace GlassFactoryWebApp.DTOs
{
    /// <summary>
    /// DTO العميل
    /// </summary>
    public class CustomerDto
    {
        public int Id { get; set; }
        public string CustomerCode { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string? ContactPerson { get; set; }
        public string? Phone { get; set; }
        public string? Mobile { get; set; }
        public string? Email { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? Country { get; set; }
        public string? TaxNumber { get; set; }
        public string? CommercialRegister { get; set; }
        public decimal CreditLimit { get; set; }
        public int PaymentTermDays { get; set; }
        public decimal CurrentBalance { get; set; }
        public bool IsActive { get; set; }
        public string CustomerType { get; set; } = string.Empty;
        public decimal DiscountPercentage { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;

        // إحصائيات العميل
        public int TotalInvoices { get; set; }
        public decimal TotalSales { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal TotalRemaining { get; set; }
        public DateTime? LastInvoiceDate { get; set; }
        public DateTime? LastPaymentDate { get; set; }
    }

    /// <summary>
    /// DTO إنشاء عميل
    /// </summary>
    public class CreateCustomerDto
    {
        [Required(ErrorMessage = "اسم العميل مطلوب")]
        [MaxLength(200, ErrorMessage = "اسم العميل لا يجب أن يتجاوز 200 حرف")]
        public string CustomerName { get; set; } = string.Empty;

        [MaxLength(100, ErrorMessage = "اسم جهة الاتصال لا يجب أن يتجاوز 100 حرف")]
        public string? ContactPerson { get; set; }

        [MaxLength(20, ErrorMessage = "رقم الهاتف لا يجب أن يتجاوز 20 حرف")]
        public string? Phone { get; set; }

        [MaxLength(20, ErrorMessage = "رقم الجوال لا يجب أن يتجاوز 20 حرف")]
        public string? Mobile { get; set; }

        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [MaxLength(100, ErrorMessage = "البريد الإلكتروني لا يجب أن يتجاوز 100 حرف")]
        public string? Email { get; set; }

        [MaxLength(500, ErrorMessage = "العنوان لا يجب أن يتجاوز 500 حرف")]
        public string? Address { get; set; }

        [MaxLength(100, ErrorMessage = "المدينة لا يجب أن يتجاوز 100 حرف")]
        public string? City { get; set; }

        [MaxLength(100, ErrorMessage = "الدولة لا يجب أن يتجاوز 100 حرف")]
        public string? Country { get; set; }

        [MaxLength(20, ErrorMessage = "الرقم الضريبي لا يجب أن يتجاوز 20 حرف")]
        public string? TaxNumber { get; set; }

        [MaxLength(20, ErrorMessage = "السجل التجاري لا يجب أن يتجاوز 20 حرف")]
        public string? CommercialRegister { get; set; }

        [Range(0, double.MaxValue, ErrorMessage = "حد الائتمان يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CreditLimit { get; set; } = 0;

        [Range(1, 365, ErrorMessage = "مدة السداد يجب أن تكون بين 1 و 365 يوم")]
        public int PaymentTermDays { get; set; } = 30;

        public string CustomerType { get; set; } = "عادي";

        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercentage { get; set; } = 0;

        public string? Notes { get; set; }
    }

    /// <summary>
    /// DTO تحديث عميل
    /// </summary>
    public class UpdateCustomerDto : CreateCustomerDto
    {
        public int Id { get; set; }
        public bool IsActive { get; set; } = true;
    }

    /// <summary>
    /// DTO دفعة العميل
    /// </summary>
    public class CustomerPaymentDto
    {
        public int Id { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public int? SalesInvoiceId { get; set; }
        public string? InvoiceNumber { get; set; }
        public string PaymentNumber { get; set; } = string.Empty;
        public DateTime PaymentDate { get; set; }
        public decimal PaymentAmount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string? CheckNumber { get; set; }
        public string? BankName { get; set; }
        public DateTime? CheckDate { get; set; }
        public string? TransferReference { get; set; }
        public string? PaymentNotes { get; set; }
        public string PaymentStatus { get; set; } = string.Empty;
        public bool IsPosted { get; set; }
        public DateTime? PostedAt { get; set; }
        public string? PostedBy { get; set; }
        public string? ReceivedBy { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO إنشاء دفعة عميل
    /// </summary>
    public class CreateCustomerPaymentDto
    {
        [Required(ErrorMessage = "العميل مطلوب")]
        public int CustomerId { get; set; }

        public int? SalesInvoiceId { get; set; }

        [Required(ErrorMessage = "تاريخ الدفعة مطلوب")]
        public DateTime PaymentDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "مبلغ الدفعة مطلوب")]
        [Range(0.01, double.MaxValue, ErrorMessage = "مبلغ الدفعة يجب أن يكون أكبر من صفر")]
        public decimal PaymentAmount { get; set; }

        [Required(ErrorMessage = "طريقة الدفع مطلوبة")]
        public string PaymentMethod { get; set; } = "نقدي";

        public string? CheckNumber { get; set; }
        public string? BankName { get; set; }
        public DateTime? CheckDate { get; set; }
        public string? TransferReference { get; set; }
        public string? PaymentNotes { get; set; }
        public string? ReceivedBy { get; set; }
    }

    /// <summary>
    /// DTO تحديث دفعة عميل
    /// </summary>
    public class UpdateCustomerPaymentDto : CreateCustomerPaymentDto
    {
        public int Id { get; set; }
        public string PaymentStatus { get; set; } = "مؤكد";
    }

    /// <summary>
    /// DTO كشف حساب العميل
    /// </summary>
    public class CustomerStatementDto
    {
        public CustomerDto Customer { get; set; } = null!;
        public List<CustomerStatementItemDto> Items { get; set; } = new();
        public decimal OpeningBalance { get; set; }
        public decimal TotalDebits { get; set; }
        public decimal TotalCredits { get; set; }
        public decimal ClosingBalance { get; set; }
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }
    }

    /// <summary>
    /// DTO عنصر كشف حساب العميل
    /// </summary>
    public class CustomerStatementItemDto
    {
        public DateTime Date { get; set; }
        public string Description { get; set; } = string.Empty;
        public string ReferenceNumber { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // فاتورة، دفعة، رصيد افتتاحي
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public decimal Balance { get; set; }
    }

    /// <summary>
    /// DTO تقرير أعمار الديون
    /// </summary>
    public class CustomerAgingDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal Current { get; set; } // 0-30 يوم
        public decimal Days30 { get; set; } // 31-60 يوم
        public decimal Days60 { get; set; } // 61-90 يوم
        public decimal Days90 { get; set; } // 91-120 يوم
        public decimal Over120 { get; set; } // أكثر من 120 يوم
        public decimal TotalBalance { get; set; }
        public DateTime? LastPaymentDate { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
    }

    /// <summary>
    /// DTO ملخص أعمار الديون
    /// </summary>
    public class AgingSummaryDto
    {
        public decimal TotalCurrent { get; set; }
        public decimal TotalDays30 { get; set; }
        public decimal TotalDays60 { get; set; }
        public decimal TotalDays90 { get; set; }
        public decimal TotalOver120 { get; set; }
        public decimal GrandTotal { get; set; }
        public int TotalCustomers { get; set; }
        public DateTime ReportDate { get; set; }
    }
}
