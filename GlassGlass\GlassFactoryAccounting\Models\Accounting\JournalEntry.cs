using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace GlassFactoryAccounting.Models.Accounting
{
    /// <summary>
    /// نموذج قيد اليومية
    /// </summary>
    public class JournalEntry
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(20)]
        public string EntryNumber { get; set; } = "";
        
        [Required]
        public DateTime EntryDate { get; set; } = DateTime.Now;
        
        [Required]
        [StringLength(500)]
        public string Description { get; set; } = "";
        
        [StringLength(50)]
        public string? ReferenceNumber { get; set; }
        
        [StringLength(100)]
        public string? ReferenceType { get; set; }
        
        public int? ReferenceId { get; set; }
        
        public List<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();
        
        public decimal TotalDebit => Details.Sum(d => d.DebitAmount);
        
        public decimal TotalCredit => Details.Sum(d => d.CreditAmount);
        
        public bool IsBalanced => TotalDebit == TotalCredit;
        
        public bool IsPosted { get; set; } = false;
        
        public DateTime? PostedDate { get; set; }
        
        public string? PostedBy { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public string CreatedBy { get; set; } = "";
        
        public DateTime? ModifiedDate { get; set; }
        
        public string? ModifiedBy { get; set; }
        
        [StringLength(1000)]
        public string? Notes { get; set; }
    }
    
    /// <summary>
    /// تفاصيل قيد اليومية
    /// </summary>
    public class JournalEntryDetail
    {
        public int Id { get; set; }
        
        public int JournalEntryId { get; set; }
        
        public JournalEntry JournalEntry { get; set; } = null!;
        
        public int AccountId { get; set; }
        
        public Account Account { get; set; } = null!;
        
        public decimal DebitAmount { get; set; }
        
        public decimal CreditAmount { get; set; }
        
        [StringLength(500)]
        public string? Description { get; set; }
        
        public int LineNumber { get; set; }
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        /// <summary>
        /// نوع الحركة (مدين أو دائن)
        /// </summary>
        public TransactionType TransactionType => DebitAmount > 0 ? TransactionType.Debit : TransactionType.Credit;
        
        /// <summary>
        /// المبلغ (سواء مدين أو دائن)
        /// </summary>
        public decimal Amount => DebitAmount > 0 ? DebitAmount : CreditAmount;
    }
    
    /// <summary>
    /// نوع الحركة المحاسبية
    /// </summary>
    public enum TransactionType
    {
        /// <summary>
        /// مدين
        /// </summary>
        Debit = 1,
        
        /// <summary>
        /// دائن
        /// </summary>
        Credit = 2
    }
}
