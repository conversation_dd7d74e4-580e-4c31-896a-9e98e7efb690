using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using Microsoft.Win32;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة فاتورة مشتريات جديدة
/// </summary>
public partial class NewPurchaseInvoiceView : UserControl, INotifyPropertyChanged
{
    private readonly MainWindow _mainWindow;
    private ObservableCollection<PurchaseItem> _invoiceItems;
    private ObservableCollection<Service> _services;
    private ObservableCollection<string> _glassTypes;
    private ObservableCollection<decimal> _glassThicknesses;
    private ObservableCollection<Supplier> _suppliers;
    
    private decimal _subTotal;
    private decimal _discountAmount;
    private decimal _finalTotal;

    public ObservableCollection<PurchaseItem> InvoiceItems
    {
        get => _invoiceItems;
        set
        {
            _invoiceItems = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<Service> Services
    {
        get => _services;
        set
        {
            _services = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<string> GlassTypes
    {
        get => _glassTypes;
        set
        {
            _glassTypes = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<decimal> GlassThicknesses
    {
        get => _glassThicknesses;
        set
        {
            _glassThicknesses = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<Supplier> Suppliers
    {
        get => _suppliers;
        set
        {
            _suppliers = value;
            OnPropertyChanged();
        }
    }

    public decimal SubTotal
    {
        get => _subTotal;
        set
        {
            _subTotal = value;
            OnPropertyChanged();
            TxtSubTotal.Text = $"{value:F2} ج.م";
            CalculateFinalTotal();
        }
    }

    public decimal DiscountAmount
    {
        get => _discountAmount;
        set
        {
            _discountAmount = value;
            OnPropertyChanged();
            TxtDiscountDisplay.Text = $"{value:F2} ج.م";
            CalculateFinalTotal();
        }
    }

    public decimal FinalTotal
    {
        get => _finalTotal;
        set
        {
            _finalTotal = value;
            OnPropertyChanged();
            TxtFinalTotal.Text = $"{value:F2} ج.م";
        }
    }

    public NewPurchaseInvoiceView(MainWindow mainWindow)
    {
        InitializeComponent();
        _mainWindow = mainWindow;
        
        InitializeData();
        LoadData();
        
        DataContext = this;
        InvoiceDataGrid.ItemsSource = InvoiceItems;
        
        // تعيين تاريخ اليوم
        DateInvoice.SelectedDate = DateTime.Now;
        
        // توليد رقم فاتورة تلقائي
        TxtInvoiceNumber.Text = $"PUR-{DateTime.Now:yyyyMMddHHmmss}";
    }

    private void InitializeData()
    {
        try
        {
            InvoiceItems = new ObservableCollection<PurchaseItem>();
            Services = new ObservableCollection<Service>();
            GlassTypes = new ObservableCollection<string>();
            GlassThicknesses = new ObservableCollection<decimal>();
            Suppliers = new ObservableCollection<Supplier>();

            // تعبئة أنواع الزجاج
            var glassTypes = GlassTypesService.GetAllGlassTypes();
            foreach (var glassType in glassTypes)
            {
                GlassTypes.Add(glassType);
            }

            // إضافة سماكات الزجاج من 1 إلى 100 ملم
            for (int i = 1; i <= 100; i++)
            {
                GlassThicknesses.Add(i);
            }

            // ربط الأحداث
            InvoiceItems.CollectionChanged += (s, e) => CalculateSubTotal();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void LoadData()
    {
        try
        {
            await LoadServices();
            await LoadSuppliers();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task LoadServices()
    {
        try
        {
            // إضافة خدمات افتراضية
            Services.Clear();
            Services.Add(new Service { Id = 1, Name = "زجاج شفاف", Description = "زجاج شفاف عادي" });
            Services.Add(new Service { Id = 2, Name = "زجاج ملون", Description = "زجاج ملون" });
            Services.Add(new Service { Id = 3, Name = "زجاج مقسى", Description = "زجاج مقسى للأمان" });
            Services.Add(new Service { Id = 4, Name = "زجاج عاكس", Description = "زجاج عاكس للحرارة" });
            Services.Add(new Service { Id = 5, Name = "زجاج مزدوج", Description = "زجاج مزدوج للعزل" });

            await Task.CompletedTask; // لتجنب تحذير async
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error loading services: {ex.Message}");
        }
    }

    private async Task LoadSuppliers()
    {
        try
        {
            var archiveService = new ArchiveService();
            var suppliers = await archiveService.GetAllSuppliersAsync();
            
            Suppliers.Clear();
            foreach (var supplier in suppliers)
            {
                Suppliers.Add(supplier);
            }
            
            CmbSupplierName.ItemsSource = Suppliers;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error loading suppliers: {ex.Message}");
        }
    }

    private void BtnBack_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _mainWindow.GoBack();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnAddNormalRow_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var newItem = new PurchaseItem
            {
                Id = InvoiceItems.Count + 1,
                Count = 1,
                IsManualRow = false
            };
            
            // ربط الأحداث للحسابات التلقائية
            newItem.PropertyChanged += (s, args) =>
            {
                if (s is PurchaseItem item)
                {
                    // حفظ نوع الزجاج الجديد تلقائياً
                    if (args.PropertyName == nameof(PurchaseItem.GlassType) && !string.IsNullOrWhiteSpace(item.GlassType))
                    {
                        GlassTypesService.EnsureGlassTypeExists(item.GlassType);

                        // تحديث قائمة أنواع الزجاج في الواجهة
                        if (!GlassTypes.Contains(item.GlassType))
                        {
                            GlassTypes.Add(item.GlassType);
                        }
                    }

                    if (args.PropertyName == nameof(PurchaseItem.TotalPrice))
                    {
                        CalculateSubTotal();
                    }
                }
            };
            
            InvoiceItems.Add(newItem);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة الصف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnAddManualRow_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var newItem = new PurchaseItem
            {
                Id = InvoiceItems.Count + 1,
                Count = 1,
                IsManualRow = true
            };
            
            // ربط الأحداث للحسابات التلقائية
            newItem.PropertyChanged += (s, args) =>
            {
                if (s is PurchaseItem item)
                {
                    // حفظ نوع الزجاج الجديد تلقائياً
                    if (args.PropertyName == nameof(PurchaseItem.GlassType) && !string.IsNullOrWhiteSpace(item.GlassType))
                    {
                        GlassTypesService.EnsureGlassTypeExists(item.GlassType);

                        // تحديث قائمة أنواع الزجاج في الواجهة
                        if (!GlassTypes.Contains(item.GlassType))
                        {
                            GlassTypes.Add(item.GlassType);
                        }
                    }

                    if (args.PropertyName == nameof(PurchaseItem.TotalPrice))
                    {
                        CalculateSubTotal();
                    }
                }
            };
            
            InvoiceItems.Add(newItem);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة الصف اليدوي: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnDeleteRow_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (InvoiceDataGrid.SelectedItem is PurchaseItem selectedItem)
            {
                InvoiceItems.Remove(selectedItem);
                
                // إعادة ترقيم الصفوف
                for (int i = 0; i < InvoiceItems.Count; i++)
                {
                    InvoiceItems[i].Id = i + 1;
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار صف للحذف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف الصف: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnManageServices_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var servicesWindow = new ServicesManagementWindow(Services);
            servicesWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح إدارة الخدمات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnAddSupplier_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var addSupplierWindow = new AddSupplierWindow();
            addSupplierWindow.Owner = Window.GetWindow(this);
            
            if (addSupplierWindow.ShowDialog() == true && addSupplierWindow.NewSupplier != null)
            {
                // إضافة المورد الجديد للقائمة
                Suppliers.Add(addSupplierWindow.NewSupplier);
                
                // اختيار المورد الجديد في القائمة المنسدلة
                CmbSupplierName.SelectedItem = addSupplierWindow.NewSupplier;
                
                MessageBox.Show("تم إضافة المورد بنجاح وتحديد اختياره في الفاتورة", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة المورد: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnManageSuppliers_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var suppliersWindow = new SuppliersManagementWindow();
            suppliersWindow.Owner = Window.GetWindow(this);

            if (suppliersWindow.ShowDialog() == true && suppliersWindow.SuppliersUpdated)
            {
                // تحديث قائمة الموردين
                LoadSuppliers();
                MessageBox.Show("تم تحديث قائمة الموردين", "تحديث",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح إدارة الموردين: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CalculateSubTotal()
    {
        try
        {
            SubTotal = InvoiceItems.Sum(item => item.TotalPrice);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error calculating subtotal: {ex.Message}");
        }
    }

    private void CalculateFinalTotal()
    {
        try
        {
            FinalTotal = SubTotal - DiscountAmount;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error calculating final total: {ex.Message}");
        }
    }

    private void ChkEnableDiscount_Checked(object sender, RoutedEventArgs e)
    {
        PnlDiscount.Visibility = Visibility.Visible;
        PnlDiscountDisplay.Visibility = Visibility.Visible;
    }

    private void ChkEnableDiscount_Unchecked(object sender, RoutedEventArgs e)
    {
        PnlDiscount.Visibility = Visibility.Collapsed;
        PnlDiscountDisplay.Visibility = Visibility.Collapsed;
        TxtDiscountAmount.Text = "0";
        DiscountAmount = 0;
    }

    private void ChkEnableNotes_Checked(object sender, RoutedEventArgs e)
    {
        TxtNotes.Visibility = Visibility.Visible;
    }

    private void ChkEnableNotes_Unchecked(object sender, RoutedEventArgs e)
    {
        TxtNotes.Visibility = Visibility.Collapsed;
        TxtNotes.Text = "";
    }

    private void TxtDiscountAmount_TextChanged(object sender, TextChangedEventArgs e)
    {
        try
        {
            if (decimal.TryParse(TxtDiscountAmount.Text, out decimal discount))
            {
                DiscountAmount = discount;
            }
            else
            {
                DiscountAmount = 0;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error parsing discount: {ex.Message}");
        }
    }

    private async void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            try
            {
                // إنشاء كائن الفاتورة
                var selectedSupplier = CmbSupplierName.SelectedItem as Supplier;
                var supplierName = selectedSupplier?.Name ?? CmbSupplierName.Text;

                var purchase = new Purchase
                {
                    InvoiceNumber = TxtInvoiceNumber.Text,
                    PurchaseDate = DateInvoice.SelectedDate ?? DateTime.Now,
                    SupplierId = selectedSupplier?.Id ?? 1,
                    TotalAmount = SubTotal,
                    Discount = DiscountAmount,
                    Tax = 0,
                    NetAmount = FinalTotal,
                    PaymentStatus = PaymentStatus.غير_مدفوع,
                    Notes = TxtNotes.Text,
                    Supplier = new Supplier { Name = supplierName }
                };

                // إضافة العناصر
                foreach (var item in InvoiceItems)
                {
                    purchase.PurchaseItems.Add(item);
                }

                // حفظ في قاعدة البيانات
                var archiveService = new ArchiveService();
                bool success = await archiveService.SavePurchaseAsync(purchase);

                if (success)
                {
                    MessageBox.Show("تم حفظ فاتورة المشتريات بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // العودة للصفحة السابقة
                    _mainWindow.GoBack();
                }
                else
                {
                    MessageBox.Show("حدث خطأ أثناء حفظ فاتورة المشتريات", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ فاتورة المشتريات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnSavePDF_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "PDF Files|*.pdf",
                FileName = $"Purchase_{TxtInvoiceNumber.Text}.pdf",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    // TODO: إنشاء خدمة PDF للمشتريات
                    MessageBox.Show("سيتم إضافة خدمة PDF للمشتريات قريباً", "قيد التطوير",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }

    private void BtnPrint_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            try
            {
                // TODO: إضافة وظيفة الطباعة للمشتريات
                MessageBox.Show("سيتم إضافة وظيفة الطباعة للمشتريات قريباً", "قيد التطوير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private bool ValidateInvoice()
    {
        var supplierName = (CmbSupplierName.SelectedItem as Supplier)?.Name ?? CmbSupplierName.Text;
        if (string.IsNullOrWhiteSpace(supplierName))
        {
            MessageBox.Show("يرجى إدخال أو اختيار اسم المورد", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }

        if (InvoiceItems.Count == 0)
        {
            MessageBox.Show("يرجى إضافة عناصر لفاتورة المشتريات", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }

        return true;
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
