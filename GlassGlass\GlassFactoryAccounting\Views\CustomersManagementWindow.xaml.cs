using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة إدارة العملاء الاحترافية
/// </summary>
public partial class CustomersManagementWindow : Window
{
    private ObservableCollection<Customer> _customers;
    private readonly SalesService _salesService;
    private readonly ArchiveService _archiveService;

    public ObservableCollection<Customer> Customers => _customers;
    public bool CustomersUpdated { get; private set; } = false;

    public CustomersManagementWindow()
    {
        InitializeComponent();
        _customers = new ObservableCollection<Customer>();
        _salesService = new SalesService();
        _archiveService = new ArchiveService();
        CustomersDataGrid.ItemsSource = _customers;
        LoadCustomers();
    }

    private async void LoadCustomers()
    {
        try
        {
            var customers = await _archiveService.GetAllCustomersAsync();
            _customers.Clear();
            foreach (var customer in customers)
            {
                _customers.Add(customer);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefresh_Click(object sender, RoutedEventArgs e)
    {
        LoadCustomers();
        MessageBox.Show("تم تحديث قائمة العملاء", "تحديث",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private async void BtnAddCustomer_Click(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrWhiteSpace(TxtCustomerName.Text))
        {
            MessageBox.Show("يرجى إدخال اسم العميل", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            return;
        }

        // التحقق من عدم تكرار العميل
        if (_customers.Any(c => c.Name.Equals(TxtCustomerName.Text, StringComparison.OrdinalIgnoreCase)))
        {
            MessageBox.Show("هذا العميل موجود بالفعل", "تنبيه",
                MessageBoxButton.OK, MessageBoxImage.Warning);
            return;
        }

        try
        {
            var newCustomer = new Customer
            {
                Name = TxtCustomerName.Text.Trim(),
                Phone = TxtCustomerPhone.Text.Trim(),
                Address = TxtCustomerAddress.Text.Trim(),
                Email = "",
                Balance = 0,
                Notes = ""
            };

            int customerId = await _archiveService.SaveCustomerAsync(newCustomer);

            if (customerId > 0)
            {
                newCustomer.Id = customerId;
                _customers.Add(newCustomer);
                CustomersUpdated = true;

                // مسح الحقول
                TxtCustomerName.Text = "";
                TxtCustomerPhone.Text = "";
                TxtCustomerAddress.Text = "";

                MessageBox.Show("تم إضافة العميل بنجاح", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء حفظ العميل", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إضافة العميل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void BtnDeleteCustomer_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var button = sender as Button;
            var selectedCustomer = button?.Tag as Customer ?? CustomersDataGrid.SelectedItem as Customer;

            if (selectedCustomer != null)
            {
                var result = MessageBox.Show($"هل تريد حذف العميل '{selectedCustomer.Name}'؟",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    bool success = await _archiveService.DeleteCustomerAsync(selectedCustomer.Id);
                    if (success)
                    {
                        _customers.Remove(selectedCustomer);
                        CustomersUpdated = true;
                        MessageBox.Show("تم حذف العميل بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("حدث خطأ أثناء حذف العميل", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل للحذف", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف العميل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnEditCustomer_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var button = sender as Button;
            var selectedCustomer = button?.Tag as Customer ?? CustomersDataGrid.SelectedItem as Customer;

            if (selectedCustomer != null)
            {
                MessageBox.Show("سيتم إضافة نافذة تعديل العميل قريباً", "قيد التطوير",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("يرجى اختيار عميل للتعديل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل العميل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = true;
        Close();
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = CustomersUpdated;
        Close();
    }
}
