import { apiClient } from './apiClient';

export interface SalesInvoice {
  id: number;
  invoiceNumber: string;
  invoiceDate: string;
  customerId: number;
  customerName: string;
  customerAddress: string;
  customerPhone: string;
  subTotal: number;
  discountPercentage: number;
  discountAmount: number;
  taxPercentage: number;
  taxAmount: number;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  paymentMethod: string;
  invoiceStatus: string;
  salesmanName: string;
  dueDate?: string;
  referenceNumber?: string;
  invoiceNotes?: string;
  isPrinted: boolean;
  isPosted: boolean;
  createdAt: string;
  createdBy: string;
  items: SalesInvoiceItem[];
  customer?: Customer;
}

export interface SalesInvoiceItem {
  id: number;
  salesInvoiceId: number;
  productId: number;
  productCode: string;
  productName: string;
  productDescription: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  discountPercentage: number;
  discountAmount: number;
  taxPercentage: number;
  taxAmount: number;
  totalPrice: number;
  lineNumber: number;
  itemNotes?: string;
  width?: number;
  height?: number;
  thickness?: number;
  glassType?: string;
  glassColor?: string;
  finishingType?: string;
}

export interface Customer {
  id: number;
  customerCode: string;
  customerName: string;
  contactPerson?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  commercialRegister?: string;
  creditLimit: number;
  paymentTermDays: number;
  currentBalance: number;
  isActive: boolean;
  customerType: string;
  discountPercentage: number;
  createdAt: string;
  createdBy: string;
  totalInvoices: number;
  totalSales: number;
  totalPaid: number;
  totalRemaining: number;
  lastInvoiceDate?: string;
  lastPaymentDate?: string;
}

export interface CustomerPayment {
  id: number;
  customerId: number;
  customerName: string;
  salesInvoiceId?: number;
  invoiceNumber?: string;
  paymentNumber: string;
  paymentDate: string;
  paymentAmount: number;
  paymentMethod: string;
  checkNumber?: string;
  bankName?: string;
  checkDate?: string;
  transferReference?: string;
  paymentNotes?: string;
  paymentStatus: string;
  isPosted: boolean;
  postedAt?: string;
  postedBy?: string;
  receivedBy?: string;
  createdAt: string;
  createdBy: string;
}

export interface CreateSalesInvoiceDto {
  invoiceNumber: string;
  invoiceDate: string;
  customerId: number;
  customerName: string;
  customerAddress: string;
  customerPhone: string;
  discountPercentage: number;
  taxPercentage: number;
  paymentMethod: string;
  salesmanName: string;
  dueDate?: string;
  referenceNumber?: string;
  invoiceNotes?: string;
  items: CreateSalesInvoiceItemDto[];
}

export interface CreateSalesInvoiceItemDto {
  productId: number;
  productCode: string;
  productName: string;
  productDescription: string;
  unit: string;
  quantity: number;
  unitPrice: number;
  discountPercentage: number;
  taxPercentage: number;
  itemNotes?: string;
  width?: number;
  height?: number;
  thickness?: number;
  glassType?: string;
  glassColor?: string;
  finishingType?: string;
}

export interface CreateCustomerDto {
  customerName: string;
  contactPerson?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  commercialRegister?: string;
  creditLimit: number;
  paymentTermDays: number;
  customerType: string;
  discountPercentage: number;
  notes?: string;
}

export interface CreateCustomerPaymentDto {
  customerId: number;
  salesInvoiceId?: number;
  paymentDate: string;
  paymentAmount: number;
  paymentMethod: string;
  checkNumber?: string;
  bankName?: string;
  checkDate?: string;
  transferReference?: string;
  paymentNotes?: string;
  receivedBy?: string;
}

export interface SalesStatistics {
  totalSales: number;
  totalPaid: number;
  totalRemaining: number;
  totalInvoices: number;
  paidInvoices: number;
  pendingInvoices: number;
  averageInvoiceValue: number;
  totalProfit: number;
  profitMargin: number;
  fromDate?: string;
  toDate?: string;
}

export interface PagedResult<T> {
  items: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface ApiResponse<T> {
  isSuccess: boolean;
  message: string;
  data: T;
  errors: string[];
  timestamp: string;
}

// خدمة المبيعات
export const salesService = {
  // فواتير المبيعات
  async getSalesInvoices(params: {
    page?: number;
    pageSize?: number;
    search?: string;
    fromDate?: Date;
    toDate?: Date;
    status?: string;
  }): Promise<ApiResponse<PagedResult<SalesInvoice>>> {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.fromDate) queryParams.append('fromDate', params.fromDate.toISOString());
    if (params.toDate) queryParams.append('toDate', params.toDate.toISOString());
    if (params.status) queryParams.append('status', params.status);

    const response = await apiClient.get(`/api/sales?${queryParams}`);
    return response.data;
  },

  async getSalesInvoice(id: number): Promise<ApiResponse<SalesInvoice>> {
    const response = await apiClient.get(`/api/sales/${id}`);
    return response.data;
  },

  async createSalesInvoice(data: CreateSalesInvoiceDto): Promise<ApiResponse<SalesInvoice>> {
    const response = await apiClient.post('/api/sales', data);
    return response.data;
  },

  async updateSalesInvoice(id: number, data: CreateSalesInvoiceDto): Promise<ApiResponse<SalesInvoice>> {
    const response = await apiClient.put(`/api/sales/${id}`, data);
    return response.data;
  },

  async deleteSalesInvoice(id: number): Promise<ApiResponse<boolean>> {
    const response = await apiClient.delete(`/api/sales/${id}`);
    return response.data;
  },

  async postSalesInvoice(id: number): Promise<ApiResponse<boolean>> {
    const response = await apiClient.post(`/api/sales/${id}/post`);
    return response.data;
  },

  async unpostSalesInvoice(id: number): Promise<ApiResponse<boolean>> {
    const response = await apiClient.post(`/api/sales/${id}/unpost`);
    return response.data;
  },

  async printSalesInvoice(id: number): Promise<Blob> {
    const response = await apiClient.get(`/api/sales/${id}/print`, {
      responseType: 'blob',
    });
    return response.data;
  },

  // العملاء
  async getCustomers(params: {
    page?: number;
    pageSize?: number;
    search?: string;
  }): Promise<ApiResponse<PagedResult<Customer>>> {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());
    if (params.search) queryParams.append('search', params.search);

    const response = await apiClient.get(`/api/customers?${queryParams}`);
    return response.data;
  },

  async getCustomer(id: number): Promise<ApiResponse<Customer>> {
    const response = await apiClient.get(`/api/customers/${id}`);
    return response.data;
  },

  async createCustomer(data: CreateCustomerDto): Promise<ApiResponse<Customer>> {
    const response = await apiClient.post('/api/customers', data);
    return response.data;
  },

  async updateCustomer(id: number, data: CreateCustomerDto): Promise<ApiResponse<Customer>> {
    const response = await apiClient.put(`/api/customers/${id}`, data);
    return response.data;
  },

  async deleteCustomer(id: number): Promise<ApiResponse<boolean>> {
    const response = await apiClient.delete(`/api/customers/${id}`);
    return response.data;
  },

  async searchCustomers(searchTerm: string): Promise<ApiResponse<Customer[]>> {
    const response = await apiClient.get(`/api/customers/search?term=${encodeURIComponent(searchTerm)}`);
    return response.data;
  },

  // مدفوعات العملاء
  async getCustomerPayments(customerId: number, params: {
    page?: number;
    pageSize?: number;
  }): Promise<ApiResponse<PagedResult<CustomerPayment>>> {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.pageSize) queryParams.append('pageSize', params.pageSize.toString());

    const response = await apiClient.get(`/api/customers/${customerId}/payments?${queryParams}`);
    return response.data;
  },

  async createCustomerPayment(data: CreateCustomerPaymentDto): Promise<ApiResponse<CustomerPayment>> {
    const response = await apiClient.post('/api/customer-payments', data);
    return response.data;
  },

  async deleteCustomerPayment(id: number): Promise<ApiResponse<boolean>> {
    const response = await apiClient.delete(`/api/customer-payments/${id}`);
    return response.data;
  },

  // الإحصائيات والتقارير
  async getSalesStatistics(params: {
    fromDate?: Date;
    toDate?: Date;
  }): Promise<ApiResponse<SalesStatistics>> {
    const queryParams = new URLSearchParams();
    
    if (params.fromDate) queryParams.append('fromDate', params.fromDate.toISOString());
    if (params.toDate) queryParams.append('toDate', params.toDate.toISOString());

    const response = await apiClient.get(`/api/sales/statistics?${queryParams}`);
    return response.data;
  },

  async generateSalesReport(params: {
    fromDate?: Date;
    toDate?: Date;
    customerId?: number;
    format?: string;
  }): Promise<Blob> {
    const queryParams = new URLSearchParams();
    
    if (params.fromDate) queryParams.append('fromDate', params.fromDate.toISOString());
    if (params.toDate) queryParams.append('toDate', params.toDate.toISOString());
    if (params.customerId) queryParams.append('customerId', params.customerId.toString());
    if (params.format) queryParams.append('format', params.format);

    const response = await apiClient.get(`/api/sales/report?${queryParams}`, {
      responseType: 'blob',
    });
    return response.data;
  },
};
