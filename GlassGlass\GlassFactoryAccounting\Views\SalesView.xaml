<UserControl x:Class="GlassFactoryAccounting.Views.SalesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900"
             FlowDirection="RightToLeft">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>

        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان والإحصائيات -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0">
                    <TextBlock Text="💰 إدارة المبيعات" FontSize="24" FontWeight="Bold"
                             Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,10"/>

                    <StackPanel Orientation="Horizontal">
                        <Border Background="{StaticResource PrimaryBrush}" CornerRadius="5" Padding="10,5" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📊" FontSize="16" Margin="0,0,5,0"/>
                                <TextBlock x:Name="TxtTotalSalesCount" Text="إجمالي المبيعات: 0"
                                         Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>

                        <Border Background="{StaticResource SuccessBrush}" CornerRadius="5" Padding="10,5" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💰" FontSize="16" Margin="0,0,5,0"/>
                                <TextBlock x:Name="TxtTotalAmount" Text="إجمالي القيمة: 0.00 ج.م"
                                         Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>

                        <Border Background="{StaticResource WarningBrush}" CornerRadius="5" Padding="10,5">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="👥" FontSize="16" Margin="0,0,5,0"/>
                                <TextBlock x:Name="TxtTotalCustomers" Text="عدد العملاء: 0"
                                         Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </StackPanel>

                <Button x:Name="BtnRefresh" Grid.Column="1" Content="🔄 تحديث"
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="10,5" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnRefresh_Click"/>
            </Grid>
        </Border>

        <!-- أزرار الإجراءات -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnNewSale" Content="➕ فاتورة مبيعات جديدة"
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnNewSale_Click"/>

                <Button x:Name="BtnManageCustomers" Content="👥 إدارة العملاء"
                        Background="{StaticResource WarningBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnManageCustomers_Click"/>

                <Button x:Name="BtnSalesReports" Content="📊 تقارير المبيعات"
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="20,10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSalesReports_Click"/>
            </StackPanel>
        </Border>
        
        <!-- قائمة فواتير المبيعات -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="📋 فواتير المبيعات المحفوظة" FontSize="18" FontWeight="Bold"
                         Margin="0,0,0,15" Foreground="{StaticResource PrimaryBrush}"/>
                
                <DataGrid x:Name="SalesDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">

                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                        <DataGridTextColumn Header="اسم العميل" Binding="{Binding Customer.Name}" Width="150"/>
                        <DataGridTextColumn Header="الخدمات" Binding="{Binding ServicesText}" Width="200"/>
                        <DataGridTextColumn Header="إجمالي الفاتورة" Binding="{Binding TotalAmount, StringFormat='{}{0:F2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat='{}{0:F2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="الإجمالي النهائي" Binding="{Binding NetAmount, StringFormat='{}{0:F2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="حالة الدفع" Binding="{Binding PaymentStatus}" Width="100"/>

                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️ عرض" Background="{StaticResource PrimaryBrush}"
                                                Foreground="White" Padding="5,2" Margin="2" FontSize="10"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnViewSale_Click" Tag="{Binding}"/>

                                        <Button Content="✏️ تعديل" Background="{StaticResource WarningBrush}"
                                                Foreground="White" Padding="5,2" Margin="2" FontSize="10"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnEditSale_Click" Tag="{Binding}"/>

                                        <Button Content="🗑️ حذف" Background="{StaticResource DangerBrush}"
                                                Foreground="White" Padding="5,2" Margin="2" FontSize="10"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnDeleteSale_Click" Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
