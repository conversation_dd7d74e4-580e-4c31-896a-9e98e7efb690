﻿#pragma checksum "..\..\..\..\Views\EmployeeBonusView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D6ECD3669A57BC1622CDEB10077CBEC29508C67F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// EmployeeBonusView
    /// </summary>
    public partial class EmployeeBonusView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 166 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbEmployee;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtEmployeeCode;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtPosition;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBranch;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBonusAmount;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbBonusReason;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpBonusDate;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid BonusesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 251 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClear;
        
        #line default
        #line hidden
        
        
        #line 252 "..\..\..\..\Views\EmployeeBonusView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/employeebonusview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\EmployeeBonusView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CmbEmployee = ((System.Windows.Controls.ComboBox)(target));
            
            #line 167 "..\..\..\..\Views\EmployeeBonusView.xaml"
            this.CmbEmployee.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbEmployee_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtEmployeeCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtPosition = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TxtBranch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtBonusAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 187 "..\..\..\..\Views\EmployeeBonusView.xaml"
            this.TxtBonusAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtBonusAmount_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CmbBonusReason = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 7:
            this.DpBonusDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 8:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.BonusesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 250 "..\..\..\..\Views\EmployeeBonusView.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnClear = ((System.Windows.Controls.Button)(target));
            
            #line 251 "..\..\..\..\Views\EmployeeBonusView.xaml"
            this.BtnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 252 "..\..\..\..\Views\EmployeeBonusView.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 10:
            
            #line 232 "..\..\..\..\Views\EmployeeBonusView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditBonus_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 236 "..\..\..\..\Views\EmployeeBonusView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteBonus_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

