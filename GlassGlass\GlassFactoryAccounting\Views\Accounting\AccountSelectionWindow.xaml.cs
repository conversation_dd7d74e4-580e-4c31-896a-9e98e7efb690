using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models.Accounting;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views.Accounting
{
    /// <summary>
    /// نافذة اختيار الحساب المحسنة
    /// </summary>
    public partial class AccountSelectionWindow : Window
    {
        private readonly AccountingService _accountingService;
        private List<Account> _allAccounts;
        private List<AccountTreeNode> _accountNodes;
        
        public Account? SelectedAccount { get; private set; }
        
        public AccountSelectionWindow()
        {
            InitializeComponent();
            _accountingService = new AccountingService();
            _allAccounts = new List<Account>();
            _accountNodes = new List<AccountTreeNode>();
            
            LoadAccounts();
        }
        
        /// <summary>
        /// تحميل الحسابات
        /// </summary>
        private void LoadAccounts()
        {
            try
            {
                _allAccounts = _accountingService.GetAllAccounts();
                BuildAccountTree();
                FilterAccounts();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// بناء شجرة الحسابات
        /// </summary>
        private void BuildAccountTree()
        {
            _accountNodes.Clear();
            
            // إنشاء عقد الحسابات الرئيسية
            var parentAccounts = _allAccounts.Where(a => a.ParentAccountId == null).ToList();
            
            foreach (var account in parentAccounts)
            {
                var node = CreateAccountNode(account);
                BuildChildNodes(node, account.Id);
                _accountNodes.Add(node);
            }
        }
        
        /// <summary>
        /// إنشاء عقدة حساب
        /// </summary>
        private AccountTreeNode CreateAccountNode(Account account)
        {
            return new AccountTreeNode
            {
                Account = account,
                AccountCode = account.AccountCode,
                AccountName = account.AccountName,
                AccountType = account.AccountType,
                AccountTypeDisplay = GetAccountTypeDisplay(account.AccountType),
                TypeIcon = GetAccountTypeIcon(account.AccountType),
                BalanceDisplay = account.Balance.ToString("N2"),
                UsabilityIndicator = account.CanBeUsedInEntries ? "✓ قابل للاستخدام" : "",
                Children = new List<AccountTreeNode>()
            };
        }
        
        /// <summary>
        /// بناء العقد الفرعية
        /// </summary>
        private void BuildChildNodes(AccountTreeNode parentNode, int parentAccountId)
        {
            var childAccounts = _allAccounts.Where(a => a.ParentAccountId == parentAccountId).ToList();
            
            foreach (var childAccount in childAccounts)
            {
                var childNode = CreateAccountNode(childAccount);
                BuildChildNodes(childNode, childAccount.Id);
                parentNode.Children.Add(childNode);
            }
        }
        
        /// <summary>
        /// الحصول على نص نوع الحساب
        /// </summary>
        private string GetAccountTypeDisplay(AccountType accountType)
        {
            return accountType switch
            {
                AccountType.Asset => "أصول",
                AccountType.Liability => "التزامات",
                AccountType.Equity => "حقوق ملكية",
                AccountType.Revenue => "إيرادات",
                AccountType.Expense => "مصروفات",
                _ => "غير محدد"
            };
        }
        
        /// <summary>
        /// الحصول على أيقونة نوع الحساب
        /// </summary>
        private string GetAccountTypeIcon(AccountType accountType)
        {
            return accountType switch
            {
                AccountType.Asset => "🏦",
                AccountType.Liability => "📋",
                AccountType.Equity => "💰",
                AccountType.Revenue => "📈",
                AccountType.Expense => "📉",
                _ => "📁"
            };
        }
        
        /// <summary>
        /// فلترة الحسابات
        /// </summary>
        private void FilterAccounts()
        {
            var filteredNodes = new List<AccountTreeNode>();
            
            foreach (var node in _accountNodes)
            {
                var filteredNode = FilterNode(node);
                if (filteredNode != null)
                {
                    filteredNodes.Add(filteredNode);
                }
            }
            
            tvAccounts.ItemsSource = filteredNodes;
        }
        
        /// <summary>
        /// فلترة عقدة واحدة
        /// </summary>
        private AccountTreeNode? FilterNode(AccountTreeNode node)
        {
            var searchText = txtSearch.Text?.ToLower() ?? "";
            var selectedType = (cmbAccountType.SelectedItem as ComboBoxItem)?.Tag?.ToString();
            var usableOnly = chkUsableOnly.IsChecked == true;
            
            // فحص العقدة الحالية
            bool nodeMatches = true;
            
            // فلتر البحث
            if (!string.IsNullOrEmpty(searchText))
            {
                nodeMatches = node.AccountCode.ToLower().Contains(searchText) ||
                             node.AccountName.ToLower().Contains(searchText);
            }
            
            // فلتر نوع الحساب
            if (!string.IsNullOrEmpty(selectedType))
            {
                nodeMatches = nodeMatches && node.AccountType.ToString() == selectedType;
            }
            
            // فلتر الحسابات القابلة للاستخدام
            if (usableOnly)
            {
                nodeMatches = nodeMatches && node.Account.CanBeUsedInEntries;
            }
            
            // فلترة العقد الفرعية
            var filteredChildren = new List<AccountTreeNode>();
            foreach (var child in node.Children)
            {
                var filteredChild = FilterNode(child);
                if (filteredChild != null)
                {
                    filteredChildren.Add(filteredChild);
                }
            }
            
            // إذا كانت العقدة أو أي من أطفالها تطابق الفلتر
            if (nodeMatches || filteredChildren.Any())
            {
                var filteredNode = new AccountTreeNode
                {
                    Account = node.Account,
                    AccountCode = node.AccountCode,
                    AccountName = node.AccountName,
                    AccountType = node.AccountType,
                    AccountTypeDisplay = node.AccountTypeDisplay,
                    TypeIcon = node.TypeIcon,
                    BalanceDisplay = node.BalanceDisplay,
                    UsabilityIndicator = node.UsabilityIndicator,
                    Children = filteredChildren
                };
                
                return filteredNode;
            }
            
            return null;
        }
        
        /// <summary>
        /// معالج تغيير البحث
        /// </summary>
        private void TxtSearch_TextChanged(object sender, TextChangedEventArgs e)
        {
            FilterAccounts();
        }
        
        /// <summary>
        /// معالج تغيير نوع الحساب
        /// </summary>
        private void CmbAccountType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            FilterAccounts();
        }
        
        /// <summary>
        /// معالج تغيير فلتر الحسابات القابلة للاستخدام
        /// </summary>
        private void ChkUsableOnly_Changed(object sender, RoutedEventArgs e)
        {
            FilterAccounts();
        }
        
        /// <summary>
        /// معالج تغيير الحساب المحدد
        /// </summary>
        private void TvAccounts_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is AccountTreeNode selectedNode)
            {
                UpdateSelectedAccountInfo(selectedNode.Account);
                btnSelect.IsEnabled = selectedNode.Account.CanBeUsedInEntries;
            }
            else
            {
                ClearSelectedAccountInfo();
                btnSelect.IsEnabled = false;
            }
        }
        
        /// <summary>
        /// تحديث معلومات الحساب المحدد
        /// </summary>
        private void UpdateSelectedAccountInfo(Account account)
        {
            txtSelectedAccount.Text = $"{account.AccountCode} - {account.AccountName}";
            txtSelectedType.Text = GetAccountTypeDisplay(account.AccountType);
            txtSelectedBalance.Text = account.Balance.ToString("N2");
            
            if (!account.CanBeUsedInEntries)
            {
                txtSelectedAccount.Text += " (حساب رئيسي - غير قابل للاستخدام في القيود)";
            }
        }
        
        /// <summary>
        /// مسح معلومات الحساب المحدد
        /// </summary>
        private void ClearSelectedAccountInfo()
        {
            txtSelectedAccount.Text = "لم يتم اختيار حساب";
            txtSelectedType.Text = "-";
            txtSelectedBalance.Text = "-";
        }
        
        /// <summary>
        /// اختيار الحساب
        /// </summary>
        private void BtnSelect_Click(object sender, RoutedEventArgs e)
        {
            if (tvAccounts.SelectedItem is AccountTreeNode selectedNode)
            {
                SelectedAccount = selectedNode.Account;
                DialogResult = true;
                Close();
            }
        }
        
        /// <summary>
        /// إلغاء الاختيار
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            SelectedAccount = null;
            DialogResult = false;
            Close();
        }
    }
    
    /// <summary>
    /// عقدة شجرة الحسابات
    /// </summary>
    public class AccountTreeNode
    {
        public Account Account { get; set; } = new Account();
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public AccountType AccountType { get; set; }
        public string AccountTypeDisplay { get; set; } = "";
        public string TypeIcon { get; set; } = "";
        public string BalanceDisplay { get; set; } = "";
        public string UsabilityIndicator { get; set; } = "";
        public List<AccountTreeNode> Children { get; set; } = new List<AccountTreeNode>();
    }
}
