using System.IO;
using System.Text.Json;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة الأصناف
    /// </summary>
    public class ItemService
    {
        private readonly string _dataPath;
        private readonly WarehouseService _warehouseService;

        public ItemService()
        {
            _dataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GlassFactory", "Items");
            _warehouseService = new WarehouseService();
            
            if (!Directory.Exists(_dataPath))
            {
                Directory.CreateDirectory(_dataPath);
            }
        }

        /// <summary>
        /// حفظ صنف جديد
        /// </summary>
        public async Task<bool> SaveItemAsync(Item item)
        {
            try
            {
                if (item.Id == 0)
                {
                    item.Id = await GetNextIdAsync();

                    // توليد كود تلقائي إذا لم يكن موجود
                    if (string.IsNullOrEmpty(item.Code))
                    {
                        item.Code = await GenerateItemCodeAsync();
                    }
                }

                // جلب اسم المخزن
                var warehouse = await _warehouseService.GetWarehouseByIdAsync(item.WarehouseId);
                if (warehouse != null)
                {
                    item.WarehouseName = warehouse.Name;
                }

                var fileName = $"Item_{item.Id}.json";
                var filePath = Path.Combine(_dataPath, fileName);

                var json = JsonSerializer.Serialize(item, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                await File.WriteAllTextAsync(filePath, json);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving item: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب جميع الأصناف
        /// </summary>
        public async Task<List<Item>> GetAllItemsAsync()
        {
            try
            {
                var items = new List<Item>();
                
                if (!Directory.Exists(_dataPath))
                    return items;

                var files = Directory.GetFiles(_dataPath, "Item_*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var item = JsonSerializer.Deserialize<Item>(json);
                        if (item != null)
                        {
                            items.Add(item);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error reading item file {file}: {ex.Message}");
                    }
                }

                return items.OrderBy(i => i.Name).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting items: {ex.Message}");
                return new List<Item>();
            }
        }

        /// <summary>
        /// جلب الأصناف حسب المخزن
        /// </summary>
        public async Task<List<Item>> GetItemsByWarehouseAsync(int warehouseId)
        {
            try
            {
                var allItems = await GetAllItemsAsync();
                return allItems.Where(i => i.WarehouseId == warehouseId).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting items by warehouse: {ex.Message}");
                return new List<Item>();
            }
        }

        /// <summary>
        /// جلب متوسط سعر الصنف من المخزون
        /// </summary>
        public async Task<decimal> GetItemAveragePriceAsync(int itemId)
        {
            try
            {
                var inventoryService = new InventoryService();
                var balances = await inventoryService.GetInventoryBalancesAsync();
                var itemBalance = balances.FirstOrDefault(b => b.ItemId == itemId);
                return itemBalance?.AveragePrice ?? 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting item average price: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// جلب صنف بالمعرف
        /// </summary>
        public async Task<Item?> GetItemByIdAsync(int id)
        {
            try
            {
                var fileName = $"Item_{id}.json";
                var filePath = Path.Combine(_dataPath, fileName);

                if (!File.Exists(filePath))
                    return null;

                var json = await File.ReadAllTextAsync(filePath);
                return JsonSerializer.Deserialize<Item>(json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting item by id: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// حذف صنف
        /// </summary>
        public async Task<bool> DeleteItemAsync(int id)
        {
            try
            {
                var fileName = $"Item_{id}.json";
                var filePath = Path.Combine(_dataPath, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting item: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// توليد كود الصنف التلقائي
        /// </summary>
        private async Task<string> GenerateItemCodeAsync()
        {
            try
            {
                var items = await GetAllItemsAsync();
                var lastCode = items
                    .Where(i => !string.IsNullOrEmpty(i.Code) && i.Code.StartsWith("IT"))
                    .Select(i => i.Code)
                    .OrderByDescending(c => c)
                    .FirstOrDefault();

                if (lastCode != null && lastCode.Length > 2)
                {
                    var numberPart = lastCode.Substring(2);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        return $"IT{(lastNumber + 1):D4}";
                    }
                }

                return "IT0001";
            }
            catch
            {
                return "IT0001";
            }
        }

        /// <summary>
        /// جلب المعرف التالي
        /// </summary>
        private async Task<int> GetNextIdAsync()
        {
            try
            {
                var items = await GetAllItemsAsync();
                return items.Count > 0 ? items.Max(i => i.Id) + 1 : 1;
            }
            catch
            {
                return 1;
            }
        }
    }
}
