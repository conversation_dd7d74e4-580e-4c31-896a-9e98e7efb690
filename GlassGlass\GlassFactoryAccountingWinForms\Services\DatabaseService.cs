using System;
using System.Data.SQLite;
using System.IO;
using System.Configuration;
using System.Windows.Forms;

namespace GlassFactoryAccountingWinForms.Services
{
    /// <summary>
    /// خدمة قاعدة البيانات
    /// </summary>
    public class DatabaseService
    {
        private readonly string _connectionString;
        private readonly string _databasePath;

        public DatabaseService()
        {
            _databasePath = Path.Combine(Application.StartupPath, "Data", "GlassFactory.db");
            _connectionString = $"Data Source={_databasePath};Version=3;";
        }

        /// <summary>
        /// تهيئة قاعدة البيانات
        /// </summary>
        public void InitializeDatabase()
        {
            try
            {
                // إنشاء مجلد البيانات إذا لم يكن موجود
                var dataDirectory = Path.GetDirectoryName(_databasePath);
                if (!Directory.Exists(dataDirectory))
                {
                    Directory.CreateDirectory(dataDirectory);
                }

                // إنشاء قاعدة البيانات إذا لم تكن موجودة
                if (!File.Exists(_databasePath))
                {
                    SQLiteConnection.CreateFile(_databasePath);
                }

                // إنشاء الجداول
                CreateTables();

                // إنشاء البيانات الأساسية
                CreateDefaultData();
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إنشاء الجداول
        /// </summary>
        private void CreateTables()
        {
            using var connection = new SQLiteConnection(_connectionString);
            connection.Open();

            // جدول الحسابات
            var createAccountsTable = @"
                CREATE TABLE IF NOT EXISTS Accounts (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    AccountCode TEXT NOT NULL UNIQUE,
                    AccountName TEXT NOT NULL,
                    AccountType INTEGER NOT NULL,
                    IsParent BOOLEAN NOT NULL DEFAULT 0,
                    ParentAccountId INTEGER NULL,
                    AccountLevel INTEGER NOT NULL DEFAULT 1,
                    Balance DECIMAL(18,2) NOT NULL DEFAULT 0,
                    DebitBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
                    CreditBalance DECIMAL(18,2) NOT NULL DEFAULT 0,
                    IsActive BOOLEAN NOT NULL DEFAULT 1,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    CreatedBy TEXT NOT NULL DEFAULT '',
                    ModifiedDate DATETIME NULL,
                    ModifiedBy TEXT NULL,
                    Notes TEXT NULL,
                    FOREIGN KEY (ParentAccountId) REFERENCES Accounts(Id)
                )";

            // جدول قيود اليومية
            var createJournalEntriesTable = @"
                CREATE TABLE IF NOT EXISTS JournalEntries (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    EntryNumber TEXT NOT NULL UNIQUE,
                    EntryDate DATE NOT NULL,
                    Description TEXT NOT NULL,
                    TotalAmount DECIMAL(18,2) NOT NULL,
                    IsPosted BOOLEAN NOT NULL DEFAULT 0,
                    PostedDate DATETIME NULL,
                    PostedBy TEXT NULL,
                    CreatedBy TEXT NOT NULL,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    Reference TEXT NULL,
                    Notes TEXT NULL
                )";

            // جدول تفاصيل قيود اليومية
            var createJournalEntryDetailsTable = @"
                CREATE TABLE IF NOT EXISTS JournalEntryDetails (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    JournalEntryId INTEGER NOT NULL,
                    AccountId INTEGER NOT NULL,
                    DebitAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
                    CreditAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
                    Description TEXT NULL,
                    LineNumber INTEGER NOT NULL,
                    CreatedDate DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (JournalEntryId) REFERENCES JournalEntries(Id),
                    FOREIGN KEY (AccountId) REFERENCES Accounts(Id)
                )";

            using var command = new SQLiteCommand(connection);
            
            command.CommandText = createAccountsTable;
            command.ExecuteNonQuery();

            command.CommandText = createJournalEntriesTable;
            command.ExecuteNonQuery();

            command.CommandText = createJournalEntryDetailsTable;
            command.ExecuteNonQuery();
        }

        /// <summary>
        /// إنشاء البيانات الأساسية
        /// </summary>
        private void CreateDefaultData()
        {
            using var connection = new SQLiteConnection(_connectionString);
            connection.Open();

            // التحقق من وجود حسابات
            var checkAccountsQuery = "SELECT COUNT(*) FROM Accounts";
            using var checkCommand = new SQLiteCommand(checkAccountsQuery, connection);
            var accountCount = Convert.ToInt32(checkCommand.ExecuteScalar());

            if (accountCount == 0)
            {
                // إنشاء الحسابات الأساسية
                var defaultAccounts = @"
                    INSERT INTO Accounts (AccountCode, AccountName, AccountType, IsParent, AccountLevel, CreatedBy) VALUES
                    ('1000', 'الأصول', 1, 1, 1, 'النظام'),
                    ('1100', 'الأصول المتداولة', 1, 1, 2, 'النظام'),
                    ('1110', 'النقدية', 1, 0, 3, 'النظام'),
                    ('1120', 'البنك', 1, 0, 3, 'النظام'),
                    ('1200', 'المخزون', 1, 0, 2, 'النظام'),
                    
                    ('2000', 'الالتزامات', 2, 1, 1, 'النظام'),
                    ('2100', 'الالتزامات المتداولة', 2, 1, 2, 'النظام'),
                    ('2110', 'الموردون', 2, 0, 3, 'النظام'),
                    
                    ('3000', 'حقوق الملكية', 3, 1, 1, 'النظام'),
                    ('3100', 'رأس المال', 3, 0, 2, 'النظام'),
                    
                    ('4000', 'الإيرادات', 4, 1, 1, 'النظام'),
                    ('4100', 'إيرادات المبيعات', 4, 0, 2, 'النظام'),
                    
                    ('5000', 'المصروفات', 5, 1, 1, 'النظام'),
                    ('5100', 'مصروفات التشغيل', 5, 0, 2, 'النظام')";

                using var insertCommand = new SQLiteCommand(defaultAccounts, connection);
                insertCommand.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// الحصول على اتصال قاعدة البيانات
        /// </summary>
        public SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(_connectionString);
        }

        /// <summary>
        /// سلسلة الاتصال
        /// </summary>
        public string ConnectionString => _connectionString;
    }
}
