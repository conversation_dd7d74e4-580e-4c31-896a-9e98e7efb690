using System;
using System.Collections.Generic;

namespace GlassFactoryAccountingWinForms.Models
{
    /// <summary>
    /// نموذج الحساب المحاسبي
    /// </summary>
    public class Account
    {
        public int Id { get; set; }
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public AccountType AccountType { get; set; }
        public bool IsParent { get; set; }
        public int? ParentAccountId { get; set; }
        public int AccountLevel { get; set; } = 1;
        public decimal Balance { get; set; }
        public decimal DebitBalance { get; set; }
        public decimal CreditBalance { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string CreatedBy { get; set; } = "";
        public DateTime? ModifiedDate { get; set; }
        public string ModifiedBy { get; set; } = "";
        public string Notes { get; set; } = "";

        /// <summary>
        /// هل يمكن استخدام هذا الحساب في القيود (الحسابات الفرعية فقط)
        /// </summary>
        public bool CanBeUsedInEntries => !IsParent;

        /// <summary>
        /// نص نوع الحساب
        /// </summary>
        public string AccountTypeDisplay
        {
            get
            {
                return AccountType switch
                {
                    AccountType.Asset => "أصول",
                    AccountType.Liability => "التزامات",
                    AccountType.Equity => "حقوق ملكية",
                    AccountType.Revenue => "إيرادات",
                    AccountType.Expense => "مصروفات",
                    _ => "غير محدد"
                };
            }
        }
    }

    /// <summary>
    /// أنواع الحسابات المحاسبية
    /// </summary>
    public enum AccountType
    {
        /// <summary>
        /// الأصول - Assets
        /// </summary>
        Asset = 1,

        /// <summary>
        /// الالتزامات - Liabilities
        /// </summary>
        Liability = 2,

        /// <summary>
        /// حقوق الملكية - Equity
        /// </summary>
        Equity = 3,

        /// <summary>
        /// الإيرادات - Revenue
        /// </summary>
        Revenue = 4,

        /// <summary>
        /// المصروفات - Expenses
        /// </summary>
        Expense = 5
    }
}
