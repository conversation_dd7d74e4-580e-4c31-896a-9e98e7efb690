<Window x:Class="GlassFactoryAccounting.Views.Accounting.AddEditAccountWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="إضافة/تعديل حساب" Height="500" Width="600"
        WindowStartupLocation="CenterScreen"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Name="txtTitle" Text="إضافة حساب جديد" 
                   FontSize="20" FontWeight="Bold" 
                   Foreground="#2C3E50" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,20"/>
        
        <!-- نموذج البيانات -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="#F8F9FA" Padding="20">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- كود الحساب -->
                <StackPanel Grid.Row="0" Margin="0,0,0,15">
                    <TextBlock Text="كود الحساب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox Name="txtAccountCode" FontSize="14" Padding="8"/>
                </StackPanel>
                
                <!-- اسم الحساب -->
                <StackPanel Grid.Row="1" Margin="0,0,0,15">
                    <TextBlock Text="اسم الحساب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox Name="txtAccountName" FontSize="14" Padding="8"/>
                </StackPanel>
                
                <!-- نوع الحساب -->
                <StackPanel Grid.Row="2" Margin="0,0,0,15">
                    <TextBlock Text="نوع الحساب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox Name="cmbAccountType" FontSize="14" Padding="8">
                        <ComboBoxItem Content="أصل" Tag="Asset"/>
                        <ComboBoxItem Content="التزام" Tag="Liability"/>
                        <ComboBoxItem Content="حقوق ملكية" Tag="Equity"/>
                        <ComboBoxItem Content="إيراد" Tag="Revenue"/>
                        <ComboBoxItem Content="مصروف" Tag="Expense"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- حساب رئيسي -->
                <StackPanel Grid.Row="3" Margin="0,0,0,15">
                    <CheckBox Name="chkIsParent" Content="حساب رئيسي (يحتوي على حسابات فرعية)" 
                              FontSize="14" FontWeight="Bold" 
                              Checked="ChkIsParent_Checked" Unchecked="ChkIsParent_Unchecked"/>
                </StackPanel>
                
                <!-- الحساب الأب -->
                <StackPanel Grid.Row="4" Name="pnlParentAccount" Margin="0,0,0,15">
                    <TextBlock Name="lblParentAccount" Text="الحساب الأب:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <ComboBox Name="cmbParentAccount" FontSize="14" Padding="8"/>
                </StackPanel>
                
                <!-- ملاحظات -->
                <StackPanel Grid.Row="5" Margin="0,0,0,15">
                    <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox Name="txtNotes" FontSize="14" Padding="8" Height="80" 
                             TextWrapping="Wrap" AcceptsReturn="True" 
                             VerticalScrollBarVisibility="Auto"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- أزرار التحكم -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Center" 
                    Margin="0,20,0,0">
            
            <Button Name="btnSave" Content="💾 حفظ" 
                    Background="#27AE60" Foreground="White" 
                    Padding="20,10" FontSize="14" FontWeight="Bold"
                    Margin="10,0" Click="BtnSave_Click"/>
            
            <Button Name="btnCancel" Content="❌ إلغاء" 
                    Background="#95A5A6" Foreground="White" 
                    Padding="20,10" FontSize="14" FontWeight="Bold"
                    Margin="10,0" Click="BtnCancel_Click"/>
            
        </StackPanel>
    </Grid>
</Window>
