﻿#pragma checksum "..\..\..\..\Views\EmployeeOvertimeView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "700388F61D893846BE7274FC919509C3726742BC"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// EmployeeOvertimeView
    /// </summary>
    public partial class EmployeeOvertimeView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 196 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbEmployee;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtEmployeeCode;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtPosition;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBranch;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtHourlyRate;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtOvertimeHours;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtOvertimeAmount;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCalculateAuto;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpOvertimeDate;
        
        #line default
        #line hidden
        
        
        #line 239 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFormula;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 256 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid OvertimeDataGrid;
        
        #line default
        #line hidden
        
        
        #line 297 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClear;
        
        #line default
        #line hidden
        
        
        #line 299 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/employeeovertimeview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CmbEmployee = ((System.Windows.Controls.ComboBox)(target));
            
            #line 197 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            this.CmbEmployee.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbEmployee_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtEmployeeCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtPosition = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TxtBranch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtHourlyRate = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtOvertimeHours = ((System.Windows.Controls.TextBox)(target));
            
            #line 222 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            this.TxtOvertimeHours.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtOvertimeHours_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TxtOvertimeAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 228 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            this.TxtOvertimeAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtOvertimeAmount_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnCalculateAuto = ((System.Windows.Controls.Button)(target));
            
            #line 230 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            this.BtnCalculateAuto.Click += new System.Windows.RoutedEventHandler(this.BtnCalculateAuto_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.DpOvertimeDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 10:
            this.TxtFormula = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.OvertimeDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 15:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 297 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.BtnClear = ((System.Windows.Controls.Button)(target));
            
            #line 298 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            this.BtnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 299 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 279 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditOvertime_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 283 "..\..\..\..\Views\EmployeeOvertimeView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteOvertime_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

