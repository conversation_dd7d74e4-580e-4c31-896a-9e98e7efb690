﻿#pragma checksum "..\..\..\..\Views\SalesView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2413400F5C8C3F009A12815964DAF7BD663CD562"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// SalesView
    /// </summary>
    public partial class SalesView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 54 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalSalesCount;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalAmount;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalCustomers;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnNewSale;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManageCustomers;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSalesReports;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\SalesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SalesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/salesview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SalesView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtTotalSalesCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.TxtTotalAmount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.TxtTotalCustomers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\Views\SalesView.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnNewSale = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\Views\SalesView.xaml"
            this.BtnNewSale.Click += new System.Windows.RoutedEventHandler(this.BtnNewSale_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnManageCustomers = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\Views\SalesView.xaml"
            this.BtnManageCustomers.Click += new System.Windows.RoutedEventHandler(this.BtnManageCustomers_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnSalesReports = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\..\Views\SalesView.xaml"
            this.BtnSalesReports.Click += new System.Windows.RoutedEventHandler(this.BtnSalesReports_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SalesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 9:
            
            #line 143 "..\..\..\..\Views\SalesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnViewSale_Click);
            
            #line default
            #line hidden
            break;
            case 10:
            
            #line 148 "..\..\..\..\Views\SalesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditSale_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 153 "..\..\..\..\Views\SalesView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteSale_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

