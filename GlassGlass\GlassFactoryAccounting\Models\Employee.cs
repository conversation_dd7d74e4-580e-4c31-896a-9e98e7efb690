namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج الموظفين
    /// </summary>
    public class Employee : BaseEntity
    {
        private string _name = string.Empty;
        private string _employeeCode = string.Empty;
        private string _position = string.Empty;
        private string _branch = string.Empty;
        private string _department = string.Empty;
        private decimal _basicSalary;
        private int _workingHours;
        private DateTime _hireDate;
        private string _phone = string.Empty;
        private string _address = string.Empty;
        private string _notes = string.Empty;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string EmployeeCode
        {
            get => _employeeCode;
            set => SetProperty(ref _employeeCode, value);
        }

        public string Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }

        public string Branch
        {
            get => _branch;
            set => SetProperty(ref _branch, value);
        }

        public string Department
        {
            get => _department;
            set => SetProperty(ref _department, value);
        }

        public decimal BasicSalary
        {
            get => _basicSalary;
            set => SetProperty(ref _basicSalary, value);
        }

        public int WorkingHours
        {
            get => _workingHours;
            set => SetProperty(ref _workingHours, value);
        }

        public DateTime HireDate
        {
            get => _hireDate;
            set => SetProperty(ref _hireDate, value);
        }

        public string Phone
        {
            get => _phone;
            set => SetProperty(ref _phone, value);
        }

        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        /// <summary>
        /// حساب أجر الساعة
        /// </summary>
        public decimal HourlyRate => WorkingHours > 0 ? BasicSalary / (WorkingHours * 30) : 0;

        public Employee()
        {
            HireDate = DateTime.Now;
            WorkingHours = 8; // افتراضي 8 ساعات
            EmployeeCode = GenerateEmployeeCode();
        }

        private string GenerateEmployeeCode()
        {
            return $"EMP{DateTime.Now:yyyyMMddHHmmss}";
        }
    }

    /// <summary>
    /// نموذج الرواتب المستحقة
    /// </summary>
    public class SalaryDue : BaseEntity
    {
        private int _employeeId;
        private string _employeeName = string.Empty;
        private string _employeeCode = string.Empty;
        private decimal _salaryAmount;
        private DateTime _dueDate;
        private string _position = string.Empty;
        private string _branch = string.Empty;
        private DateTime _hireDate;
        private bool _isPaid;
        private string _notes = string.Empty;

        public int EmployeeId
        {
            get => _employeeId;
            set => SetProperty(ref _employeeId, value);
        }

        public string EmployeeName
        {
            get => _employeeName;
            set => SetProperty(ref _employeeName, value);
        }

        public string EmployeeCode
        {
            get => _employeeCode;
            set => SetProperty(ref _employeeCode, value);
        }

        public decimal SalaryAmount
        {
            get => _salaryAmount;
            set => SetProperty(ref _salaryAmount, value);
        }

        public DateTime DueDate
        {
            get => _dueDate;
            set => SetProperty(ref _dueDate, value);
        }

        public string Position
        {
            get => _position;
            set => SetProperty(ref _position, value);
        }

        public string Branch
        {
            get => _branch;
            set => SetProperty(ref _branch, value);
        }

        public DateTime HireDate
        {
            get => _hireDate;
            set => SetProperty(ref _hireDate, value);
        }

        public bool IsPaid
        {
            get => _isPaid;
            set => SetProperty(ref _isPaid, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public SalaryDue()
        {
            DueDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            IsPaid = false;
        }
    }

    /// <summary>
    /// نموذج السلف
    /// </summary>
    public class EmployeeAdvance : BaseEntity
    {
        private int _employeeId;
        private string _employeeName = string.Empty;
        private string _employeeCode = string.Empty;
        private decimal _advanceAmount;
        private string _responsiblePerson = string.Empty;
        private string _paymentType = string.Empty;
        private DateTime _advanceDate;
        private string _notes = string.Empty;

        public int EmployeeId
        {
            get => _employeeId;
            set => SetProperty(ref _employeeId, value);
        }

        public string EmployeeName
        {
            get => _employeeName;
            set => SetProperty(ref _employeeName, value);
        }

        public string EmployeeCode
        {
            get => _employeeCode;
            set => SetProperty(ref _employeeCode, value);
        }

        public decimal AdvanceAmount
        {
            get => _advanceAmount;
            set => SetProperty(ref _advanceAmount, value);
        }

        public string ResponsiblePerson
        {
            get => _responsiblePerson;
            set => SetProperty(ref _responsiblePerson, value);
        }

        public string PaymentType
        {
            get => _paymentType;
            set => SetProperty(ref _paymentType, value);
        }

        public DateTime AdvanceDate
        {
            get => _advanceDate;
            set => SetProperty(ref _advanceDate, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public EmployeeAdvance()
        {
            AdvanceDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج المكافآت
    /// </summary>
    public class EmployeeBonus : BaseEntity
    {
        private int _employeeId;
        private string _employeeName = string.Empty;
        private string _employeeCode = string.Empty;
        private decimal _bonusAmount;
        private string _bonusReason = string.Empty;
        private DateTime _bonusDate;
        private string _notes = string.Empty;

        public int EmployeeId
        {
            get => _employeeId;
            set => SetProperty(ref _employeeId, value);
        }

        public string EmployeeName
        {
            get => _employeeName;
            set => SetProperty(ref _employeeName, value);
        }

        public string EmployeeCode
        {
            get => _employeeCode;
            set => SetProperty(ref _employeeCode, value);
        }

        public decimal BonusAmount
        {
            get => _bonusAmount;
            set => SetProperty(ref _bonusAmount, value);
        }

        public string BonusReason
        {
            get => _bonusReason;
            set => SetProperty(ref _bonusReason, value);
        }

        public DateTime BonusDate
        {
            get => _bonusDate;
            set => SetProperty(ref _bonusDate, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public EmployeeBonus()
        {
            BonusDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج الخصومات
    /// </summary>
    public class EmployeeDeduction : BaseEntity
    {
        private int _employeeId;
        private string _employeeName = string.Empty;
        private string _employeeCode = string.Empty;
        private decimal _deductionAmount;
        private string _deductionReason = string.Empty;
        private DateTime _deductionDate;
        private string _notes = string.Empty;

        public int EmployeeId
        {
            get => _employeeId;
            set => SetProperty(ref _employeeId, value);
        }

        public string EmployeeName
        {
            get => _employeeName;
            set => SetProperty(ref _employeeName, value);
        }

        public string EmployeeCode
        {
            get => _employeeCode;
            set => SetProperty(ref _employeeCode, value);
        }

        public decimal DeductionAmount
        {
            get => _deductionAmount;
            set => SetProperty(ref _deductionAmount, value);
        }

        public string DeductionReason
        {
            get => _deductionReason;
            set => SetProperty(ref _deductionReason, value);
        }

        public DateTime DeductionDate
        {
            get => _deductionDate;
            set => SetProperty(ref _deductionDate, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public EmployeeDeduction()
        {
            DeductionDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج الوقت الإضافي
    /// </summary>
    public class EmployeeOvertime : BaseEntity
    {
        private int _employeeId;
        private string _employeeName = string.Empty;
        private string _employeeCode = string.Empty;
        private decimal _overtimeHours;
        private decimal _overtimeAmount;
        private DateTime _overtimeDate;
        private string _notes = string.Empty;

        public int EmployeeId
        {
            get => _employeeId;
            set => SetProperty(ref _employeeId, value);
        }

        public string EmployeeName
        {
            get => _employeeName;
            set => SetProperty(ref _employeeName, value);
        }

        public string EmployeeCode
        {
            get => _employeeCode;
            set => SetProperty(ref _employeeCode, value);
        }

        public decimal OvertimeHours
        {
            get => _overtimeHours;
            set => SetProperty(ref _overtimeHours, value);
        }

        public decimal OvertimeAmount
        {
            get => _overtimeAmount;
            set => SetProperty(ref _overtimeAmount, value);
        }

        public DateTime OvertimeDate
        {
            get => _overtimeDate;
            set => SetProperty(ref _overtimeDate, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public EmployeeOvertime()
        {
            OvertimeDate = DateTime.Now;
        }
    }

    /// <summary>
    /// نموذج سداد الرواتب
    /// </summary>
    public class SalaryPayment : BaseEntity
    {
        private int _employeeId;
        private string _employeeName = string.Empty;
        private string _employeeCode = string.Empty;
        private decimal _salaryPaid;
        private decimal _bonusAmount;
        private decimal _overtimeAmount;
        private decimal _totalPaid;
        private string _responsiblePerson = string.Empty;
        private DateTime _paymentDate;
        private string _notes = string.Empty;

        public int EmployeeId
        {
            get => _employeeId;
            set => SetProperty(ref _employeeId, value);
        }

        public string EmployeeName
        {
            get => _employeeName;
            set => SetProperty(ref _employeeName, value);
        }

        public string EmployeeCode
        {
            get => _employeeCode;
            set => SetProperty(ref _employeeCode, value);
        }

        public decimal SalaryPaid
        {
            get => _salaryPaid;
            set => SetProperty(ref _salaryPaid, value);
        }

        public decimal BonusAmount
        {
            get => _bonusAmount;
            set => SetProperty(ref _bonusAmount, value);
        }

        public decimal OvertimeAmount
        {
            get => _overtimeAmount;
            set => SetProperty(ref _overtimeAmount, value);
        }

        public decimal TotalPaid
        {
            get => _totalPaid;
            set => SetProperty(ref _totalPaid, value);
        }

        public string ResponsiblePerson
        {
            get => _responsiblePerson;
            set => SetProperty(ref _responsiblePerson, value);
        }

        public DateTime PaymentDate
        {
            get => _paymentDate;
            set => SetProperty(ref _paymentDate, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public SalaryPayment()
        {
            PaymentDate = DateTime.Now;
        }

        /// <summary>
        /// حساب إجمالي المدفوع
        /// </summary>
        public void CalculateTotal()
        {
            TotalPaid = SalaryPaid + BonusAmount + OvertimeAmount;
        }
    }

    /// <summary>
    /// نموذج الرواتب القديم (للتوافق)
    /// </summary>
    public class Salary : BaseEntity
    {
        private int _employeeId;
        private Employee? _employee;
        private DateTime _salaryMonth;
        private decimal _basicSalary;
        private decimal _allowances;
        private decimal _overtime;
        private decimal _bonus;
        private decimal _deductions;
        private decimal _taxes;
        private decimal _netSalary;
        private bool _isPaid;
        private DateTime? _paidDate;
        private string _notes = string.Empty;

        public int EmployeeId
        {
            get => _employeeId;
            set => SetProperty(ref _employeeId, value);
        }

        public Employee? Employee
        {
            get => _employee;
            set => SetProperty(ref _employee, value);
        }

        public DateTime SalaryMonth
        {
            get => _salaryMonth;
            set => SetProperty(ref _salaryMonth, value);
        }

        public decimal BasicSalary
        {
            get => _basicSalary;
            set => SetProperty(ref _basicSalary, value);
        }

        public decimal Allowances
        {
            get => _allowances;
            set => SetProperty(ref _allowances, value);
        }

        public decimal Overtime
        {
            get => _overtime;
            set => SetProperty(ref _overtime, value);
        }

        public decimal Bonus
        {
            get => _bonus;
            set => SetProperty(ref _bonus, value);
        }

        public decimal Deductions
        {
            get => _deductions;
            set => SetProperty(ref _deductions, value);
        }

        public decimal Taxes
        {
            get => _taxes;
            set => SetProperty(ref _taxes, value);
        }

        public decimal NetSalary
        {
            get => _netSalary;
            set => SetProperty(ref _netSalary, value);
        }

        public bool IsPaid
        {
            get => _isPaid;
            set => SetProperty(ref _isPaid, value);
        }

        public DateTime? PaidDate
        {
            get => _paidDate;
            set => SetProperty(ref _paidDate, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public Salary()
        {
            SalaryMonth = DateTime.Now;
            IsPaid = false;
        }

        /// <summary>
        /// حساب الراتب الصافي
        /// </summary>
        public void CalculateNetSalary()
        {
            NetSalary = BasicSalary + Allowances + Overtime + Bonus - Deductions - Taxes;
        }
    }

    /// <summary>
    /// أنواع السداد
    /// </summary>
    public enum PaymentType
    {
        نقدي = 1,
        شيك = 2,
        تحويل_بنكي = 3,
        بطاقة_ائتمان = 4
    }

    /// <summary>
    /// أسباب المكافآت
    /// </summary>
    public enum BonusReason
    {
        تميز_في_الأداء = 1,
        مكافأة_شهرية = 2,
        مكافأة_سنوية = 3,
        مكافأة_مشروع = 4,
        مكافأة_عيد = 5,
        أخرى = 6
    }

    /// <summary>
    /// أسباب الخصومات
    /// </summary>
    public enum DeductionReason
    {
        غياب = 1,
        تأخير = 2,
        خصم_تأديبي = 3,
        تأمينات = 4,
        ضرائب = 5,
        سلفة = 6,
        أخرى = 7
    }
}
