# 🏭 نظام حسابات مصنع الزجاج المحسن
**Enhanced Glass Factory Accounting System**

---

## 📋 معلومات المشروع

**اسم المشروع:** نظام حسابات مصنع الزجاج المحسن  
**المالك:** حسام محمد حسان أحمد  
**تاريخ الإنشاء:** 2024  
**الإصدار:** 1.1.0 - النظام المحاسبي المحسن 2025  
**الترخيص:** ملكية خاصة - جميع الحقوق محفوظة  

---

## 🚀 **طريقة التشغيل السريع**

### الطريقة الأسهل والأسرع:
```bash
# انقر نقرة مزدوجة على الملف:
🚀_تشغيل_النظام_المحاسبي_المحسن.bat
```

### أو للاختبار السريع:
```bash
# انقر نقرة مزدوجة على الملف:
تشغيل_سريع_للاختبار.bat
```

### أو لاختبار شامل:
```bash
# انقر نقرة مزدوجة على الملف:
اختبار_النظام_المحاسبي_المحسن.bat
```

---

## 🎯 **التحسينات المحاسبية الجديدة**

### 🌳 **1. شجرة الحسابات المحسنة**
- ✅ **دعم المستويات المتعددة:** حتى 5 مستويات من الحسابات الفرعية
- ✅ **حساب المستوى تلقائياً:** يتم تحديد مستوى الحساب عند الإضافة
- ✅ **منع استخدام الحسابات الرئيسية:** في القيود المحاسبية
- ✅ **مؤشرات بصرية:** أيقونات ومؤشرات لتوضيح نوع وحالة الحساب

### 📝 **2. قيود اليومية المحسنة**
- ✅ **التحقق التلقائي من التوازن:** إجمالي المدين = إجمالي الدائن
- ✅ **التحقق من صحة الحسابات:** منع استخدام الحسابات الرئيسية
- ✅ **رسائل خطأ واضحة:** توضيح سبب الخطأ وكيفية الإصلاح
- ✅ **حفظ وترحيل منفصل:** إمكانية حفظ بدون ترحيل للمرونة

### 🔍 **3. نافذة اختيار الحسابات المتقدمة**
- ✅ **البحث المتقدم:** بكود الحساب أو اسم الحساب
- ✅ **فلترة متقدمة:** حسب نوع الحساب والقابلية للاستخدام
- ✅ **عرض هرمي:** شجرة الحسابات مع المستويات
- ✅ **مؤشرات بصرية:** أيقونات وألوان توضيحية

### 📊 **4. ميزان المراجعة المحسن**
- ✅ **فلترة متقدمة:** حسب الفترة الزمنية ونوع الحساب
- ✅ **إخفاء الأرصدة الصفرية:** لعرض أوضح
- ✅ **تصدير إلى Excel:** مع تنسيق احترافي
- ✅ **ملخص الأرصدة:** مع مؤشر التوازن المرئي

### 🔗 **5. التكامل مع الموديولات**
- ✅ **ربط تلقائي مع المبيعات:** إنشاء قيود تلقائية للفواتير
- ✅ **ربط مع نظام الرواتب:** قيود الرواتب والخصومات
- ✅ **ربط مع المصروفات:** قيود المصروفات التلقائية
- ✅ **ربط مع التصنيع:** قيود تكاليف التصنيع
- ✅ **ربط مع المخزون:** قيود حركات المخزون

---

## 🎯 **كيفية الاستخدام**

### **1. الوصول للنظام المحاسبي المحسن:**
```
الصفحة الرئيسية → الحسابات → شجرة الحسابات
```

### **2. إضافة حساب جديد:**
1. اختر الحساب الأب (اختياري للحسابات الفرعية)
2. أدخل كود الحساب (مثل: 1110)
3. أدخل اسم الحساب (مثل: النقدية)
4. اختر نوع الحساب (أصول، التزامات، إيرادات، مصروفات)
5. حدد إذا كان حساب رئيسي أم فرعي
6. سيتم حساب المستوى تلقائياً

### **3. إنشاء قيد يومية محسن:**
1. اختر "قيد اليومية" من قائمة الحسابات
2. أدخل وصف القيد
3. أضف الأسطر (مدين ودائن)
4. استخدم نافذة اختيار الحسابات المحسنة
5. تأكد من توازن القيد (مؤشر مرئي)
6. احفظ أو احفظ مع ترحيل

### **4. عرض ميزان المراجعة المحسن:**
1. اذهب إلى: الحسابات → ميزان المراجعة
2. اختر الفترة الزمنية
3. طبق الفلاتر المطلوبة
4. صدر إلى Excel أو اطبع التقرير

### **5. ربط الموديولات:**
```csharp
// مثال في موديول المبيعات
var integrationService = new AccountingIntegrationService();
integrationService.CreateSalesJournalEntry(
    saleId, totalAmount, cashAmount, creditAmount, 
    customerName, invoiceNumber
);
```

---

## 🔧 **المتطلبات التقنية**

### متطلبات النظام:
- **نظام التشغيل:** Windows 10 أو أحدث
- **.NET Runtime:** .NET 8.0 أو أحدث
- **الذاكرة:** 4 GB RAM (الحد الأدنى)
- **مساحة القرص:** 500 MB مساحة فارغة
- **الدقة:** 1024x768 (الحد الأدنى)

### الحزم والتبعيات:
- System.Data.SQLite
- Microsoft.Extensions.DependencyInjection
- CommunityToolkit.Mvvm
- EPPlus (للتصدير إلى Excel)
- MaterialDesignThemes
- MaterialDesignColors

---

## 🏗️ **هيكل المشروع المحسن**

```
GlassFactoryAccounting/
├── 🚀 🚀_تشغيل_النظام_المحاسبي_المحسن.bat    # ملف التشغيل الرئيسي الجديد
├── ⚡ تشغيل_سريع_للاختبار.bat              # ملف التشغيل السريع
├── 🧪 اختبار_النظام_المحاسبي_المحسن.bat     # ملف الاختبار الشامل
├── 📄 README_ENHANCED.md                    # هذا الملف
├── 📄 ACCOUNTING_SYSTEM_ENHANCEMENTS.md     # دليل التحسينات التفصيلي
├── 📁 Models/Accounting/                    # نماذج البيانات المحسنة
│   ├── Account.cs                           # نموذج الحساب المحسن
│   ├── JournalEntry.cs                      # نموذج قيد اليومية
│   └── TrialBalanceItem.cs                  # نموذج ميزان المراجعة
├── 📁 Services/                             # الخدمات المحسنة
│   ├── AccountingService.cs                 # خدمة المحاسبة المحسنة
│   └── AccountingIntegrationService.cs     # خدمة التكامل الجديدة
├── 📁 Views/Accounting/                     # الواجهات المحسنة
│   ├── ChartOfAccountsWindow.xaml          # شجرة الحسابات
│   ├── JournalEntryWindow.xaml             # قيد اليومية المحسن
│   ├── AccountSelectionWindow.xaml         # نافذة اختيار الحسابات الجديدة
│   └── EnhancedTrialBalanceWindow.xaml     # ميزان المراجعة المحسن
└── 📁 Release/                              # الملفات التنفيذية
    └── GlassFactoryAccounting.exe
```

---

## 🎯 **نصائح للاستخدام الأمثل**

### **للمحاسبين:**
1. **ابدأ بإعداد شجرة الحسابات** حسب طبيعة عملك
2. **استخدم الحسابات الفرعية فقط** في القيود
3. **راجع ميزان المراجعة بانتظام** للتأكد من التوازن
4. **استفد من التكامل التلقائي** مع الموديولات الأخرى

### **للمطورين:**
1. **استخدم `AccountingIntegrationService`** لربط الموديولات
2. **تحقق من `CanAccountBeUsedInEntries`** قبل استخدام الحساب
3. **استدعي `UpdateAllAccountBalances`** بعد العمليات الكبيرة
4. **استخدم `GetTrialBalanceAdvanced`** للتقارير المتقدمة

---

## 📞 **الدعم والصيانة**

**المطور:** حسام محمد حسان أحمد  
**التاريخ:** ديسمبر 2025  
**الإصدار:** 1.1.0 - النظام المحاسبي المحسن  

---

## 🎉 **النظام المحاسبي المحسن جاهز للاستخدام التجاري!**

تم تحسين النظام المحاسبي بالكامل مع الحفاظ على جميع الوظائف الموجودة وإضافة مميزات متقدمة جديدة.

**🚀 للتشغيل السريع: انقر نقرة مزدوجة على `🚀_تشغيل_النظام_المحاسبي_المحسن.bat`**
