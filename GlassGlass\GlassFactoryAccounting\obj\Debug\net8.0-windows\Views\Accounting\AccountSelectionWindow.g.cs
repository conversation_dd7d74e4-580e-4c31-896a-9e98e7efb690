﻿#pragma checksum "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "3CC01FA599D83B0CDA562420B4F6A0EAF07C4B89"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views.Accounting {
    
    
    /// <summary>
    /// AccountSelectionWindow
    /// </summary>
    public partial class AccountSelectionWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtSearch;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbAccountType;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkUsableOnly;
        
        #line default
        #line hidden
        
        
        #line 70 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView tvAccounts;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSelectedAccount;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSelectedType;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSelectedBalance;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSelect;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/GlassFactoryAccounting;V1.0.0.0;component/views/accounting/accountselectionwindo" +
                    "w.xaml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 41 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            this.txtSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearch_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.cmbAccountType = ((System.Windows.Controls.ComboBox)(target));
            
            #line 49 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            this.cmbAccountType.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbAccountType_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.chkUsableOnly = ((System.Windows.Controls.CheckBox)(target));
            
            #line 62 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            this.chkUsableOnly.Checked += new System.Windows.RoutedEventHandler(this.ChkUsableOnly_Changed);
            
            #line default
            #line hidden
            
            #line 62 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            this.chkUsableOnly.Unchecked += new System.Windows.RoutedEventHandler(this.ChkUsableOnly_Changed);
            
            #line default
            #line hidden
            return;
            case 4:
            this.tvAccounts = ((System.Windows.Controls.TreeView)(target));
            
            #line 71 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            this.tvAccounts.SelectedItemChanged += new System.Windows.RoutedPropertyChangedEventHandler<object>(this.TvAccounts_SelectedItemChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.txtSelectedAccount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.txtSelectedType = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.txtSelectedBalance = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.btnSelect = ((System.Windows.Controls.Button)(target));
            
            #line 134 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            this.btnSelect.Click += new System.Windows.RoutedEventHandler(this.BtnSelect_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 140 "..\..\..\..\..\Views\Accounting\AccountSelectionWindow.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

