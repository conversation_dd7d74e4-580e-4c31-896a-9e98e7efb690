using System.Collections.ObjectModel;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج أوامر التصنيع
    /// </summary>
    public class ManufacturingOrder : BaseEntity
    {
        private string _orderNumber = string.Empty;
        private DateTime _orderDate;
        private DateTime _startDate;
        private DateTime? _endDate;
        private int _productId;
        private Product? _product;
        private int _quantity;
        private ManufacturingStatus _status;
        private decimal _estimatedCost;
        private decimal _actualCost;
        private string _notes = string.Empty;
        private ObservableCollection<ManufacturingMaterial> _materials;
        private ObservableCollection<ManufacturingStep> _steps;

        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        public DateTime OrderDate
        {
            get => _orderDate;
            set => SetProperty(ref _orderDate, value);
        }

        public DateTime StartDate
        {
            get => _startDate;
            set => SetProperty(ref _startDate, value);
        }

        public DateTime? EndDate
        {
            get => _endDate;
            set => SetProperty(ref _endDate, value);
        }

        public int ProductId
        {
            get => _productId;
            set => SetProperty(ref _productId, value);
        }

        public Product? Product
        {
            get => _product;
            set => SetProperty(ref _product, value);
        }

        public int Quantity
        {
            get => _quantity;
            set => SetProperty(ref _quantity, value);
        }

        public ManufacturingStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public decimal EstimatedCost
        {
            get => _estimatedCost;
            set => SetProperty(ref _estimatedCost, value);
        }

        public decimal ActualCost
        {
            get => _actualCost;
            set => SetProperty(ref _actualCost, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public ObservableCollection<ManufacturingMaterial> Materials
        {
            get => _materials;
            set => SetProperty(ref _materials, value);
        }

        public ObservableCollection<ManufacturingStep> Steps
        {
            get => _steps;
            set => SetProperty(ref _steps, value);
        }

        public ManufacturingOrder()
        {
            _materials = new ObservableCollection<ManufacturingMaterial>();
            _steps = new ObservableCollection<ManufacturingStep>();
            OrderDate = DateTime.Now;
            StartDate = DateTime.Now;
            Status = ManufacturingStatus.مجدول;
        }
    }

    /// <summary>
    /// نموذج المواد المستخدمة في التصنيع
    /// </summary>
    public class ManufacturingMaterial : BaseEntity
    {
        private int _manufacturingOrderId;
        private string _materialName = string.Empty;
        private decimal _requiredQuantity;
        private decimal _usedQuantity;
        private string _unit = string.Empty;
        private decimal _unitCost;
        private decimal _totalCost;

        public int ManufacturingOrderId
        {
            get => _manufacturingOrderId;
            set => SetProperty(ref _manufacturingOrderId, value);
        }

        public string MaterialName
        {
            get => _materialName;
            set => SetProperty(ref _materialName, value);
        }

        public decimal RequiredQuantity
        {
            get => _requiredQuantity;
            set => SetProperty(ref _requiredQuantity, value);
        }

        public decimal UsedQuantity
        {
            get => _usedQuantity;
            set => SetProperty(ref _usedQuantity, value);
        }

        public string Unit
        {
            get => _unit;
            set => SetProperty(ref _unit, value);
        }

        public decimal UnitCost
        {
            get => _unitCost;
            set => SetProperty(ref _unitCost, value);
        }

        public decimal TotalCost
        {
            get => _totalCost;
            set => SetProperty(ref _totalCost, value);
        }

        /// <summary>
        /// حساب التكلفة الإجمالية
        /// </summary>
        public void CalculateTotalCost()
        {
            TotalCost = UsedQuantity * UnitCost;
        }
    }

    /// <summary>
    /// نموذج خطوات التصنيع
    /// </summary>
    public class ManufacturingStep : BaseEntity
    {
        private int _manufacturingOrderId;
        private string _stepName = string.Empty;
        private string _description = string.Empty;
        private int _sequence;
        private DateTime? _startTime;
        private DateTime? _endTime;
        private StepStatus _status;
        private int? _assignedEmployeeId;
        private Employee? _assignedEmployee;
        private string _notes = string.Empty;

        public int ManufacturingOrderId
        {
            get => _manufacturingOrderId;
            set => SetProperty(ref _manufacturingOrderId, value);
        }

        public string StepName
        {
            get => _stepName;
            set => SetProperty(ref _stepName, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public int Sequence
        {
            get => _sequence;
            set => SetProperty(ref _sequence, value);
        }

        public DateTime? StartTime
        {
            get => _startTime;
            set => SetProperty(ref _startTime, value);
        }

        public DateTime? EndTime
        {
            get => _endTime;
            set => SetProperty(ref _endTime, value);
        }

        public StepStatus Status
        {
            get => _status;
            set => SetProperty(ref _status, value);
        }

        public int? AssignedEmployeeId
        {
            get => _assignedEmployeeId;
            set => SetProperty(ref _assignedEmployeeId, value);
        }

        public Employee? AssignedEmployee
        {
            get => _assignedEmployee;
            set => SetProperty(ref _assignedEmployee, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public ManufacturingStep()
        {
            Status = StepStatus.في_الانتظار;
        }

        /// <summary>
        /// حساب مدة الخطوة
        /// </summary>
        public TimeSpan? GetDuration()
        {
            if (StartTime.HasValue && EndTime.HasValue)
            {
                return EndTime.Value - StartTime.Value;
            }
            return null;
        }
    }

    /// <summary>
    /// حالة أمر التصنيع
    /// </summary>
    public enum ManufacturingStatus
    {
        مجدول = 1,
        قيد_التنفيذ = 2,
        مكتمل = 3,
        متوقف = 4,
        ملغي = 5
    }

    /// <summary>
    /// حالة خطوة التصنيع
    /// </summary>
    public enum StepStatus
    {
        في_الانتظار = 1,
        قيد_التنفيذ = 2,
        مكتمل = 3,
        متوقف = 4,
        ملغي = 5
    }
}
