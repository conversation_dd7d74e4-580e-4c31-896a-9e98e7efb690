using System.IO;
using System.Text.Json;
using GlassFactoryAccounting.Models;
using System.Linq;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة الأرشيف الشاملة لحفظ جميع أنواع البيانات
    /// </summary>
    public class ArchiveService
    {
        private readonly string _archiveBasePath;

        public ArchiveService()
        {
            try
            {
                var appDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
                _archiveBasePath = Path.Combine(appDataPath, "GlassFactoryAccounting", "Archive");

                // إنشاء مجلدات الأرشيف
                CreateArchiveFolders();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ArchiveService constructor error: {ex.Message}");
                // استخدام مسار افتراضي
                _archiveBasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Archive");
                try
                {
                    CreateArchiveFolders();
                }
                catch (Exception ex2)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to create archive folders: {ex2.Message}");
                }
            }
        }

        private void CreateArchiveFolders()
        {
            try
            {
                // المجلد الرئيسي للأرشيف
                if (!Directory.Exists(_archiveBasePath))
                    Directory.CreateDirectory(_archiveBasePath);

                // مجلدات فرعية لكل نوع من البيانات
                var subFolders = new[]
                {
                    "Sales",           // فواتير المبيعات
                    "Purchases",       // فواتير المشتريات
                    "Customers",       // بيانات العملاء
                    "Suppliers",       // بيانات الموردين
                    "Products",        // بيانات المنتجات
                    "Services",        // بيانات الخدمات
                    "Employees",       // بيانات الموظفين
                    "Expenses",        // المصروفات
                    "Reports",         // التقارير
                    "Backups"          // النسخ الاحتياطية
                };

                foreach (var folder in subFolders)
                {
                    var folderPath = Path.Combine(_archiveBasePath, folder);
                    if (!Directory.Exists(folderPath))
                        Directory.CreateDirectory(folderPath);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating archive folders: {ex.Message}");
            }
        }

        #region Sales Archive

        /// <summary>
        /// حفظ فاتورة مبيعات في الأرشيف
        /// </summary>
        public async Task<bool> SaveSaleAsync(Sale sale)
        {
            try
            {
                // التأكد من وجود المجلدات
                CreateArchiveFolders();

                var salesFolder = Path.Combine(_archiveBasePath, "Sales");

                // تنظيف رقم الفاتورة من الأحرف غير المسموحة
                var cleanInvoiceNumber = string.Join("_", sale.InvoiceNumber.Split(Path.GetInvalidFileNameChars()));
                var fileName = $"Sale_{cleanInvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}.json";
                var filePath = Path.Combine(salesFolder, fileName);

                // التأكد من عدم وجود الملف مسبقاً
                int counter = 1;
                while (File.Exists(filePath))
                {
                    fileName = $"Sale_{cleanInvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}_{counter}.json";
                    filePath = Path.Combine(salesFolder, fileName);
                    counter++;
                }

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                };

                var json = JsonSerializer.Serialize(sale, options);

                // كتابة الملف مع إعادة المحاولة
                await WriteFileWithRetryAsync(filePath, json);

                // إنشاء فهرس للبحث السريع (بدون انتظار لتجنب التعليق)
                _ = Task.Run(async () => await UpdateSalesIndexAsync());

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving sale: {ex.Message}");
                // محاولة حفظ في مجلد مؤقت
                return await SaveSaleToTempAsync(sale);
            }
        }

        /// <summary>
        /// كتابة ملف مع إعادة المحاولة
        /// </summary>
        private async Task WriteFileWithRetryAsync(string filePath, string content, int maxRetries = 3)
        {
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    await File.WriteAllTextAsync(filePath, content, System.Text.Encoding.UTF8);
                    return;
                }
                catch (IOException) when (i < maxRetries - 1)
                {
                    await Task.Delay(100); // انتظار قصير قبل إعادة المحاولة
                }
            }
        }

        /// <summary>
        /// حفظ في مجلد مؤقت في حالة فشل الحفظ العادي
        /// </summary>
        private async Task<bool> SaveSaleToTempAsync(Sale sale)
        {
            try
            {
                var tempFolder = Path.Combine(Path.GetTempPath(), "GlassFactoryBackup");
                if (!Directory.Exists(tempFolder))
                    Directory.CreateDirectory(tempFolder);

                var cleanInvoiceNumber = string.Join("_", sale.InvoiceNumber.Split(Path.GetInvalidFileNameChars()));
                var fileName = $"Sale_{cleanInvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}.json";
                var filePath = Path.Combine(tempFolder, fileName);

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(sale, options);
                await File.WriteAllTextAsync(filePath, json);

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// جلب جميع فواتير المبيعات
        /// </summary>
        public async Task<List<Sale>> GetAllSalesAsync()
        {
            var sales = new List<Sale>();

            try
            {
                // التأكد من وجود المجلدات
                CreateArchiveFolders();

                var salesFolder = Path.Combine(_archiveBasePath, "Sales");

                if (Directory.Exists(salesFolder))
                {
                    var files = Directory.GetFiles(salesFolder, "*.json", SearchOption.TopDirectoryOnly);

                    // معالجة الملفات بشكل متوازي مع حد أقصى
                    var semaphore = new SemaphoreSlim(5); // حد أقصى 5 ملفات في نفس الوقت
                    var tasks = files.Select(async file =>
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            return await LoadSaleFromFileAsync(file);
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    });

                    var results = await Task.WhenAll(tasks);
                    // إصلاح مشكلة AddRange مع IEnumerable<Sale?>
                    sales.AddRange(results.Where(s => s != null).Cast<Sale>());
                }

                // البحث في المجلد المؤقت أيضاً
                await LoadSalesFromTempAsync(sales);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading sales: {ex.Message}");
            }

            return sales.OrderByDescending(s => s.SaleDate).ToList();
        }

        /// <summary>
        /// تحميل فاتورة من ملف
        /// </summary>
        private async Task<Sale?> LoadSaleFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return null;

                var json = await File.ReadAllTextAsync(filePath, System.Text.Encoding.UTF8);

                if (string.IsNullOrWhiteSpace(json))
                    return null;

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                };

                var sale = JsonSerializer.Deserialize<Sale>(json, options);

                if (sale != null)
                {
                    // إضافة نص الخدمات للعرض
                    if (sale.SaleItems?.Any() == true)
                    {
                        sale.ServicesText = string.Join(", ", sale.SaleItems.Select(i => i.Service).Distinct().Where(s => !string.IsNullOrEmpty(s)));
                    }
                    else
                    {
                        sale.ServicesText = "لا توجد خدمات";
                    }
                }

                return sale;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading sale from {filePath}: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تحميل الفواتير من المجلد المؤقت
        /// </summary>
        private async Task LoadSalesFromTempAsync(List<Sale> sales)
        {
            try
            {
                var tempFolder = Path.Combine(Path.GetTempPath(), "GlassFactoryBackup");
                if (Directory.Exists(tempFolder))
                {
                    var files = Directory.GetFiles(tempFolder, "*.json");
                    foreach (var file in files)
                    {
                        var sale = await LoadSaleFromFileAsync(file);
                        if (sale != null)
                        {
                            sales.Add(sale);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading temp sales: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف فاتورة مبيعات
        /// </summary>
        public async Task<bool> DeleteSaleAsync(string invoiceNumber)
        {
            try
            {
                var salesFolder = Path.Combine(_archiveBasePath, "Sales");
                var files = Directory.GetFiles(salesFolder, "*.json");
                
                foreach (var file in files)
                {
                    try
                    {
                        var json = await File.ReadAllTextAsync(file);
                        var sale = JsonSerializer.Deserialize<Sale>(json);
                        if (sale?.InvoiceNumber == invoiceNumber)
                        {
                            File.Delete(file);
                            await UpdateSalesIndexAsync();
                            return true;
                        }
                    }
                    catch
                    {
                        // تجاهل الملفات التالفة
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting sale: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// تحديث فهرس المبيعات للبحث السريع
        /// </summary>
        private async Task UpdateSalesIndexAsync()
        {
            try
            {
                var sales = await GetAllSalesAsync();
                var index = new
                {
                    LastUpdated = DateTime.Now,
                    TotalSales = sales.Count,
                    TotalAmount = sales.Sum(s => s.NetAmount),
                    DateRange = new
                    {
                        From = sales.Count > 0 ? sales.Min(s => s.SaleDate) : DateTime.Now,
                        To = sales.Count > 0 ? sales.Max(s => s.SaleDate) : DateTime.Now
                    },
                    TopCustomers = sales.GroupBy(s => s.Customer?.Name)
                                       .Select(g => new { Customer = g.Key, Count = g.Count() })
                                       .OrderByDescending(x => x.Count)
                                       .Take(5)
                                       .ToList(),
                    TopServices = sales.SelectMany(s => s.SaleItems.Select(i => i.Service))
                                      .GroupBy(s => s)
                                      .Select(g => new { Service = g.Key, Count = g.Count() })
                                      .OrderByDescending(x => x.Count)
                                      .Take(5)
                                      .ToList()
                };

                var indexPath = Path.Combine(_archiveBasePath, "sales_index.json");
                var indexJson = JsonSerializer.Serialize(index, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(indexPath, indexJson);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating sales index: {ex.Message}");
            }
        }

        #endregion

        #region Customers Archive

        /// <summary>
        /// حفظ عميل في الأرشيف
        /// </summary>
        public async Task<int> SaveCustomerAsync(Customer customer)
        {
            try
            {
                var customersFolder = Path.Combine(_archiveBasePath, "Customers");
                
                // توليد ID جديد
                var customers = await GetAllCustomersAsync();
                customer.Id = customers.Count > 0 ? customers.Max(c => c.Id) + 1 : 1;
                
                var fileName = $"Customer_{customer.Id}.json";
                var filePath = Path.Combine(customersFolder, fileName);
                
                var json = JsonSerializer.Serialize(customer, new JsonSerializerOptions 
                { 
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });
                
                await File.WriteAllTextAsync(filePath, json);
                return customer.Id;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving customer: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// جلب جميع العملاء
        /// </summary>
        public async Task<List<Customer>> GetAllCustomersAsync()
        {
            var customers = new List<Customer>();

            try
            {
                var customersFolder = Path.Combine(_archiveBasePath, "Customers");
                
                if (Directory.Exists(customersFolder))
                {
                    var files = Directory.GetFiles(customersFolder, "*.json");
                    foreach (var file in files)
                    {
                        try
                        {
                            var json = await File.ReadAllTextAsync(file);
                            var customer = JsonSerializer.Deserialize<Customer>(json);
                            if (customer != null)
                                customers.Add(customer);
                        }
                        catch
                        {
                            // تجاهل الملفات التالفة
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading customers: {ex.Message}");
            }

            return customers.OrderBy(c => c.Name).ToList();
        }

        /// <summary>
        /// حذف عميل من الأرشيف
        /// </summary>
        public Task<bool> DeleteCustomerAsync(int customerId)
        {
            try
            {
                var customersFolder = Path.Combine(_archiveBasePath, "Customers");
                var fileName = $"Customer_{customerId}.json";
                var filePath = Path.Combine(customersFolder, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return Task.FromResult(true);
                }

                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting customer: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        #endregion

        #region Suppliers Archive

        /// <summary>
        /// حفظ مورد في الأرشيف
        /// </summary>
        public async Task<int> SaveSupplierAsync(Supplier supplier)
        {
            try
            {
                var suppliersFolder = Path.Combine(_archiveBasePath, "Suppliers");

                // توليد ID جديد
                var suppliers = await GetAllSuppliersAsync();
                supplier.Id = suppliers.Count > 0 ? suppliers.Max(s => s.Id) + 1 : 1;

                var fileName = $"Supplier_{supplier.Id}.json";
                var filePath = Path.Combine(suppliersFolder, fileName);

                var json = JsonSerializer.Serialize(supplier, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                await File.WriteAllTextAsync(filePath, json);
                return supplier.Id;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving supplier: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// جلب جميع الموردين
        /// </summary>
        public async Task<List<Supplier>> GetAllSuppliersAsync()
        {
            var suppliers = new List<Supplier>();

            try
            {
                var suppliersFolder = Path.Combine(_archiveBasePath, "Suppliers");

                if (Directory.Exists(suppliersFolder))
                {
                    var files = Directory.GetFiles(suppliersFolder, "*.json");
                    foreach (var file in files)
                    {
                        try
                        {
                            var json = await File.ReadAllTextAsync(file);
                            var supplier = JsonSerializer.Deserialize<Supplier>(json);
                            if (supplier != null)
                                suppliers.Add(supplier);
                        }
                        catch
                        {
                            // تجاهل الملفات التالفة
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading suppliers: {ex.Message}");
            }

            return suppliers.OrderBy(s => s.Name).ToList();
        }

        /// <summary>
        /// حذف مورد من الأرشيف
        /// </summary>
        public Task<bool> DeleteSupplierAsync(int supplierId)
        {
            try
            {
                var suppliersFolder = Path.Combine(_archiveBasePath, "Suppliers");
                var fileName = $"Supplier_{supplierId}.json";
                var filePath = Path.Combine(suppliersFolder, fileName);

                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    return Task.FromResult(true);
                }

                return Task.FromResult(false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting supplier: {ex.Message}");
                return Task.FromResult(false);
            }
        }

        #endregion

        #region Purchases Archive

        /// <summary>
        /// حفظ فاتورة مشتريات في الأرشيف
        /// </summary>
        public async Task<bool> SavePurchaseAsync(Purchase purchase)
        {
            try
            {
                // التأكد من وجود المجلدات
                CreateArchiveFolders();

                var purchasesFolder = Path.Combine(_archiveBasePath, "Purchases");

                // تنظيف رقم الفاتورة من الأحرف غير المسموحة
                var cleanInvoiceNumber = string.Join("_", purchase.InvoiceNumber.Split(Path.GetInvalidFileNameChars()));
                var fileName = $"Purchase_{cleanInvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}.json";
                var filePath = Path.Combine(purchasesFolder, fileName);

                // التأكد من عدم وجود الملف مسبقاً
                int counter = 1;
                while (File.Exists(filePath))
                {
                    fileName = $"Purchase_{cleanInvoiceNumber}_{DateTime.Now:yyyyMMddHHmmss}_{counter}.json";
                    filePath = Path.Combine(purchasesFolder, fileName);
                    counter++;
                }

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                };

                var json = JsonSerializer.Serialize(purchase, options);

                // كتابة الملف مع إعادة المحاولة
                await WriteFileWithRetryAsync(filePath, json);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving purchase: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب جميع فواتير المشتريات
        /// </summary>
        public async Task<List<Purchase>> GetAllPurchasesAsync()
        {
            var purchases = new List<Purchase>();

            try
            {
                // التأكد من وجود المجلدات
                CreateArchiveFolders();

                var purchasesFolder = Path.Combine(_archiveBasePath, "Purchases");

                if (Directory.Exists(purchasesFolder))
                {
                    var files = Directory.GetFiles(purchasesFolder, "*.json", SearchOption.TopDirectoryOnly);

                    foreach (var file in files)
                    {
                        var purchase = await LoadPurchaseFromFileAsync(file);
                        if (purchase != null)
                        {
                            purchases.Add(purchase);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading purchases: {ex.Message}");
            }

            return purchases.OrderByDescending(p => p.PurchaseDate).ToList();
        }

        /// <summary>
        /// تحميل فاتورة مشتريات من ملف
        /// </summary>
        private async Task<Purchase?> LoadPurchaseFromFileAsync(string filePath)
        {
            try
            {
                if (!File.Exists(filePath))
                    return null;

                var json = await File.ReadAllTextAsync(filePath, System.Text.Encoding.UTF8);

                if (string.IsNullOrWhiteSpace(json))
                    return null;

                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true,
                    DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull
                };

                var purchase = JsonSerializer.Deserialize<Purchase>(json, options);

                if (purchase != null)
                {
                    // إضافة نص الخدمات للعرض
                    if (purchase.PurchaseItems?.Any() == true)
                    {
                        purchase.ServicesText = string.Join(", ", purchase.PurchaseItems.Select(i => i.Service).Distinct().Where(s => !string.IsNullOrEmpty(s)));
                    }
                    else
                    {
                        purchase.ServicesText = "لا توجد خدمات";
                    }
                }

                return purchase;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading purchase from {filePath}: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region General Archive Methods

        /// <summary>
        /// إنشاء نسخة احتياطية شاملة
        /// </summary>
        public async Task<bool> CreateFullBackupAsync(string backupPath)
        {
            try
            {
                var backupFolder = Path.Combine(backupPath, $"GlassFactory_Backup_{DateTime.Now:yyyyMMdd_HHmmss}");
                Directory.CreateDirectory(backupFolder);

                // نسخ جميع مجلدات الأرشيف
                await CopyDirectoryAsync(_archiveBasePath, backupFolder);
                
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating backup: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// نسخ مجلد بشكل متكرر
        /// </summary>
        private async Task CopyDirectoryAsync(string sourceDir, string destDir)
        {
            Directory.CreateDirectory(destDir);

            foreach (var file in Directory.GetFiles(sourceDir))
            {
                var destFile = Path.Combine(destDir, Path.GetFileName(file));
                File.Copy(file, destFile, true);
            }

            foreach (var dir in Directory.GetDirectories(sourceDir))
            {
                var destSubDir = Path.Combine(destDir, Path.GetFileName(dir));
                await CopyDirectoryAsync(dir, destSubDir);
            }
        }

        /// <summary>
        /// جلب إحصائيات الأرشيف
        /// </summary>
        public async Task<ArchiveStatistics> GetArchiveStatisticsAsync()
        {
            try
            {
                var sales = await GetAllSalesAsync();
                var customers = await GetAllCustomersAsync();

                return new ArchiveStatistics
                {
                    TotalSales = sales.Count,
                    TotalSalesAmount = sales.Sum(s => s.NetAmount),
                    TotalCustomers = customers.Count,
                    LastSaleDate = sales.Count > 0 ? sales.Max(s => s.SaleDate) : DateTime.MinValue,
                    ArchiveSize = GetDirectorySize(_archiveBasePath)
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting archive statistics: {ex.Message}");
                return new ArchiveStatistics();
            }
        }

        /// <summary>
        /// حساب حجم المجلد
        /// </summary>
        private long GetDirectorySize(string path)
        {
            try
            {
                if (!Directory.Exists(path))
                    return 0;

                return Directory.GetFiles(path, "*", SearchOption.AllDirectories)
                               .Sum(file => new FileInfo(file).Length);
            }
            catch
            {
                return 0;
            }
        }

        #endregion
    }

    /// <summary>
    /// إحصائيات الأرشيف
    /// </summary>
    public class ArchiveStatistics
    {
        public int TotalSales { get; set; }
        public decimal TotalSalesAmount { get; set; }
        public int TotalCustomers { get; set; }
        public DateTime LastSaleDate { get; set; }
        public long ArchiveSize { get; set; }
    }
}
