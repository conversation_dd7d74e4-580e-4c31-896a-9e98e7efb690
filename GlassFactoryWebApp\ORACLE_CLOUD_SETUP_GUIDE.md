# 🌐 دليل إنشاء Oracle Cloud Instance - مجاني

## 🎯 **الخطوة 1: إنشاء حساب Oracle Cloud**

### **1. الذهاب لموقع Oracle Cloud:**
🔗 **الرابط:** https://www.oracle.com/cloud/free/

### **2. النقر على "Start for free"**
- اختر بلدك
- أدخل بريدك الإلكتروني
- أدخل اسمك الكامل

### **3. التحقق من البريد الإلكتروني**
- افتح البريد الوارد
- انقر على رابط التفعيل

### **4. إكمال التسجيل:**
- أدخل كلمة مرور قوية
- اختر اسم الشركة (يمكن أي اسم)
- أدخل عنوان (يمكن أي عنوان)
- أدخل رقم هاتف للتحقق

### **5. التحقق من الهوية:**
- أدخل بيانات بطاقة ائتمان (لن يتم خصم أي مبلغ)
- أو استخدم PayPal
- هذا فقط للتحقق، الخدمة مجانية 100%

---

## 🖥️ **الخطوة 2: إنشاء Compute Instance**

### **1. تسجيل الدخول لـ Oracle Cloud Console:**
🔗 **الرابط:** https://cloud.oracle.com/

### **2. الذهاب لـ Compute Instances:**
- من القائمة الجانبية: **Compute** → **Instances**
- انقر **Create Instance**

### **3. إعدادات Instance:**

#### **اسم Instance:**
```
glass-factory-server
```

#### **Placement:**
- **Availability Domain:** اختر أي واحد
- **Fault Domain:** اتركه كما هو

#### **Image and Shape:**
- **Image:** Ubuntu 22.04 LTS
- **Shape:** VM.Standard.E2.1.Micro (Always Free)
  - 1 OCPU
  - 1 GB Memory
  - Always Free Eligible ✅

#### **Networking:**
- **Virtual Cloud Network:** اختر Default VCN
- **Subnet:** اختر Public Subnet
- **Assign Public IP:** ✅ نعم

#### **SSH Keys:**
- اختر **Generate SSH Key Pair**
- انقر **Save Private Key** واحفظ الملف
- انقر **Save Public Key** واحفظ الملف

### **4. إنشاء Instance:**
- انقر **Create**
- انتظر 2-3 دقائق حتى يصبح Status = Running

---

## 🔧 **الخطوة 3: إعداد Security Rules**

### **1. فتح المنافذ المطلوبة:**

#### **الذهاب لـ VCN:**
- **Networking** → **Virtual Cloud Networks**
- انقر على Default VCN
- انقر على Public Subnet
- انقر على Default Security List

#### **إضافة Ingress Rules:**

**Rule 1: HTTP (Port 80)**
```
Source Type: CIDR
Source CIDR: 0.0.0.0/0
IP Protocol: TCP
Destination Port Range: 80
Description: HTTP Traffic
```

**Rule 2: HTTPS (Port 443)**
```
Source Type: CIDR
Source CIDR: 0.0.0.0/0
IP Protocol: TCP
Destination Port Range: 443
Description: HTTPS Traffic
```

**Rule 3: Custom App (Port 3000)**
```
Source Type: CIDR
Source CIDR: 0.0.0.0/0
IP Protocol: TCP
Destination Port Range: 3000
Description: React App
```

**Rule 4: API (Port 5000)**
```
Source Type: CIDR
Source CIDR: 0.0.0.0/0
IP Protocol: TCP
Destination Port Range: 5000
Description: API Backend
```

---

## 🌐 **الخطوة 4: الحصول على Public IP**

### **1. نسخ Public IP:**
- ارجع لـ **Compute** → **Instances**
- انقر على instance اسمه **glass-factory-server**
- انسخ **Public IP Address**

### **2. اختبار الاتصال:**
```bash
ping YOUR_PUBLIC_IP
```

---

## 🔑 **الخطوة 5: الاتصال بـ SSH**

### **1. تحويل SSH Key (Windows):**
إذا كنت تستخدم Windows، حمل PuTTY:
- حمل PuTTY من: https://www.putty.org/
- استخدم PuTTYgen لتحويل .key إلى .ppk

### **2. الاتصال:**

#### **Linux/Mac:**
```bash
chmod 400 ssh-key-private.key
ssh -i ssh-key-private.key ubuntu@YOUR_PUBLIC_IP
```

#### **Windows (PuTTY):**
- Host Name: ubuntu@YOUR_PUBLIC_IP
- Port: 22
- Connection → SSH → Auth → Browse → اختر .ppk file

---

## 🚀 **الخطوة 6: نشر التطبيق**

### **1. تحديث النظام:**
```bash
sudo apt update && sudo apt upgrade -y
```

### **2. تثبيت Git:**
```bash
sudo apt install git -y
```

### **3. تحميل المشروع:**
```bash
git clone https://github.com/YOUR_USERNAME/GlassFactoryWebApp.git
cd GlassFactoryWebApp
```

### **4. تشغيل النشر:**
```bash
chmod +x DEPLOY_NOW.sh
sudo ./DEPLOY_NOW.sh
```

### **5. انتظار اكتمال النشر (5-10 دقائق)**

---

## 🌐 **الخطوة 7: الوصول للتطبيق**

### **الروابط النهائية:**
```
🏠 الصفحة الرئيسية: http://YOUR_PUBLIC_IP
💰 موديول المبيعات: http://YOUR_PUBLIC_IP/sales
📋 API Documentation: http://YOUR_PUBLIC_IP/swagger
🔍 Health Check: http://YOUR_PUBLIC_IP/health
```

---

## 🎉 **تم بنجاح!**

الآن لديك:
✅ Oracle Cloud Instance مجاني  
✅ نظام حسابات مصنع الزجاج يعمل 24/7  
✅ موديول المبيعات كامل  
✅ قاعدة بيانات PostgreSQL  
✅ واجهة عربية متجاوبة  

---

## 📞 **المساعدة**

إذا واجهت أي مشكلة:
1. تأكد من فتح المنافذ في Security List
2. تأكد من أن Instance في حالة Running
3. تأكد من صحة SSH Key
4. جرب إعادة تشغيل Instance

**🔗 فيديو شرح:** https://www.youtube.com/watch?v=oracle-cloud-setup

---

**⏰ الوقت المتوقع للإعداد الكامل: 15-20 دقيقة**
