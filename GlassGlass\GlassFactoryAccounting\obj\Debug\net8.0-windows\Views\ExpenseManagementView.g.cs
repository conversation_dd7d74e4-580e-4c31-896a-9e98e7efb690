﻿#pragma checksum "..\..\..\..\Views\ExpenseManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "960EE8DEAB1F04DE81EAAE68440C9E0CE472F16D"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ExpenseManagementView
    /// </summary>
    public partial class ExpenseManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 92 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtMainExpenseName;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSubExpenseName;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkIsProjectRelated;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ProjectDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtProjectTitle;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpProjectStartDate;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtProjectDescription;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtProjectNotes;
        
        #line default
        #line hidden
        
        
        #line 199 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClear;
        
        #line default
        #line hidden
        
        
        #line 222 "..\..\..\..\Views\ExpenseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid DgExpenseCategories;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/expensemanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ExpenseManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtMainExpenseName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TxtSubExpenseName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.ChkIsProjectRelated = ((System.Windows.Controls.CheckBox)(target));
            
            #line 141 "..\..\..\..\Views\ExpenseManagementView.xaml"
            this.ChkIsProjectRelated.Checked += new System.Windows.RoutedEventHandler(this.ChkIsProjectRelated_Checked);
            
            #line default
            #line hidden
            
            #line 141 "..\..\..\..\Views\ExpenseManagementView.xaml"
            this.ChkIsProjectRelated.Unchecked += new System.Windows.RoutedEventHandler(this.ChkIsProjectRelated_Unchecked);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ProjectDetailsPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 6:
            this.TxtProjectTitle = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.DpProjectStartDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 8:
            this.TxtProjectDescription = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.TxtProjectNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 202 "..\..\..\..\Views\ExpenseManagementView.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnClear = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\..\Views\ExpenseManagementView.xaml"
            this.BtnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.DgExpenseCategories = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 13:
            
            #line 335 "..\..\..\..\Views\ExpenseManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEdit_Click);
            
            #line default
            #line hidden
            break;
            case 14:
            
            #line 338 "..\..\..\..\Views\ExpenseManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDelete_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

