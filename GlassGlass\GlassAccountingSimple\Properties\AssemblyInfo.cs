using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;

[assembly: AssemblyT<PERSON><PERSON>("Glass Factory Accounting System")]
[assembly: AssemblyDescription("Simple Windows Forms Accounting System")]
[assembly: AssemblyConfiguration("")]
[assembly: Assembly<PERSON>om<PERSON>y("<PERSON><PERSON><PERSON>")]
[assembly: AssemblyProduct("Glass Factory Accounting")]
[assembly: AssemblyCopyright("Copyright © 2025")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyCulture("")]

[assembly: ComVisible(false)]

[assembly: Guid("a1b2c3d4-e5f6-7890-abcd-************")]

[assembly: AssemblyVersion("*******")]
[assembly: AssemblyFileVersion("*******")]
