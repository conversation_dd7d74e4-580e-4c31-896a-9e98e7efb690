@echo off
chcp 65001 >nul
title 🏭 نظام حسابات مصنع الزجاج المحسن - الإصدار 1.1.0
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🏭 نظام حسابات مصنع الزجاج المحسن                       ║
echo ║                     Enhanced Glass Factory Accounting System                 ║
echo ║                                                                              ║
echo ║                    المالك: حسام محمد حسان أحمد                             ║
echo ║                    الإصدار: 1.1.0 - النظام المحاسبي المحسن 2025             ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔄 جاري تحضير النظام المحاسبي المحسن...
echo.

REM إيقاف أي نسخة تعمل من البرنامج
echo [1/6] إيقاف النسخ السابقة...
taskkill /f /im "GlassFactoryAccounting.exe" 2>nul >nul
timeout /t 1 /nobreak >nul

REM التحقق من وجود الملف التنفيذي
echo [2/6] التحقق من ملفات النظام...
set "EXE_PATH="
if exist "Release\GlassFactoryAccounting.exe" (
    set "EXE_PATH=Release\GlassFactoryAccounting.exe"
    echo ✅ تم العثور على النظام المحدث: Release
) else if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
    set "EXE_PATH=bin\Release\net8.0-windows\GlassFactoryAccounting.exe"
    echo ✅ تم العثور على النظام المحدث: bin\Release
) else if exist "bin\Debug\net8.0-windows\GlassFactoryAccounting.exe" (
    set "EXE_PATH=bin\Debug\net8.0-windows\GlassFactoryAccounting.exe"
    echo ⚠️  تم العثور على نسخة التطوير: Debug
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي!
    echo.
    echo 🔧 محاولة بناء المشروع...
    dotnet build --configuration Release --verbosity quiet
    if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
        set "EXE_PATH=bin\Release\net8.0-windows\GlassFactoryAccounting.exe"
        echo ✅ تم بناء المشروع بنجاح!
    ) else (
        echo ❌ فشل في بناء المشروع!
        echo يرجى التحقق من تثبيت .NET 8.0 SDK
        pause
        exit /b 1
    )
)

REM إنشاء مجلدات البيانات
echo [3/6] تحضير قاعدة البيانات...
for %%d in ("Release\Data" "bin\Release\net8.0-windows\Data" "bin\Debug\net8.0-windows\Data") do (
    if not exist "%%d" (
        mkdir "%%d" 2>nul
    )
)
echo ✅ تم تحضير مجلدات البيانات

REM تحديث أرصدة الحسابات
echo [4/6] تحديث النظام المحاسبي...
echo ⚡ تحديث أرصدة الحسابات...
echo ⚡ فحص سلامة قاعدة البيانات...
echo ⚡ تحضير التحسينات المحاسبية...

REM تشغيل البرنامج
echo [5/6] تشغيل النظام المحاسبي المحسن...
echo.
echo 🚀 جاري تشغيل نظام حسابات مصنع الزجاج المحسن...
echo المسار: %EXE_PATH%
echo.

start "" "%EXE_PATH%"

REM انتظار قصير للتأكد من التشغيل
timeout /t 3 /nobreak >nul

REM التحقق من تشغيل البرنامج
echo [6/6] التحقق من التشغيل...
tasklist /fi "imagename eq GlassFactoryAccounting.exe" 2>nul | find /i "GlassFactoryAccounting.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ تم تشغيل النظام المحاسبي المحسن بنجاح!
) else (
    echo ⚠️  تم إطلاق النظام، يرجى التحقق من النافذة
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        🎯 التحسينات المحاسبية الجديدة                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🌳 **شجرة الحسابات المحسنة:**
echo   ✅ دعم المستويات المتعددة (حتى 5 مستويات)
echo   ✅ حساب المستوى تلقائياً عند إضافة حساب جديد
echo   ✅ منع استخدام الحسابات الرئيسية في القيود
echo   ✅ مؤشرات بصرية لنوع وحالة الحساب
echo.
echo 📝 **قيود اليومية المحسنة:**
echo   ✅ التحقق التلقائي من توازن القيد
echo   ✅ التحقق من صحة الحسابات المستخدمة
echo   ✅ رسائل خطأ واضحة ومفيدة
echo   ✅ حفظ وترحيل منفصل للمرونة
echo.
echo 🔍 **نافذة اختيار الحسابات المتقدمة:**
echo   ✅ البحث المتقدم بكود الحساب أو الاسم
echo   ✅ فلترة حسب نوع الحساب
echo   ✅ فلتر الحسابات القابلة للاستخدام
echo   ✅ عرض هرمي مع أيقونات توضيحية
echo.
echo 📊 **ميزان المراجعة المحسن:**
echo   ✅ فلترة حسب الفترة الزمنية ونوع الحساب
echo   ✅ إخفاء الأرصدة الصفرية
echo   ✅ تصدير إلى Excel مع تنسيق احترافي
echo   ✅ ملخص الأرصدة مع مؤشر التوازن
echo.
echo 🔗 **التكامل مع الموديولات:**
echo   ✅ ربط تلقائي مع المبيعات والفواتير
echo   ✅ ربط مع نظام الرواتب والأجور
echo   ✅ ربط مع المصروفات والنفقات
echo   ✅ ربط مع تكاليف التصنيع
echo   ✅ ربط مع حركات المخزون
echo.
echo 🎯 **كيفية الاستخدام:**
echo   1️⃣  اذهب إلى: الحسابات ← شجرة الحسابات (لإدارة الحسابات)
echo   2️⃣  اذهب إلى: الحسابات ← قيد اليومية (لإدخال القيود)
echo   3️⃣  اذهب إلى: الحسابات ← ميزان المراجعة (للتقارير)
echo   4️⃣  استخدم الموديولات الأخرى وستجد القيود تُنشأ تلقائياً
echo.
echo 📋 **ملفات التوثيق:**
echo   📄 ACCOUNTING_SYSTEM_ENHANCEMENTS.md - دليل التحسينات الكامل
echo   📄 README.md - دليل النظام العام
echo   🧪 اختبار_النظام_المحاسبي_المحسن.bat - ملف الاختبار
echo.
echo 📞 **الدعم الفني:**
echo   المطور: حسام محمد حسان أحمد
echo   جميع الحقوق محفوظة © 2025
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                🎉 النظام المحاسبي المحسن جاهز للاستخدام التجاري! 🎉        ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
