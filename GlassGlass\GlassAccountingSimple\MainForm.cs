using System;
using System.Drawing;
using System.Windows.Forms;

namespace GlassAccountingSimple
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            SetupForm();
        }

        private void SetupForm()
        {
            this.Text = "🏭 نظام حسابات مصنع الزجاج - Glass Factory Accounting";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            MessageBox.Show("البرنامج يعمل بنجاح!\nThe program is working successfully!", 
                "نجح - Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnAccounts_Click(object sender, EventArgs e)
        {
            MessageBox.Show("شجرة الحسابات ستكون متاحة قريباً\nChart of Accounts coming soon!", 
                "قريباً - Coming Soon", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق البرنامج؟\nDo you want to exit?", 
                "تأكيد - Confirm", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
    }
}
