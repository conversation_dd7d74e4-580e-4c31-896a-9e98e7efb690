﻿<Window x:Class="GlassFactoryAccounting.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:local="clr-namespace:GlassFactoryAccounting"
        xmlns:sys="clr-namespace:System;assembly=mscorlib"
        mc:Ignorable="d"
        Title="نظام حسابات مصنع الزجاج"
        Height="800" Width="1200"
        WindowState="Maximized"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">

    <Window.Resources>
        <!-- تعريف الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="AccentBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="BackgroundBrush" Color="#F5F5F5"/>
        <SolidColorBrush x:Key="SurfaceBrush" Color="#FFFFFF"/>

        <!-- نمط الأزرار الجانبية -->
        <Style x:Key="SideMenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#33FFFFFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#66FFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>



        <!-- ستايل للأزرار المدمجة (جنباً إلى جنب) -->
        <Style x:Key="CompactMenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="8,10"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="6">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                            VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#33FFFFFF"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#66FFFFFF"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundBrush}">
        <Grid.ColumnDefinitions>
            <ColumnDefinition x:Name="SideMenuColumn" Width="250"/>
            <ColumnDefinition Width="*"/>
        </Grid.ColumnDefinitions>

        <!-- القائمة الجانبية -->
        <Border x:Name="SideMenuBorder" Grid.Column="0" Background="{StaticResource PrimaryBrush}">
            <StackPanel>
                <!-- شعار البرنامج -->
                <Border Background="#1976D2" Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="🏭" FontSize="32" HorizontalAlignment="Center" Foreground="White"/>
                        <TextBlock Text="نظام حسابات" FontSize="18" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="White"/>
                        <TextBlock Text="مصنع الزجاج" FontSize="16"
                                 HorizontalAlignment="Center" Foreground="White"/>
                    </StackPanel>
                </Border>

                <Button x:Name="BtnDashboard" Content="🏠 الرئيسية" Style="{StaticResource SideMenuButtonStyle}" Click="BtnDashboard_Click"/>
                <Button x:Name="BtnSales" Content="💰 المبيعات" Style="{StaticResource SideMenuButtonStyle}" Click="BtnSales_Click"/>
                <Button x:Name="BtnPurchases" Content="🛒 المشتريات" Style="{StaticResource SideMenuButtonStyle}" Click="BtnPurchases_Click"/>
                <Button x:Name="BtnInventory" Content="📦 المخازن" Style="{StaticResource SideMenuButtonStyle}" Click="BtnInventory_Click"/>
                <Button x:Name="BtnManufacturing" Content="🏭 التصنيع" Style="{StaticResource SideMenuButtonStyle}" Click="BtnManufacturing_Click"/>
                <Button x:Name="BtnExpenses" Content="💸 المصروفات" Style="{StaticResource SideMenuButtonStyle}" Click="BtnExpenses_Click"/>
                <Button x:Name="BtnEmployeeAdvances" Content="👥 عهدات الموظفين" Style="{StaticResource SideMenuButtonStyle}" Click="BtnEmployeeAdvances_Click"/>
                <Button x:Name="BtnSalaries" Content="💵 الرواتب والأجور" Style="{StaticResource SideMenuButtonStyle}" Click="BtnSalaries_Click"/>
                <Button x:Name="BtnServices" Content="🔧 الخدمات" Style="{StaticResource SideMenuButtonStyle}" Click="BtnServices_Click"/>

                <!-- إدارة الشركة -->
                <Separator Margin="10,5" Background="#66FFFFFF"/>
                <TextBlock Text="إدارة الشركة" FontSize="12" FontWeight="Bold" Foreground="#CCFFFFFF"
                          HorizontalAlignment="Center" Margin="0,5"/>

                <!-- أزرار إدارة الشركة في صف واحد -->
                <Grid Margin="5,2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Button Grid.Column="0" x:Name="BtnCompanyBranches" Content="🏢 الفروع"
                            Style="{StaticResource CompactMenuButtonStyle}" Click="BtnCompanyBranches_Click"
                            Margin="0,0,2,0"/>
                    <Button Grid.Column="1" x:Name="BtnResponsiblePersons" Content="👤 المسؤولين"
                            Style="{StaticResource CompactMenuButtonStyle}" Click="BtnResponsiblePersons_Click"
                            Margin="2,0,0,0"/>
                </Grid>

                <Separator Margin="10,5" Background="#66FFFFFF"/>
                <Button x:Name="BtnAccounting" Content="📊 الحسابات" Style="{StaticResource SideMenuButtonStyle}" Click="BtnAccounting_Click"/>
                <Button x:Name="BtnReports" Content="📋 التقارير" Style="{StaticResource SideMenuButtonStyle}" Click="BtnReports_Click"/>
                <Button x:Name="BtnArchive" Content="📁 الأرشيف والتحليلات" Style="{StaticResource SideMenuButtonStyle}" Click="BtnArchive_Click"/>
                <Button x:Name="BtnSettings" Content="⚙️ الإعدادات" Style="{StaticResource SideMenuButtonStyle}" Click="BtnSettings_Click"/>
            </StackPanel>
        </Border>

        <!-- المحتوى الرئيسي -->
        <Grid Grid.Column="1">
            <Grid.RowDefinitions>
                <RowDefinition Height="60"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- شريط العنوان -->
            <Border Grid.Row="0" Background="{StaticResource SurfaceBrush}"
                    BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
                <Grid Margin="20,0">
                    <!-- زر إخفاء/إظهار القائمة الجانبية -->
                    <Button x:Name="BtnToggleSideMenu" Content="☰"
                           Background="Transparent" BorderThickness="0"
                           FontSize="20" FontWeight="Bold"
                           Foreground="{StaticResource PrimaryBrush}"
                           HorizontalAlignment="Left" VerticalAlignment="Center"
                           Width="40" Height="40" Cursor="Hand"
                           Click="BtnToggleSideMenu_Click"
                           ToolTip="إخفاء/إظهار القائمة الجانبية"/>

                    <TextBlock x:Name="TxtPageTitle" Text="الرئيسية"
                             FontSize="24" FontWeight="Bold"
                             VerticalAlignment="Center" Margin="50,0,0,0"
                             Foreground="{StaticResource PrimaryBrush}"/>

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                        <TextBlock Text="التاريخ: " VerticalAlignment="Center" Margin="0,0,5,0"/>
                        <TextBlock x:Name="TxtCurrentDate" Text="{Binding Source={x:Static sys:DateTime.Now}, StringFormat='{}{0:yyyy/MM/dd HH:mm}'}"
                                 VerticalAlignment="Center" FontWeight="Bold"/>
                        <TextBlock Text=" | الوقت: " VerticalAlignment="Center" Margin="10,0,5,0"/>
                        <TextBlock x:Name="TxtCurrentTime" VerticalAlignment="Center" FontWeight="Bold"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- منطقة المحتوى -->
            <ContentControl x:Name="MainContentArea" Grid.Row="1" Margin="20"/>
        </Grid>
    </Grid>
</Window>
