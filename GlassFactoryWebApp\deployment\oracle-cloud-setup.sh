#!/bin/bash

# 🏭 Glass Factory Accounting System - Oracle Cloud Deployment Script
# نص نشر نظام حسابات مصنع الزجاج على Oracle Cloud Free Tier

set -e

echo "🚀 بدء نشر نظام حسابات مصنع الزجاج على Oracle Cloud"
echo "=================================================="

# متغيرات النشر
APP_NAME="glass-factory-app"
DB_NAME="glass_factory_db"
DB_USER="glass_factory_user"
DB_PASSWORD="GlassFactory2025!"
DOMAIN_NAME="glass-factory.your-domain.com"

# تحديث النظام
echo "📦 تحديث النظام..."
sudo apt update && sudo apt upgrade -y

# تثبيت Docker و Docker Compose
echo "🐳 تثبيت Docker..."
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

echo "🐳 تثبيت Docker Compose..."
sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# تثبيت .NET 8 SDK
echo "⚙️ تثبيت .NET 8 SDK..."
wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
sudo dpkg -i packages-microsoft-prod.deb
sudo apt update
sudo apt install -y dotnet-sdk-8.0

# تثبيت Node.js و npm
echo "📦 تثبيت Node.js..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# تثبيت PostgreSQL
echo "🗄️ تثبيت PostgreSQL..."
sudo apt install -y postgresql postgresql-contrib

# إعداد قاعدة البيانات
echo "🗄️ إعداد قاعدة البيانات..."
sudo -u postgres psql << EOF
CREATE DATABASE $DB_NAME;
CREATE USER $DB_USER WITH ENCRYPTED PASSWORD '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
ALTER USER $DB_USER CREATEDB;
\q
EOF

# تثبيت Nginx
echo "🌐 تثبيت Nginx..."
sudo apt install -y nginx

# تثبيت Certbot للـ SSL
echo "🔒 تثبيت Certbot..."
sudo apt install -y certbot python3-certbot-nginx

# إنشاء مجلدات التطبيق
echo "📁 إنشاء مجلدات التطبيق..."
sudo mkdir -p /var/www/$APP_NAME
sudo mkdir -p /var/log/$APP_NAME
sudo chown -R $USER:$USER /var/www/$APP_NAME
sudo chown -R $USER:$USER /var/log/$APP_NAME

# نسخ ملفات التطبيق
echo "📋 نسخ ملفات التطبيق..."
cp -r ../GlassFactoryWebApp/* /var/www/$APP_NAME/

# بناء التطبيق
echo "🔨 بناء التطبيق..."
cd /var/www/$APP_NAME

# بناء Backend
echo "🔨 بناء Backend..."
dotnet restore
dotnet publish -c Release -o ./publish

# بناء Frontend
echo "🔨 بناء Frontend..."
cd client
npm install
npm run build
cd ..

# إنشاء ملف خدمة systemd
echo "⚙️ إنشاء خدمة systemd..."
sudo tee /etc/systemd/system/$APP_NAME.service > /dev/null << EOF
[Unit]
Description=Glass Factory Accounting System
After=network.target

[Service]
Type=notify
ExecStart=/usr/bin/dotnet /var/www/$APP_NAME/publish/GlassFactoryWebApp.dll
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=$APP_NAME
User=$USER
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=ASPNETCORE_URLS=http://localhost:5000
WorkingDirectory=/var/www/$APP_NAME/publish

[Install]
WantedBy=multi-user.target
EOF

# تمكين وبدء الخدمة
echo "🚀 تمكين وبدء الخدمة..."
sudo systemctl daemon-reload
sudo systemctl enable $APP_NAME
sudo systemctl start $APP_NAME

# إعداد Nginx
echo "🌐 إعداد Nginx..."
sudo tee /etc/nginx/sites-available/$APP_NAME > /dev/null << EOF
server {
    listen 80;
    server_name $DOMAIN_NAME;
    
    # Redirect HTTP to HTTPS
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name $DOMAIN_NAME;
    
    # SSL Configuration (will be added by Certbot)
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Static files
    location / {
        root /var/www/$APP_NAME/client/build;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API routes
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # File uploads
    client_max_body_size 50M;
    
    # Logs
    access_log /var/log/nginx/$APP_NAME.access.log;
    error_log /var/log/nginx/$APP_NAME.error.log;
}
EOF

# تمكين الموقع
sudo ln -sf /etc/nginx/sites-available/$APP_NAME /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

# إعداد SSL Certificate
echo "🔒 إعداد شهادة SSL..."
sudo certbot --nginx -d $DOMAIN_NAME --non-interactive --agree-tos --email admin@$DOMAIN_NAME

# إعداد جدار الحماية
echo "🔥 إعداد جدار الحماية..."
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

# إعداد النسخ الاحتياطية التلقائية
echo "💾 إعداد النسخ الاحتياطية..."
sudo mkdir -p /var/backups/$APP_NAME

# إنشاء سكريبت النسخ الاحتياطي
sudo tee /usr/local/bin/backup-$APP_NAME.sh > /dev/null << EOF
#!/bin/bash
BACKUP_DIR="/var/backups/$APP_NAME"
DATE=\$(date +%Y%m%d_%H%M%S)

# Database backup
sudo -u postgres pg_dump $DB_NAME > \$BACKUP_DIR/db_backup_\$DATE.sql

# Application files backup
tar -czf \$BACKUP_DIR/app_backup_\$DATE.tar.gz -C /var/www/$APP_NAME .

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: \$DATE"
EOF

sudo chmod +x /usr/local/bin/backup-$APP_NAME.sh

# إضافة مهمة cron للنسخ الاحتياطي اليومي
echo "⏰ إعداد النسخ الاحتياطي التلقائي..."
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-$APP_NAME.sh") | crontab -

# إعداد مراقبة النظام
echo "📊 إعداد مراقبة النظام..."
sudo tee /usr/local/bin/monitor-$APP_NAME.sh > /dev/null << EOF
#!/bin/bash
if ! systemctl is-active --quiet $APP_NAME; then
    echo "Service $APP_NAME is down, restarting..."
    sudo systemctl restart $APP_NAME
    echo "Service restarted at \$(date)" >> /var/log/$APP_NAME/restart.log
fi
EOF

sudo chmod +x /usr/local/bin/monitor-$APP_NAME.sh

# إضافة مهمة cron للمراقبة كل 5 دقائق
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/monitor-$APP_NAME.sh") | crontab -

# تحسين الأداء
echo "⚡ تحسين الأداء..."
# تحسين PostgreSQL
sudo tee -a /etc/postgresql/*/main/postgresql.conf > /dev/null << EOF
# Performance tuning for Oracle Cloud Free Tier
shared_buffers = 128MB
effective_cache_size = 512MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
EOF

sudo systemctl restart postgresql

echo "✅ تم نشر النظام بنجاح!"
echo "=================================================="
echo "🌐 الموقع: https://$DOMAIN_NAME"
echo "🗄️ قاعدة البيانات: $DB_NAME"
echo "👤 مستخدم قاعدة البيانات: $DB_USER"
echo "📊 حالة الخدمة: sudo systemctl status $APP_NAME"
echo "📋 سجلات التطبيق: sudo journalctl -u $APP_NAME -f"
echo "🔄 إعادة تشغيل: sudo systemctl restart $APP_NAME"
echo "💾 النسخ الاحتياطية: /var/backups/$APP_NAME"
echo "=================================================="
echo "🎉 نظام حسابات مصنع الزجاج جاهز للاستخدام!"
