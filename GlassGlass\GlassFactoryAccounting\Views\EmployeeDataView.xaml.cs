using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة بيانات الموظف
    /// </summary>
    public partial class EmployeeDataView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;

        public EmployeeDataView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _mainWindow = mainWindow;
            
            InitializeForm();
            LoadEmployees();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ اليوم كافتراضي
            DpHireDate.SelectedDate = DateTime.Now;

            // تعيين عدد ساعات العمل الافتراضي
            TxtWorkingHours.Text = "8";

            // توليد كود الموظف تلقائياً
            GenerateEmployeeCode();
        }

        private void GenerateEmployeeCode()
        {
            TxtEmployeeCode.Text = $"EMP{DateTime.Now:yyyyMMddHHmmss}";
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                EmployeesDataGrid.ItemsSource = employees;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var employee = new Employee
                {
                    Name = TxtEmployeeName.Text.Trim(),
                    EmployeeCode = TxtEmployeeCode.Text.Trim(),
                    Position = TxtPosition.Text.Trim(),
                    Branch = (CmbBranch.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "",
                    Department = (CmbDepartment.SelectedItem as ComboBoxItem)?.Content?.ToString() ?? "",
                    BasicSalary = decimal.Parse(TxtBasicSalary.Text),
                    WorkingHours = int.Parse(TxtWorkingHours.Text),
                    HireDate = DpHireDate.SelectedDate ?? DateTime.Now,
                    Phone = "", // سيتم إضافة حقل لاحقاً
                    Address = "", // سيتم إضافة حقل لاحقاً
                    Notes = TxtNotes.Text.Trim()
                };

                // التحقق من وضع التعديل
                if (BtnSave.Tag != null && int.TryParse(BtnSave.Tag.ToString(), out int employeeId))
                {
                    // وضع التعديل
                    employee.Id = employeeId;
                    if (_payrollService.UpdateEmployee(employee))
                    {
                        MessageBox.Show("تم تحديث بيانات الموظف بنجاح!", "نجح التحديث",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        ClearForm();
                        LoadEmployees();

                        // إعادة تعيين الزر للوضع العادي
                        BtnSave.Content = "💾 حفظ الموظف";
                        BtnSave.Tag = null;
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث بيانات الموظف!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    // وضع الإضافة
                    if (_payrollService.AddEmployee(employee))
                    {
                        MessageBox.Show("تم حفظ بيانات الموظف بنجاح!", "نجح الحفظ",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        ClearForm();
                        LoadEmployees();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ بيانات الموظف!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات الموظف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtEmployeeName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الموظف", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtEmployeeName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtPosition.Text))
            {
                MessageBox.Show("يرجى إدخال الوظيفة", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtPosition.Focus();
                return false;
            }

            if (CmbBranch.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الفرع", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbBranch.Focus();
                return false;
            }

            if (CmbDepartment.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار القسم", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbDepartment.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtBasicSalary.Text) || !decimal.TryParse(TxtBasicSalary.Text, out decimal salary) || salary <= 0)
            {
                MessageBox.Show("يرجى إدخال راتب أساسي صحيح", "بيانات غير صحيحة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtBasicSalary.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtWorkingHours.Text) || !int.TryParse(TxtWorkingHours.Text, out int hours) || hours <= 0)
            {
                MessageBox.Show("يرجى إدخال عدد ساعات عمل صحيح", "بيانات غير صحيحة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtWorkingHours.Focus();
                return false;
            }

            if (!DpHireDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ بدء العمل", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpHireDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            TxtEmployeeName.Clear();
            TxtPosition.Clear();
            CmbBranch.SelectedIndex = -1;
            CmbDepartment.SelectedIndex = -1;
            TxtBasicSalary.Clear();
            TxtWorkingHours.Text = "8";
            DpHireDate.SelectedDate = DateTime.Now;
            TxtNotes.Clear();
            
            GenerateEmployeeCode();
            TxtEmployeeName.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditEmployee_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int employeeId)
                {
                    // البحث عن الموظف في الجدول
                    var employees = _payrollService.GetAllEmployees();
                    var employee = employees.FirstOrDefault(emp => emp.Id == employeeId);

                    if (employee != null)
                    {
                        // تعبئة النموذج ببيانات الموظف للتعديل
                        TxtEmployeeName.Text = employee.Name;
                        TxtEmployeeCode.Text = employee.EmployeeCode;
                        TxtPosition.Text = employee.Position;

                        // البحث عن الفرع في القائمة المنسدلة
                        foreach (ComboBoxItem item in CmbBranch.Items)
                        {
                            if (item.Content.ToString() == employee.Branch)
                            {
                                CmbBranch.SelectedItem = item;
                                break;
                            }
                        }

                        // البحث عن القسم في القائمة المنسدلة
                        foreach (ComboBoxItem item in CmbDepartment.Items)
                        {
                            if (item.Content.ToString() == employee.Department)
                            {
                                CmbDepartment.SelectedItem = item;
                                break;
                            }
                        }

                        TxtBasicSalary.Text = employee.BasicSalary.ToString("N2");
                        TxtWorkingHours.Text = employee.WorkingHours.ToString();
                        DpHireDate.SelectedDate = employee.HireDate;
                        TxtNotes.Text = employee.Notes;

                        // تغيير نص الزر إلى "تحديث"
                        BtnSave.Content = "🔄 تحديث الموظف";
                        BtnSave.Tag = employeeId; // حفظ معرف الموظف

                        MessageBox.Show("تم تحميل بيانات الموظف للتعديل", "تعديل الموظف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeleteEmployee_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int employeeId)
                {
                    // البحث عن الموظف
                    var employees = _payrollService.GetAllEmployees();
                    var employee = employees.FirstOrDefault(emp => emp.Id == employeeId);

                    if (employee != null)
                    {
                        var result = MessageBox.Show(
                            $"هل أنت متأكد من حذف الموظف؟\n\nالاسم: {employee.Name}\nالكود: {employee.EmployeeCode}\nالوظيفة: {employee.Position}\n\n⚠️ تحذير: سيتم حذف جميع البيانات المرتبطة بهذا الموظف (الرواتب، السلف، المكافآت، الخصومات، إلخ)",
                            "تأكيد حذف الموظف",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Warning);

                        if (result == MessageBoxResult.Yes)
                        {
                            if (_payrollService.DeleteEmployee(employeeId))
                            {
                                MessageBox.Show($"تم حذف الموظف بنجاح!\n\nالموظف المحذوف: {employee.Name}\nالكود: {employee.EmployeeCode}",
                                    "تم الحذف بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                                LoadEmployees();
                            }
                            else
                            {
                                MessageBox.Show("فشل في حذف الموظف!", "خطأ",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        }
                    }
                    else
                    {
                        MessageBox.Show("لم يتم العثور على الموظف!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الموظف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
