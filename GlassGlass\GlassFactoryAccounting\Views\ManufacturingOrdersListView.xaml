<UserControl x:Class="GlassFactoryAccounting.Views.ManufacturingOrdersListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000">
    
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان مع زر الرجوع -->
            <Border Grid.Row="0" Background="#2C3E50" CornerRadius="10" Padding="15" Margin="0,0,0,20">
                <Grid>
                    <Button x:Name="btnBack" Content="⬅️ رجوع"
                           Background="#1E5A73" Foreground="White" Padding="10,5"
                           FontSize="14" FontWeight="Bold" HorizontalAlignment="Left"
                           Click="BtnBack_Click"/>

                    <TextBlock Text="📋 عرض أوامر التصنيع"
                              FontSize="24" FontWeight="Bold"
                              Foreground="White" HorizontalAlignment="Center"/>

                    <Button x:Name="btnToggleNavigationTools"
                           Content="❌ إخفاء أدوات التنقل"
                           Background="#6C757D" Foreground="White"
                           FontSize="12" FontWeight="Bold"
                           Padding="12,6" HorizontalAlignment="Right"
                           BorderThickness="0" Cursor="Hand"
                           ToolTip="إخفاء/إظهار شريط البحث والفلاتر لتوسيع مساحة العمل"
                           Click="BtnToggleNavigationTools_Click"/>
                </Grid>
            </Border>

            <!-- أدوات البحث والتصفية -->
            <GroupBox x:Name="SearchAndFilterPanel" Grid.Row="1" Header="🔍 البحث والتصفية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <!-- الصف الأول -->
                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                        <TextBlock Text="رقم الأمر:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtSearchOrderNumber" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                        <TextBlock Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtSearchCustomer" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                        <TextBlock Text="حالة الطلب:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="cmbSearchStatus" Padding="8" FontSize="14">
                            <ComboBoxItem Content="الكل" IsSelected="True"/>
                            <ComboBoxItem Content="تحت التشغيل"/>
                            <ComboBoxItem Content="مكتمل"/>
                            <ComboBoxItem Content="مؤجل"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="5" VerticalAlignment="Bottom">
                        <Button x:Name="btnSearch" Content="🔍 بحث" 
                               Background="#17A2B8" Foreground="White" 
                               Padding="15,8" FontSize="14" FontWeight="Bold"
                               Click="BtnSearch_Click"/>
                    </StackPanel>

                    <!-- الصف الثاني -->
                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                        <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="dpFromDate" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                        <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="dpToDate" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="2" Margin="5" VerticalAlignment="Bottom">
                        <Button x:Name="btnClearSearch" Content="🗑️ مسح البحث" 
                               Background="#6C757D" Foreground="White" 
                               Padding="15,8" FontSize="14" FontWeight="Bold"
                               Click="BtnClearSearch_Click"/>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="3" Margin="5" VerticalAlignment="Bottom">
                        <Button x:Name="btnRefresh" Content="🔄 تحديث" 
                               Background="#28A745" Foreground="White" 
                               Padding="15,8" FontSize="14" FontWeight="Bold"
                               Click="BtnRefresh_Click"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- جدول أوامر التصنيع -->
            <GroupBox Grid.Row="2" Header="📊 أوامر التصنيع" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <DataGrid x:Name="dgManufacturingOrders" AutoGenerateColumns="False" 
                         CanUserAddRows="False" CanUserDeleteRows="False"
                         GridLinesVisibility="All" HeadersVisibility="All"
                         FontSize="12" Margin="10" IsReadOnly="True"
                         SelectionMode="Single" SelectionUnit="FullRow">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الأمر" Binding="{Binding OrderNumber}" Width="100"/>
                        <DataGridTextColumn Header="اسم العميل" Binding="{Binding CustomerName}" Width="150"/>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="100"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding OrderDate, StringFormat=dd/MM/yyyy}" Width="100"/>
                        <DataGridTextColumn Header="الحالة" Binding="{Binding OrderStatus}" Width="100"/>
                        <DataGridTextColumn Header="إجمالي م²" Binding="{Binding TotalSquareMeters, StringFormat=F2}" Width="100"/>
                        <DataGridTextColumn Header="تكلفة الخدمات" Binding="{Binding TotalServicesCost, StringFormat=F2}" Width="120"/>
                        <DataGridTextColumn Header="التكاليف الإضافية" Binding="{Binding TotalAdditionalCosts, StringFormat=F2}" Width="130"/>
                        <DataGridTextColumn Header="المجموع الكلي" Binding="{Binding TotalCost, StringFormat=F2}" Width="120"/>
                        <DataGridTextColumn Header="سعر المتر" Binding="{Binding PricePerMeter, StringFormat=F2}" Width="100"/>
                        <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>
                    </DataGrid.Columns>
                </DataGrid>
            </GroupBox>

            <!-- الأزرار السفلية -->
            <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button x:Name="btnViewOrder" Content="👁️ عرض التفاصيل" 
                       Background="#17A2B8" Foreground="White" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnViewOrder_Click"/>
                
                <Button x:Name="btnEditOrder" Content="✏️ تعديل الأمر" 
                       Background="#FFC107" Foreground="Black" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnEditOrder_Click"/>
                
                <Button x:Name="btnDeleteOrder" Content="🗑️ حذف الأمر" 
                       Background="#DC3545" Foreground="White" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnDeleteOrder_Click"/>
                
                <Button x:Name="btnPrintOrder" Content="🖨️ طباعة"
                       Background="#6F42C1" Foreground="White" Padding="15,8"
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnPrintOrder_Click"/>

                <Button x:Name="btnSavePDF" Content="📄 حفظ PDF"
                       Background="#FD7E14" Foreground="White" Padding="15,8"
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnSavePDF_Click"/>

                <Button x:Name="btnExportExcel" Content="📊 تصدير Excel"
                       Background="#198754" Foreground="White" Padding="15,8"
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnExportExcel_Click"/>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</UserControl>
