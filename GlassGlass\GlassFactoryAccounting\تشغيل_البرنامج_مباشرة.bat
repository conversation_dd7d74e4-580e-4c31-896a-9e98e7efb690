@echo off
chcp 65001 >nul
title 🏭 تشغيل نظام حسابات مصنع الزجاج
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🏭 نظام حسابات مصنع الزجاج المحسن                       ║
echo ║                     Enhanced Glass Factory Accounting System                 ║
echo ║                                                                              ║
echo ║                    المالك: حسام محمد حسان أحمد                             ║
echo ║                    الإصدار: 1.1.0 - النظام المحاسبي المحسن 2025             ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 جاري تشغيل النظام...

REM إيقاف أي نسخة تعمل
taskkill /f /im "GlassFactoryAccounting.exe" 2>nul >nul

REM تشغيل البرنامج من مجلد Release
if exist "Release\GlassFactoryAccounting.exe" (
    echo ✅ تم العثور على البرنامج في مجلد Release
    cd Release
    start "" "GlassFactoryAccounting.exe"
    cd ..
    goto :success
)

REM تشغيل البرنامج من مجلد bin\Release
if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
    echo ✅ تم العثور على البرنامج في مجلد bin\Release
    cd "bin\Release\net8.0-windows"
    start "" "GlassFactoryAccounting.exe"
    cd ..\..\..
    goto :success
)

REM تشغيل البرنامج من مجلد bin\Debug
if exist "bin\Debug\net8.0-windows\GlassFactoryAccounting.exe" (
    echo ⚠️  تم العثور على نسخة Debug
    cd "bin\Debug\net8.0-windows"
    start "" "GlassFactoryAccounting.exe"
    cd ..\..\..
    goto :success
)

echo ❌ لم يتم العثور على البرنامج!
echo يرجى التأكد من وجود الملف التنفيذي في أحد المجلدات التالية:
echo - Release\GlassFactoryAccounting.exe
echo - bin\Release\net8.0-windows\GlassFactoryAccounting.exe
echo - bin\Debug\net8.0-windows\GlassFactoryAccounting.exe
pause
exit /b 1

:success
echo.
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 🎯 التحسينات الجديدة:
echo ────────────────────────────────
echo 🌳 شجرة الحسابات المحسنة مع المستويات المتعددة
echo 📝 قيود اليومية مع التحقق التلقائي من التوازن
echo 🔍 نافذة اختيار الحسابات المتقدمة
echo 📊 ميزان المراجعة المحسن مع التصدير
echo 🔗 التكامل التلقائي مع جميع الموديولات
echo.
echo 📋 للوصول للنظام المحاسبي:
echo الصفحة الرئيسية → الحسابات → شجرة الحسابات
echo.
timeout /t 5 /nobreak >nul
