<UserControl x:Class="GlassFactoryAccounting.Views.SalaryDueView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">

    <UserControl.Resources>
        <!-- الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="LightBrush" Color="#F5F5F5"/>

        <!-- نمط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط الأزرار -->
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط زر النجاح -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#388E3C"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1B5E20"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط موحد لحقول الإدخال (TextBox, ComboBox, DatePicker) -->
        <Style x:Key="UnifiedInputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>

        <!-- نمط التسميات -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="5,5,5,0"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Background="{StaticResource LightBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="💰" FontSize="24" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <TextBlock Text="الرواتب المستحقة" FontSize="20" FontWeight="Bold"
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- النموذج -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- اسم الموظف -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="اسم الموظف:" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="CmbEmployee" Grid.Row="0" Grid.Column="1" Margin="5" Style="{StaticResource UnifiedComboBoxStyle}"
                          SelectionChanged="CmbEmployee_SelectionChanged"/>

                <!-- كود الموظف -->
                <TextBlock Grid.Row="1" Grid.Column="0" Text="كود الموظف:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtEmployeeCode" Grid.Row="1" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}" IsReadOnly="True" Background="#F0F0F0"/>

                <!-- الراتب المستحق -->
                <TextBlock Grid.Row="2" Grid.Column="0" Text="الراتب المستحق:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtSalaryAmount" Grid.Row="2" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}" IsReadOnly="True" Background="#F0F0F0"/>

                <!-- تاريخ بدء العمل -->
                <TextBlock Grid.Row="3" Grid.Column="0" Text="تاريخ بدء العمل:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtHireDate" Grid.Row="3" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}" IsReadOnly="True" Background="#F0F0F0"/>

                <!-- الوظيفة -->
                <TextBlock Grid.Row="4" Grid.Column="0" Text="الوظيفة:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtPosition" Grid.Row="4" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}" IsReadOnly="True" Background="#F0F0F0"/>

                <!-- الفرع -->
                <TextBlock Grid.Row="5" Grid.Column="0" Text="الفرع:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtBranch" Grid.Row="5" Grid.Column="1" Style="{StaticResource UnifiedInputStyle}" IsReadOnly="True" Background="#F0F0F0"/>

                <!-- تاريخ استحقاق الراتب -->
                <TextBlock Grid.Row="6" Grid.Column="0" Text="تاريخ استحقاق الراتب:" Style="{StaticResource LabelStyle}"/>
                <DatePicker x:Name="DpDueDate" Grid.Row="6" Grid.Column="1" Margin="5" Style="{StaticResource UnifiedDatePickerStyle}"/>

                <!-- ملاحظات -->
                <TextBlock Grid.Row="0" Grid.Column="2" Text="ملاحظات:" Style="{StaticResource LabelStyle}"/>
                <TextBox x:Name="TxtNotes" Grid.Row="1" Grid.Column="2" Grid.RowSpan="6" Style="{StaticResource UnifiedInputStyle}"
                         Height="200" TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>

            </Grid>
        </Border>

        <!-- قائمة الرواتب المستحقة -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Height="680">
                <DataGrid x:Name="SalariesDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="كود الموظف" Binding="{Binding EmployeeCode}" Width="100"/>
                    <DataGridTextColumn Header="اسم الموظف" Binding="{Binding EmployeeName}" Width="150"/>
                    <DataGridTextColumn Header="الوظيفة" Binding="{Binding Position}" Width="120"/>
                    <DataGridTextColumn Header="الفرع" Binding="{Binding Branch}" Width="100"/>
                    <DataGridTextColumn Header="المبلغ المستحق" Binding="{Binding SalaryAmount, StringFormat='{}{0:N2}'}" Width="120"/>
                    <DataGridTextColumn Header="تاريخ الاستحقاق" Binding="{Binding DueDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="120"/>
                    <DataGridCheckBoxColumn Header="مدفوع" Binding="{Binding IsPaid}" Width="80"/>
                    <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="120"/>
                    <DataGridTemplateColumn Header="الإجراءات" Width="160">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="✏️ تعديل" Background="#2196F3" Foreground="White"
                                            BorderThickness="0" Padding="8,4" Margin="2" FontSize="10"
                                            Cursor="Hand" Click="BtnEditSalaryDue_Click"
                                            Tag="{Binding Id}"/>
                                    <Button Content="🗑️ حذف" Background="#F44336" Foreground="White"
                                            BorderThickness="0" Padding="8,4" Margin="2" FontSize="10"
                                            Cursor="Hand" Click="BtnDeleteSalaryDue_Click"
                                            Tag="{Binding Id}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
                </DataGrid>
            </ScrollViewer>
        </Border>

        <!-- الأزرار -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ الراتب المستحق" Style="{StaticResource SuccessButtonStyle}" Click="BtnSave_Click"/>
                <Button x:Name="BtnClear" Content="🗑️ مسح الحقول" Style="{StaticResource ButtonStyle}" Click="BtnClear_Click"/>
                <Button x:Name="BtnBack" Content="🔙 العودة" Style="{StaticResource ButtonStyle}" Click="BtnBack_Click"/>
            </StackPanel>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
