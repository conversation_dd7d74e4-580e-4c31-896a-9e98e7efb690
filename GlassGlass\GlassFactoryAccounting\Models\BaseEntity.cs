using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// الكلاس الأساسي لجميع الكيانات في النظام
    /// </summary>
    public abstract class BaseEntity : INotifyPropertyChanged
    {
        private int _id;
        private DateTime _createdDate;
        private DateTime _modifiedDate;
        private bool _isActive;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }

        public DateTime ModifiedDate
        {
            get => _modifiedDate;
            set => SetProperty(ref _modifiedDate, value);
        }

        public bool IsActive
        {
            get => _isActive;
            set => SetProperty(ref _isActive, value);
        }

        protected BaseEntity()
        {
            CreatedDate = DateTime.Now;
            ModifiedDate = DateTime.Now;
            IsActive = true;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
