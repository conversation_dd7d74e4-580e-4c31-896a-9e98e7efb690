using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using GlassFactoryAccountingWinForms.Models;

namespace GlassFactoryAccountingWinForms.Services
{
    /// <summary>
    /// خدمة المحاسبة
    /// </summary>
    public class AccountingService
    {
        private readonly DatabaseService _databaseService;

        public AccountingService()
        {
            _databaseService = new DatabaseService();
        }

        #region إدارة الحسابات

        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        public List<Account> GetAllAccounts()
        {
            var accounts = new List<Account>();

            try
            {
                using var connection = _databaseService.GetConnection();
                connection.Open();

                var sql = @"
                    SELECT Id, AccountCode, AccountName, AccountType, IsParent, ParentAccountId, 
                           AccountLevel, Balance, DebitBalance, CreditBalance, IsActive, 
                           CreatedDate, CreatedBy, ModifiedDate, ModifiedBy, Notes
                    FROM Accounts 
                    WHERE IsActive = 1 
                    ORDER BY AccountCode";

                using var command = new SQLiteCommand(sql, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    accounts.Add(new Account
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        AccountCode = reader["AccountCode"]?.ToString() ?? "",
                        AccountName = reader["AccountName"]?.ToString() ?? "",
                        AccountType = (AccountType)Convert.ToInt32(reader["AccountType"]),
                        IsParent = Convert.ToBoolean(reader["IsParent"]),
                        ParentAccountId = reader["ParentAccountId"] == DBNull.Value ? null : Convert.ToInt32(reader["ParentAccountId"]),
                        AccountLevel = Convert.ToInt32(reader["AccountLevel"]),
                        Balance = Convert.ToDecimal(reader["Balance"]),
                        DebitBalance = Convert.ToDecimal(reader["DebitBalance"]),
                        CreditBalance = Convert.ToDecimal(reader["CreditBalance"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                        ModifiedDate = reader["ModifiedDate"] == DBNull.Value ? null : Convert.ToDateTime(reader["ModifiedDate"]),
                        ModifiedBy = reader["ModifiedBy"]?.ToString() ?? "",
                        Notes = reader["Notes"]?.ToString() ?? ""
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في الحصول على الحسابات: {ex.Message}", ex);
            }

            return accounts;
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        public Account GetAccountById(int id)
        {
            var accounts = GetAllAccounts();
            return accounts.FirstOrDefault(a => a.Id == id);
        }

        /// <summary>
        /// الحصول على الحسابات القابلة للاستخدام في القيود
        /// </summary>
        public List<Account> GetUsableAccounts()
        {
            return GetAllAccounts().Where(a => !a.IsParent).ToList();
        }

        /// <summary>
        /// حفظ حساب جديد أو تحديث موجود
        /// </summary>
        public bool SaveAccount(Account account)
        {
            try
            {
                using var connection = _databaseService.GetConnection();
                connection.Open();

                string sql;
                if (account.Id == 0)
                {
                    // إضافة حساب جديد
                    sql = @"
                        INSERT INTO Accounts (AccountCode, AccountName, AccountType, IsParent, ParentAccountId, 
                                            AccountLevel, Balance, DebitBalance, CreditBalance, IsActive, 
                                            CreatedDate, CreatedBy, Notes)
                        VALUES (@AccountCode, @AccountName, @AccountType, @IsParent, @ParentAccountId, 
                                @AccountLevel, @Balance, @DebitBalance, @CreditBalance, @IsActive, 
                                @CreatedDate, @CreatedBy, @Notes)";
                }
                else
                {
                    // تحديث حساب موجود
                    sql = @"
                        UPDATE Accounts SET 
                            AccountCode = @AccountCode, AccountName = @AccountName, AccountType = @AccountType,
                            IsParent = @IsParent, ParentAccountId = @ParentAccountId, AccountLevel = @AccountLevel,
                            Balance = @Balance, DebitBalance = @DebitBalance, CreditBalance = @CreditBalance,
                            IsActive = @IsActive, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy, 
                            Notes = @Notes
                        WHERE Id = @Id";
                }

                // حساب مستوى الحساب تلقائياً
                if (account.ParentAccountId.HasValue)
                {
                    var parentAccount = GetAccountById(account.ParentAccountId.Value);
                    account.AccountLevel = parentAccount?.AccountLevel + 1 ?? 1;
                }
                else
                {
                    account.AccountLevel = 1;
                }

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                command.Parameters.AddWithValue("@AccountName", account.AccountName);
                command.Parameters.AddWithValue("@AccountType", (int)account.AccountType);
                command.Parameters.AddWithValue("@IsParent", account.IsParent);
                command.Parameters.AddWithValue("@ParentAccountId", account.ParentAccountId);
                command.Parameters.AddWithValue("@AccountLevel", account.AccountLevel);
                command.Parameters.AddWithValue("@Balance", account.Balance);
                command.Parameters.AddWithValue("@DebitBalance", account.DebitBalance);
                command.Parameters.AddWithValue("@CreditBalance", account.CreditBalance);
                command.Parameters.AddWithValue("@IsActive", account.IsActive);
                command.Parameters.AddWithValue("@Notes", account.Notes);
                command.Parameters.AddWithValue("@CreatedBy", account.CreatedBy);

                if (account.Id == 0)
                {
                    command.Parameters.AddWithValue("@CreatedDate", account.CreatedDate);
                }
                else
                {
                    command.Parameters.AddWithValue("@Id", account.Id);
                    command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                    command.Parameters.AddWithValue("@ModifiedBy", account.ModifiedBy);
                }

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حفظ الحساب: {ex.Message}", ex);
            }
        }

        #endregion

        #region قيود اليومية

        /// <summary>
        /// إنشاء قيد يومية جديد
        /// </summary>
        public bool CreateJournalEntry(JournalEntry entry)
        {
            try
            {
                // التحقق من توازن القيد
                if (!entry.IsBalanced)
                {
                    throw new Exception("القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن.");
                }

                // التحقق من صحة الحسابات المستخدمة
                foreach (var detail in entry.Details)
                {
                    var account = GetAccountById(detail.AccountId);
                    if (account == null)
                    {
                        throw new Exception($"الحساب غير موجود: {detail.AccountId}");
                    }
                    if (!account.CanBeUsedInEntries)
                    {
                        throw new Exception($"لا يمكن استخدام الحساب الرئيسي '{account.AccountName}' في القيود. يرجى استخدام الحسابات الفرعية فقط.");
                    }
                }

                using var connection = _databaseService.GetConnection();
                connection.Open();

                using var transaction = connection.BeginTransaction();

                try
                {
                    // إنشاء رقم قيد تلقائي إذا لم يكن موجود
                    if (string.IsNullOrEmpty(entry.EntryNumber))
                    {
                        entry.EntryNumber = GenerateEntryNumber();
                    }

                    // حفظ قيد اليومية
                    var entrySql = @"
                        INSERT INTO JournalEntries (EntryNumber, EntryDate, Description, TotalAmount, 
                                                  IsPosted, CreatedBy, CreatedDate, Reference, Notes)
                        VALUES (@EntryNumber, @EntryDate, @Description, @TotalAmount, 
                                @IsPosted, @CreatedBy, @CreatedDate, @Reference, @Notes);
                        SELECT last_insert_rowid();";

                    using var entryCommand = new SQLiteCommand(entrySql, connection, transaction);
                    entryCommand.Parameters.AddWithValue("@EntryNumber", entry.EntryNumber);
                    entryCommand.Parameters.AddWithValue("@EntryDate", entry.EntryDate.ToString("yyyy-MM-dd"));
                    entryCommand.Parameters.AddWithValue("@Description", entry.Description);
                    entryCommand.Parameters.AddWithValue("@TotalAmount", entry.TotalAmount);
                    entryCommand.Parameters.AddWithValue("@IsPosted", entry.IsPosted);
                    entryCommand.Parameters.AddWithValue("@CreatedBy", entry.CreatedBy);
                    entryCommand.Parameters.AddWithValue("@CreatedDate", entry.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    entryCommand.Parameters.AddWithValue("@Reference", entry.Reference);
                    entryCommand.Parameters.AddWithValue("@Notes", entry.Notes);

                    var entryId = Convert.ToInt32(entryCommand.ExecuteScalar());
                    entry.Id = entryId;

                    // حفظ تفاصيل القيد
                    var detailSql = @"
                        INSERT INTO JournalEntryDetails (JournalEntryId, AccountId, DebitAmount, CreditAmount, 
                                                       Description, LineNumber, CreatedDate)
                        VALUES (@JournalEntryId, @AccountId, @DebitAmount, @CreditAmount, 
                                @Description, @LineNumber, @CreatedDate)";

                    for (int i = 0; i < entry.Details.Count; i++)
                    {
                        var detail = entry.Details[i];
                        detail.JournalEntryId = entryId;
                        detail.LineNumber = i + 1;

                        using var detailCommand = new SQLiteCommand(detailSql, connection, transaction);
                        detailCommand.Parameters.AddWithValue("@JournalEntryId", detail.JournalEntryId);
                        detailCommand.Parameters.AddWithValue("@AccountId", detail.AccountId);
                        detailCommand.Parameters.AddWithValue("@DebitAmount", detail.DebitAmount);
                        detailCommand.Parameters.AddWithValue("@CreditAmount", detail.CreditAmount);
                        detailCommand.Parameters.AddWithValue("@Description", detail.Description);
                        detailCommand.Parameters.AddWithValue("@LineNumber", detail.LineNumber);
                        detailCommand.Parameters.AddWithValue("@CreatedDate", detail.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));

                        detailCommand.ExecuteNonQuery();
                    }

                    transaction.Commit();
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء قيد اليومية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// توليد رقم قيد تلقائي
        /// </summary>
        private string GenerateEntryNumber()
        {
            try
            {
                using var connection = _databaseService.GetConnection();
                connection.Open();

                var sql = "SELECT COUNT(*) FROM JournalEntries WHERE EntryDate >= @StartOfYear";
                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@StartOfYear", new DateTime(DateTime.Now.Year, 1, 1).ToString("yyyy-MM-dd"));

                var count = Convert.ToInt32(command.ExecuteScalar());
                return $"JE{DateTime.Now:yyyy}{(count + 1):D4}";
            }
            catch
            {
                return $"JE{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        #endregion
    }
}
