using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models.Accounting;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views.Accounting
{
    /// <summary>
    /// نافذة شجرة الحسابات
    /// </summary>
    public partial class ChartOfAccountsWindow : Window
    {
        private readonly AccountingService _accountingService;
        private readonly ObservableCollection<AccountTreeViewModel> _accountTree;
        
        public ChartOfAccountsWindow()
        {
            InitializeComponent();
            _accountingService = new AccountingService();
            _accountTree = new ObservableCollection<AccountTreeViewModel>();
            
            InitializeWindow();
            LoadAccountTree();
        }
        
        /// <summary>
        /// تهيئة النافذة
        /// </summary>
        private void InitializeWindow()
        {
            tvAccounts.ItemsSource = _accountTree;
            tvAccounts.SelectedItemChanged += TvAccounts_SelectedItemChanged;
        }
        
        /// <summary>
        /// تحميل شجرة الحسابات
        /// </summary>
        private void LoadAccountTree()
        {
            try
            {
                var accounts = _accountingService.GetAllAccounts();
                _accountTree.Clear();
                
                // تحميل الحسابات الرئيسية أولاً
                var parentAccounts = accounts.Where(a => a.ParentAccountId == null).OrderBy(a => a.AccountCode);
                
                foreach (var parentAccount in parentAccounts)
                {
                    var treeItem = new AccountTreeViewModel
                    {
                        Id = parentAccount.Id,
                        AccountCode = parentAccount.AccountCode,
                        AccountName = parentAccount.AccountName,
                        AccountType = parentAccount.AccountType,
                        Balance = _accountingService.GetAccountBalance(parentAccount.Id),
                        IsParent = parentAccount.IsParent,
                        Children = new ObservableCollection<AccountTreeViewModel>()
                    };
                    
                    // تحميل الحسابات الفرعية
                    LoadChildAccounts(treeItem, accounts);
                    
                    _accountTree.Add(treeItem);
                }
                
                if (_accountTree.Count == 0)
                {
                    MessageBox.Show("لا توجد حسابات. سيتم إنشاء الحسابات الافتراضية.", "معلومات", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    _accountingService.CreateDefaultAccounts();
                    LoadAccountTree();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل شجرة الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// تحميل الحسابات الفرعية
        /// </summary>
        private void LoadChildAccounts(AccountTreeViewModel parent, System.Collections.Generic.List<Account> allAccounts)
        {
            var childAccounts = allAccounts.Where(a => a.ParentAccountId == parent.Id).OrderBy(a => a.AccountCode);
            
            foreach (var childAccount in childAccounts)
            {
                var childItem = new AccountTreeViewModel
                {
                    Id = childAccount.Id,
                    AccountCode = childAccount.AccountCode,
                    AccountName = childAccount.AccountName,
                    AccountType = childAccount.AccountType,
                    Balance = _accountingService.GetAccountBalance(childAccount.Id),
                    IsParent = childAccount.IsParent,
                    Children = new ObservableCollection<AccountTreeViewModel>()
                };
                
                // تحميل الحسابات الفرعية للحساب الفرعي (إذا وجدت)
                LoadChildAccounts(childItem, allAccounts);
                
                parent.Children.Add(childItem);
            }
        }
        
        /// <summary>
        /// معالج تغيير الحساب المحدد
        /// </summary>
        private void TvAccounts_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
        {
            if (e.NewValue is AccountTreeViewModel selectedAccount)
            {
                txtSelectedAccountCode.Text = selectedAccount.AccountCode;
                txtSelectedAccountName.Text = selectedAccount.AccountName;
                txtSelectedAccountType.Text = selectedAccount.AccountTypeDisplay;
                txtSelectedAccountBalance.Text = selectedAccount.BalanceDisplay;
            }
            else
            {
                txtSelectedAccountCode.Text = "-";
                txtSelectedAccountName.Text = "-";
                txtSelectedAccountType.Text = "-";
                txtSelectedAccountBalance.Text = "-";
            }
        }
        
        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        private void BtnAddAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedAccount = tvAccounts.SelectedItem as AccountTreeViewModel;
                var addAccountWindow = new AddEditAccountWindow(null, selectedAccount?.Id);
                
                if (addAccountWindow.ShowDialog() == true)
                {
                    LoadAccountTree();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// تعديل حساب
        /// </summary>
        private void BtnEditAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedAccount = tvAccounts.SelectedItem as AccountTreeViewModel;
                if (selectedAccount == null)
                {
                    MessageBox.Show("يرجى اختيار حساب للتعديل", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                var account = _accountingService.GetAccountById(selectedAccount.Id);
                var editAccountWindow = new AddEditAccountWindow(account);
                
                if (editAccountWindow.ShowDialog() == true)
                {
                    LoadAccountTree();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// حذف حساب
        /// </summary>
        private void BtnDeleteAccount_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var selectedAccount = tvAccounts.SelectedItem as AccountTreeViewModel;
                if (selectedAccount == null)
                {
                    MessageBox.Show("يرجى اختيار حساب للحذف", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }
                
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الحساب '{selectedAccount.AccountName}'؟\n\nتحذير: لا يمكن التراجع عن هذا الإجراء.",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    if (_accountingService.DeleteAccount(selectedAccount.Id))
                    {
                        MessageBox.Show("تم حذف الحساب بنجاح", "نجح", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadAccountTree();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الحساب. قد يكون له حركات أو حسابات فرعية.", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// تحديث الشجرة
        /// </summary>
        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadAccountTree();
        }
    }
    
    /// <summary>
    /// نموذج عرض شجرة الحسابات
    /// </summary>
    public class AccountTreeViewModel
    {
        public int Id { get; set; }
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public AccountType AccountType { get; set; }
        public decimal Balance { get; set; }
        public bool IsParent { get; set; }
        public ObservableCollection<AccountTreeViewModel> Children { get; set; } = new();
        
        public string AccountTypeDisplay
        {
            get
            {
                return AccountType switch
                {
                    AccountType.Asset => "أصل",
                    AccountType.Liability => "التزام",
                    AccountType.Equity => "حقوق ملكية",
                    AccountType.Revenue => "إيراد",
                    AccountType.Expense => "مصروف",
                    _ => "غير محدد"
                };
            }
        }
        
        public string BalanceDisplay
        {
            get
            {
                if (Balance == 0) return "0.00";
                return Balance > 0 ? $"{Balance:N2} مدين" : $"{Math.Abs(Balance):N2} دائن";
            }
        }
    }
}
