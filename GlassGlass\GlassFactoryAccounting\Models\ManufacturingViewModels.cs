using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نماذج العرض لموديول التصنيع
    /// </summary>

    /// <summary>
    /// نموذج عرض لوح الزجاج
    /// </summary>
    public class GlassPanelViewModel : INotifyPropertyChanged
    {
        private int _rowNumber;
        private string _glassType = string.Empty;
        private string _thickness = string.Empty;
        private decimal _length;
        private decimal _width;
        private decimal _squareMeters;
        private int _quantity = 1;
        private decimal _totalSquareMeters;
        private decimal _price;
        private decimal _totalValue;
        private string _notes = string.Empty;

        public int RowNumber
        {
            get => _rowNumber;
            set { _rowNumber = value; OnPropertyChanged(); }
        }

        public string GlassType
        {
            get => _glassType;
            set { _glassType = value; OnPropertyChanged(); }
        }

        public string Thickness
        {
            get => _thickness;
            set { _thickness = value; OnPropertyChanged(); }
        }

        public decimal Length
        {
            get => _length;
            set 
            { 
                _length = value; 
                OnPropertyChanged(); 
                CalculateValues();
            }
        }

        public decimal Width
        {
            get => _width;
            set 
            { 
                _width = value; 
                OnPropertyChanged(); 
                CalculateValues();
            }
        }

        public decimal SquareMeters
        {
            get => _squareMeters;
            set { _squareMeters = value; OnPropertyChanged(); }
        }

        public int Quantity
        {
            get => _quantity;
            set 
            { 
                _quantity = value; 
                OnPropertyChanged(); 
                CalculateValues();
            }
        }

        public decimal TotalSquareMeters
        {
            get => _totalSquareMeters;
            set { _totalSquareMeters = value; OnPropertyChanged(); }
        }

        public decimal Price
        {
            get => _price;
            set 
            { 
                _price = value; 
                OnPropertyChanged(); 
                CalculateValues();
            }
        }

        public decimal TotalValue
        {
            get => _totalValue;
            set { _totalValue = value; OnPropertyChanged(); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(); }
        }

        private void CalculateValues()
        {
            // حساب المساحة بالمتر المربع
            SquareMeters = (Length * Width) / 1000000;
            
            // حساب إجمالي المساحة
            TotalSquareMeters = SquareMeters * Quantity;
            
            // حساب إجمالي القيمة
            TotalValue = Price * TotalSquareMeters;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض المقاس المطلوب
    /// </summary>
    public class RequiredSizeViewModel : INotifyPropertyChanged
    {
        private int _rowNumber;
        private string _refCode = string.Empty;
        private string _glassType = string.Empty;
        private string _thickness = string.Empty;
        private decimal _length;
        private decimal _width;
        private decimal _squareMeters;
        private int _quantity = 1;
        private decimal _totalSquareMeters;

        public int RowNumber
        {
            get => _rowNumber;
            set { _rowNumber = value; OnPropertyChanged(); }
        }

        public string RefCode
        {
            get => _refCode;
            set { _refCode = value; OnPropertyChanged(); }
        }

        public string GlassType
        {
            get => _glassType;
            set { _glassType = value; OnPropertyChanged(); }
        }

        public string Thickness
        {
            get => _thickness;
            set { _thickness = value; OnPropertyChanged(); }
        }

        public decimal Length
        {
            get => _length;
            set 
            { 
                _length = value; 
                OnPropertyChanged(); 
                CalculateValues();
            }
        }

        public decimal Width
        {
            get => _width;
            set 
            { 
                _width = value; 
                OnPropertyChanged(); 
                CalculateValues();
            }
        }

        public decimal SquareMeters
        {
            get => _squareMeters;
            set { _squareMeters = value; OnPropertyChanged(); }
        }

        public int Quantity
        {
            get => _quantity;
            set 
            { 
                _quantity = value; 
                OnPropertyChanged(); 
                CalculateValues();
            }
        }

        public decimal TotalSquareMeters
        {
            get => _totalSquareMeters;
            set { _totalSquareMeters = value; OnPropertyChanged(); }
        }

        private void CalculateValues()
        {
            // حساب المساحة بالمتر المربع
            SquareMeters = (Length * Width) / 1000000;
            
            // حساب إجمالي المساحة
            TotalSquareMeters = SquareMeters * Quantity;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض تكلفة الخدمة
    /// </summary>
    public class ServiceCostViewModel : INotifyPropertyChanged
    {
        private int _rowNumber;
        private string _serviceName = string.Empty;
        private string _description = string.Empty;
        private decimal _quantity = 1;
        private decimal _price;
        private decimal _value;

        public int RowNumber
        {
            get => _rowNumber;
            set { _rowNumber = value; OnPropertyChanged(); }
        }

        public string ServiceName
        {
            get => _serviceName;
            set { _serviceName = value; OnPropertyChanged(); }
        }

        public string Description
        {
            get => _description;
            set { _description = value; OnPropertyChanged(); }
        }

        public decimal Quantity
        {
            get => _quantity;
            set 
            { 
                _quantity = value; 
                OnPropertyChanged(); 
                CalculateValue();
            }
        }

        public decimal Price
        {
            get => _price;
            set 
            { 
                _price = value; 
                OnPropertyChanged(); 
                CalculateValue();
            }
        }

        public decimal Value
        {
            get => _value;
            set { _value = value; OnPropertyChanged(); }
        }

        private void CalculateValue()
        {
            Value = Quantity * Price;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض التكلفة الإضافية
    /// </summary>
    public class AdditionalCostViewModel : INotifyPropertyChanged
    {
        private int _rowNumber;
        private string _description = string.Empty;
        private decimal _value;
        private string _notes = string.Empty;

        public int RowNumber
        {
            get => _rowNumber;
            set { _rowNumber = value; OnPropertyChanged(); }
        }

        public string Description
        {
            get => _description;
            set { _description = value; OnPropertyChanged(); }
        }

        public decimal Value
        {
            get => _value;
            set { _value = value; OnPropertyChanged(); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(); }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض خدمة الفيلم
    /// </summary>
    public class FilmServiceViewModel : INotifyPropertyChanged
    {
        private int _rowNumber;
        private string _filmType = string.Empty;
        private decimal _length;
        private decimal _width;
        private decimal _squareMeters;
        private int _count = 1;
        private decimal _totalSquareMeters;
        private string _reflectiveDirection = "من الداخل";

        public int RowNumber
        {
            get => _rowNumber;
            set { _rowNumber = value; OnPropertyChanged(); }
        }

        public string FilmType
        {
            get => _filmType;
            set { _filmType = value; OnPropertyChanged(); }
        }

        public decimal Length
        {
            get => _length;
            set
            {
                _length = value;
                OnPropertyChanged();
                CalculateValues();
            }
        }

        public decimal Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged();
                CalculateValues();
            }
        }

        public decimal SquareMeters
        {
            get => _squareMeters;
            set { _squareMeters = value; OnPropertyChanged(); }
        }

        public int Count
        {
            get => _count;
            set
            {
                _count = value;
                OnPropertyChanged();
                CalculateValues();
            }
        }

        public decimal TotalSquareMeters
        {
            get => _totalSquareMeters;
            set { _totalSquareMeters = value; OnPropertyChanged(); }
        }

        public string ReflectiveDirection
        {
            get => _reflectiveDirection;
            set { _reflectiveDirection = value; OnPropertyChanged(); }
        }

        private void CalculateValues()
        {
            // حساب المساحة بالمتر المربع
            SquareMeters = (Length * Width) / 1000000;

            // حساب إجمالي المساحة
            TotalSquareMeters = SquareMeters * Count;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض خدمة الدبل جلاس
    /// </summary>
    public class DoubleGlassServiceViewModel : INotifyPropertyChanged
    {
        private int _rowNumber;
        private string _spacerType = string.Empty;
        private decimal _spacerLength;
        private int _count = 1;
        private decimal _totalLinearMeters;
        private string _reflectiveDirection = "من الداخل";
        private string _notes = string.Empty;

        public int RowNumber
        {
            get => _rowNumber;
            set { _rowNumber = value; OnPropertyChanged(); }
        }

        public string SpacerType
        {
            get => _spacerType;
            set { _spacerType = value; OnPropertyChanged(); }
        }

        public decimal SpacerLength
        {
            get => _spacerLength;
            set
            {
                _spacerLength = value;
                OnPropertyChanged();
                CalculateValues();
            }
        }

        public int Count
        {
            get => _count;
            set
            {
                _count = value;
                OnPropertyChanged();
                CalculateValues();
            }
        }

        public decimal TotalLinearMeters
        {
            get => _totalLinearMeters;
            set { _totalLinearMeters = value; OnPropertyChanged(); }
        }

        public string ReflectiveDirection
        {
            get => _reflectiveDirection;
            set { _reflectiveDirection = value; OnPropertyChanged(); }
        }

        public string Notes
        {
            get => _notes;
            set { _notes = value; OnPropertyChanged(); }
        }

        private void CalculateValues()
        {
            // حساب إجمالي المتر الطولي = العدد × طول السبيسر (بالمتر)
            TotalLinearMeters = (Count * SpacerLength) / 1000;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
