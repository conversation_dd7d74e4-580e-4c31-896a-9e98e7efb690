<Window x:Class="GlassFactoryAccounting.Views.WarehouseEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل المخزن" Height="300" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#757575"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="تعديل بيانات المخزن" FontSize="18" FontWeight="Bold"
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- كود المخزن -->
        <StackPanel Grid.Row="1" Margin="0,0,0,15">
            <TextBlock Text="كود المخزن:" FontWeight="Bold" Margin="0,0,0,5" HorizontalAlignment="Center"/>
            <TextBox x:Name="TxtCode" Height="35" VerticalContentAlignment="Center"
                     FontSize="14" Padding="10" IsReadOnly="True" Background="#F5F5F5"
                     HorizontalContentAlignment="Center"/>
        </StackPanel>

        <!-- اسم المخزن -->
        <StackPanel Grid.Row="2" Margin="0,0,0,15">
            <TextBlock Text="اسم المخزن:" FontWeight="Bold" Margin="0,0,0,5" HorizontalAlignment="Center"/>
            <TextBox x:Name="TxtName" Height="35" VerticalContentAlignment="Center"
                     FontSize="14" Padding="10" HorizontalContentAlignment="Center"/>
        </StackPanel>

        <!-- موقع المخزن -->
        <StackPanel Grid.Row="3" Margin="0,0,0,15">
            <TextBlock Text="موقع المخزن:" FontWeight="Bold" Margin="0,0,0,5" HorizontalAlignment="Center"/>
            <TextBox x:Name="TxtLocation" Height="35" VerticalContentAlignment="Center"
                     FontSize="14" Padding="10" HorizontalContentAlignment="Center"/>
        </StackPanel>

        <!-- الوصف -->
        <StackPanel Grid.Row="4" Margin="0,0,0,15">
            <TextBlock Text="وصف المخزن:" FontWeight="Bold" Margin="0,0,0,5" HorizontalAlignment="Center"/>
            <TextBox x:Name="TxtDescription" Height="35" VerticalContentAlignment="Center"
                     FontSize="14" Padding="10" HorizontalContentAlignment="Center"/>
        </StackPanel>

        <!-- الأزرار -->
        <StackPanel Grid.Row="7" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button x:Name="BtnSave" Content="💾 حفظ" 
                    Style="{StaticResource SuccessButtonStyle}"
                    Margin="0,0,10,0" Click="BtnSave_Click"/>
            <Button x:Name="BtnCancel" Content="❌ إلغاء" 
                    Style="{StaticResource SecondaryButtonStyle}"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
