using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// صفحة عرض أوامر التصنيع
    /// </summary>
    public partial class ManufacturingOrdersListView : UserControl
    {
        private readonly ManufacturingService _manufacturingService;
        private ObservableCollection<NewManufacturingOrder> _manufacturingOrders;
        private bool _areNavigationToolsVisible = true;

        public ManufacturingOrdersListView()
        {
            InitializeComponent();
            _manufacturingService = new ManufacturingService();
            _manufacturingOrders = new ObservableCollection<NewManufacturingOrder>();
            
            dgManufacturingOrders.ItemsSource = _manufacturingOrders;
            
            LoadData();
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                var orders = _manufacturingService.GetAllManufacturingOrders();
                
                _manufacturingOrders.Clear();
                foreach (var order in orders)
                {
                    _manufacturingOrders.Add(order);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أوامر التصنيع: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// الرجوع للقائمة الرئيسية
        /// </summary>
        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // البحث عن النافذة الرئيسية
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    // العودة لصفحة التصنيع الرئيسية
                    var manufacturingView = new ManufacturingView();
                    mainWindow.ShowView(manufacturingView, "وحدة التصنيع");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة للقائمة الرئيسية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// البحث
        /// </summary>
        private void BtnSearch_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var allOrders = _manufacturingService.GetAllManufacturingOrders();
                var filteredOrders = allOrders.AsEnumerable();

                // تصفية حسب رقم الأمر
                if (!string.IsNullOrWhiteSpace(txtSearchOrderNumber.Text))
                {
                    filteredOrders = filteredOrders.Where(o => 
                        o.OrderNumber.Contains(txtSearchOrderNumber.Text, StringComparison.OrdinalIgnoreCase));
                }

                // تصفية حسب اسم العميل
                if (!string.IsNullOrWhiteSpace(txtSearchCustomer.Text))
                {
                    filteredOrders = filteredOrders.Where(o => 
                        o.CustomerName.Contains(txtSearchCustomer.Text, StringComparison.OrdinalIgnoreCase));
                }

                // تصفية حسب الحالة
                if (cmbSearchStatus.SelectedIndex > 0)
                {
                    var selectedStatus = ((ComboBoxItem)cmbSearchStatus.SelectedItem).Content.ToString();
                    filteredOrders = filteredOrders.Where(o => o.OrderStatus == selectedStatus);
                }

                // تصفية حسب التاريخ
                if (dpFromDate.SelectedDate.HasValue)
                {
                    filteredOrders = filteredOrders.Where(o => o.OrderDate >= dpFromDate.SelectedDate.Value);
                }

                if (dpToDate.SelectedDate.HasValue)
                {
                    filteredOrders = filteredOrders.Where(o => o.OrderDate <= dpToDate.SelectedDate.Value);
                }

                // تحديث الجدول
                _manufacturingOrders.Clear();
                foreach (var order in filteredOrders)
                {
                    _manufacturingOrders.Add(order);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مسح البحث
        /// </summary>
        private void BtnClearSearch_Click(object sender, RoutedEventArgs e)
        {
            txtSearchOrderNumber.Text = "";
            txtSearchCustomer.Text = "";
            cmbSearchStatus.SelectedIndex = 0;
            dpFromDate.SelectedDate = null;
            dpToDate.SelectedDate = null;
            
            LoadData();
        }

        /// <summary>
        /// تحديث البيانات
        /// </summary>
        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadData();
        }

        /// <summary>
        /// عرض تفاصيل الأمر
        /// </summary>
        private void BtnViewOrder_Click(object sender, RoutedEventArgs e)
        {
            var selectedOrder = dgManufacturingOrders.SelectedItem as NewManufacturingOrder;
            if (selectedOrder == null)
            {
                MessageBox.Show("يرجى اختيار أمر تصنيع أولاً", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // فتح صفحة عرض تفاصيل الأمر
                var detailsView = new ManufacturingOrderDetailsView(selectedOrder);
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.ShowView(detailsView, $"تفاصيل أمر التصنيع - {selectedOrder.OrderNumber}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التفاصيل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل الأمر
        /// </summary>
        private void BtnEditOrder_Click(object sender, RoutedEventArgs e)
        {
            var selectedOrder = dgManufacturingOrders.SelectedItem as NewManufacturingOrder;
            if (selectedOrder == null)
            {
                MessageBox.Show("يرجى اختيار أمر تصنيع أولاً", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                // فتح صفحة تعديل الأمر
                var editView = new ManufacturingOrderView(selectedOrder);
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.ShowView(editView, $"تعديل أمر التصنيع - {selectedOrder.OrderNumber}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة التعديل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف الأمر
        /// </summary>
        private void BtnDeleteOrder_Click(object sender, RoutedEventArgs e)
        {
            var selectedOrder = dgManufacturingOrders.SelectedItem as NewManufacturingOrder;
            if (selectedOrder == null)
            {
                MessageBox.Show("يرجى اختيار أمر تصنيع أولاً", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف أمر التصنيع رقم {selectedOrder.OrderNumber}؟\n\nهذا الإجراء لا يمكن التراجع عنه!", 
                "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    bool success = _manufacturingService.DeleteManufacturingOrder(selectedOrder.Id);
                    if (success)
                    {
                        MessageBox.Show("تم حذف أمر التصنيع بنجاح", "نجح الحذف", 
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        LoadData();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف أمر التصنيع", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف أمر التصنيع: {ex.Message}", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// طباعة الأمر
        /// </summary>
        private void BtnPrintOrder_Click(object sender, RoutedEventArgs e)
        {
            var selectedOrder = dgManufacturingOrders.SelectedItem as NewManufacturingOrder;
            if (selectedOrder == null)
            {
                MessageBox.Show("يرجى اختيار أمر تصنيع أولاً", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show("سيتم تطوير ميزة الطباعة قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// حفظ PDF
        /// </summary>
        private void BtnSavePDF_Click(object sender, RoutedEventArgs e)
        {
            var selectedOrder = dgManufacturingOrders.SelectedItem as NewManufacturingOrder;
            if (selectedOrder == null)
            {
                MessageBox.Show("يرجى اختيار أمر تصنيع أولاً", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            MessageBox.Show("سيتم تطوير ميزة حفظ PDF قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير ميزة التصدير إلى Excel قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// إظهار/إخفاء أدوات التنقل
        /// </summary>
        private void BtnToggleNavigationTools_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_areNavigationToolsVisible)
                {
                    // إخفاء أدوات التنقل
                    SearchAndFilterPanel.Visibility = Visibility.Collapsed;
                    btnToggleNavigationTools.Content = "✅ إظهار أدوات التنقل";
                    btnToggleNavigationTools.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(40, 167, 69)); // أخضر
                    btnToggleNavigationTools.ToolTip = "إظهار شريط البحث والفلاتر";
                    _areNavigationToolsVisible = false;
                }
                else
                {
                    // إظهار أدوات التنقل
                    SearchAndFilterPanel.Visibility = Visibility.Visible;
                    btnToggleNavigationTools.Content = "❌ إخفاء أدوات التنقل";
                    btnToggleNavigationTools.Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(108, 117, 125)); // رمادي
                    btnToggleNavigationTools.ToolTip = "إخفاء شريط البحث والفلاتر لتوسيع مساحة العمل";
                    _areNavigationToolsVisible = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إظهار/إخفاء أدوات التنقل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
