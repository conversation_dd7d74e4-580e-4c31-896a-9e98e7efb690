using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using Microsoft.Win32;
using System.IO;
using System.Linq;
using System.Diagnostics;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة تعديل فاتورة مبيعات
/// </summary>
public partial class EditSaleInvoiceView : UserControl, INotifyPropertyChanged
{
    private readonly MainWindow _mainWindow;
    private readonly Sale _originalSale;
    private ObservableCollection<SaleItem> _invoiceItems;
    private ObservableCollection<Service> _services;
    private ObservableCollection<decimal> _glassThicknesses;
    private ObservableCollection<Customer> _customers;
    private decimal _subTotal;
    private decimal _discountAmount;
    private decimal _finalTotal;

    public ObservableCollection<SaleItem> InvoiceItems
    {
        get => _invoiceItems;
        set
        {
            _invoiceItems = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<Service> Services
    {
        get => _services;
        set
        {
            _services = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<decimal> GlassThicknesses
    {
        get => _glassThicknesses;
        set
        {
            _glassThicknesses = value;
            OnPropertyChanged();
        }
    }

    public decimal SubTotal
    {
        get => _subTotal;
        set
        {
            _subTotal = value;
            OnPropertyChanged();
            UpdateTotals();
        }
    }

    public decimal DiscountAmount
    {
        get => _discountAmount;
        set
        {
            _discountAmount = value;
            OnPropertyChanged();
            UpdateTotals();
        }
    }

    public decimal FinalTotal
    {
        get => _finalTotal;
        set
        {
            _finalTotal = value;
            OnPropertyChanged();
        }
    }

    public EditSaleInvoiceView(MainWindow mainWindow, Sale saleToEdit)
    {
        InitializeComponent();
        DataContext = this;
        _mainWindow = mainWindow;
        _originalSale = saleToEdit;
        
        InitializeData();
        LoadSaleData();
    }

    private void InitializeData()
    {
        // تهيئة المجموعات
        InvoiceItems = new ObservableCollection<SaleItem>();
        Services = new ObservableCollection<Service>();
        GlassThicknesses = new ObservableCollection<decimal>();
        _customers = new ObservableCollection<Customer>();

        // تعبئة الخدمات الافتراضية
        Services.Add(new Service { Name = "تقطيع", Description = "تقطيع الزجاج حسب المقاسات" });
        Services.Add(new Service { Name = "تركيب", Description = "تركيب الزجاج" });
        Services.Add(new Service { Name = "صقل", Description = "صقل حواف الزجاج" });
        Services.Add(new Service { Name = "تثقيب", Description = "عمل ثقوب في الزجاج" });

        // تعبئة سماكات الزجاج من 1 إلى 100 ملم
        for (decimal thickness = 1; thickness <= 100; thickness += 0.5m)
        {
            GlassThicknesses.Add(thickness);
        }

        // ربط الجدول بالبيانات
        InvoiceDataGrid.ItemsSource = InvoiceItems;
        CmbCustomerName.ItemsSource = _customers;

        // مراقبة تغييرات العناصر
        InvoiceItems.CollectionChanged += (s, e) => CalculateSubTotal();
        
        // تحميل العملاء
        LoadCustomers();
    }

    private void LoadSaleData()
    {
        // تحميل بيانات الفاتورة الأصلية
        TxtTitle.Text = $"✏️ تعديل الفاتورة - {_originalSale.InvoiceNumber}";
        TxtInvoiceNumber.Text = _originalSale.InvoiceNumber;
        DateInvoice.SelectedDate = _originalSale.SaleDate;
        
        // تحديد العميل
        var customer = _customers.FirstOrDefault(c => c.Name == _originalSale.Customer?.Name);
        if (customer != null)
        {
            CmbCustomerName.SelectedItem = customer;
        }
        else
        {
            CmbCustomerName.Text = _originalSale.Customer?.Name ?? "";
        }

        // تحميل عناصر الفاتورة
        InvoiceItems.Clear();
        foreach (var item in _originalSale.SaleItems)
        {
            var newItem = new SaleItem
            {
                Id = item.Id,
                Service = item.Service,
                GlassThickness = item.GlassThickness,
                Details = item.Details,
                Length = item.Length,
                Width = item.Width,
                Area = item.Area,
                Count = item.Count,
                TotalArea = item.TotalArea,
                UnitPrice = item.UnitPrice,
                TotalPrice = item.TotalPrice,
                IsManualRow = item.IsManualRow
            };
            
            // ربط الأحداث
            newItem.PropertyChanged += SaleItem_PropertyChanged;
            InvoiceItems.Add(newItem);
        }

        // تحميل الخصم والملاحظات
        if (_originalSale.Discount > 0)
        {
            ChkEnableDiscount.IsChecked = true;
            TxtDiscountAmount.Text = _originalSale.Discount.ToString();
            DiscountAmount = _originalSale.Discount;
        }

        if (!string.IsNullOrWhiteSpace(_originalSale.Notes))
        {
            ChkEnableNotes.IsChecked = true;
            TxtNotes.Text = _originalSale.Notes;
        }

        CalculateSubTotal();
    }

    private void LoadCustomers()
    {
        try
        {
            Task.Run(async () =>
            {
                try
                {
                    var salesService = new SalesService();
                    var customers = await salesService.GetAllCustomersAsync();

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        _customers.Clear();
                        foreach (var customer in customers)
                        {
                            _customers.Add(customer);
                        }
                    });
                }
                catch (Exception)
                {
                    // يمكن إضافة تسجيل الأخطاء هنا
                }
            });
        }
        catch (Exception)
        {
            // يمكن إضافة تسجيل الأخطاء هنا
        }
    }

    private void BtnBack_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد العودة بدون حفظ التعديلات؟", "تأكيد العودة", 
            MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            _mainWindow.GoBack();
        }
    }

    private void BtnAddNormalRow_Click(object sender, RoutedEventArgs e)
    {
        var newItem = new SaleItem
        {
            Id = GetNextRowNumber(),
            Service = Services.FirstOrDefault()?.Name ?? "",
            GlassThickness = 6,
            Details = "",
            Length = 0,
            Width = 0,
            Count = 1,
            UnitPrice = 0,
            IsManualRow = false
        };

        newItem.PropertyChanged += SaleItem_PropertyChanged;
        InvoiceItems.Add(newItem);
        RefreshRowNumbers();
    }

    private void BtnAddManualRow_Click(object sender, RoutedEventArgs e)
    {
        var newItem = new SaleItem
        {
            Id = GetNextRowNumber(),
            Service = Services.FirstOrDefault()?.Name ?? "",
            GlassThickness = 6,
            Details = "",
            Length = 0,
            Width = 0,
            Area = 0,
            Count = 0,
            TotalArea = 0,
            UnitPrice = 0,
            IsManualRow = true
        };

        newItem.PropertyChanged += SaleItem_PropertyChanged;
        InvoiceItems.Add(newItem);
        RefreshRowNumbers();
    }

    private void BtnDeleteRow_Click(object sender, RoutedEventArgs e)
    {
        if (InvoiceDataGrid.SelectedItem is SaleItem selectedItem)
        {
            var result = MessageBox.Show("هل تريد حذف هذا الصف؟", "تأكيد الحذف", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                InvoiceItems.Remove(selectedItem);
                RefreshRowNumbers();
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار صف للحذف", "تنبيه", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private int GetNextRowNumber()
    {
        return InvoiceItems.Count + 1;
    }

    private void RefreshRowNumbers()
    {
        for (int i = 0; i < InvoiceItems.Count; i++)
        {
            InvoiceItems[i].Id = i + 1;
        }
    }

    private void SaleItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (sender is SaleItem item)
        {
            if (!item.IsManualRow)
            {
                if (e.PropertyName == nameof(SaleItem.Length) || e.PropertyName == nameof(SaleItem.Width))
                {
                    item.CalculateArea();
                }
                else if (e.PropertyName == nameof(SaleItem.Count) || e.PropertyName == nameof(SaleItem.Area))
                {
                    item.CalculateTotalArea();
                }
                
                if (e.PropertyName == nameof(SaleItem.UnitPrice) || e.PropertyName == nameof(SaleItem.TotalArea))
                {
                    item.CalculateTotalPrice();
                }
            }
            else
            {
                if (e.PropertyName == nameof(SaleItem.UnitPrice) || e.PropertyName == nameof(SaleItem.TotalArea))
                {
                    item.CalculateTotalPrice();
                }
            }
        }
        
        CalculateSubTotal();
    }

    private void CalculateSubTotal()
    {
        SubTotal = InvoiceItems.Sum(item => item.TotalPrice);
        TxtSubTotal.Text = $"{SubTotal:F2} ج.م";
    }

    private void ChkEnableDiscount_Checked(object sender, RoutedEventArgs e)
    {
        PnlDiscount.Visibility = Visibility.Visible;
        PnlDiscountDisplay.Visibility = Visibility.Visible;
    }

    private void ChkEnableDiscount_Unchecked(object sender, RoutedEventArgs e)
    {
        PnlDiscount.Visibility = Visibility.Collapsed;
        PnlDiscountDisplay.Visibility = Visibility.Collapsed;
        TxtDiscountAmount.Text = "0";
        DiscountAmount = 0;
    }

    private void ChkEnableNotes_Checked(object sender, RoutedEventArgs e)
    {
        TxtNotes.Visibility = Visibility.Visible;
    }

    private void ChkEnableNotes_Unchecked(object sender, RoutedEventArgs e)
    {
        TxtNotes.Visibility = Visibility.Collapsed;
        TxtNotes.Text = "";
    }

    private void TxtDiscountAmount_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (decimal.TryParse(TxtDiscountAmount.Text, out decimal discount))
        {
            DiscountAmount = discount;
        }
        else
        {
            DiscountAmount = 0;
        }
    }

    private void UpdateTotals()
    {
        TxtDiscountDisplay.Text = $"{DiscountAmount:F2} ج.م";
        FinalTotal = SubTotal - DiscountAmount;
        TxtFinalTotal.Text = $"{FinalTotal:F2} ج.م";
    }

    private void BtnManageServices_Click(object sender, RoutedEventArgs e)
    {
        var servicesWindow = new ServicesManagementWindow(Services);
        servicesWindow.ShowDialog();
    }

    private void BtnManageCustomers_Click(object sender, RoutedEventArgs e)
    {
        var customersWindow = new CustomersManagementWindow();
        if (customersWindow.ShowDialog() == true)
        {
            LoadCustomers();
        }
    }

    private async void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            try
            {
                // حذف الفاتورة الأصلية
                await DeleteOriginalSale();

                // إنشاء الفاتورة المحدثة
                var selectedCustomer = CmbCustomerName.SelectedItem as Customer;
                var customerName = selectedCustomer?.Name ?? CmbCustomerName.Text;

                var updatedSale = new Sale
                {
                    InvoiceNumber = TxtInvoiceNumber.Text,
                    SaleDate = DateInvoice.SelectedDate ?? DateTime.Now,
                    CustomerId = selectedCustomer?.Id ?? 1,
                    TotalAmount = SubTotal,
                    Discount = DiscountAmount,
                    Tax = 0,
                    NetAmount = FinalTotal,
                    PaymentStatus = PaymentStatus.غير_مدفوع,
                    Notes = TxtNotes.Text,
                    Customer = new Customer { Name = customerName }
                };

                foreach (var item in InvoiceItems)
                {
                    updatedSale.SaleItems.Add(item);
                }

                var salesService = new SalesService();
                bool success = await salesService.SaveSaleAsync(updatedSale);

                if (success)
                {
                    MessageBox.Show("تم حفظ التعديلات بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    _mainWindow.GoBack();
                }
                else
                {
                    MessageBox.Show("حدث خطأ أثناء حفظ التعديلات", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private async Task DeleteOriginalSale()
    {
        try
        {
            var salesService = new SalesService();
            await salesService.DeleteSaleAsync(_originalSale.InvoiceNumber);
        }
        catch (Exception)
        {
            // يمكن إضافة تسجيل الأخطاء هنا
        }
    }

    private void BtnSavePDF_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "PDF Files|*.pdf",
                FileName = $"Invoice_{TxtInvoiceNumber.Text}.pdf",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    var pdfService = new InvoicePdfService();
                    var customerName = (CmbCustomerName.SelectedItem as Customer)?.Name ?? CmbCustomerName.Text;
                    bool success = pdfService.CreateInvoicePdf(
                        customerName,
                        TxtInvoiceNumber.Text,
                        DateInvoice.SelectedDate ?? DateTime.Now,
                        InvoiceItems,
                        SubTotal,
                        DiscountAmount,
                        FinalTotal,
                        TxtNotes.Text,
                        saveDialog.FileName
                    );

                    if (success)
                    {
                        MessageBox.Show($"تم حفظ الفاتورة بنجاح في:\n{saveDialog.FileName}", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        var result = MessageBox.Show("هل تريد فتح الملف الآن؟", "فتح الملف",
                            MessageBoxButton.YesNo, MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                        }
                    }
                    else
                    {
                        MessageBox.Show("حدث خطأ أثناء إنشاء ملف PDF", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }

    private void BtnPrint_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            try
            {
                var tempPath = Path.Combine(Path.GetTempPath(), $"Invoice_{TxtInvoiceNumber.Text}_{DateTime.Now:yyyyMMddHHmmss}.pdf");

                var pdfService = new InvoicePdfService();
                var customerName = (CmbCustomerName.SelectedItem as Customer)?.Name ?? CmbCustomerName.Text;
                bool success = pdfService.CreateInvoicePdf(
                    customerName,
                    TxtInvoiceNumber.Text,
                    DateInvoice.SelectedDate ?? DateTime.Now,
                    InvoiceItems,
                    SubTotal,
                    DiscountAmount,
                    FinalTotal,
                    TxtNotes.Text,
                    tempPath
                );

                if (success)
                {
                    var printProcess = new ProcessStartInfo(tempPath)
                    {
                        UseShellExecute = true,
                        Verb = "print"
                    };

                    Process.Start(printProcess);

                    MessageBox.Show("تم إرسال الفاتورة للطباعة", "طباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("حدث خطأ أثناء تحضير الفاتورة للطباعة", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private bool ValidateInvoice()
    {
        var customerName = (CmbCustomerName.SelectedItem as Customer)?.Name ?? CmbCustomerName.Text;
        if (string.IsNullOrWhiteSpace(customerName))
        {
            MessageBox.Show("يرجى إدخال أو اختيار اسم العميل", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }

        if (InvoiceItems.Count == 0)
        {
            MessageBox.Show("يرجى إضافة عناصر للفاتورة", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }

        return true;
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
