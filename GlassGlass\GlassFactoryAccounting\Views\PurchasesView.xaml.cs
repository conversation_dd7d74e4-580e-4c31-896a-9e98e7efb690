using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة إدارة المشتريات
/// </summary>
public partial class PurchasesView : UserControl
{
    private ObservableCollection<Purchase> _purchases;
    private readonly ArchiveService _archiveService;

    public ObservableCollection<Purchase> Purchases
    {
        get => _purchases;
        set => _purchases = value;
    }

    public PurchasesView()
    {
        InitializeComponent();
        _archiveService = new ArchiveService();
        _purchases = new ObservableCollection<Purchase>();
        
        PurchasesDataGrid.ItemsSource = _purchases;
        LoadPurchases();
    }

    private async void LoadPurchases()
    {
        try
        {
            var purchases = await _archiveService.GetAllPurchasesAsync();
            
            _purchases.Clear();
            foreach (var purchase in purchases)
            {
                _purchases.Add(purchase);
            }
            
            UpdateStatistics();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل فواتير المشتريات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            TxtTotalPurchases.Text = $"إجمالي المشتريات: {_purchases.Count}";
            TxtTotalAmount.Text = $"إجمالي القيمة: {_purchases.Sum(p => p.NetAmount):F2} ج.م";
            
            var uniqueSuppliers = _purchases.Select(p => p.Supplier?.Name).Distinct().Count();
            TxtTotalSuppliers.Text = $"عدد الموردين: {uniqueSuppliers}";
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
        }
    }

    private void BtnRefresh_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LoadPurchases();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnNewPurchase_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.ShowNewPurchaseInvoice();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح فاتورة مشتريات جديدة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnManageSuppliers_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var suppliersWindow = new SuppliersManagementWindow();
            suppliersWindow.Owner = Window.GetWindow(this);

            if (suppliersWindow.ShowDialog() == true && suppliersWindow.SuppliersUpdated)
            {
                // تحديث البيانات إذا تم تعديل الموردين
                LoadPurchasesData();
                MessageBox.Show("تم تحديث قائمة الموردين بنجاح", "تحديث",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح إدارة الموردين: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnPurchaseReports_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // TODO: إنشاء تقارير المشتريات
            MessageBox.Show("سيتم إضافة تقارير المشتريات قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تقارير المشتريات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnView_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.Tag is Purchase purchase)
            {
                // TODO: إنشاء نافذة عرض فاتورة المشتريات
                MessageBox.Show($"عرض فاتورة المشتريات رقم: {purchase.InvoiceNumber}", "عرض الفاتورة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض الفاتورة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnEdit_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.Tag is Purchase purchase)
            {
                // TODO: إنشاء نافذة تعديل فاتورة المشتريات
                MessageBox.Show($"تعديل فاتورة المشتريات رقم: {purchase.InvoiceNumber}", "تعديل الفاتورة", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل الفاتورة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void BtnDelete_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (sender is Button button && button.Tag is Purchase purchase)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف فاتورة المشتريات رقم: {purchase.InvoiceNumber}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // TODO: إضافة وظيفة حذف فاتورة المشتريات
                    MessageBox.Show("سيتم إضافة وظيفة الحذف قريباً", "قيد التطوير", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void LoadPurchasesData()
    {
        try
        {
            // تحديث البيانات - يمكن إضافة المزيد من الوظائف هنا لاحقاً
            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات المشتريات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
