{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm install"}, "deploy": {"startCommand": "node server.js", "healthcheckPath": "/health", "healthcheckTimeout": 300, "numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3}, "environments": {"production": {"variables": {"NODE_ENV": "production", "PORT": "$PORT"}}}}