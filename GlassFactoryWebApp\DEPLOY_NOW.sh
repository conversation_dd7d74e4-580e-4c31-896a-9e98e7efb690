#!/bin/bash

# 🚀 Glass Factory - Quick Deploy to Oracle Cloud
# نشر سريع لنظام حسابات مصنع الزجاج

set -e

echo "🚀 بدء النشر السريع على Oracle Cloud..."
echo "=================================================="

# متغيرات
DOMAIN="glass-factory-demo.ddns.net"
PROJECT_NAME="glass-factory"

# تحديث النظام
echo "📦 تحديث النظام..."
sudo apt update

# تثبيت Docker إذا لم يكن موجود
if ! command -v docker &> /dev/null; then
    echo "🐳 تثبيت Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
fi

# تثبيت Docker Compose إذا لم يكن موجود
if ! command -v docker-compose &> /dev/null; then
    echo "🐳 تثبيت Docker Compose..."
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
fi

# إنشاء المجلدات المطلوبة
echo "📁 إنشاء المجلدات..."
mkdir -p uploads reports logs ssl database

# إنشاء ملف init.sql لقاعدة البيانات
echo "🗄️ إعداد قاعدة البيانات..."
cat > database/init.sql << 'EOF'
-- Glass Factory Database Initialization
-- إعداد قاعدة بيانات مصنع الزجاج

-- إنشاء جداول النظام
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- جدول المستخدمين (سيتم إنشاؤه بواسطة Identity)
-- جدول العملاء
-- جدول فواتير المبيعات
-- إلخ... (سيتم إنشاؤها بواسطة Entity Framework Migrations)

-- بيانات تجريبية
INSERT INTO "AspNetRoles" ("Id", "Name", "NormalizedName", "ConcurrencyStamp") 
VALUES 
    ('1', 'Admin', 'ADMIN', uuid_generate_v4()::text),
    ('2', 'User', 'USER', uuid_generate_v4()::text),
    ('3', 'Manager', 'MANAGER', uuid_generate_v4()::text)
ON CONFLICT DO NOTHING;

-- المزيد من البيانات التجريبية ستضاف تلقائياً
EOF

# بناء ونشر التطبيق
echo "🔨 بناء ونشر التطبيق..."
docker-compose -f docker-compose.production.yml down --remove-orphans
docker-compose -f docker-compose.production.yml build --no-cache
docker-compose -f docker-compose.production.yml up -d

# انتظار بدء الخدمات
echo "⏳ انتظار بدء الخدمات..."
sleep 30

# فحص حالة الخدمات
echo "🔍 فحص حالة الخدمات..."
docker-compose -f docker-compose.production.yml ps

# تثبيت وإعداد Nginx (إذا لم يكن موجود)
if ! command -v nginx &> /dev/null; then
    echo "🌐 تثبيت Nginx..."
    sudo apt install -y nginx
fi

# إعداد Nginx للتطبيق
echo "🌐 إعداد Nginx..."
sudo tee /etc/nginx/sites-available/$PROJECT_NAME > /dev/null << EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Swagger
    location /swagger {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    client_max_body_size 50M;
}
EOF

# تمكين الموقع
sudo ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl reload nginx

# إعداد جدار الحماية
echo "🔥 إعداد جدار الحماية..."
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw --force enable

# فحص نهائي
echo "🔍 فحص نهائي..."
sleep 10

# اختبار الاتصال
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend يعمل بنجاح"
else
    echo "❌ مشكلة في Frontend"
fi

if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    echo "✅ Backend API يعمل بنجاح"
else
    echo "❌ مشكلة في Backend API"
fi

echo ""
echo "🎉 تم النشر بنجاح!"
echo "=================================================="
echo "🌐 الموقع: http://$DOMAIN"
echo "🔗 Frontend: http://$DOMAIN"
echo "🔗 Backend API: http://$DOMAIN/api"
echo "🔗 Swagger: http://$DOMAIN/swagger"
echo "=================================================="
echo ""
echo "📊 حالة الخدمات:"
docker-compose -f docker-compose.production.yml ps
echo ""
echo "📋 للمراقبة:"
echo "docker-compose -f docker-compose.production.yml logs -f"
echo ""
echo "🔄 لإعادة التشغيل:"
echo "docker-compose -f docker-compose.production.yml restart"
echo ""
echo "🛑 للإيقاف:"
echo "docker-compose -f docker-compose.production.yml down"
echo ""

# إنشاء ملف معلومات النشر
cat > DEPLOYMENT_INFO.txt << EOF
Glass Factory Accounting System - Deployment Information
========================================================

Deployment Date: $(date)
Domain: $DOMAIN
Status: DEPLOYED

URLs:
- Frontend: http://$DOMAIN
- Backend API: http://$DOMAIN/api
- Swagger UI: http://$DOMAIN/swagger

Services:
- PostgreSQL Database: Running on port 5432
- Backend API: Running on port 5000
- Frontend React: Running on port 3000
- Nginx Proxy: Running on port 80

Docker Containers:
$(docker-compose -f docker-compose.production.yml ps)

To monitor:
docker-compose -f docker-compose.production.yml logs -f

To restart:
docker-compose -f docker-compose.production.yml restart

To stop:
docker-compose -f docker-compose.production.yml down
EOF

echo "📄 معلومات النشر محفوظة في: DEPLOYMENT_INFO.txt"
echo ""
echo "🎯 النظام جاهز للاستخدام على: http://$DOMAIN"
