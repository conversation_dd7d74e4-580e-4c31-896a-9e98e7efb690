#!/bin/bash

# 🚀 Glass Factory - Auto Setup on Oracle Cloud
# إعداد تلقائي كامل لنظام حسابات مصنع الزجاج على Oracle Cloud

set -e

echo "🚀 بدء الإعداد التلقائي لنظام حسابات مصنع الزجاج"
echo "=================================================="

# ألوان للنص
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# متغيرات
PROJECT_NAME="glass-factory"
DB_NAME="glass_factory_db"
DB_USER="glass_factory_user"
DB_PASSWORD="GlassFactory2025!"
DOMAIN="glass-factory-demo.ddns.net"

# دالة لطباعة رسائل ملونة
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# فحص نظام التشغيل
print_info "فحص نظام التشغيل..."
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
        print_status "نظام التشغيل: $OS $VER"
    fi
else
    print_error "هذا السكريبت يعمل على Linux فقط"
    exit 1
fi

# فحص صلاحيات المدير
if [[ $EUID -eq 0 ]]; then
    print_warning "يتم تشغيل السكريبت بصلاحيات المدير"
else
    print_info "سيتم طلب صلاحيات المدير عند الحاجة"
fi

# تحديث النظام
print_info "تحديث النظام..."
sudo apt update && sudo apt upgrade -y
print_status "تم تحديث النظام"

# تثبيت الحزم الأساسية
print_info "تثبيت الحزم الأساسية..."
sudo apt install -y curl wget git unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
print_status "تم تثبيت الحزم الأساسية"

# تثبيت Docker
print_info "تثبيت Docker..."
if ! command -v docker &> /dev/null; then
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    sudo systemctl start docker
    sudo systemctl enable docker
    print_status "تم تثبيت Docker"
else
    print_status "Docker موجود مسبقاً"
fi

# تثبيت Docker Compose
print_info "تثبيت Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.15.1/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    print_status "تم تثبيت Docker Compose"
else
    print_status "Docker Compose موجود مسبقاً"
fi

# تثبيت .NET 8 SDK
print_info "تثبيت .NET 8 SDK..."
if ! command -v dotnet &> /dev/null; then
    wget https://packages.microsoft.com/config/ubuntu/22.04/packages-microsoft-prod.deb -O packages-microsoft-prod.deb
    sudo dpkg -i packages-microsoft-prod.deb
    sudo apt update
    sudo apt install -y dotnet-sdk-8.0
    print_status "تم تثبيت .NET 8 SDK"
else
    print_status ".NET SDK موجود مسبقاً"
fi

# تثبيت Node.js
print_info "تثبيت Node.js..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
    sudo apt install -y nodejs
    print_status "تم تثبيت Node.js"
else
    print_status "Node.js موجود مسبقاً"
fi

# تثبيت PostgreSQL
print_info "تثبيت PostgreSQL..."
if ! command -v psql &> /dev/null; then
    sudo apt install -y postgresql postgresql-contrib
    sudo systemctl start postgresql
    sudo systemctl enable postgresql
    print_status "تم تثبيت PostgreSQL"
else
    print_status "PostgreSQL موجود مسبقاً"
fi

# إعداد قاعدة البيانات
print_info "إعداد قاعدة البيانات..."
sudo -u postgres psql << EOF
DROP DATABASE IF EXISTS $DB_NAME;
DROP USER IF EXISTS $DB_USER;
CREATE DATABASE $DB_NAME;
CREATE USER $DB_USER WITH ENCRYPTED PASSWORD '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON DATABASE $DB_NAME TO $DB_USER;
ALTER USER $DB_USER CREATEDB;
\q
EOF
print_status "تم إعداد قاعدة البيانات"

# تثبيت Nginx
print_info "تثبيت Nginx..."
if ! command -v nginx &> /dev/null; then
    sudo apt install -y nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    print_status "تم تثبيت Nginx"
else
    print_status "Nginx موجود مسبقاً"
fi

# إنشاء مجلدات المشروع
print_info "إنشاء مجلدات المشروع..."
sudo mkdir -p /var/www/$PROJECT_NAME
sudo mkdir -p /var/log/$PROJECT_NAME
sudo mkdir -p /var/backups/$PROJECT_NAME
sudo chown -R $USER:$USER /var/www/$PROJECT_NAME
sudo chown -R $USER:$USER /var/log/$PROJECT_NAME
sudo chown -R $USER:$USER /var/backups/$PROJECT_NAME
print_status "تم إنشاء مجلدات المشروع"

# نسخ ملفات المشروع
print_info "نسخ ملفات المشروع..."
cp -r . /var/www/$PROJECT_NAME/
cd /var/www/$PROJECT_NAME
print_status "تم نسخ ملفات المشروع"

# بناء Backend
print_info "بناء Backend..."
dotnet restore
dotnet publish -c Release -o ./publish
print_status "تم بناء Backend"

# بناء Frontend
print_info "بناء Frontend..."
cd client
npm install
REACT_APP_API_URL=http://$(curl -s ifconfig.me)/api npm run build
cd ..
print_status "تم بناء Frontend"

# إنشاء خدمة systemd
print_info "إنشاء خدمة systemd..."
sudo tee /etc/systemd/system/$PROJECT_NAME.service > /dev/null << EOF
[Unit]
Description=Glass Factory Accounting System
After=network.target

[Service]
Type=notify
ExecStart=/usr/bin/dotnet /var/www/$PROJECT_NAME/publish/GlassFactoryWebApp.dll
Restart=always
RestartSec=10
KillSignal=SIGINT
SyslogIdentifier=$PROJECT_NAME
User=$USER
Environment=ASPNETCORE_ENVIRONMENT=Production
Environment=ASPNETCORE_URLS=http://localhost:5000
Environment=ConnectionStrings__DefaultConnection=Host=localhost;Database=$DB_NAME;Username=$DB_USER;Password=$DB_PASSWORD
WorkingDirectory=/var/www/$PROJECT_NAME/publish

[Install]
WantedBy=multi-user.target
EOF

sudo systemctl daemon-reload
sudo systemctl enable $PROJECT_NAME
sudo systemctl start $PROJECT_NAME
print_status "تم إنشاء وتشغيل خدمة systemd"

# الحصول على Public IP
PUBLIC_IP=$(curl -s ifconfig.me)
print_info "Public IP: $PUBLIC_IP"

# إعداد Nginx
print_info "إعداد Nginx..."
sudo tee /etc/nginx/sites-available/$PROJECT_NAME > /dev/null << EOF
server {
    listen 80;
    server_name $PUBLIC_IP $DOMAIN;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # Frontend (React build)
    location / {
        root /var/www/$PROJECT_NAME/client/build;
        try_files \$uri \$uri/ /index.html;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Swagger UI
    location /swagger {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection keep-alive;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }
    
    # Health check
    location /health {
        proxy_pass http://localhost:5000/health;
        access_log off;
    }
    
    client_max_body_size 50M;
}
EOF

sudo ln -sf /etc/nginx/sites-available/$PROJECT_NAME /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default
sudo nginx -t && sudo systemctl reload nginx
print_status "تم إعداد Nginx"

# إعداد جدار الحماية
print_info "إعداد جدار الحماية..."
sudo ufw allow OpenSSH
sudo ufw allow 'Nginx Full'
sudo ufw --force enable
print_status "تم إعداد جدار الحماية"

# إنشاء سكريبت النسخ الاحتياطي
print_info "إعداد النسخ الاحتياطية..."
sudo tee /usr/local/bin/backup-$PROJECT_NAME.sh > /dev/null << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/glass-factory"
DATE=$(date +%Y%m%d_%H%M%S)

# Database backup
sudo -u postgres pg_dump glass_factory_db > $BACKUP_DIR/db_backup_$DATE.sql

# Application files backup
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz -C /var/www/glass-factory .

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed: $DATE"
EOF

sudo chmod +x /usr/local/bin/backup-$PROJECT_NAME.sh

# إضافة مهمة cron للنسخ الاحتياطي
(crontab -l 2>/dev/null; echo "0 2 * * * /usr/local/bin/backup-$PROJECT_NAME.sh") | crontab -
print_status "تم إعداد النسخ الاحتياطية"

# فحص نهائي
print_info "فحص نهائي للخدمات..."
sleep 10

# اختبار الخدمات
if systemctl is-active --quiet $PROJECT_NAME; then
    print_status "خدمة Backend تعمل بنجاح"
else
    print_error "مشكلة في خدمة Backend"
fi

if systemctl is-active --quiet nginx; then
    print_status "خدمة Nginx تعمل بنجاح"
else
    print_error "مشكلة في خدمة Nginx"
fi

if systemctl is-active --quiet postgresql; then
    print_status "خدمة PostgreSQL تعمل بنجاح"
else
    print_error "مشكلة في خدمة PostgreSQL"
fi

# اختبار الاتصال
if curl -f http://localhost:5000/health > /dev/null 2>&1; then
    print_status "Backend API يستجيب بنجاح"
else
    print_warning "Backend API لا يستجيب (قد يحتاج وقت إضافي)"
fi

if curl -f http://localhost > /dev/null 2>&1; then
    print_status "Frontend يعمل بنجاح"
else
    print_warning "Frontend لا يعمل (تحقق من إعدادات Nginx)"
fi

echo ""
echo "🎉 تم الإعداد بنجاح!"
echo "=================================================="
echo "🌐 الموقع: http://$PUBLIC_IP"
echo "💰 موديول المبيعات: http://$PUBLIC_IP/sales"
echo "📋 API Documentation: http://$PUBLIC_IP/swagger"
echo "🔍 Health Check: http://$PUBLIC_IP/health"
echo "=================================================="
echo ""
echo "📊 حالة الخدمات:"
sudo systemctl status $PROJECT_NAME --no-pager -l
echo ""
echo "📋 للمراقبة:"
echo "sudo journalctl -u $PROJECT_NAME -f"
echo ""
echo "🔄 لإعادة التشغيل:"
echo "sudo systemctl restart $PROJECT_NAME"
echo ""
echo "💾 النسخ الاحتياطية:"
echo "/var/backups/$PROJECT_NAME/"
echo ""

# إنشاء ملف معلومات النشر
cat > /var/www/$PROJECT_NAME/DEPLOYMENT_INFO.txt << EOF
Glass Factory Accounting System - Deployment Information
========================================================

Deployment Date: $(date)
Public IP: $PUBLIC_IP
Domain: $DOMAIN
Status: DEPLOYED

URLs:
- Frontend: http://$PUBLIC_IP
- Sales Module: http://$PUBLIC_IP/sales
- Backend API: http://$PUBLIC_IP/api
- Swagger UI: http://$PUBLIC_IP/swagger
- Health Check: http://$PUBLIC_IP/health

Services Status:
- Backend: $(systemctl is-active $PROJECT_NAME)
- Nginx: $(systemctl is-active nginx)
- PostgreSQL: $(systemctl is-active postgresql)

Database:
- Name: $DB_NAME
- User: $DB_USER
- Host: localhost
- Port: 5432

Directories:
- Application: /var/www/$PROJECT_NAME
- Logs: /var/log/$PROJECT_NAME
- Backups: /var/backups/$PROJECT_NAME

Commands:
- Restart: sudo systemctl restart $PROJECT_NAME
- Logs: sudo journalctl -u $PROJECT_NAME -f
- Backup: sudo /usr/local/bin/backup-$PROJECT_NAME.sh
EOF

print_status "معلومات النشر محفوظة في: /var/www/$PROJECT_NAME/DEPLOYMENT_INFO.txt"
echo ""
echo "🎯 النظام جاهز للاستخدام على: http://$PUBLIC_IP"
echo "📞 للدعم: <EMAIL>"
