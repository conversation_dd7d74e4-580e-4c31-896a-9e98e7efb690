---
layout: default
title: نظام حسابات مصنع الزجاج
---

# 🏭 نظام حسابات مصنع الزجاج
## Glass Factory Accounting System

### 🚀 النظام متاح الآن مباشرة!

---

## 🌐 **الروابط المباشرة:**

### **🎯 النسخة التجريبية:**
- **🏠 التطبيق الرئيسي:** [glass-factory-demo.surge.sh](https://glass-factory-demo.surge.sh)
- **💰 موديول المبيعات:** [glass-factory-demo.surge.sh/sales](https://glass-factory-demo.surge.sh/sales)
- **📋 النسخة التفاعلية:** [docs/index.html](./docs/index.html)

---

## 💰 **موديول المبيعات - الوظائف المتاحة:**

### ✅ **إدارة العملاء**
- إضافة وتعديل وحذف العملاء
- تتبع أرصدة العملاء
- إدارة حدود الائتمان
- كشوف حسابات العملاء

### ✅ **فواتير المبيعات**
- إنشاء فواتير مبيعات جديدة
- تعديل الفواتير الموجودة
- ترحيل وإلغاء ترحيل الفواتير
- طباعة الفواتير PDF

### ✅ **مدفوعات العملاء**
- تسجيل مدفوعات العملاء
- طرق دفع متعددة (نقدي، شيك، تحويل، بطاقة)
- ربط الدفعات بالفواتير
- تتبع حالة الدفعات

### ✅ **التقارير والإحصائيات**
- تقارير المبيعات اليومية والشهرية
- إحصائيات العملاء
- تقارير أعمار الديون
- تصدير التقارير PDF/Excel

---

## 🛠️ **التقنيات المستخدمة:**

### **Backend:**
- ASP.NET Core 8.0
- Entity Framework Core
- PostgreSQL
- JWT Authentication
- AutoMapper
- Swagger/OpenAPI

### **Frontend:**
- React 18
- TypeScript
- Material-UI (MUI)
- React Query
- React Router
- Arabic RTL Support

### **Deployment:**
- GitHub Pages
- Surge.sh
- Netlify
- Vercel
- Railway.app

---

## 🎯 **المميزات الرئيسية:**

### **🌍 دعم اللغة العربية:**
- تخطيط RTL كامل
- خطوط عربية احترافية
- تنسيق التواريخ والأرقام
- رسائل التحقق بالعربية

### **📱 تصميم متجاوب:**
- يعمل على جميع الأجهزة
- واجهة مستخدم حديثة
- تجربة مستخدم محسنة
- سرعة تحميل عالية

### **🔐 أمان متقدم:**
- مصادقة JWT
- تشفير البيانات
- حماية من SQL Injection
- حماية من XSS

---

## 📊 **إحصائيات المشروع:**

- **📅 تاريخ النشر:** 6 ديسمبر 2024
- **🔢 الإصدار:** 2.0.0
- **👤 المطور:** حسام محمد حسان أحمد
- **📧 الدعم:** <EMAIL>

---

## 🧪 **كيفية الاختبار:**

1. **افتح الرابط المباشر:** [glass-factory-demo.surge.sh](https://glass-factory-demo.surge.sh)
2. **انتقل لموديول المبيعات**
3. **جرب إضافة عميل جديد**
4. **جرب إنشاء فاتورة مبيعات**
5. **جرب تسجيل دفعة**
6. **راجع التقارير**

---

## 📞 **التواصل:**

للاستفسارات والدعم التقني:
- **📧 البريد الإلكتروني:** <EMAIL>
- **🌐 الموقع:** [glass-factory-demo.surge.sh](https://glass-factory-demo.surge.sh)

---

**🎉 النظام جاهز للاستخدام والمراجعة!**
