<Window x:Class="GlassFactoryAccounting.Views.ServicesManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
        mc:Ignorable="d"
        Title="إدارة الخدمات المتاحة" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <TextBlock Text="🔧 إدارة الخدمات المتاحة" FontSize="20" FontWeight="Bold" 
                     Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- إضافة خدمة جديدة -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="اسم الخدمة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtServiceName" Height="35" VerticalContentAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="10,0,10,0">
                    <TextBlock Text="الوصف:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtServiceDescription" Height="35" VerticalContentAlignment="Center"/>
                </StackPanel>
                
                <Button Grid.Column="2" x:Name="BtnAddService" Content="إضافة" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="15,8" FontWeight="Bold" VerticalAlignment="Bottom"
                        BorderThickness="0" Cursor="Hand" Click="BtnAddService_Click"/>
            </Grid>
        </Border>
        
        <!-- قائمة الخدمات -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="الخدمات المتاحة:" FontSize="16" FontWeight="Bold" 
                         Margin="0,0,0,10" Foreground="{StaticResource PrimaryBrush}"/>
                
                <DataGrid Grid.Row="1" x:Name="ServicesDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40"
                          FontSize="14">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الخدمة" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="*"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="حذف" Background="{StaticResource DangerBrush}" 
                                            Foreground="White" Padding="8,4"
                                            BorderThickness="0" Cursor="Hand"
                                            Click="BtnDeleteService_Click"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        
        <!-- أزرار الإغلاق -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ وإغلاق" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSave_Click"/>
                
                <Button x:Name="BtnCancel" Content="❌ إلغاء" 
                        Background="{StaticResource DangerBrush}" Foreground="White"
                        Padding="20,10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
