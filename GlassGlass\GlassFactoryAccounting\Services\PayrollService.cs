using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة الرواتب والأجور الجديدة
    /// </summary>
    public class PayrollService
    {
        private readonly DatabaseContext _context;

        public PayrollService()
        {
            _context = new DatabaseContext();
            InitializeTables();
        }

        private void InitializeTables()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // إنشاء جدول الموظفين المحدث
                var createEmployeesTable = @"
                    CREATE TABLE IF NOT EXISTS Employees (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL,
                        EmployeeCode TEXT UNIQUE NOT NULL,
                        Position TEXT NOT NULL,
                        Branch TEXT NOT NULL,
                        Department TEXT NOT NULL,
                        BasicSalary DECIMAL NOT NULL DEFAULT 0,
                        WorkingHours INTEGER NOT NULL DEFAULT 8,
                        HireDate DATETIME NOT NULL,
                        Phone TEXT,
                        Address TEXT,
                        Notes TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1
                    )";

                using var command = new SQLiteCommand(createEmployeesTable, connection);
                command.ExecuteNonQuery();

                // إنشاء جدول الرواتب المستحقة
                var createSalaryDueTable = @"
                    CREATE TABLE IF NOT EXISTS SalaryDue (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        EmployeeId INTEGER NOT NULL,
                        EmployeeName TEXT NOT NULL,
                        EmployeeCode TEXT NOT NULL,
                        SalaryAmount DECIMAL NOT NULL DEFAULT 0,
                        DueDate DATETIME NOT NULL,
                        Position TEXT NOT NULL,
                        Branch TEXT NOT NULL,
                        HireDate DATETIME NOT NULL,
                        IsPaid BOOLEAN DEFAULT 0,
                        Notes TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                using var salaryDueCommand = new SQLiteCommand(createSalaryDueTable, connection);
                salaryDueCommand.ExecuteNonQuery();

                // إنشاء جدول السلف
                var createAdvancesTable = @"
                    CREATE TABLE IF NOT EXISTS EmployeeAdvances (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        EmployeeId INTEGER NOT NULL,
                        EmployeeName TEXT NOT NULL,
                        EmployeeCode TEXT NOT NULL,
                        AdvanceAmount DECIMAL NOT NULL DEFAULT 0,
                        ResponsiblePerson TEXT NOT NULL,
                        PaymentType TEXT NOT NULL,
                        AdvanceDate DATETIME NOT NULL,
                        Notes TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                using var advancesCommand = new SQLiteCommand(createAdvancesTable, connection);
                advancesCommand.ExecuteNonQuery();

                // إنشاء جدول المكافآت
                var createBonusTable = @"
                    CREATE TABLE IF NOT EXISTS EmployeeBonuses (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        EmployeeId INTEGER NOT NULL,
                        EmployeeName TEXT NOT NULL,
                        EmployeeCode TEXT NOT NULL,
                        BonusAmount DECIMAL NOT NULL DEFAULT 0,
                        BonusReason TEXT NOT NULL,
                        BonusDate DATETIME NOT NULL,
                        Notes TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                using var bonusCommand = new SQLiteCommand(createBonusTable, connection);
                bonusCommand.ExecuteNonQuery();

                // إنشاء جدول الخصومات
                var createDeductionsTable = @"
                    CREATE TABLE IF NOT EXISTS EmployeeDeductions (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        EmployeeId INTEGER NOT NULL,
                        EmployeeName TEXT NOT NULL,
                        EmployeeCode TEXT NOT NULL,
                        DeductionAmount DECIMAL NOT NULL DEFAULT 0,
                        DeductionReason TEXT NOT NULL,
                        DeductionDate DATETIME NOT NULL,
                        Notes TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                using var deductionsCommand = new SQLiteCommand(createDeductionsTable, connection);
                deductionsCommand.ExecuteNonQuery();

                // إنشاء جدول الوقت الإضافي
                var createOvertimeTable = @"
                    CREATE TABLE IF NOT EXISTS EmployeeOvertime (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        EmployeeId INTEGER NOT NULL,
                        EmployeeName TEXT NOT NULL,
                        EmployeeCode TEXT NOT NULL,
                        OvertimeHours DECIMAL NOT NULL DEFAULT 0,
                        OvertimeAmount DECIMAL NOT NULL DEFAULT 0,
                        OvertimeDate DATETIME NOT NULL,
                        Notes TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                using var overtimeCommand = new SQLiteCommand(createOvertimeTable, connection);
                overtimeCommand.ExecuteNonQuery();

                // إنشاء جدول سداد الرواتب
                var createPaymentsTable = @"
                    CREATE TABLE IF NOT EXISTS SalaryPayments (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        EmployeeId INTEGER NOT NULL,
                        EmployeeName TEXT NOT NULL,
                        EmployeeCode TEXT NOT NULL,
                        SalaryPaid DECIMAL NOT NULL DEFAULT 0,
                        BonusAmount DECIMAL DEFAULT 0,
                        OvertimeAmount DECIMAL DEFAULT 0,
                        TotalPaid DECIMAL NOT NULL DEFAULT 0,
                        ResponsiblePerson TEXT NOT NULL,
                        PaymentDate DATETIME NOT NULL,
                        Notes TEXT,
                        CreatedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        ModifiedDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        FOREIGN KEY (EmployeeId) REFERENCES Employees(Id)
                    )";

                using var paymentsCommand = new SQLiteCommand(createPaymentsTable, connection);
                paymentsCommand.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing payroll tables: {ex.Message}");
            }
        }

        #region Employee Management

        public bool AddEmployee(Employee employee)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO Employees (Name, EmployeeCode, Position, Branch, Department, BasicSalary, WorkingHours, HireDate, Phone, Address, Notes)
                    VALUES (@Name, @EmployeeCode, @Position, @Branch, @Department, @BasicSalary, @WorkingHours, @HireDate, @Phone, @Address, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Name", employee.Name);
                command.Parameters.AddWithValue("@EmployeeCode", employee.EmployeeCode);
                command.Parameters.AddWithValue("@Position", employee.Position);
                command.Parameters.AddWithValue("@Branch", employee.Branch);
                command.Parameters.AddWithValue("@Department", employee.Department);
                command.Parameters.AddWithValue("@BasicSalary", employee.BasicSalary);
                command.Parameters.AddWithValue("@WorkingHours", employee.WorkingHours);
                command.Parameters.AddWithValue("@HireDate", employee.HireDate);
                command.Parameters.AddWithValue("@Phone", employee.Phone ?? string.Empty);
                command.Parameters.AddWithValue("@Address", employee.Address ?? string.Empty);
                command.Parameters.AddWithValue("@Notes", employee.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding employee: {ex.Message}");
                return false;
            }
        }

        public List<Employee> GetAllEmployees()
        {
            var employees = new List<Employee>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT Id, Name, EmployeeCode, Position, Branch, Department, BasicSalary, WorkingHours, HireDate, Phone, Address, Notes, CreatedDate, ModifiedDate, IsActive
                    FROM Employees
                    WHERE IsActive = 1
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    employees.Add(new Employee
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        EmployeeCode = reader["EmployeeCode"].ToString() ?? "",
                        Position = reader["Position"].ToString() ?? "",
                        Branch = reader["Branch"].ToString() ?? "",
                        Department = reader["Department"].ToString() ?? "",
                        BasicSalary = Convert.ToDecimal(reader["BasicSalary"]),
                        WorkingHours = Convert.ToInt32(reader["WorkingHours"]),
                        HireDate = Convert.ToDateTime(reader["HireDate"]),
                        Phone = reader["Phone"]?.ToString() ?? "",
                        Address = reader["Address"]?.ToString() ?? "",
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"])
                    });
                }

                // لا توجد بيانات افتراضية - البداية من صفحة فارغة
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employees: {ex.Message}");
            }

            return employees;
        }

        // PayrollService لا يوجد تعريف لدالة UpdateEmployee، يجب إضافتها هنا
        public bool UpdateEmployee(Employee employee)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();
                var query = @"
            UPDATE Employees
            SET Name = @Name, EmployeeCode = @EmployeeCode, Position = @Position, Branch = @Branch, Department = @Department,
                BasicSalary = @BasicSalary, WorkingHours = @WorkingHours, HireDate = @HireDate, Phone = @Phone, Address = @Address, Notes = @Notes, ModifiedDate = @ModifiedDate
            WHERE Id = @Id";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", employee.Id);
                command.Parameters.AddWithValue("@Name", employee.Name);
                command.Parameters.AddWithValue("@EmployeeCode", employee.EmployeeCode);
                command.Parameters.AddWithValue("@Position", employee.Position);
                command.Parameters.AddWithValue("@Branch", employee.Branch);
                command.Parameters.AddWithValue("@Department", employee.Department);
                command.Parameters.AddWithValue("@BasicSalary", employee.BasicSalary);
                command.Parameters.AddWithValue("@WorkingHours", employee.WorkingHours);
                command.Parameters.AddWithValue("@HireDate", employee.HireDate);
                command.Parameters.AddWithValue("@Phone", employee.Phone ?? string.Empty);
                command.Parameters.AddWithValue("@Address", employee.Address ?? string.Empty);
                command.Parameters.AddWithValue("@Notes", employee.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);
                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating employee: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Salary Due Management

        public bool AddSalaryDue(SalaryDue salaryDue)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO SalaryDue (EmployeeId, EmployeeName, EmployeeCode, SalaryAmount, DueDate, Position, Branch, HireDate, IsPaid, Notes)
                    VALUES (@EmployeeId, @EmployeeName, @EmployeeCode, @SalaryAmount, @DueDate, @Position, @Branch, @HireDate, @IsPaid, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", salaryDue.EmployeeId);
                command.Parameters.AddWithValue("@EmployeeName", salaryDue.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", salaryDue.EmployeeCode);
                command.Parameters.AddWithValue("@SalaryAmount", salaryDue.SalaryAmount);
                command.Parameters.AddWithValue("@DueDate", salaryDue.DueDate);
                command.Parameters.AddWithValue("@Position", salaryDue.Position);
                command.Parameters.AddWithValue("@Branch", salaryDue.Branch);
                command.Parameters.AddWithValue("@HireDate", salaryDue.HireDate);
                command.Parameters.AddWithValue("@IsPaid", salaryDue.IsPaid);
                command.Parameters.AddWithValue("@Notes", salaryDue.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding salary due: {ex.Message}");
                return false;
            }
        }

        public List<SalaryDue> GetAllSalaryDue()
        {
            var salaryDues = new List<SalaryDue>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT Id, EmployeeId, EmployeeName, EmployeeCode, SalaryAmount, DueDate, Position, Branch, HireDate, IsPaid, Notes, CreatedDate, ModifiedDate, IsActive
                    FROM SalaryDue
                    WHERE IsActive = 1
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    salaryDues.Add(new SalaryDue
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        EmployeeId = Convert.ToInt32(reader["EmployeeId"]),
                        EmployeeName = reader["EmployeeName"]?.ToString() ?? "",
                        EmployeeCode = reader["EmployeeCode"]?.ToString() ?? "",
                        SalaryAmount = Convert.ToDecimal(reader["SalaryAmount"]),
                        DueDate = Convert.ToDateTime(reader["DueDate"]),
                        Position = reader["Position"]?.ToString() ?? "",
                        Branch = reader["Branch"]?.ToString() ?? "",
                        HireDate = Convert.ToDateTime(reader["HireDate"]),
                        IsPaid = Convert.ToBoolean(reader["IsPaid"]),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting salary dues: {ex.Message}");
            }

            return salaryDues;
        }

        public bool UpdateSalaryDue(SalaryDue salaryDue)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE SalaryDue
                    SET EmployeeName = @EmployeeName, EmployeeCode = @EmployeeCode, SalaryAmount = @SalaryAmount,
                        DueDate = @DueDate, Position = @Position, Branch = @Branch, HireDate = @HireDate,
                        IsPaid = @IsPaid, Notes = @Notes, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", salaryDue.Id);
                command.Parameters.AddWithValue("@EmployeeName", salaryDue.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", salaryDue.EmployeeCode);
                command.Parameters.AddWithValue("@SalaryAmount", salaryDue.SalaryAmount);
                command.Parameters.AddWithValue("@DueDate", salaryDue.DueDate);
                command.Parameters.AddWithValue("@Position", salaryDue.Position);
                command.Parameters.AddWithValue("@Branch", salaryDue.Branch);
                command.Parameters.AddWithValue("@HireDate", salaryDue.HireDate);
                command.Parameters.AddWithValue("@IsPaid", salaryDue.IsPaid);
                command.Parameters.AddWithValue("@Notes", salaryDue.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating salary due: {ex.Message}");
                return false;
            }
        }

        public bool DeleteSalaryDue(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "UPDATE SalaryDue SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting salary due: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Employee Advances Management

        public bool AddEmployeeAdvance(EmployeeAdvance advance)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO EmployeeAdvances (EmployeeId, EmployeeName, EmployeeCode, AdvanceAmount, ResponsiblePerson, PaymentType, AdvanceDate, Notes)
                    VALUES (@EmployeeId, @EmployeeName, @EmployeeCode, @AdvanceAmount, @ResponsiblePerson, @PaymentType, @AdvanceDate, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", advance.EmployeeId);
                command.Parameters.AddWithValue("@EmployeeName", advance.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", advance.EmployeeCode);
                command.Parameters.AddWithValue("@AdvanceAmount", advance.AdvanceAmount);
                command.Parameters.AddWithValue("@ResponsiblePerson", advance.ResponsiblePerson);
                command.Parameters.AddWithValue("@PaymentType", advance.PaymentType);
                command.Parameters.AddWithValue("@AdvanceDate", advance.AdvanceDate);
                command.Parameters.AddWithValue("@Notes", advance.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding employee advance: {ex.Message}");
                return false;
            }
        }

        public List<EmployeeAdvance> GetAllEmployeeAdvances()
        {
            var advances = new List<EmployeeAdvance>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT Id, EmployeeId, EmployeeName, EmployeeCode, AdvanceAmount, ResponsiblePerson, PaymentType, AdvanceDate, Notes, CreatedDate, ModifiedDate, IsActive
                    FROM EmployeeAdvances
                    WHERE IsActive = 1
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    advances.Add(new EmployeeAdvance
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        EmployeeId = Convert.ToInt32(reader["EmployeeId"]),
                        EmployeeName = reader["EmployeeName"]?.ToString() ?? "",
                        EmployeeCode = reader["EmployeeCode"]?.ToString() ?? "",
                        AdvanceAmount = Convert.ToDecimal(reader["AdvanceAmount"]),
                        ResponsiblePerson = reader["ResponsiblePerson"]?.ToString() ?? "",
                        PaymentType = reader["PaymentType"]?.ToString() ?? "",
                        AdvanceDate = Convert.ToDateTime(reader["AdvanceDate"]),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee advances: {ex.Message}");
            }

            return advances;
        }

        public bool UpdateEmployeeAdvance(EmployeeAdvance advance)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE EmployeeAdvances
                    SET EmployeeName = @EmployeeName, EmployeeCode = @EmployeeCode, AdvanceAmount = @AdvanceAmount,
                        ResponsiblePerson = @ResponsiblePerson, PaymentType = @PaymentType, AdvanceDate = @AdvanceDate,
                        Notes = @Notes, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", advance.Id);
                command.Parameters.AddWithValue("@EmployeeName", advance.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", advance.EmployeeCode);
                command.Parameters.AddWithValue("@AdvanceAmount", advance.AdvanceAmount);
                command.Parameters.AddWithValue("@ResponsiblePerson", advance.ResponsiblePerson);
                command.Parameters.AddWithValue("@PaymentType", advance.PaymentType);
                command.Parameters.AddWithValue("@AdvanceDate", advance.AdvanceDate);
                command.Parameters.AddWithValue("@Notes", advance.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating employee advance: {ex.Message}");
                return false;
            }
        }

        public bool DeleteEmployeeAdvance(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "UPDATE EmployeeAdvances SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting employee advance: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Employee Bonuses Management

        public bool AddEmployeeBonus(EmployeeBonus bonus)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO EmployeeBonuses (EmployeeId, EmployeeName, EmployeeCode, BonusAmount, BonusReason, BonusDate, Notes)
                    VALUES (@EmployeeId, @EmployeeName, @EmployeeCode, @BonusAmount, @BonusReason, @BonusDate, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", bonus.EmployeeId);
                command.Parameters.AddWithValue("@EmployeeName", bonus.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", bonus.EmployeeCode);
                command.Parameters.AddWithValue("@BonusAmount", bonus.BonusAmount);
                command.Parameters.AddWithValue("@BonusReason", bonus.BonusReason);
                command.Parameters.AddWithValue("@BonusDate", bonus.BonusDate);
                command.Parameters.AddWithValue("@Notes", bonus.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding employee bonus: {ex.Message}");
                return false;
            }
        }

        public bool UpdateEmployeeBonus(EmployeeBonus bonus)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE EmployeeBonuses
                    SET EmployeeName = @EmployeeName, EmployeeCode = @EmployeeCode, BonusAmount = @BonusAmount,
                        BonusReason = @BonusReason, BonusDate = @BonusDate, Notes = @Notes, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", bonus.Id);
                command.Parameters.AddWithValue("@EmployeeName", bonus.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", bonus.EmployeeCode);
                command.Parameters.AddWithValue("@BonusAmount", bonus.BonusAmount);
                command.Parameters.AddWithValue("@BonusReason", bonus.BonusReason);
                command.Parameters.AddWithValue("@BonusDate", bonus.BonusDate);
                command.Parameters.AddWithValue("@Notes", bonus.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating employee bonus: {ex.Message}");
                return false;
            }
        }

        public bool DeleteEmployeeBonus(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "UPDATE EmployeeBonuses SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting employee bonus: {ex.Message}");
                return false;
            }
        }

        public List<EmployeeBonus> GetAllEmployeeBonuses()
        {
            var bonuses = new List<EmployeeBonus>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT Id, EmployeeId, EmployeeName, EmployeeCode, BonusAmount, BonusReason, BonusDate, Notes, CreatedDate, ModifiedDate, IsActive
                    FROM EmployeeBonuses
                    WHERE IsActive = 1
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    bonuses.Add(new EmployeeBonus
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        EmployeeId = Convert.ToInt32(reader["EmployeeId"]),
                        EmployeeName = reader["EmployeeName"]?.ToString() ?? "",
                        EmployeeCode = reader["EmployeeCode"]?.ToString() ?? "",
                        BonusAmount = Convert.ToDecimal(reader["BonusAmount"]),
                        BonusReason = reader["BonusReason"]?.ToString() ?? "",
                        BonusDate = Convert.ToDateTime(reader["BonusDate"]),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee bonuses: {ex.Message}");
            }

            return bonuses;
        }

        #endregion

        #region Employee Deductions Management

        public bool AddEmployeeDeduction(EmployeeDeduction deduction)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO EmployeeDeductions (EmployeeId, EmployeeName, EmployeeCode, DeductionAmount, DeductionReason, DeductionDate, Notes)
                    VALUES (@EmployeeId, @EmployeeName, @EmployeeCode, @DeductionAmount, @DeductionReason, @DeductionDate, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", deduction.EmployeeId);
                command.Parameters.AddWithValue("@EmployeeName", deduction.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", deduction.EmployeeCode);
                command.Parameters.AddWithValue("@DeductionAmount", deduction.DeductionAmount);
                command.Parameters.AddWithValue("@DeductionReason", deduction.DeductionReason);
                command.Parameters.AddWithValue("@DeductionDate", deduction.DeductionDate);
                command.Parameters.AddWithValue("@Notes", deduction.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding employee deduction: {ex.Message}");
                return false;
            }
        }

        public bool UpdateEmployeeDeduction(EmployeeDeduction deduction)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE EmployeeDeductions
                    SET EmployeeName = @EmployeeName, EmployeeCode = @EmployeeCode, DeductionAmount = @DeductionAmount,
                        DeductionReason = @DeductionReason, DeductionDate = @DeductionDate, Notes = @Notes, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", deduction.Id);
                command.Parameters.AddWithValue("@EmployeeName", deduction.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", deduction.EmployeeCode);
                command.Parameters.AddWithValue("@DeductionAmount", deduction.DeductionAmount);
                command.Parameters.AddWithValue("@DeductionReason", deduction.DeductionReason);
                command.Parameters.AddWithValue("@DeductionDate", deduction.DeductionDate);
                command.Parameters.AddWithValue("@Notes", deduction.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating employee deduction: {ex.Message}");
                return false;
            }
        }

        public bool DeleteEmployeeDeduction(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "UPDATE EmployeeDeductions SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting employee deduction: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Employee Overtime Management

        public bool AddEmployeeOvertime(EmployeeOvertime overtime)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO EmployeeOvertime (EmployeeId, EmployeeName, EmployeeCode, OvertimeHours, OvertimeAmount, OvertimeDate, Notes)
                    VALUES (@EmployeeId, @EmployeeName, @EmployeeCode, @OvertimeHours, @OvertimeAmount, @OvertimeDate, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", overtime.EmployeeId);
                command.Parameters.AddWithValue("@EmployeeName", overtime.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", overtime.EmployeeCode);
                command.Parameters.AddWithValue("@OvertimeHours", overtime.OvertimeHours);
                command.Parameters.AddWithValue("@OvertimeAmount", overtime.OvertimeAmount);
                command.Parameters.AddWithValue("@OvertimeDate", overtime.OvertimeDate);
                command.Parameters.AddWithValue("@Notes", overtime.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding employee overtime: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حساب قيمة الوقت الإضافي
        /// </summary>
        public decimal CalculateOvertimeAmount(Employee employee, decimal overtimeHours)
        {
            if (employee == null || overtimeHours <= 0)
                return 0;

            var hourlyRate = employee.HourlyRate;
            // المعادلة: (عدد الساعات × أجر الساعة) + (عدد الساعات × 0.5 × أجر الساعة)
            return (overtimeHours * hourlyRate) + (overtimeHours * 0.5m * hourlyRate);
        }

        public bool UpdateEmployeeOvertime(EmployeeOvertime overtime)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE EmployeeOvertime
                    SET EmployeeName = @EmployeeName, EmployeeCode = @EmployeeCode, OvertimeHours = @OvertimeHours,
                        OvertimeAmount = @OvertimeAmount, OvertimeDate = @OvertimeDate, Notes = @Notes, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", overtime.Id);
                command.Parameters.AddWithValue("@EmployeeName", overtime.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", overtime.EmployeeCode);
                command.Parameters.AddWithValue("@OvertimeHours", overtime.OvertimeHours);
                command.Parameters.AddWithValue("@OvertimeAmount", overtime.OvertimeAmount);
                command.Parameters.AddWithValue("@OvertimeDate", overtime.OvertimeDate);
                command.Parameters.AddWithValue("@Notes", overtime.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating employee overtime: {ex.Message}");
                return false;
            }
        }

        public bool DeleteEmployeeOvertime(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "UPDATE EmployeeOvertime SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting employee overtime: {ex.Message}");
                return false;
            }
        }

        public List<EmployeeOvertime> GetAllEmployeeOvertime()
        {
            var overtimes = new List<EmployeeOvertime>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT Id, EmployeeId, EmployeeName, EmployeeCode, OvertimeHours, OvertimeAmount, OvertimeDate, Notes, CreatedDate, ModifiedDate, IsActive
                    FROM EmployeeOvertime
                    WHERE IsActive = 1
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    overtimes.Add(new EmployeeOvertime
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        EmployeeId = Convert.ToInt32(reader["EmployeeId"]),
                        EmployeeName = reader["EmployeeName"]?.ToString() ?? "",
                        EmployeeCode = reader["EmployeeCode"]?.ToString() ?? "",
                        OvertimeHours = Convert.ToDecimal(reader["OvertimeHours"]),
                        OvertimeAmount = Convert.ToDecimal(reader["OvertimeAmount"]),
                        OvertimeDate = Convert.ToDateTime(reader["OvertimeDate"]),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee overtime: {ex.Message}");
            }

            return overtimes;
        }

        /// <summary>
        /// حذف جميع البيانات للاختبار
        /// </summary>
        public bool ClearAllTestData()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var queries = new[]
                {
                    "DELETE FROM EmployeeAdvances",
                    "DELETE FROM EmployeeBonuses",
                    "DELETE FROM EmployeeOvertime",
                    "DELETE FROM EmployeeDeductions",
                    "DELETE FROM SalaryPayments",
                    "DELETE FROM SalaryDue"
                };

                foreach (var query in queries)
                {
                    using var command = new SQLiteCommand(query, connection);
                    command.ExecuteNonQuery();
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing test data: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Salary Payments Management

        public bool AddSalaryPayment(SalaryPayment payment)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO SalaryPayments (EmployeeId, EmployeeName, EmployeeCode, SalaryPaid, BonusAmount, OvertimeAmount, TotalPaid, ResponsiblePerson, PaymentDate, Notes)
                    VALUES (@EmployeeId, @EmployeeName, @EmployeeCode, @SalaryPaid, @BonusAmount, @OvertimeAmount, @TotalPaid, @ResponsiblePerson, @PaymentDate, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", payment.EmployeeId);
                command.Parameters.AddWithValue("@EmployeeName", payment.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", payment.EmployeeCode);
                command.Parameters.AddWithValue("@SalaryPaid", payment.SalaryPaid);
                command.Parameters.AddWithValue("@BonusAmount", payment.BonusAmount);
                command.Parameters.AddWithValue("@OvertimeAmount", payment.OvertimeAmount);
                command.Parameters.AddWithValue("@TotalPaid", payment.TotalPaid);
                command.Parameters.AddWithValue("@ResponsiblePerson", payment.ResponsiblePerson);
                command.Parameters.AddWithValue("@PaymentDate", payment.PaymentDate);
                command.Parameters.AddWithValue("@Notes", payment.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding salary payment: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع المدفوعات
        /// </summary>
        public List<SalaryPayment> GetAllPayments()
        {
            var payments = new List<SalaryPayment>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT Id, EmployeeId, EmployeeName, EmployeeCode, SalaryPaid, BonusAmount, OvertimeAmount, TotalPaid, ResponsiblePerson, PaymentDate, Notes, CreatedDate, ModifiedDate, IsActive
                    FROM SalaryPayments
                    WHERE IsActive = 1
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    payments.Add(new SalaryPayment
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        EmployeeId = Convert.ToInt32(reader["EmployeeId"]),
                        EmployeeName = reader["EmployeeName"]?.ToString() ?? "",
                        EmployeeCode = reader["EmployeeCode"]?.ToString() ?? "",
                        SalaryPaid = Convert.ToDecimal(reader["SalaryPaid"]),
                        BonusAmount = Convert.ToDecimal(reader["BonusAmount"]),
                        OvertimeAmount = Convert.ToDecimal(reader["OvertimeAmount"]),
                        TotalPaid = Convert.ToDecimal(reader["TotalPaid"]),
                        ResponsiblePerson = reader["ResponsiblePerson"]?.ToString() ?? "",
                        PaymentDate = Convert.ToDateTime(reader["PaymentDate"]),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting salary payments: {ex.Message}");
            }

            return payments;
        }

        public bool UpdateSalaryPayment(SalaryPayment payment)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE SalaryPayments
                    SET EmployeeName = @EmployeeName, EmployeeCode = @EmployeeCode, SalaryPaid = @SalaryPaid,
                        BonusAmount = @BonusAmount, OvertimeAmount = @OvertimeAmount, TotalPaid = @TotalPaid,
                        ResponsiblePerson = @ResponsiblePerson, PaymentDate = @PaymentDate, Notes = @Notes, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", payment.Id);
                command.Parameters.AddWithValue("@EmployeeName", payment.EmployeeName);
                command.Parameters.AddWithValue("@EmployeeCode", payment.EmployeeCode);
                command.Parameters.AddWithValue("@SalaryPaid", payment.SalaryPaid);
                command.Parameters.AddWithValue("@BonusAmount", payment.BonusAmount);
                command.Parameters.AddWithValue("@OvertimeAmount", payment.OvertimeAmount);
                command.Parameters.AddWithValue("@TotalPaid", payment.TotalPaid);
                command.Parameters.AddWithValue("@ResponsiblePerson", payment.ResponsiblePerson);
                command.Parameters.AddWithValue("@PaymentDate", payment.PaymentDate);
                command.Parameters.AddWithValue("@Notes", payment.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating salary payment: {ex.Message}");
                return false;
            }
        }

        public bool DeleteSalaryPayment(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "UPDATE SalaryPayments SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", id);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting salary payment: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع الخصومات
        /// </summary>
        public List<EmployeeDeduction> GetAllDeductions()
        {
            var deductions = new List<EmployeeDeduction>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT Id, EmployeeId, EmployeeName, EmployeeCode, DeductionAmount, DeductionReason, DeductionDate, Notes, CreatedDate, ModifiedDate, IsActive
                    FROM EmployeeDeductions
                    WHERE IsActive = 1
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    deductions.Add(new EmployeeDeduction
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        EmployeeId = Convert.ToInt32(reader["EmployeeId"]),
                        EmployeeName = reader["EmployeeName"]?.ToString() ?? "",
                        EmployeeCode = reader["EmployeeCode"]?.ToString() ?? "",
                        DeductionAmount = Convert.ToDecimal(reader["DeductionAmount"]),
                        DeductionReason = reader["DeductionReason"]?.ToString() ?? "",
                        DeductionDate = Convert.ToDateTime(reader["DeductionDate"]),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedDate = Convert.ToDateTime(reader["CreatedDate"]),
                        ModifiedDate = Convert.ToDateTime(reader["ModifiedDate"]),
                        IsActive = Convert.ToBoolean(reader["IsActive"])
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee deductions: {ex.Message}");
            }

            return deductions;
        }



        /// <summary>
        /// إنشاء تقرير كامل للموظفين
        /// </summary>
        public List<EmployeeFullReport> GenerateFullReport()
        {
            try
            {
                var employees = GetAllEmployees();
                var reportData = new List<EmployeeFullReport>();

                foreach (var employee in employees)
                {
                    var report = new EmployeeFullReport
                    {
                        EmployeeId = employee.Id,
                        EmployeeCode = employee.EmployeeCode,
                        EmployeeName = employee.Name,
                        Position = employee.Position,
                        BasicSalary = employee.BasicSalary,
                        TotalSalaryDue = GetEmployeeUnpaidSalaryTotal(employee.Id), // الرواتب المستحقة الفعلية
                        TotalAdvances = GetEmployeeRemainingAdvances(employee.Id), // السلف الفعلية
                        TotalBonuses = GetEmployeeBonusTotal(employee.Id), // المكافآت الفعلية
                        TotalDeductions = GetEmployeeDeductionTotal(employee.Id), // الخصومات الفعلية
                        TotalOvertime = GetEmployeeOvertimeTotal(employee.Id), // الوقت الإضافي الفعلي
                        TotalPayments = GetEmployeePaymentTotal(employee.Id) // المدفوعات الفعلية
                    };

                    // حساب المتبقي
                    report.RemainingBalance = (report.TotalSalaryDue + report.TotalBonuses + report.TotalOvertime)
                                            - (report.TotalAdvances + report.TotalDeductions + report.TotalPayments);

                    reportData.Add(report);
                }

                return reportData;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating full report: {ex.Message}");
                return new List<EmployeeFullReport>();
            }
        }

        /// <summary>
        /// حذف موظف
        /// </summary>
        public bool DeleteEmployee(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();
                using var transaction = connection.BeginTransaction();

                try
                {
                    // حذف جميع البيانات المرتبطة بالموظف
                    var queries = new[]
                    {
                        "DELETE FROM SalaryPayments WHERE EmployeeId = @EmployeeId",
                        "DELETE FROM EmployeeOvertime WHERE EmployeeId = @EmployeeId",
                        "DELETE FROM EmployeeDeductions WHERE EmployeeId = @EmployeeId",
                        "DELETE FROM EmployeeBonuses WHERE EmployeeId = @EmployeeId",
                        "DELETE FROM EmployeeAdvances WHERE EmployeeId = @EmployeeId",
                        "DELETE FROM SalaryDue WHERE EmployeeId = @EmployeeId",
                        "DELETE FROM Employees WHERE Id = @EmployeeId"
                    };

                    foreach (var query in queries)
                    {
                        using var command = new SQLiteCommand(query, connection, transaction);
                        command.Parameters.AddWithValue("@EmployeeId", employeeId);
                        command.ExecuteNonQuery();
                    }

                    transaction.Commit();
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع المسؤولين عن السداد
        /// </summary>
        public List<ResponsiblePerson> GetAllResponsiblePersons()
        {
            var persons = new List<ResponsiblePerson>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // استخدام الخدمة المركزية بدلاً من الاستعلام المباشر
                var companyService = new CompanyManagementService();
                return companyService.GetAllResponsiblePersons();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting responsible persons: {ex.Message}");
                // في حالة الخطأ، إرجاع قائمة فارغة
                return new List<ResponsiblePerson>();
            }
        }

        /// <summary>
        /// إضافة مسؤول عن السداد
        /// </summary>
        public bool AddResponsiblePerson(ResponsiblePerson person)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO ResponsiblePersons (Name, Position, Department, Phone, Email, IsActive, Notes)
                    VALUES (@Name, @Position, @Department, @Phone, @Email, @IsActive, @Notes)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Name", person.Name);
                command.Parameters.AddWithValue("@Position", person.Position);
                command.Parameters.AddWithValue("@Department", person.Department);
                command.Parameters.AddWithValue("@Phone", person.Phone ?? string.Empty);
                command.Parameters.AddWithValue("@Email", person.Email ?? string.Empty);
                command.Parameters.AddWithValue("@IsActive", person.IsActive);
                command.Parameters.AddWithValue("@Notes", person.Notes ?? string.Empty);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding responsible person: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث مسؤول عن السداد
        /// </summary>
        public bool UpdateResponsiblePerson(ResponsiblePerson person)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE ResponsiblePersons
                    SET Name = @Name, Position = @Position, Department = @Department,
                        Phone = @Phone, Email = @Email, IsActive = @IsActive, Notes = @Notes, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", person.Id);
                command.Parameters.AddWithValue("@Name", person.Name);
                command.Parameters.AddWithValue("@Position", person.Position);
                command.Parameters.AddWithValue("@Department", person.Department);
                command.Parameters.AddWithValue("@Phone", person.Phone ?? string.Empty);
                command.Parameters.AddWithValue("@Email", person.Email ?? string.Empty);
                command.Parameters.AddWithValue("@IsActive", person.IsActive);
                command.Parameters.AddWithValue("@Notes", person.Notes ?? string.Empty);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating responsible person: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف مسؤول عن السداد
        /// </summary>
        public bool DeleteResponsiblePerson(int personId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "UPDATE ResponsiblePersons SET IsActive = 0, ModifiedDate = @ModifiedDate WHERE Id = @Id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@Id", personId);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting responsible person: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Employee Calculation Methods

        /// <summary>
        /// حساب إجمالي الوقت الإضافي المستحق للموظف
        /// </summary>
        public decimal GetEmployeeOvertimeTotal(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT COALESCE(SUM(OvertimeAmount), 0) as Total
                    FROM EmployeeOvertime
                    WHERE EmployeeId = @EmployeeId AND IsActive = 1";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", employeeId);

                var result = command.ExecuteScalar();
                return Convert.ToDecimal(result ?? 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee overtime total: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب إجمالي الخصومات المستحقة للموظف
        /// </summary>
        public decimal GetEmployeeDeductionTotal(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT COALESCE(SUM(DeductionAmount), 0) as Total
                    FROM EmployeeDeductions
                    WHERE EmployeeId = @EmployeeId AND IsActive = 1";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", employeeId);

                var result = command.ExecuteScalar();
                return Convert.ToDecimal(result ?? 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee deduction total: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب إجمالي المكافآت المستحقة للموظف
        /// </summary>
        public decimal GetEmployeeBonusTotal(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT COALESCE(SUM(BonusAmount), 0) as Total
                    FROM EmployeeBonuses
                    WHERE EmployeeId = @EmployeeId AND IsActive = 1";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", employeeId);

                var result = command.ExecuteScalar();
                return Convert.ToDecimal(result ?? 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee bonus total: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب إجمالي الرواتب غير المدفوعة للموظف
        /// </summary>
        public decimal GetEmployeeUnpaidSalaryTotal(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT COALESCE(SUM(SalaryAmount), 0) as Total
                    FROM SalaryDue
                    WHERE EmployeeId = @EmployeeId AND IsActive = 1 AND IsPaid = 0";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", employeeId);

                var result = command.ExecuteScalar();
                return Convert.ToDecimal(result ?? 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee unpaid salary total: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب إجمالي المدفوعات للموظف
        /// </summary>
        public decimal GetEmployeePaymentTotal(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT COALESCE(SUM(TotalPaid), 0) as Total
                    FROM SalaryPayments
                    WHERE EmployeeId = @EmployeeId AND IsActive = 1";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", employeeId);

                var result = command.ExecuteScalar();
                return Convert.ToDecimal(result ?? 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee payment total: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب السلف المتبقية للموظف
        /// </summary>
        public decimal GetEmployeeRemainingAdvances(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT COALESCE(SUM(AdvanceAmount), 0) as Total
                    FROM EmployeeAdvances
                    WHERE EmployeeId = @EmployeeId AND IsActive = 1";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", employeeId);

                var result = command.ExecuteScalar();
                return Convert.ToDecimal(result ?? 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee remaining advances: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// حساب المكافآت المتبقية للموظف
        /// </summary>
        public decimal GetEmployeeRemainingBonuses(int employeeId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT COALESCE(SUM(BonusAmount), 0) as Total
                    FROM EmployeeBonuses
                    WHERE EmployeeId = @EmployeeId AND IsActive = 1";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@EmployeeId", employeeId);

                var result = command.ExecuteScalar();
                return Convert.ToDecimal(result ?? 0);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting employee remaining bonuses: {ex.Message}");
                return 0;
            }
        }

        #endregion
    }
}
