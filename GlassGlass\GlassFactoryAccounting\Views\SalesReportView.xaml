<UserControl x:Class="GlassFactoryAccounting.Views.SalesReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان مع زر العودة -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="BtnBack" Content="⬅️ عودة" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,8" Margin="0,0,20,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnBack_Click"/>
                
                <TextBlock Text="💰 تقرير المبيعات" FontSize="24" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                
                <Button x:Name="BtnExportExcel" Content="📊 تصدير Excel" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="15,8" Margin="20,0,0,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnExportExcel_Click"/>
                
                <Button x:Name="BtnPrintReport" Content="🖨️ طباعة" 
                        Background="{StaticResource WarningBrush}" Foreground="White"
                        Padding="15,8" Margin="10,0,0,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnPrintReport_Click"/>
            </StackPanel>
        </Border>
        
        <!-- فلاتر البحث -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول من الفلاتر -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,10,10">
                        <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="DateFrom" Height="35" SelectedDateChanged="DateFilter_Changed"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="5,0,5,10">
                        <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="DateTo" Height="35" SelectedDateChanged="DateFilter_Changed"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Margin="5,0,10,10">
                        <TextBlock Text="البحث في العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCustomerSearch" Height="35" VerticalContentAlignment="Center"
                                 TextChanged="TxtCustomerSearch_TextChanged"
                                 Text="البحث في أسماء العملاء..." Foreground="Gray"
                                 GotFocus="TxtCustomerSearch_GotFocus" LostFocus="TxtCustomerSearch_LostFocus"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3" VerticalAlignment="Bottom" Margin="0,0,0,10">
                        <Button x:Name="BtnClearFilters" Content="🗑️ مسح الفلاتر"
                                Background="{StaticResource DangerBrush}" Foreground="White"
                                Padding="15,8" FontWeight="Bold"
                                BorderThickness="0" Cursor="Hand" Click="BtnClearFilters_Click"/>
                    </StackPanel>
                </Grid>

                <!-- الصف الثاني من الفلاتر -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="فلتر الخدمة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CmbServiceFilter" Height="35"
                                  SelectionChanged="ServiceFilter_Changed"
                                  DisplayMemberPath="Name" SelectedValuePath="Name"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Margin="5,0">
                        <TextBlock Text="فلتر نوع الزجاج:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CmbGlassTypeFilter" Height="35"
                                  SelectionChanged="GlassTypeFilter_Changed"/>
                    </StackPanel>

                    <StackPanel Grid.Column="2" Margin="5,0">
                        <TextBlock Text="فلتر سمك الزجاج:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CmbGlassThicknessFilter" Height="35"
                                  SelectionChanged="GlassThicknessFilter_Changed"/>
                    </StackPanel>

                    <StackPanel Grid.Column="3" Margin="10,0,0,0">
                        <TextBlock Text="حالة الدفع:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="CmbPaymentStatusFilter" Height="35"
                                  SelectionChanged="PaymentStatusFilter_Changed"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>
        
        <!-- إحصائيات سريعة -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="عدد الفواتير" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtFilteredCount" Text="0" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي المبيعات" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtFilteredTotal" Text="0.00 ج.م" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource SuccessBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الخصومات" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtFilteredDiscount" Text="0.00 ج.م" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource WarningBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="صافي المبيعات" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtFilteredNet" Text="0.00 ج.م" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource DangerBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- جدول فواتير المبيعات -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 فواتير المبيعات المحفوظة" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                    
                    <Button x:Name="BtnRefreshData" Content="🔄 تحديث" 
                            Background="{StaticResource SuccessBrush}" Foreground="White"
                            Padding="10,5" Margin="20,0,0,0" FontWeight="Bold"
                            BorderThickness="0" Cursor="Hand" Click="BtnRefreshData_Click"/>
                </StackPanel>
                
                <DataGrid x:Name="SalesReportDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40"
                          FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding SaleDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                        <DataGridTextColumn Header="اسم العميل" Binding="{Binding Customer.Name}" Width="150"/>
                        <DataGridTextColumn Header="المبلغ الإجمالي" Binding="{Binding TotalAmount, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="المبلغ الصافي" Binding="{Binding NetAmount, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="عدد العناصر" Binding="{Binding SaleItems.Count}" Width="100"/>
                        <DataGridTextColumn Header="الخدمات" Binding="{Binding ServicesText}" Width="150"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="250">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️" Background="{StaticResource PrimaryBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnViewInvoice_Click" ToolTip="عرض الفاتورة"/>
                                        
                                        <Button Content="✏️" Background="{StaticResource WarningBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnEditInvoice_Click" ToolTip="تعديل الفاتورة"/>
                                        
                                        <Button Content="📄" Background="{StaticResource SuccessBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnPrintInvoice_Click" ToolTip="طباعة PDF"/>
                                        
                                        <Button Content="🗑️" Background="{StaticResource DangerBrush}" 
                                                Foreground="White" Padding="5,2" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnDeleteInvoice_Click" ToolTip="حذف الفاتورة"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
