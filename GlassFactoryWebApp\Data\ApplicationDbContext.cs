using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using GlassFactoryWebApp.Models;

namespace GlassFactoryWebApp.Data
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي
    /// </summary>
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        // Sales Module
        public DbSet<Customer> Customers { get; set; }
        public DbSet<SalesInvoice> SalesInvoices { get; set; }
        public DbSet<SalesInvoiceItem> SalesInvoiceItems { get; set; }
        public DbSet<CustomerPayment> CustomerPayments { get; set; }

        // Purchases Module
        public DbSet<Supplier> Suppliers { get; set; }
        public DbSet<PurchaseInvoice> PurchaseInvoices { get; set; }
        public DbSet<PurchaseInvoiceItem> PurchaseInvoiceItems { get; set; }
        public DbSet<SupplierPayment> SupplierPayments { get; set; }

        // Inventory Module
        public DbSet<Product> Products { get; set; }
        public DbSet<ProductCategory> ProductCategories { get; set; }
        public DbSet<InventoryMovement> InventoryMovements { get; set; }
        public DbSet<Warehouse> Warehouses { get; set; }
        public DbSet<StockAdjustment> StockAdjustments { get; set; }

        // Payroll Module
        public DbSet<Employee> Employees { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<PayrollRecord> PayrollRecords { get; set; }
        public DbSet<Attendance> AttendanceRecords { get; set; }
        public DbSet<LeaveRequest> LeaveRequests { get; set; }
        public DbSet<EmployeeAdvance> EmployeeAdvances { get; set; }

        // Expenses Module
        public DbSet<Expense> Expenses { get; set; }
        public DbSet<ExpenseCategory> ExpenseCategories { get; set; }
        public DbSet<EmployeeCustody> EmployeeCustodies { get; set; }

        // Accounting Module
        public DbSet<Account> Accounts { get; set; }
        public DbSet<JournalEntry> JournalEntries { get; set; }
        public DbSet<JournalEntryDetail> JournalEntryDetails { get; set; }
        public DbSet<CostCenter> CostCenters { get; set; }
        public DbSet<Project> Projects { get; set; }

        // Manufacturing Module
        public DbSet<ManufacturingOrder> ManufacturingOrders { get; set; }
        public DbSet<ManufacturingOrderItem> ManufacturingOrderItems { get; set; }
        public DbSet<ProductionOperation> ProductionOperations { get; set; }
        public DbSet<ProductionBOM> ProductionBOMs { get; set; }
        public DbSet<QualityCheck> QualityChecks { get; set; }
        public DbSet<ProductionCost> ProductionCosts { get; set; }
        public DbSet<WorkCenter> WorkCenters { get; set; }

        // Reports and Analytics
        public DbSet<Report> Reports { get; set; }
        public DbSet<ReportParameter> ReportParameters { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // Configure decimal precision
            foreach (var entityType in builder.Model.GetEntityTypes())
            {
                var properties = entityType.ClrType.GetProperties()
                    .Where(p => p.PropertyType == typeof(decimal) || p.PropertyType == typeof(decimal?));

                foreach (var property in properties)
                {
                    builder.Entity(entityType.Name).Property(property.Name)
                        .HasColumnType("decimal(18,2)");
                }
            }

            // Configure relationships and constraints
            ConfigureCustomerRelationships(builder);
            ConfigureSupplierRelationships(builder);
            ConfigureProductRelationships(builder);
            ConfigureAccountingRelationships(builder);
            ConfigureManufacturingRelationships(builder);
            ConfigurePayrollRelationships(builder);

            // Configure indexes for performance
            ConfigureIndexes(builder);

            // Seed initial data
            SeedInitialData(builder);
        }

        private void ConfigureCustomerRelationships(ModelBuilder builder)
        {
            builder.Entity<SalesInvoice>()
                .HasOne(s => s.Customer)
                .WithMany(c => c.SalesInvoices)
                .HasForeignKey(s => s.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<SalesInvoiceItem>()
                .HasOne(si => si.SalesInvoice)
                .WithMany(s => s.Items)
                .HasForeignKey(si => si.SalesInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<CustomerPayment>()
                .HasOne(cp => cp.Customer)
                .WithMany(c => c.Payments)
                .HasForeignKey(cp => cp.CustomerId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureSupplierRelationships(ModelBuilder builder)
        {
            builder.Entity<PurchaseInvoice>()
                .HasOne(p => p.Supplier)
                .WithMany(s => s.PurchaseInvoices)
                .HasForeignKey(p => p.SupplierId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<PurchaseInvoiceItem>()
                .HasOne(pi => pi.PurchaseInvoice)
                .WithMany(p => p.Items)
                .HasForeignKey(pi => pi.PurchaseInvoiceId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigureProductRelationships(ModelBuilder builder)
        {
            builder.Entity<Product>()
                .HasOne(p => p.Category)
                .WithMany(c => c.Products)
                .HasForeignKey(p => p.CategoryId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<ProductCategory>()
                .HasOne(pc => pc.ParentCategory)
                .WithMany(pc => pc.SubCategories)
                .HasForeignKey(pc => pc.ParentCategoryId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureAccountingRelationships(ModelBuilder builder)
        {
            builder.Entity<Account>()
                .HasOne(a => a.ParentAccount)
                .WithMany(a => a.SubAccounts)
                .HasForeignKey(a => a.ParentAccountId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<JournalEntryDetail>()
                .HasOne(jed => jed.JournalEntry)
                .WithMany(je => je.Details)
                .HasForeignKey(jed => jed.JournalEntryId)
                .OnDelete(DeleteBehavior.Cascade);

            builder.Entity<JournalEntryDetail>()
                .HasOne(jed => jed.Account)
                .WithMany(a => a.JournalEntryDetails)
                .HasForeignKey(jed => jed.AccountId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureManufacturingRelationships(ModelBuilder builder)
        {
            builder.Entity<ManufacturingOrder>()
                .HasOne(mo => mo.Product)
                .WithMany(p => p.ManufacturingItems)
                .HasForeignKey(mo => mo.ProductId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<ManufacturingOrderItem>()
                .HasOne(moi => moi.ManufacturingOrder)
                .WithMany(mo => mo.Materials)
                .HasForeignKey(moi => moi.ManufacturingOrderId)
                .OnDelete(DeleteBehavior.Cascade);
        }

        private void ConfigurePayrollRelationships(ModelBuilder builder)
        {
            builder.Entity<Employee>()
                .HasOne(e => e.Department)
                .WithMany(d => d.Employees)
                .HasForeignKey(e => e.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            builder.Entity<PayrollRecord>()
                .HasOne(pr => pr.Employee)
                .WithMany(e => e.PayrollRecords)
                .HasForeignKey(pr => pr.EmployeeId)
                .OnDelete(DeleteBehavior.Restrict);
        }

        private void ConfigureIndexes(ModelBuilder builder)
        {
            // Customer indexes
            builder.Entity<Customer>()
                .HasIndex(c => c.CustomerCode)
                .IsUnique();

            // Supplier indexes
            builder.Entity<Supplier>()
                .HasIndex(s => s.SupplierCode)
                .IsUnique();

            // Product indexes
            builder.Entity<Product>()
                .HasIndex(p => p.ProductCode)
                .IsUnique();

            // Account indexes
            builder.Entity<Account>()
                .HasIndex(a => a.AccountCode)
                .IsUnique();

            // Invoice indexes
            builder.Entity<SalesInvoice>()
                .HasIndex(si => si.InvoiceNumber)
                .IsUnique();

            builder.Entity<PurchaseInvoice>()
                .HasIndex(pi => pi.InvoiceNumber)
                .IsUnique();

            // Journal Entry indexes
            builder.Entity<JournalEntry>()
                .HasIndex(je => je.EntryNumber)
                .IsUnique();

            // Employee indexes
            builder.Entity<Employee>()
                .HasIndex(e => e.EmployeeCode)
                .IsUnique();
        }

        private void SeedInitialData(ModelBuilder builder)
        {
            // This will be implemented in SeedData.cs
        }

        public override int SaveChanges()
        {
            UpdateTimestamps();
            return base.SaveChanges();
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            UpdateTimestamps();
            return await base.SaveChangesAsync(cancellationToken);
        }

        private void UpdateTimestamps()
        {
            var entries = ChangeTracker.Entries()
                .Where(e => e.Entity is BaseEntity && (e.State == EntityState.Added || e.State == EntityState.Modified));

            foreach (var entry in entries)
            {
                var entity = (BaseEntity)entry.Entity;

                if (entry.State == EntityState.Added)
                {
                    entity.CreatedAt = DateTime.UtcNow;
                }
                else if (entry.State == EntityState.Modified)
                {
                    entity.UpdatedAt = DateTime.UtcNow;
                }
            }
        }
    }
}
