@echo off
title Glass Factory Accounting - Ready Project
color 0A

echo.
echo ===============================================
echo   Glass Factory Accounting System
echo   READY TO RUN PROJECT
echo ===============================================
echo.

echo Opening the working project...
echo.

if exist "GlassGlass\GlassFactoryWorking\GlassFactoryWorking.sln" (
    echo Found project file
    start "" "GlassGlass\GlassFactoryWorking\GlassFactoryWorking.sln"
    echo.
    echo INSTRUCTIONS:
    echo 1. Visual Studio will open
    echo 2. Press F5 to run
    echo 3. You will see a form with Arabic title
    echo 4. Click the green button to test
    echo.
) else (
    echo ERROR: Project file not found!
    echo Looking for: GlassGlass\GlassFactoryWorking\GlassFactoryWorking.sln
    pause
)

timeout /t 5 /nobreak >nul
