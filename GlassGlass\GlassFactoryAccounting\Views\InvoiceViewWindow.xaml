<Window x:Class="GlassFactoryAccounting.Views.InvoiceViewWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
        mc:Ignorable="d"
        Title="عرض الفاتورة" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <TextBlock x:Name="TxtTitle" Text="📄 عرض الفاتورة" FontSize="20" FontWeight="Bold" 
                     Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- بيانات الفاتورة الأساسية -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="TxtInvoiceNumber" FontSize="16" Foreground="{StaticResource PrimaryBrush}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1">
                    <TextBlock Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="TxtCustomerName" FontSize="16" Foreground="{StaticResource PrimaryBrush}"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2">
                    <TextBlock Text="تاريخ الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="TxtInvoiceDate" FontSize="16" Foreground="{StaticResource PrimaryBrush}"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- تفاصيل الفاتورة -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="📋 تفاصيل الفاتورة" FontSize="16" FontWeight="Bold" 
                         Margin="0,0,0,10" Foreground="{StaticResource PrimaryBrush}"/>
                
                <DataGrid Grid.Row="1" x:Name="ItemsDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="35"
                          FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ت" Binding="{Binding Id}" Width="40"/>
                        <DataGridTextColumn Header="الخدمة" Binding="{Binding Service}" Width="100"/>
                        <DataGridTextColumn Header="سمك الزجاج" Binding="{Binding GlassThickness}" Width="80"/>
                        <DataGridTextColumn Header="تفاصيل" Binding="{Binding Details}" Width="100"/>
                        <DataGridTextColumn Header="الطول" Binding="{Binding Length}" Width="80"/>
                        <DataGridTextColumn Header="العرض" Binding="{Binding Width}" Width="80"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Area, StringFormat='{}{0:F4}'}" Width="80"/>
                        <DataGridTextColumn Header="العدد" Binding="{Binding Count}" Width="60"/>
                        <DataGridTextColumn Header="إجمالي الكمية" Binding="{Binding TotalArea, StringFormat='{}{0:F4}'}" Width="100"/>
                        <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat='{}{0:F2}'}" Width="80"/>
                        <DataGridTextColumn Header="إجمالي القيمة" Binding="{Binding TotalPrice, StringFormat='{}{0:F2}'}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        
        <!-- الإجماليات -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>
                
                <!-- الملاحظات -->
                <StackPanel Grid.Column="0">
                    <TextBlock Text="الملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBlock x:Name="TxtNotes" TextWrapping="Wrap" MaxHeight="80"/>
                </StackPanel>
                
                <!-- الإجماليات -->
                <StackPanel Grid.Column="1">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="إجمالي الفاتورة:" FontWeight="Bold" Width="120"/>
                        <TextBlock x:Name="TxtSubTotal" FontWeight="Bold" Foreground="{StaticResource PrimaryBrush}"/>
                    </StackPanel>
                    
                    <StackPanel x:Name="PnlDiscount" Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="الخصم:" FontWeight="Bold" Width="120"/>
                        <TextBlock x:Name="TxtDiscount" FontWeight="Bold" Foreground="#F44336"/>
                    </StackPanel>
                    
                    <Separator Margin="0,5"/>
                    
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="الإجمالي النهائي:" FontWeight="Bold" FontSize="16" Width="120"/>
                        <TextBlock x:Name="TxtFinalTotal" FontWeight="Bold" FontSize="16" Foreground="{StaticResource SuccessBrush}"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- أزرار الإغلاق -->
        <Border Grid.Row="4" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnClose" Content="❌ إغلاق" 
                        Background="#F44336" Foreground="White"
                        Padding="20,10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnClose_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
