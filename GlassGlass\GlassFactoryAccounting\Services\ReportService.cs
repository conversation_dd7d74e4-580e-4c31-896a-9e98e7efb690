using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using OfficeOpenXml;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة التقارير والتصدير
    /// </summary>
    public class ReportService
    {
        private readonly SettingsService _settingsService;

        public ReportService(SettingsService settingsService)
        {
            _settingsService = settingsService;
        }

        /// <summary>
        /// تصدير قائمة المبيعات إلى PDF
        /// </summary>
        public Task<bool> ExportSalesToPdfAsync(List<Sale> sales, string filePath)
        {
            try
            {
                var document = new Document(PageSize.A4, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
                
                document.Open();

                // إضافة العنوان
                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18);
                var title = new Paragraph("تقرير المبيعات", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(title);

                // إضافة معلومات الشركة
                var settings = _settingsService.GetSettings();
                var companyInfo = new Paragraph($"{settings.CompanyName}\n{settings.CompanyAddress}\nتليفون: {settings.CompanyPhone}")
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(companyInfo);

                // إضافة جدول البيانات
                var table = new PdfPTable(6) { WidthPercentage = 100 };
                table.SetWidths(new float[] { 15, 15, 25, 15, 15, 15 });

                // إضافة رؤوس الأعمدة
                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
                table.AddCell(new PdfPCell(new Phrase("رقم الفاتورة", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                table.AddCell(new PdfPCell(new Phrase("التاريخ", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                table.AddCell(new PdfPCell(new Phrase("العميل", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                table.AddCell(new PdfPCell(new Phrase("المبلغ", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                table.AddCell(new PdfPCell(new Phrase("الخصم", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                table.AddCell(new PdfPCell(new Phrase("الصافي", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

                // إضافة البيانات
                var cellFont = FontFactory.GetFont(FontFactory.HELVETICA, 10);
                foreach (var sale in sales)
                {
                    table.AddCell(new PdfPCell(new Phrase(sale.InvoiceNumber, cellFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    table.AddCell(new PdfPCell(new Phrase(sale.SaleDate.ToString("yyyy/MM/dd"), cellFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    table.AddCell(new PdfPCell(new Phrase(sale.Customer?.Name ?? "", cellFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    table.AddCell(new PdfPCell(new Phrase($"{sale.TotalAmount:N2}", cellFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    table.AddCell(new PdfPCell(new Phrase($"{sale.Discount:N2}", cellFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    table.AddCell(new PdfPCell(new Phrase($"{sale.NetAmount:N2}", cellFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                }

                document.Add(table);

                // إضافة الإجماليات
                var totalAmount = sales.Sum(s => s.NetAmount);
                var totalParagraph = new Paragraph($"\nإجمالي المبيعات: {totalAmount:N2} {settings.Currency}", titleFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingBefore = 20
                };
                document.Add(totalParagraph);

                document.Close();
                return Task.FromResult(true);
            }
            catch (Exception)
            {
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// تصدير قائمة المبيعات إلى Excel
        /// </summary>
        public async Task<bool> ExportSalesToExcelAsync(List<Sale> sales, string filePath)
        {
            try
            {
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
                
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("المبيعات");

                // إضافة رؤوس الأعمدة
                worksheet.Cells[1, 1].Value = "رقم الفاتورة";
                worksheet.Cells[1, 2].Value = "التاريخ";
                worksheet.Cells[1, 3].Value = "العميل";
                worksheet.Cells[1, 4].Value = "المبلغ الإجمالي";
                worksheet.Cells[1, 5].Value = "الخصم";
                worksheet.Cells[1, 6].Value = "الصافي";
                worksheet.Cells[1, 7].Value = "حالة الدفع";

                // تنسيق رؤوس الأعمدة
                using (var range = worksheet.Cells[1, 1, 1, 7])
                {
                    range.Style.Font.Bold = true;
                    range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
                    range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightBlue);
                }

                // إضافة البيانات
                for (int i = 0; i < sales.Count; i++)
                {
                    var sale = sales[i];
                    var row = i + 2;

                    worksheet.Cells[row, 1].Value = sale.InvoiceNumber;
                    worksheet.Cells[row, 2].Value = sale.SaleDate.ToString("yyyy/MM/dd");
                    worksheet.Cells[row, 3].Value = sale.Customer?.Name ?? "";
                    worksheet.Cells[row, 4].Value = sale.TotalAmount;
                    worksheet.Cells[row, 5].Value = sale.Discount;
                    worksheet.Cells[row, 6].Value = sale.NetAmount;
                    worksheet.Cells[row, 7].Value = sale.PaymentStatus.ToString();
                }

                // تنسيق الأعمدة
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();

                // إضافة الإجماليات
                var lastRow = sales.Count + 3;
                worksheet.Cells[lastRow, 5].Value = "الإجمالي:";
                worksheet.Cells[lastRow, 6].Formula = $"SUM(F2:F{sales.Count + 1})";
                worksheet.Cells[lastRow, 5, lastRow, 6].Style.Font.Bold = true;

                await package.SaveAsAsync(new FileInfo(filePath));
                return true;
            }
            catch (Exception)
            {
                return false;
            }
        }

        /// <summary>
        /// طباعة فاتورة مبيعات
        /// </summary>
        public Task<bool> PrintSaleInvoiceAsync(Sale sale, string filePath)
        {
            try
            {
                var document = new Document(PageSize.A4, 50, 50, 25, 25);
                var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
                
                document.Open();

                var settings = _settingsService.GetSettings();

                // رأس الفاتورة
                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 20);
                var title = new Paragraph("فاتورة مبيعات", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(title);

                // معلومات الشركة والفاتورة
                var infoTable = new PdfPTable(2) { WidthPercentage = 100 };
                infoTable.SetWidths(new float[] { 50, 50 });

                var companyCell = new PdfPCell(new Phrase($"{settings.CompanyName}\n{settings.CompanyAddress}\nتليفون: {settings.CompanyPhone}"))
                {
                    Border = Rectangle.NO_BORDER,
                    HorizontalAlignment = Element.ALIGN_LEFT
                };

                var invoiceCell = new PdfPCell(new Phrase($"رقم الفاتورة: {sale.InvoiceNumber}\nالتاريخ: {sale.SaleDate:yyyy/MM/dd}\nالعميل: {sale.Customer?.Name}"))
                {
                    Border = Rectangle.NO_BORDER,
                    HorizontalAlignment = Element.ALIGN_RIGHT
                };

                infoTable.AddCell(companyCell);
                infoTable.AddCell(invoiceCell);
                document.Add(infoTable);

                // جدول العناصر (سيتم إضافة التفاصيل لاحقاً)
                var itemsTable = new PdfPTable(5) { WidthPercentage = 100, SpacingBefore = 20 };
                itemsTable.SetWidths(new float[] { 10, 40, 15, 15, 20 });

                // رؤوس الأعمدة
                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12);
                itemsTable.AddCell(new PdfPCell(new Phrase("م", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                itemsTable.AddCell(new PdfPCell(new Phrase("الصنف", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                itemsTable.AddCell(new PdfPCell(new Phrase("الكمية", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                itemsTable.AddCell(new PdfPCell(new Phrase("السعر", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                itemsTable.AddCell(new PdfPCell(new Phrase("الإجمالي", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

                document.Add(itemsTable);

                // الإجماليات
                var totalsTable = new PdfPTable(2) { WidthPercentage = 50, HorizontalAlignment = Element.ALIGN_RIGHT, SpacingBefore = 20 };
                totalsTable.AddCell(new PdfPCell(new Phrase("الإجمالي:", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{sale.TotalAmount:N2} {settings.Currency}", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                
                totalsTable.AddCell(new PdfPCell(new Phrase("الخصم:", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{sale.Discount:N2} {settings.Currency}", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                
                totalsTable.AddCell(new PdfPCell(new Phrase("الصافي:", titleFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{sale.NetAmount:N2} {settings.Currency}", titleFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT });

                document.Add(totalsTable);

                document.Close();
                return Task.FromResult(true);
            }
            catch (Exception)
            {
                return Task.FromResult(false);
            }
        }
    }
}
