using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة تسجيل خصم الموظف
    /// </summary>
    public partial class EmployeeDeductionView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;

        public EmployeeDeductionView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadDeductions();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ اليوم كافتراضي
            DpDeductionDate.SelectedDate = DateTime.Now;

            // تحميل أسباب الخصومات الشائعة
            CmbDeductionReason.Items.Add("غياب بدون عذر");
            CmbDeductionReason.Items.Add("تأخير متكرر");
            CmbDeductionReason.Items.Add("خصم تأمينات");
            CmbDeductionReason.Items.Add("خصم ضرائب");
            CmbDeductionReason.Items.Add("خصم سلفة");
            CmbDeductionReason.Items.Add("خصم تأديبي");
            CmbDeductionReason.Items.Add("خصم إداري");
            CmbDeductionReason.Items.Add("خصم أخرى");
            CmbDeductionReason.SelectedIndex = 0;
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.ItemsSource = employees;
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDeductions()
        {
            try
            {
                var deductions = _payrollService.GetAllDeductions();
                DeductionsDataGrid.ItemsSource = deductions;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الخصومات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbEmployee_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbEmployee.SelectedItem is Employee selectedEmployee)
            {
                // تعبئة جميع البيانات تلقائياً
                TxtEmployeeCode.Text = selectedEmployee.EmployeeCode;
                TxtEmployeeName.Text = selectedEmployee.Name;
                TxtPosition.Text = selectedEmployee.Position;
                TxtBranch.Text = selectedEmployee.Branch;
                TxtDepartment.Text = selectedEmployee.Department;

                // تعيين قيمة خصم افتراضية (يمكن تعديلها)
                TxtDeductionAmount.Text = "100.00";
            }
            else
            {
                ClearEmployeeFields();
            }
        }

        private void ClearEmployeeFields()
        {
            TxtEmployeeCode.Clear();
            TxtEmployeeName.Clear();
            TxtPosition.Clear();
            TxtBranch.Clear();
            TxtDepartment.Clear();
            TxtDeductionAmount.Clear();
        }

        private void TxtDeductionAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التحقق من صحة المبلغ المدخل
            if (sender is TextBox textBox)
            {
                if (!string.IsNullOrEmpty(textBox.Text))
                {
                    if (!decimal.TryParse(textBox.Text, out _))
                    {
                        // إزالة الأحرف غير الصحيحة
                        int caretIndex = textBox.CaretIndex;
                        textBox.Text = System.Text.RegularExpressions.Regex.Replace(textBox.Text, @"[^\d.]", "");
                        textBox.CaretIndex = Math.Min(caretIndex, textBox.Text.Length);
                    }
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedEmployee = CmbEmployee.SelectedItem as Employee;
                if (selectedEmployee == null)
                {
                    MessageBox.Show("يرجى اختيار موظف.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var deduction = new EmployeeDeduction
                {
                    EmployeeId = selectedEmployee.Id,
                    EmployeeName = TxtEmployeeName.Text,
                    EmployeeCode = TxtEmployeeCode.Text,
                    DeductionAmount = decimal.Parse(TxtDeductionAmount.Text),
                    DeductionReason = CmbDeductionReason.Text,
                    DeductionDate = DpDeductionDate.SelectedDate ?? DateTime.Now,
                    Notes = TxtNotes.Text.Trim()
                };

                if (_payrollService.AddEmployeeDeduction(deduction))
                {
                    MessageBox.Show($"تم حفظ خصم الموظف بنجاح!\n\nتفاصيل الخصم:\nالموظف: {deduction.EmployeeName}\nالكود: {deduction.EmployeeCode}\nقيمة الخصم: {deduction.DeductionAmount:N2} جنيه\nالسبب: {deduction.DeductionReason}\nالتاريخ: {deduction.DeductionDate:yyyy/MM/dd}",
                        "تم حفظ الخصم بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                    ClearForm();
                    LoadDeductions();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الخصم!", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الخصم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbEmployee.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbEmployee.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtDeductionAmount.Text))
            {
                MessageBox.Show("يرجى إدخال قيمة الخصم", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtDeductionAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtDeductionAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للخصم", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtDeductionAmount.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CmbDeductionReason.Text))
            {
                MessageBox.Show("يرجى إدخال سبب الخصم", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbDeductionReason.Focus();
                return false;
            }

            if (!DpDeductionDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ الخصم", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpDeductionDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            CmbEmployee.SelectedIndex = -1;
            ClearEmployeeFields();
            CmbDeductionReason.SelectedIndex = 0;
            DpDeductionDate.SelectedDate = DateTime.Now;
            TxtNotes.Clear();
            CmbEmployee.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeleteDeduction_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int deductionId)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذا الخصم؟", "تأكيد الحذف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (_payrollService.DeleteEmployeeDeduction(deductionId))
                        {
                            MessageBox.Show("تم حذف الخصم بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadDeductions();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الخصم!", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الخصم: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditDeduction_Click(object sender, RoutedEventArgs e)
        {
            // TODO: تنفيذ منطق التعديل هنا حسب الحاجة
            MessageBox.Show("ميزة التعديل غير مفعلة حالياً. يرجى استكمال منطق التعديل إذا لزم.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
