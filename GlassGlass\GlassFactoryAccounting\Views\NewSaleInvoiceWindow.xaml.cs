using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using Microsoft.Win32;
using System.IO;
using System.Linq;
using System.Diagnostics;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة فاتورة مبيعات جديدة
/// </summary>
public partial class NewSaleInvoiceWindow : Window, INotifyPropertyChanged
{
    private ObservableCollection<SaleItem> _invoiceItems;
    private ObservableCollection<Service> _services;
    private ObservableCollection<decimal> _glassThicknesses;
    private decimal _subTotal;
    private decimal _discountAmount;
    private decimal _finalTotal;

    public ObservableCollection<SaleItem> InvoiceItems
    {
        get => _invoiceItems;
        set
        {
            _invoiceItems = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<Service> Services
    {
        get => _services;
        set
        {
            _services = value;
            OnPropertyChanged();
        }
    }

    public ObservableCollection<decimal> GlassThicknesses
    {
        get => _glassThicknesses;
        set
        {
            _glassThicknesses = value;
            OnPropertyChanged();
        }
    }

    public decimal SubTotal
    {
        get => _subTotal;
        set
        {
            _subTotal = value;
            OnPropertyChanged();
            UpdateTotals();
        }
    }

    public decimal DiscountAmount
    {
        get => _discountAmount;
        set
        {
            _discountAmount = value;
            OnPropertyChanged();
            UpdateTotals();
        }
    }

    public decimal FinalTotal
    {
        get => _finalTotal;
        set
        {
            _finalTotal = value;
            OnPropertyChanged();
        }
    }

    public NewSaleInvoiceWindow()
    {
        InitializeComponent();
        DataContext = this;
        
        InitializeData();
        GenerateInvoiceNumber();
    }

    private void InitializeData()
    {
        // تهيئة المجموعات
        InvoiceItems = new ObservableCollection<SaleItem>();
        Services = new ObservableCollection<Service>();
        GlassThicknesses = new ObservableCollection<decimal>();

        // تعبئة الخدمات الافتراضية
        Services.Add(new Service { Name = "تقطيع", Description = "تقطيع الزجاج حسب المقاسات" });
        Services.Add(new Service { Name = "تركيب", Description = "تركيب الزجاج" });
        Services.Add(new Service { Name = "صقل", Description = "صقل حواف الزجاج" });
        Services.Add(new Service { Name = "تثقيب", Description = "عمل ثقوب في الزجاج" });

        // تعبئة سماكات الزجاج من 1 إلى 100 ملم
        for (decimal thickness = 1; thickness <= 100; thickness += 0.5m)
        {
            GlassThicknesses.Add(thickness);
        }

        // ربط الجدول بالبيانات
        InvoiceDataGrid.ItemsSource = InvoiceItems;

        // مراقبة تغييرات العناصر
        InvoiceItems.CollectionChanged += (s, e) => CalculateSubTotal();
    }

    private void GenerateInvoiceNumber()
    {
        // توليد رقم فاتورة تلقائي
        TxtInvoiceNumber.Text = $"INV-{DateTime.Now:yyyyMMdd}-{DateTime.Now:HHmmss}";
    }

    private void BtnAddNormalRow_Click(object sender, RoutedEventArgs e)
    {
        var newItem = new SaleItem
        {
            Id = InvoiceItems.Count + 1,
            Service = Services.FirstOrDefault()?.Name ?? "",
            GlassThickness = 6,
            Details = "",
            Length = 0,
            Width = 0,
            Count = 1,
            UnitPrice = 0,
            IsManualRow = false
        };

        // ربط الأحداث لحساب المعادلات التلقائية
        newItem.PropertyChanged += SaleItem_PropertyChanged;

        InvoiceItems.Add(newItem);

        // إزالة النافذة المنبثقة
    }

    private void BtnAddManualRow_Click(object sender, RoutedEventArgs e)
    {
        var newItem = new SaleItem
        {
            Id = InvoiceItems.Count + 1,
            Service = Services.FirstOrDefault()?.Name ?? "",
            GlassThickness = 6,
            Details = "",
            Length = 0,
            Width = 0,
            Area = 0,
            Count = 0,
            TotalArea = 0,
            UnitPrice = 0,
            IsManualRow = true
        };

        // ربط الأحداث لحساب إجمالي القيمة فقط
        newItem.PropertyChanged += SaleItem_PropertyChanged;

        InvoiceItems.Add(newItem);

        // إزالة النافذة المنبثقة
    }

    private void BtnDeleteRow_Click(object sender, RoutedEventArgs e)
    {
        if (InvoiceDataGrid.SelectedItem is SaleItem selectedItem)
        {
            var result = MessageBox.Show("هل تريد حذف هذا الصف؟", "تأكيد الحذف", 
                MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                InvoiceItems.Remove(selectedItem);
                
                // إعادة ترقيم الصفوف
                for (int i = 0; i < InvoiceItems.Count; i++)
                {
                    InvoiceItems[i].Id = i + 1;
                }
            }
        }
        else
        {
            MessageBox.Show("يرجى اختيار صف للحذف", "تنبيه", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private void SaleItem_PropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (sender is SaleItem item)
        {
            // حساب المعادلات حسب نوع الصف
            if (!item.IsManualRow) // صف عادي - حساب تلقائي لجميع المعادلات
            {
                if (e.PropertyName == nameof(SaleItem.Length) || e.PropertyName == nameof(SaleItem.Width))
                {
                    item.CalculateArea();
                }
                else if (e.PropertyName == nameof(SaleItem.Count) || e.PropertyName == nameof(SaleItem.Area))
                {
                    item.CalculateTotalArea();
                }

                if (e.PropertyName == nameof(SaleItem.UnitPrice) || e.PropertyName == nameof(SaleItem.TotalArea))
                {
                    item.CalculateTotalPrice();
                }
            }
            else // صف يدوي - حساب إجمالي القيمة فقط (إجمالي الكمية يدوي)
            {
                if (e.PropertyName == nameof(SaleItem.UnitPrice) || e.PropertyName == nameof(SaleItem.TotalArea))
                {
                    item.CalculateTotalPrice();
                }
                // في الصف اليدوي، إجمالي الكمية يُكتب يدوياً ولا يُحسب تلقائياً
            }
        }

        CalculateSubTotal();
    }

    private void CalculateSubTotal()
    {
        SubTotal = InvoiceItems.Sum(item => item.TotalPrice);
        TxtSubTotal.Text = $"{SubTotal:F2} ج.م";
    }

    private void ChkEnableDiscount_Checked(object sender, RoutedEventArgs e)
    {
        PnlDiscount.Visibility = Visibility.Visible;
        PnlDiscountDisplay.Visibility = Visibility.Visible;
    }

    private void ChkEnableDiscount_Unchecked(object sender, RoutedEventArgs e)
    {
        PnlDiscount.Visibility = Visibility.Collapsed;
        PnlDiscountDisplay.Visibility = Visibility.Collapsed;
        TxtDiscountAmount.Text = "0";
        DiscountAmount = 0;
    }

    private void ChkEnableNotes_Checked(object sender, RoutedEventArgs e)
    {
        TxtNotes.Visibility = Visibility.Visible;
    }

    private void ChkEnableNotes_Unchecked(object sender, RoutedEventArgs e)
    {
        TxtNotes.Visibility = Visibility.Collapsed;
        TxtNotes.Text = "";
    }

    private void TxtDiscountAmount_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (decimal.TryParse(TxtDiscountAmount.Text, out decimal discount))
        {
            DiscountAmount = discount;
        }
        else
        {
            DiscountAmount = 0;
        }
    }

    private void UpdateTotals()
    {
        TxtDiscountDisplay.Text = $"{DiscountAmount:F2} ج.م";
        FinalTotal = SubTotal - DiscountAmount;
        TxtFinalTotal.Text = $"{FinalTotal:F2} ج.م";
    }

    private void BtnManageServices_Click(object sender, RoutedEventArgs e)
    {
        var servicesWindow = new ServicesManagementWindow(Services);
        servicesWindow.ShowDialog();
    }

    private void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            // حفظ الفاتورة في قاعدة البيانات
            MessageBox.Show("تم حفظ الفاتورة بنجاح", "نجح", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void BtnSavePDF_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "PDF Files|*.pdf",
                FileName = $"Invoice_{TxtInvoiceNumber.Text}.pdf",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    var pdfService = new InvoicePdfService();
                    bool success = pdfService.CreateInvoicePdf(
                        TxtCustomerName.Text,
                        TxtInvoiceNumber.Text,
                        DateInvoice.SelectedDate ?? DateTime.Now,
                        InvoiceItems,
                        SubTotal,
                        DiscountAmount,
                        FinalTotal,
                        TxtNotes.Text,
                        saveDialog.FileName
                    );

                    if (success)
                    {
                        MessageBox.Show($"تم حفظ الفاتورة بنجاح في:\n{saveDialog.FileName}", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        // فتح الملف تلقائياً
                        var result = MessageBox.Show("هل تريد فتح الملف الآن؟", "فتح الملف",
                            MessageBoxButton.YesNo, MessageBoxImage.Question);

                        if (result == MessageBoxResult.Yes)
                        {
                            Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                        }
                    }
                    else
                    {
                        MessageBox.Show("حدث خطأ أثناء إنشاء ملف PDF", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }
    }

    private void BtnPrint_Click(object sender, RoutedEventArgs e)
    {
        if (ValidateInvoice())
        {
            try
            {
                // إنشاء ملف PDF مؤقت للطباعة
                var tempPath = Path.Combine(Path.GetTempPath(), $"Invoice_{TxtInvoiceNumber.Text}_{DateTime.Now:yyyyMMddHHmmss}.pdf");

                var pdfService = new InvoicePdfService();
                bool success = pdfService.CreateInvoicePdf(
                    TxtCustomerName.Text,
                    TxtInvoiceNumber.Text,
                    DateInvoice.SelectedDate ?? DateTime.Now,
                    InvoiceItems,
                    SubTotal,
                    DiscountAmount,
                    FinalTotal,
                    TxtNotes.Text,
                    tempPath
                );

                if (success)
                {
                    // فتح الملف للطباعة
                    var printProcess = new ProcessStartInfo(tempPath)
                    {
                        UseShellExecute = true,
                        Verb = "print"
                    };

                    Process.Start(printProcess);

                    MessageBox.Show("تم إرسال الفاتورة للطباعة", "طباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("حدث خطأ أثناء تحضير الفاتورة للطباعة", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("هل تريد إغلاق الفاتورة بدون حفظ؟", "تأكيد الإغلاق", 
            MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            Close();
        }
    }

    private bool ValidateInvoice()
    {
        if (string.IsNullOrWhiteSpace(TxtCustomerName.Text))
        {
            MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }

        if (InvoiceItems.Count == 0)
        {
            MessageBox.Show("يرجى إضافة عناصر للفاتورة", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
            return false;
        }

        return true;
    }

    public event PropertyChangedEventHandler? PropertyChanged;
    
    protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
