using System.ComponentModel.DataAnnotations;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج العميل
    /// </summary>
    public class Customer : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string CustomerCode { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string CustomerName { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? ContactPerson { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(20)]
        public string? Mobile { get; set; }

        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(500)]
        public string? Address { get; set; }

        [MaxLength(100)]
        public string? City { get; set; }

        [MaxLength(100)]
        public string? Country { get; set; }

        [MaxLength(20)]
        public string? TaxNumber { get; set; }

        [MaxLength(20)]
        public string? CommercialRegister { get; set; }

        public decimal CreditLimit { get; set; } = 0;

        public int PaymentTermDays { get; set; } = 30;

        public decimal CurrentBalance { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        [MaxLength(50)]
        public string CustomerType { get; set; } = "عادي"; // عادي، جملة، تجزئة

        public decimal DiscountPercentage { get; set; } = 0;

        // Navigation Properties
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; } = new List<SalesInvoice>();
        public virtual ICollection<CustomerPayment> Payments { get; set; } = new List<CustomerPayment>();
    }
}
