using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace GlassFactoryAccounting.Views.Accounting
{
    /// <summary>
    /// الواجهة الرئيسية للنظام المحاسبي
    /// </summary>
    public partial class AccountingMainView : UserControl
    {
        public AccountingMainView()
        {
            InitializeComponent();
        }
        
        /// <summary>
        /// شجرة الحسابات
        /// </summary>
        private void ChartOfAccounts_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var chartOfAccountsWindow = new ChartOfAccountsWindow();
                chartOfAccountsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح شجرة الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// قيد اليومية
        /// </summary>
        private void JournalEntry_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var journalEntryWindow = new JournalEntryWindow();
                journalEntryWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح قيد اليومية: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// دفتر الأستاذ العام
        /// </summary>
        private void GeneralLedger_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var generalLedgerWindow = new GeneralLedgerWindow();
                generalLedgerWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح دفتر الأستاذ: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// ميزان المراجعة
        /// </summary>
        private void TrialBalance_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var trialBalanceWindow = new TrialBalanceWindow();
                trialBalanceWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح ميزان المراجعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// قائمة الدخل
        /// </summary>
        private void IncomeStatement_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var incomeStatementWindow = new IncomeStatementWindow();
                incomeStatementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح قائمة الدخل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// الميزانية العمومية
        /// </summary>
        private void BalanceSheet_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var balanceSheetWindow = new BalanceSheetWindow();
                balanceSheetWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح الميزانية العمومية: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// كشف حساب
        /// </summary>
        private void AccountStatement_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var accountStatementWindow = new AccountStatementWindow();
                accountStatementWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح كشف حساب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// التقارير المحاسبية
        /// </summary>
        private void AccountingReports_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var accountingReportsWindow = new AccountingReportsWindow();
                accountingReportsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح التقارير المحاسبية: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// إعدادات النظام المحاسبي
        /// </summary>
        private void AccountingSettings_Click(object sender, MouseButtonEventArgs e)
        {
            try
            {
                var accountingSettingsWindow = new AccountingSettingsWindow();
                accountingSettingsWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح إعدادات النظام: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
