<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 نظام حسابات مصنع الزجاج - Glass Factory Accounting System</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', '<PERSON>hom<PERSON>', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #2c3e50 !important;
        }

        .container-fluid {
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header h2 {
            font-size: 1.5rem;
            color: #7f8c8d;
            margin-bottom: 20px;
            font-weight: 400;
        }

        .status {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .demo-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }

        .sales-module {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }

        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .api-demo {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        .tech-item {
            background: #f8f9fa;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #2c3e50;
            border: 1px solid #dee2e6;
        }

        .modal-content {
            direction: rtl;
            text-align: right;
        }

        .table {
            direction: rtl;
            text-align: right;
        }

        .form-control {
            text-align: right;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .container-fluid {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="#">
                🏭 نظام حسابات مصنع الزجاج
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#home">الرئيسية</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#sales">المبيعات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#api">API</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 80px;">
        <!-- Header Section -->
        <div class="header" id="home">
            <h1>🏭 نظام حسابات مصنع الزجاج</h1>
            <h2>Glass Factory Accounting System</h2>
            <div class="status">
                <i class="fas fa-check-circle"></i>
                النظام يعمل مباشرة - System Live Now!
            </div>
        </div>

        <!-- Sales Module Demo -->
        <div class="demo-section" id="sales">
            <h3>🚀 موديول المبيعات - نسخة تفاعلية مباشرة</h3>

            <div class="row">
                <div class="col-md-6">
                    <div class="sales-module">
                        <h4 class="text-center mb-4">💰 إدارة المبيعات</h4>

                        <div class="feature-card" onclick="showCustomers()">
                            <h5><i class="fas fa-users"></i> إدارة العملاء</h5>
                            <p>إضافة وتعديل وحذف العملاء مع تتبع كامل للحسابات</p>
                            <small class="text-muted">انقر للتجربة</small>
                        </div>

                        <div class="feature-card" onclick="showInvoices()">
                            <h5><i class="fas fa-file-invoice"></i> فواتير المبيعات</h5>
                            <p>إنشاء وطباعة وترحيل فواتير المبيعات</p>
                            <small class="text-muted">انقر للتجربة</small>
                        </div>

                        <div class="feature-card" onclick="showPayments()">
                            <h5><i class="fas fa-credit-card"></i> المدفوعات</h5>
                            <p>تسجيل ومتابعة مدفوعات العملاء</p>
                            <small class="text-muted">انقر للتجربة</small>
                        </div>

                        <div class="feature-card" onclick="showReports()">
                            <h5><i class="fas fa-chart-bar"></i> التقارير</h5>
                            <p>تقارير مفصلة وإحصائيات شاملة</p>
                            <small class="text-muted">انقر للتجربة</small>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div id="demo-area" class="p-4 bg-light rounded" style="min-height: 400px;">
                        <h5 class="text-center">🎯 منطقة التجربة التفاعلية</h5>
                        <p class="text-center text-muted">انقر على أي وظيفة من الجانب الأيسر لتجربتها مباشرة</p>
                        <div class="text-center mt-4">
                            <i class="fas fa-mouse-pointer fa-3x text-primary"></i>
                            <p class="mt-3">ابدأ بالنقر على إحدى الوظائف</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Demo Section -->
        <div class="demo-section" id="api">
            <h3>📋 API Documentation - واجهة برمجة التطبيقات</h3>
            <div class="api-demo">
                <div class="row">
                    <div class="col-md-6">
                        <h5>🔧 المميزات التقنية:</h5>
                        <ul class="list-unstyled">
                            <li>✅ ASP.NET Core 8.0</li>
                            <li>✅ React 18 + TypeScript</li>
                            <li>✅ PostgreSQL Database</li>
                            <li>✅ JWT Authentication</li>
                            <li>✅ Arabic RTL Support</li>
                            <li>✅ Material-UI Components</li>
                            <li>✅ PDF Generation</li>
                            <li>✅ Excel Export</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>📊 APIs المتاحة:</h5>
                        <div class="api-endpoint">
                            <code>GET /api/customers</code> - قائمة العملاء<br>
                            <code>POST /api/customers</code> - إضافة عميل<br>
                            <code>GET /api/invoices</code> - قائمة الفواتير<br>
                            <code>POST /api/invoices</code> - إنشاء فاتورة<br>
                            <code>GET /api/payments</code> - قائمة المدفوعات<br>
                            <code>GET /api/reports</code> - التقارير<br>
                        </div>
                        <button class="btn btn-custom mt-3" onclick="showApiDemo()">
                            <i class="fas fa-code"></i> تجربة API
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Project Info -->
        <div class="demo-section">
            <h3>📊 معلومات المشروع</h3>
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>👤 المطور</h5>
                        <p>حسام محمد حسان أحمد</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>🔢 الإصدار</h5>
                        <p>2.0.0</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>📅 تاريخ النشر</h5>
                        <p>6 ديسمبر 2024</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <h5>🌐 الحالة</h5>
                        <p class="text-success">متاح مباشرة</p>
                    </div>
                </div>
            </div>

            <div class="tech-stack">
                <div class="tech-item">ASP.NET Core 8.0</div>
                <div class="tech-item">React 18</div>
                <div class="tech-item">TypeScript</div>
                <div class="tech-item">PostgreSQL</div>
                <div class="tech-item">Material-UI</div>
                <div class="tech-item">JWT Auth</div>
                <div class="tech-item">Arabic RTL</div>
                <div class="tech-item">Bootstrap 5</div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Customer Modal -->
    <div class="modal fade" id="customerModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">👥 إدارة العملاء</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>إضافة عميل جديد:</h6>
                            <form id="customerForm">
                                <div class="mb-3">
                                    <label class="form-label">اسم العميل</label>
                                    <input type="text" class="form-control" id="customerName" placeholder="أدخل اسم العميل">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="text" class="form-control" id="customerPhone" placeholder="05xxxxxxxx">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="customerEmail" placeholder="<EMAIL>">
                                </div>
                                <button type="button" class="btn btn-custom" onclick="addCustomer()">
                                    <i class="fas fa-plus"></i> إضافة العميل
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <h6>قائمة العملاء:</h6>
                            <div id="customersList">
                                <div class="card mb-2">
                                    <div class="card-body">
                                        <h6>شركة الزجاج المتطور</h6>
                                        <p class="mb-1">الهاتف: 0501234567</p>
                                        <p class="mb-1">البريد: <EMAIL></p>
                                        <small class="text-success">الرصيد: 15,000 ريال</small>
                                    </div>
                                </div>
                                <div class="card mb-2">
                                    <div class="card-body">
                                        <h6>مؤسسة البناء الحديث</h6>
                                        <p class="mb-1">الهاتف: 0507654321</p>
                                        <p class="mb-1">البريد: <EMAIL></p>
                                        <small class="text-warning">الرصيد: 8,500 ريال</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice Modal -->
    <div class="modal fade" id="invoiceModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">📋 فواتير المبيعات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h6>قائمة الفواتير:</h6>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>رقم الفاتورة</th>
                                            <th>العميل</th>
                                            <th>التاريخ</th>
                                            <th>المبلغ</th>
                                            <th>الحالة</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="invoicesList">
                                        <tr>
                                            <td>INV-001</td>
                                            <td>شركة الزجاج المتطور</td>
                                            <td>2024-12-06</td>
                                            <td>5,000 ريال</td>
                                            <td><span class="badge bg-success">مدفوعة</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="printInvoice('INV-001')">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>INV-002</td>
                                            <td>مؤسسة البناء الحديث</td>
                                            <td>2024-12-05</td>
                                            <td>3,200 ريال</td>
                                            <td><span class="badge bg-warning">معلقة</span></td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary" onclick="printInvoice('INV-002')">
                                                    <i class="fas fa-print"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <h6>إنشاء فاتورة جديدة:</h6>
                            <form id="invoiceForm">
                                <div class="mb-3">
                                    <label class="form-label">العميل</label>
                                    <select class="form-control" id="invoiceCustomer">
                                        <option>شركة الزجاج المتطور</option>
                                        <option>مؤسسة البناء الحديث</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">المنتج</label>
                                    <input type="text" class="form-control" placeholder="زجاج شفاف 6مم">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الكمية</label>
                                    <input type="number" class="form-control" placeholder="10">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">السعر</label>
                                    <input type="number" class="form-control" placeholder="150">
                                </div>
                                <button type="button" class="btn btn-custom" onclick="createInvoice()">
                                    <i class="fas fa-plus"></i> إنشاء الفاتورة
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Sample data
        let customers = [
            { id: 1, name: 'شركة الزجاج المتطور', phone: '0501234567', email: '<EMAIL>', balance: 15000 },
            { id: 2, name: 'مؤسسة البناء الحديث', phone: '0507654321', email: '<EMAIL>', balance: 8500 }
        ];

        let invoices = [
            { id: 'INV-001', customer: 'شركة الزجاج المتطور', date: '2024-12-06', amount: 5000, status: 'مدفوعة' },
            { id: 'INV-002', customer: 'مؤسسة البناء الحديث', date: '2024-12-05', amount: 3200, status: 'معلقة' }
        ];

        let payments = [
            { id: 1, customer: 'شركة الزجاج المتطور', amount: 5000, date: '2024-12-06', type: 'نقدي' },
            { id: 2, customer: 'مؤسسة البناء الحديث', amount: 2000, date: '2024-12-05', type: 'تحويل بنكي' }
        ];

        // Demo functions
        function showCustomers() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>👥 إدارة العملاء</h5>
                <p class="text-muted">عرض وإدارة قائمة العملاء</p>
                <div class="row">
                    ${customers.map(customer => `
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6>${customer.name}</h6>
                                    <p class="mb-1"><i class="fas fa-phone"></i> ${customer.phone}</p>
                                    <p class="mb-1"><i class="fas fa-envelope"></i> ${customer.email}</p>
                                    <p class="mb-0"><i class="fas fa-wallet"></i> الرصيد: ${customer.balance.toLocaleString()} ريال</p>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <button class="btn btn-custom" data-bs-toggle="modal" data-bs-target="#customerModal">
                    <i class="fas fa-plus"></i> إضافة عميل جديد
                </button>
                <button class="btn btn-outline-primary" onclick="exportCustomers()">
                    <i class="fas fa-download"></i> تصدير Excel
                </button>
            `;
        }

        function showInvoices() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>📋 فواتير المبيعات</h5>
                <p class="text-muted">إدارة فواتير المبيعات والطباعة</p>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoices.map(invoice => `
                                <tr>
                                    <td>${invoice.id}</td>
                                    <td>${invoice.customer}</td>
                                    <td>${invoice.date}</td>
                                    <td>${invoice.amount.toLocaleString()} ريال</td>
                                    <td><span class="badge ${invoice.status === 'مدفوعة' ? 'bg-success' : 'bg-warning'}">${invoice.status}</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="printInvoice('${invoice.id}')">
                                            <i class="fas fa-print"></i> طباعة
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                <button class="btn btn-custom" data-bs-toggle="modal" data-bs-target="#invoiceModal">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </button>
            `;
        }

        function showPayments() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>💳 مدفوعات العملاء</h5>
                <p class="text-muted">تسجيل ومتابعة مدفوعات العملاء</p>
                <div class="row">
                    ${payments.map(payment => `
                        <div class="col-md-6 mb-3">
                            <div class="card">
                                <div class="card-body">
                                    <h6>${payment.type}</h6>
                                    <p class="mb-1">العميل: ${payment.customer}</p>
                                    <p class="mb-1">المبلغ: ${payment.amount.toLocaleString()} ريال</p>
                                    <p class="mb-0">التاريخ: ${payment.date}</p>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                <button class="btn btn-custom" onclick="addPayment()">
                    <i class="fas fa-money-bill"></i> تسجيل دفعة جديدة
                </button>
            `;
        }

        function showReports() {
            const totalSales = invoices.reduce((sum, inv) => sum + inv.amount, 0);
            const paidInvoices = invoices.filter(inv => inv.status === 'مدفوعة').length;
            const pendingAmount = invoices.filter(inv => inv.status === 'معلقة').reduce((sum, inv) => sum + inv.amount, 0);

            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>📊 التقارير والإحصائيات</h5>
                <p class="text-muted">تقارير شاملة عن أداء المبيعات</p>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary">${invoices.length}</h3>
                                <p>إجمالي الفواتير</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">${totalSales.toLocaleString()}</h3>
                                <p>إجمالي المبيعات (ريال)</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info">${customers.length}</h3>
                                <p>عدد العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-warning">${pendingAmount.toLocaleString()}</h3>
                                <p>المبالغ المعلقة (ريال)</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-danger me-2" onclick="exportPDF()">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                    <button class="btn btn-outline-success" onclick="exportExcel()">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                </div>
            `;
        }

        function showApiDemo() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>📋 API Documentation Demo</h5>
                <p class="text-muted">تجربة واجهة برمجة التطبيقات</p>
                <div class="row">
                    <div class="col-md-6">
                        <h6>🔧 APIs المتاحة:</h6>
                        <div class="api-list">
                            <div class="api-item mb-2 p-2 bg-light rounded">
                                <code class="text-success">GET</code> <code>/api/customers</code>
                                <small class="d-block text-muted">جلب قائمة العملاء</small>
                            </div>
                            <div class="api-item mb-2 p-2 bg-light rounded">
                                <code class="text-primary">POST</code> <code>/api/customers</code>
                                <small class="d-block text-muted">إضافة عميل جديد</small>
                            </div>
                            <div class="api-item mb-2 p-2 bg-light rounded">
                                <code class="text-success">GET</code> <code>/api/invoices</code>
                                <small class="d-block text-muted">جلب قائمة الفواتير</small>
                            </div>
                            <div class="api-item mb-2 p-2 bg-light rounded">
                                <code class="text-primary">POST</code> <code>/api/invoices</code>
                                <small class="d-block text-muted">إنشاء فاتورة جديدة</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>🧪 تجربة API:</h6>
                        <div class="api-test">
                            <select class="form-control mb-2" id="apiEndpoint">
                                <option value="customers">GET /api/customers</option>
                                <option value="invoices">GET /api/invoices</option>
                                <option value="payments">GET /api/payments</option>
                            </select>
                            <button class="btn btn-custom" onclick="testApi()">
                                <i class="fas fa-play"></i> تشغيل API
                            </button>
                            <div id="apiResult" class="mt-3 p-3 bg-dark text-light rounded" style="display: none;">
                                <pre id="apiOutput"></pre>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // Action functions
        function addCustomer() {
            const name = document.getElementById('customerName').value;
            const phone = document.getElementById('customerPhone').value;
            const email = document.getElementById('customerEmail').value;

            if (name && phone && email) {
                const newCustomer = {
                    id: customers.length + 1,
                    name: name,
                    phone: phone,
                    email: email,
                    balance: 0
                };

                customers.push(newCustomer);

                // Update customers list
                const customersList = document.getElementById('customersList');
                const newCard = document.createElement('div');
                newCard.className = 'card mb-2';
                newCard.innerHTML = `
                    <div class="card-body">
                        <h6>${name}</h6>
                        <p class="mb-1">الهاتف: ${phone}</p>
                        <p class="mb-1">البريد: ${email}</p>
                        <small class="text-info">الرصيد: 0 ريال</small>
                    </div>
                `;
                customersList.appendChild(newCard);

                // Clear form
                document.getElementById('customerForm').reset();

                // Show success message
                alert('✅ تم إضافة العميل بنجاح!');
            } else {
                alert('⚠️ يرجى ملء جميع الحقول');
            }
        }

        function createInvoice() {
            const customer = document.getElementById('invoiceCustomer').value;
            const newInvoice = {
                id: `INV-${String(invoices.length + 1).padStart(3, '0')}`,
                customer: customer,
                date: new Date().toISOString().split('T')[0],
                amount: Math.floor(Math.random() * 5000) + 1000,
                status: 'معلقة'
            };

            invoices.push(newInvoice);
            alert('✅ تم إنشاء الفاتورة بنجاح!');
            showInvoices(); // Refresh the view
        }

        function printInvoice(invoiceId) {
            const invoice = invoices.find(inv => inv.id === invoiceId);
            if (invoice) {
                const printContent = `
                    <div style="direction: rtl; text-align: right; font-family: Arial;">
                        <h2>🏭 مصنع الزجاج المتطور</h2>
                        <h3>فاتورة مبيعات</h3>
                        <hr>
                        <p><strong>رقم الفاتورة:</strong> ${invoice.id}</p>
                        <p><strong>العميل:</strong> ${invoice.customer}</p>
                        <p><strong>التاريخ:</strong> ${invoice.date}</p>
                        <p><strong>المبلغ:</strong> ${invoice.amount.toLocaleString()} ريال</p>
                        <p><strong>الحالة:</strong> ${invoice.status}</p>
                        <hr>
                        <p>شكراً لتعاملكم معنا</p>
                    </div>
                `;

                const printWindow = window.open('', '_blank');
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.print();
            }
        }

        function addPayment() {
            const customer = prompt('اسم العميل:');
            const amount = prompt('مبلغ الدفعة:');

            if (customer && amount) {
                const newPayment = {
                    id: payments.length + 1,
                    customer: customer,
                    amount: parseInt(amount),
                    date: new Date().toISOString().split('T')[0],
                    type: 'نقدي'
                };

                payments.push(newPayment);
                alert('✅ تم تسجيل الدفعة بنجاح!');
                showPayments(); // Refresh the view
            }
        }

        function testApi() {
            const endpoint = document.getElementById('apiEndpoint').value;
            const resultDiv = document.getElementById('apiResult');
            const outputPre = document.getElementById('apiOutput');

            let data;
            switch(endpoint) {
                case 'customers':
                    data = customers;
                    break;
                case 'invoices':
                    data = invoices;
                    break;
                case 'payments':
                    data = payments;
                    break;
                default:
                    data = { error: 'Unknown endpoint' };
            }

            outputPre.textContent = JSON.stringify(data, null, 2);
            resultDiv.style.display = 'block';
        }

        function exportPDF() {
            alert('📄 سيتم تصدير التقرير بصيغة PDF\n\nهذه وظيفة تجريبية - في النسخة الكاملة سيتم تصدير ملف PDF فعلي');
        }

        function exportExcel() {
            alert('📊 سيتم تصدير التقرير بصيغة Excel\n\nهذه وظيفة تجريبية - في النسخة الكاملة سيتم تصدير ملف Excel فعلي');
        }

        function exportCustomers() {
            alert('📊 سيتم تصدير قائمة العملاء بصيغة Excel\n\nهذه وظيفة تجريبية - في النسخة الكاملة سيتم تصدير ملف Excel فعلي');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add animation to cards
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // Show welcome message
            setTimeout(() => {
                showCustomers(); // Show customers by default
            }, 1000);
        });
    </script>
</body>
</html>