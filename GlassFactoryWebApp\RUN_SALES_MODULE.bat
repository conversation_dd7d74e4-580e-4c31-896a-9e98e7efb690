@echo off
title Glass Factory Web App - Sales Module Test
color 0A

echo.
echo ===============================================
echo   🏭 Glass Factory Accounting System
echo   💰 Sales Module - Ready for Testing
echo ===============================================
echo.

echo 📋 Sales Module Components:
echo ✅ Backend API (ASP.NET Core 8.0)
echo ✅ Frontend UI (React + TypeScript)
echo ✅ Database Models (PostgreSQL)
echo ✅ Sales Invoices Management
echo ✅ Customer Management
echo ✅ Customer Payments
echo ✅ Reports and Statistics
echo ✅ PDF Generation
echo ✅ Excel Export
echo.

echo 🚀 Starting Development Servers...
echo.

echo Starting Backend API...
start "Backend API" cmd /k "cd /d %~dp0 && dotnet run --urls=https://localhost:7001"

timeout /t 3 /nobreak >nul

echo Starting Frontend React App...
start "Frontend React" cmd /k "cd /d %~dp0\client && npm start"

timeout /t 3 /nobreak >nul

echo.
echo ===============================================
echo   🌐 Application URLs
echo ===============================================
echo.
echo 🔗 Frontend: http://localhost:3000
echo 🔗 Backend API: https://localhost:7001
echo 🔗 Swagger UI: https://localhost:7001/swagger
echo.

echo ===============================================
echo   📋 Sales Module Features
echo ===============================================
echo.
echo 💰 Sales Invoices:
echo   - Create new sales invoices
echo   - Edit existing invoices
echo   - Post/Unpost invoices
echo   - Print invoices to PDF
echo   - Search and filter invoices
echo.
echo 👥 Customer Management:
echo   - Add new customers
echo   - Edit customer information
echo   - View customer details
echo   - Customer credit limits
echo   - Customer statements
echo.
echo 💳 Customer Payments:
echo   - Record customer payments
echo   - Multiple payment methods
echo   - Payment tracking
echo   - Payment reports
echo.
echo 📊 Reports:
echo   - Sales statistics
echo   - Customer aging reports
echo   - Top customers report
echo   - Top products report
echo   - Export to PDF/Excel
echo.

echo ===============================================
echo   🧪 Testing Instructions
echo ===============================================
echo.
echo 1. Open browser and go to: http://localhost:3000
echo 2. Navigate to Sales Module
echo 3. Test the following features:
echo.
echo 📋 Sales Invoices:
echo   - Click "فاتورة جديدة" to create new invoice
echo   - Add customer and invoice items
echo   - Save and view the invoice
echo   - Test print functionality
echo.
echo 👥 Customers:
echo   - Click "عميل جديد" to add customer
echo   - Fill customer information
echo   - View customer list and details
echo.
echo 💳 Payments:
echo   - Record a payment for a customer
echo   - Test different payment methods
echo   - View payment history
echo.
echo 📊 Reports:
echo   - Generate sales reports
echo   - Export to PDF/Excel
echo   - View statistics dashboard
echo.

echo ===============================================
echo   🔧 Development Notes
echo ===============================================
echo.
echo 🗄️ Database: PostgreSQL (Auto-created)
echo 🔐 Authentication: JWT Tokens
echo 🌐 API: RESTful with Swagger documentation
echo 📱 UI: Responsive Material-UI design
echo 🌍 Language: Arabic RTL support
echo 📄 PDF: QuestPDF for invoice generation
echo 📊 Excel: ClosedXML for report export
echo.

echo ===============================================
echo   ⚠️ Important Notes
echo ===============================================
echo.
echo 1. Make sure PostgreSQL is running
echo 2. Database will be created automatically
echo 3. Sample data will be seeded on first run
echo 4. All features are fully functional
echo 5. Arabic text is properly supported (RTL)
echo.

echo Press any key to open the application in browser...
pause >nul

start http://localhost:3000/sales

echo.
echo 🎉 Sales Module is ready for testing!
echo.
echo To stop the servers:
echo - Close the Backend API window
echo - Close the Frontend React window
echo - Or press Ctrl+C in each window
echo.

pause
