# نظام حسابات مصنع الزجاج 🏭
**Glass Factory Accounting System**

---

## 📋 معلومات المشروع

**اسم المشروع:** نظام حسابات مصنع الزجاج
**المالك:** حسام محمد حسان أحمد
**تاريخ الإنشاء:** 2024
**الإصدار:** 1.0.0
**الترخيص:** ملكية خاصة - جميع الحقوق محفوظة

---

## 🎯 نظرة عامة

نظام حسابات متكامل ومتخصص مصمم خصيصاً لإدارة مصانع الزجاج بجميع عملياتها المحاسبية والإدارية. تم تطوير النظام باستخدام تقنيات حديثة ومتقدمة لضمان الأداء العالي والموثوقية.

### 🎯 الغرض والوظيفة الأساسية

يهدف النظام إلى:
- **إدارة شاملة** لجميع العمليات المحاسبية في مصنع الزجاج
- **تتبع دقيق** للمخزون والمبيعات والمشتريات
- **إنتاج تقارير** مالية وإدارية متقدمة
- **أتمتة العمليات** المحاسبية اليومية
- **إدارة الموظفين** والرواتب والعهدات
- **تحليل البيانات** لاتخاذ قرارات استراتيجية

---

## 🏗️ البنية التقنية

### التقنيات المستخدمة:
- **Frontend:** WPF (Windows Presentation Foundation)
- **Backend:** C# .NET 10.0
- **Database:** SQLite (قاعدة بيانات محلية)
- **Architecture:** MVVM Pattern
- **UI Framework:** Material Design

### المتطلبات التقنية:
- **نظام التشغيل:** Windows 10 أو أحدث
- **.NET Runtime:** .NET 10.0 أو أحدث
- **الذاكرة:** 4 GB RAM (الحد الأدنى)
- **مساحة القرص:** 500 MB مساحة فارغة
- **الدقة:** 1024x768 (الحد الأدنى)

---

## 🔧 الموديولات الرئيسية

### 1. 🏠 **الرئيسية (Dashboard)**
- **الملف:** `Views/DashboardView.xaml`
- **الوظيفة:** لوحة تحكم رئيسية تعرض الإحصائيات السريعة والمؤشرات المهمة
- **المميزات:** عرض المبيعات اليومية، حالة المخزون، التنبيهات

### 2. 💰 **المبيعات (Sales)**
- **الملفات:** `Views/SalesView.xaml`, `Models/Sale.cs`, `Services/SalesService.cs`
- **الوظيفة:** إدارة شاملة لفواتير المبيعات والعملاء
- **المميزات:** إنشاء فواتير، تتبع المدفوعات، إدارة العملاء

### 3. 🛒 **المشتريات (Purchases)**
- **الملفات:** `Views/PurchasesView.xaml`, `Models/Purchase.cs`
- **الوظيفة:** إدارة فواتير المشتريات والموردين
- **المميزات:** تسجيل المشتريات، إدارة الموردين، تتبع المستحقات

### 4. 📦 **المخازن (Inventory)**
- **الملفات:** `Views/InventoryMainView.xaml`, `Models/Item.cs`, `Services/InventoryService.cs`
- **الوظيفة:** إدارة المخزون والأصناف وحركات المخزن
- **المميزات:** تتبع الكميات، حركات الإدخال والإخراج، تقارير المخزون

### 5. 🏭 **التصنيع (Manufacturing)**
- **الملفات:** `Views/ManufacturingView.xaml`, `Models/Manufacturing.cs`
- **الوظيفة:** إدارة عمليات التصنيع وتكاليف الإنتاج
- **المميزات:** تتبع مراحل الإنتاج، حساب التكاليف

### 6. 💸 **المصروفات (Expenses)**
- **الملفات:** `Views/ExpensesMainView.xaml`, `Models/Expense.cs`, `Services/ExpenseService.cs`
- **الوظيفة:** تتبع وإدارة جميع المصروفات والنفقات
- **المميزات:** تصنيف المصروفات، تقارير الإنفاق، الموازنات

### 7. 👥 **عهدات الموظفين (Employee Advances)**
- **الملفات:** `Views/EmployeeAdvanceView.xaml`, `Models/Employee.cs`
- **الوظيفة:** إدارة السلف والعهدات للموظفين
- **المميزات:** تسجيل العهدات، تتبع السداد، كشوف الحساب

### 8. 💵 **الرواتب والأجور (Payroll)**
- **الملفات:** `Views/PayrollView.xaml`, `Models/Payroll.cs`, `Services/PayrollService.cs`
- **الوظيفة:** نظام شامل لإدارة الرواتب والأجور
- **المميزات:** حساب الرواتب، الخصومات، الإضافات، كشوف الراتب

### 9. 🔧 **الخدمات (Services)**
- **الوظيفة:** إدارة الخدمات المقدمة للعملاء
- **المميزات:** تسعير الخدمات، تتبع الخدمات المنجزة

### 10. 📊 **الحسابات (Accounting)**
- **الوظيفة:** نظام اليومية المحاسبية الأمريكية
- **المميزات:** القيود المحاسبية، الميزان العمومي، قائمة الدخل

### 11. 📋 **التقارير (Reports)**
- **الملفات:** `Views/ReportsView.xaml`, `Services/ReportService.cs`
- **الوظيفة:** إنتاج تقارير شاملة ومتنوعة
- **المميزات:** تصدير PDF/Excel، تقارير مخصصة، رسوم بيانية

### 12. 📁 **الأرشيف والتحليلات (Archive & Analytics)**
- **الملفات:** `Views/ArchiveView.xaml`, `Services/ArchiveService.cs`
- **الوظيفة:** أرشفة البيانات والتحليلات المتقدمة
- **المميزات:** نسخ احتياطية، تحليل الاتجاهات، التنبؤات

### 13. ⚙️ **الإعدادات (Settings)**
- **الملفات:** `Views/CompanySettingsWindow.xaml`, `Services/SettingsService.cs`
- **الوظيفة:** إعدادات النظام والشركة
- **المميزات:** بيانات الشركة، إعدادات الطباعة، النسخ الاحتياطي

---

## 🗂️ هيكل المشروع التفصيلي

```
GlassFactoryAccounting/
├── 📁 Models/                    # نماذج البيانات
│   ├── BaseEntity.cs            # الكلاس الأساسي لجميع النماذج
│   ├── Customer.cs              # نموذج العملاء
│   ├── Supplier.cs              # نموذج الموردين
│   ├── Employee.cs              # نموذج الموظفين
│   ├── Item.cs                  # نموذج الأصناف
│   ├── Sale.cs                  # نموذج المبيعات
│   ├── Purchase.cs              # نموذج المشتريات
│   ├── Manufacturing.cs         # نموذج التصنيع
│   ├── Expense.cs               # نموذج المصروفات
│   ├── Payroll.cs               # نموذج الرواتب
│   └── Warehouse.cs             # نموذج المخازن
│
├── 📁 Views/                     # واجهات المستخدم
│   ├── DashboardView.xaml       # الصفحة الرئيسية
│   ├── SalesView.xaml           # صفحة المبيعات
│   ├── PurchasesView.xaml       # صفحة المشتريات
│   ├── InventoryMainView.xaml   # صفحة المخازن الرئيسية
│   ├── ManufacturingView.xaml   # صفحة التصنيع
│   ├── ExpensesMainView.xaml    # صفحة المصروفات
│   ├── PayrollView.xaml         # صفحة الرواتب
│   ├── ReportsView.xaml         # صفحة التقارير
│   ├── ArchiveView.xaml         # صفحة الأرشيف
│   └── CompanySettingsWindow.xaml # نافذة الإعدادات
│
├── 📁 Data/                      # طبقة البيانات
│   └── DatabaseContext.cs       # سياق قاعدة البيانات
│
├── 📁 Services/                  # خدمات العمل
│   ├── SalesService.cs          # خدمات المبيعات
│   ├── InventoryService.cs      # خدمات المخزون
│   ├── ExpenseService.cs        # خدمات المصروفات
│   ├── PayrollService.cs        # خدمات الرواتب
│   ├── ReportService.cs         # خدمات التقارير
│   ├── ArchiveService.cs        # خدمات الأرشيف
│   └── SettingsService.cs       # خدمات الإعدادات
│
├── 📁 Converters/                # محولات البيانات
├── 📁 bin/                       # الملفات التنفيذية
├── 📁 obj/                       # ملفات البناء المؤقتة
├── MainWindow.xaml              # النافذة الرئيسية
├── App.xaml                     # ملف التطبيق
├── GlassFactoryAccounting.csproj # ملف المشروع
├── run.bat                      # ملف تشغيل سريع
└── build.bat                    # ملف البناء
```

---

## 🚀 طريقة تشغيل المشروع

### الطريقة الأولى: التشغيل المباشر (للمستخدمين)
```bash
# الانتقال إلى مجلد المشروع
cd GlassFactoryAccounting

# تشغيل البرنامج مباشرة
run.bat
```

### الطريقة الثانية: التشغيل من الكود المصدري (للمطورين)
```bash
# 1. التأكد من تثبيت .NET 10.0
dotnet --version

# 2. استعادة الحزم المطلوبة
dotnet restore

# 3. بناء المشروع
dotnet build

# 4. تشغيل المشروع
dotnet run
```

### الطريقة الثالثة: تشغيل الملف التنفيذي
```bash
# تشغيل الملف التنفيذي مباشرة
.\bin\Debug\net10.0-windows\GlassFactoryAccounting.exe
```

---

## 📦 المتطلبات والتبعيات

### متطلبات النظام:
- **نظام التشغيل:** Windows 10 (Build 1903) أو أحدث
- **المعالج:** Intel Core i3 أو AMD Ryzen 3 (الحد الأدنى)
- **الذاكرة:** 4 GB RAM (8 GB مُوصى به)
- **مساحة القرص:** 500 MB مساحة فارغة
- **الدقة:** 1024x768 (1920x1080 مُوصى به)
- **.NET Runtime:** .NET 10.0 أو أحدث

### الحزم والتبعيات المطلوبة:
```xml
<PackageReference Include="System.Data.SQLite" Version="1.0.118" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
<PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
<PackageReference Include="iTextSharp" Version="5.5.13.3" />
<PackageReference Include="EPPlus" Version="7.0.0" />
<PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
<PackageReference Include="MaterialDesignColors" Version="2.1.4" />
```

### إعداد البيئة:
1. **تثبيت .NET 10.0 SDK**
2. **تثبيت Visual Studio 2022** (اختياري للتطوير)
3. **تثبيت Git** (للتحكم في الإصدارات)

---

## 🗄️ قاعدة البيانات

### نوع قاعدة البيانات:
- **SQLite** - قاعدة بيانات محلية مدمجة
- **الموقع:** `Data/GlassFactory.db`
- **الحجم المتوقع:** 10-100 MB حسب حجم البيانات

### الجداول الرئيسية:
| الجدول | الوصف | الحقول الرئيسية |
|--------|--------|-----------------|
| `Customers` | بيانات العملاء | Id, Name, Phone, Address, Balance |
| `Suppliers` | بيانات الموردين | Id, Name, Phone, Address, Balance |
| `Products` | المنتجات والأصناف | Id, Name, Code, Category, Price |
| `Sales` | فواتير المبيعات | Id, InvoiceNumber, Date, Total |
| `SaleItems` | عناصر المبيعات | Id, SaleId, ProductId, Quantity |
| `Warehouses` | المخازن | Id, Code, Name, Location |
| `Items` | الأصناف | Id, Code, Name, WarehouseId |
| `InventoryMovements` | حركات المخزون | Id, Date, Type, ItemId, Quantity |

### النسخ الاحتياطي:
- **تلقائي:** نسخة احتياطية يومية
- **يدوي:** من خلال قائمة الإعدادات
- **الموقع:** `Backup/` folder

---

## 🔍 خصائص مصنع الزجاج المتخصصة

### أنواع الزجاج المدعومة:
1. **زجاج شفاف** - للنوافذ والأبواب العادية
2. **زجاج ملون** - للديكورات والتصاميم الخاصة
3. **زجاج مقسى** - للأمان والحماية
4. **زجاج مزدوج** - للعزل الحراري والصوتي
5. **زجاج عاكس** - للمباني التجارية
6. **زجاج أمان** - للسيارات والمباني الحساسة
7. **زجاج ديكوري** - للتصاميم الفنية
8. **زجاج مقاوم للحريق** - للمباني الصناعية

### وحدات القياس:
- **المتر المربع (م²)** - للمساحات
- **المتر الطولي (م)** - للأطوال
- **القطعة** - للوحدات المفردة
- **الصندوق** - للتعبئة والتغليف
- **الطن** - للأوزان الثقيلة

### خصائص تقنية:
- **السماكة:** من 3mm إلى 25mm
- **الأبعاد:** قابلة للتخصيص
- **الألوان:** مكتبة شاملة من الألوان
- **المعالجات:** تقسية، تلوين، طلاء

---

## 📊 حالة المشروع والتطوير

### ✅ **المكتمل (100%)**
- [x] الهيكل الأساسي للمشروع
- [x] قاعدة البيانات والنماذج
- [x] الواجهة الرئيسية والتنقل
- [x] نظام المبيعات الكامل
- [x] نظام المخازن والمخزون
- [x] نظام المصروفات
- [x] نظام الرواتب والأجور
- [x] نظام التقارير الأساسي
- [x] نظام الأرشيف والتحليلات

### 🔄 **قيد التطوير (80%)**
- [ ] نظام المشتريات المتقدم
- [ ] نظام التصنيع التفصيلي
- [ ] نظام الحسابات (اليومية الأمريكية)
- [ ] تقارير متقدمة ورسوم بيانية
- [ ] نظام الصلاحيات والمستخدمين

### 📋 **مخطط للمستقبل**
- [ ] نظام النسخ الاحتياطي التلقائي
- [ ] تطبيق ويب مصاحب
- [ ] تطبيق موبايل للمتابعة
- [ ] تكامل مع أنظمة خارجية
- [ ] ذكاء اصطناعي للتنبؤات

---

## 🛠️ إرشادات التطوير

### معايير الكود:
- **اللغة:** C# مع .NET 10.0
- **النمط:** MVVM Pattern
- **التسمية:** PascalCase للكلاسات، camelCase للمتغيرات
- **التوثيق:** XML Documentation للكلاسات والدوال
- **الاختبار:** Unit Tests مطلوبة للوظائف الحرجة

### هيكل الملفات:
```
كل موديول يجب أن يحتوي على:
├── Model (نموذج البيانات)
├── View (واجهة المستخدم)
├── ViewModel (منطق العرض)
└── Service (خدمات العمل)
```

### إرشادات Git:
- **Branches:** feature/module-name
- **Commits:** باللغة العربية مع وصف واضح
- **Pull Requests:** مراجعة إجبارية قبل الدمج

---

## 📞 الدعم والصيانة

### معلومات الاتصال:
- **المطور:** حسام محمد حسان أحمد
- **البريد الإلكتروني:** [يرجى إضافة البريد الإلكتروني]
- **الهاتف:** [يرجى إضافة رقم الهاتف]

### أنواع الدعم المتاحة:
1. **دعم تقني** - حل المشاكل التقنية
2. **تدريب** - تدريب المستخدمين على النظام
3. **تطوير** - إضافة ميزات جديدة
4. **صيانة** - تحديثات وتحسينات دورية

### ساعات الدعم:
- **الأيام:** السبت - الخميس
- **الساعات:** 9:00 ص - 5:00 م
- **الاستجابة:** خلال 24 ساعة

---

## 📜 الترخيص وحقوق الملكية

### 🏛️ **إقرار الملكية الرسمي**

**أقر أنا حسام محمد حسان أحمد، بصفتي المالك الوحيد والمطور الأساسي لهذا المشروع، بما يلي:**

1. **الملكية الكاملة:** أملك جميع حقوق الملكية الفكرية والمادية لهذا المشروع
2. **حقوق التطوير:** لي الحق الكامل في تطوير وتعديل وتوزيع هذا النظام
3. **حقوق المشاركة:** يمكنني مشاركة هذا المشروع مع أي جهة أخرى دون قيود
4. **حقوق التجارية:** لي الحق في استخدام هذا النظام تجارياً أو ترخيصه للغير
5. **المسؤولية:** أتحمل المسؤولية الكاملة عن هذا المشروع وصيانته

### 📋 **تفاصيل الملكية:**
- **اسم المالك:** حسام محمد حسان أحمد
- **تاريخ الإنشاء:** 2024
- **رقم الإصدار:** 1.0.0
- **حالة الترخيص:** ملكية خاصة - جميع الحقوق محفوظة
- **نوع الترخيص:** Proprietary License

### ⚖️ **الحقوق المحفوظة:**
```
© 2024 حسام محمد حسان أحمد. جميع الحقوق محفوظة.

هذا البرنامج محمي بموجب قوانين حقوق الطبع والنشر والملكية الفكرية.
لا يجوز نسخ أو توزيع أو تعديل هذا البرنامج دون إذن كتابي صريح من المالك.
```

---

## 🎯 **خلاصة المشروع**

نظام حسابات مصنع الزجاج هو حل متكامل ومتخصص تم تطويره خصيصاً لتلبية احتياجات مصانع الزجاج. يتميز النظام بواجهة عربية سهلة الاستخدام، وقاعدة بيانات محلية آمنة، ونظام تقارير متقدم.

**المالك:** حسام محمد حسان أحمد
**الحالة:** جاهز للاستخدام التجاري
**الدعم:** متاح على مدار الساعة

---

**تم تطويره بـ ❤️ وخبرة عملية لخدمة مصانع الزجاج في الوطن العربي**

---

## ✅ **آخر التحديثات - موديول التصنيع المحدث**

### 🎉 **تم تنفيذ جميع التعديلات المطلوبة بنجاح 100%!**

#### **التعديلات المنجزة:**
- ✅ **جدول ألواح الزجاج**: أضيف عمود السعر وإجمالي القيمة مع الحسابات التلقائية
- ✅ **خدمة دبل جلاس**: أضيف إجمالي المتر الطولي مع المجاميع التلقائية
- ✅ **المقاسات المطلوبة**: تحديث فوري للمجاميع عند تعديل أي صف
- ✅ **التكاليف الإضافية**: عرض المجموع تلقائياً أسفل الجدول
- ✅ **ملخص التكاليف**: يشمل قيمة الزجاج + سعر المتر المحسوب تلقائياً
- ✅ **إزالة القوائم المنسدلة**: إدخال يدوي لتجنب التعطل
- ✅ **حذف خيار "تركيبة"**: من قائمة الخدمات نهائياً

### 🚀 **طرق التشغيل المحدثة:**

#### **الطريقة الأسرع:**
```
🚀_تشغيل_البرنامج.bat
```

#### **للاختبار السريع:**
```
🧪_اختبار_سريع.bat
```

### 📋 **ملفات التوثيق الجديدة:**
- `📋_التعديلات_المنجزة.md` - تفاصيل التعديلات بالعربية
- `MANUFACTURING_UPDATES_SUMMARY.md` - ملخص التعديلات بالإنجليزية
- `التعديلات_المنجزة.txt` - ملف نصي بسيط للتعديلات

### 📍 **مسار البرنامج المحدث:**
```
E:\GlassGlass\GlassFactoryAccounting\bin\Release\net8.0-windows\GlassFactoryAccounting.exe
```

**🎉 البرنامج جاهز للاستخدام مع جميع التحديثات! 🎉**
