<UserControl x:Class="GlassFactoryAccounting.Views.ExpenseReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="FilterButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#3498DB"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20" Background="#F5F5F5">
        <StackPanel>
            <!-- العنوان -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="تقرير المصروفات" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                        <TextBlock Text="جميع المصروفات ما عدا المرتبطة بمشاريع" FontSize="14" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- فلاتر البحث -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="🔍 فلاتر البحث" FontSize="16" FontWeight="Bold" 
                               Margin="0,0,0,15" Foreground="#2C3E50"/>

                    <!-- الصف الأول من الفلاتر -->
                    <Grid Grid.Row="1" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- من تاريخ -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="من تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="DpFromDate" Height="35"/>
                        </StackPanel>

                        <!-- إلى تاريخ -->
                        <StackPanel Grid.Column="1" Margin="10,0,10,0">
                            <TextBlock Text="إلى تاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <DatePicker x:Name="DpToDate" Height="35"/>
                        </StackPanel>

                        <!-- نوع المصروف -->
                        <StackPanel Grid.Column="2" Margin="10,0,10,0">
                            <TextBlock Text="نوع المصروف:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CmbExpenseTypeFilter" Height="35"/>
                        </StackPanel>

                        <!-- الفرع -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                            <TextBlock Text="الفرع:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CmbBranchFilter" Height="35"/>
                        </StackPanel>
                    </Grid>

                    <!-- الصف الثاني من الفلاتر -->
                    <Grid Grid.Row="2" Margin="0,0,0,15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- المسؤول -->
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="المسؤول:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CmbResponsiblePersonFilter" Height="35"/>
                        </StackPanel>

                        <!-- طريقة السداد -->
                        <StackPanel Grid.Column="1" Margin="10,0,10,0">
                            <TextBlock Text="طريقة السداد:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CmbPaymentMethodFilter" Height="35"/>
                        </StackPanel>

                        <!-- حالة المصروف -->
                        <StackPanel Grid.Column="2" Margin="10,0,10,0">
                            <TextBlock Text="حالة المصروف:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <ComboBox x:Name="CmbExpenseStatusFilter" Height="35"/>
                        </StackPanel>

                        <!-- مساحة فارغة -->
                        <StackPanel Grid.Column="3" Margin="10,0,0,0">
                        </StackPanel>
                    </Grid>

                    <!-- أزرار الفلاتر -->
                    <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="BtnFilter" Content="🔍 تطبيق الفلتر" 
                                Style="{StaticResource FilterButtonStyle}"
                                Width="120" Height="35" Margin="0,0,10,0" Click="BtnFilter_Click"/>
                        <Button x:Name="BtnClearFilter" Content="🗑️ مسح الفلتر" 
                                Style="{StaticResource FilterButtonStyle}"
                                Background="#95A5A6" Width="120" Height="35" Click="BtnClearFilter_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- إحصائيات سريعة -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <!-- إجمالي المصروفات -->
                    <Border Grid.Column="0" Background="#E74C3C" CornerRadius="8" Padding="15" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="💰" FontSize="24" HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock Text="إجمالي المصروفات" FontSize="12" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock x:Name="TxtTotalAmount" Text="0.00" FontSize="16" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- عدد المصروفات -->
                    <Border Grid.Column="1" Background="#27AE60" CornerRadius="8" Padding="15" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock Text="عدد المصروفات" FontSize="12" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock x:Name="TxtTotalCount" Text="0" FontSize="16" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- متوسط المصروف -->
                    <Border Grid.Column="2" Background="#F39C12" CornerRadius="8" Padding="15" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="📊" FontSize="24" HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock Text="متوسط المصروف" FontSize="12" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock x:Name="TxtAverageAmount" Text="0.00" FontSize="16" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                        </StackPanel>
                    </Border>

                    <!-- أكبر مصروف -->
                    <Border Grid.Column="3" Background="#9B59B6" CornerRadius="8" Padding="15" Margin="5">
                        <StackPanel HorizontalAlignment="Center">
                            <TextBlock Text="⬆️" FontSize="24" HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock Text="أكبر مصروف" FontSize="12" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                            <TextBlock x:Name="TxtMaxAmount" Text="0.00" FontSize="16" FontWeight="Bold" 
                                       HorizontalAlignment="Center" Foreground="White"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </Border>

            <!-- جدول المصروفات -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="📋 قائمة المصروفات" FontSize="18" FontWeight="Bold" 
                               Margin="0,0,0,15" Foreground="#2C3E50"/>

                    <DataGrid Grid.Row="1" x:Name="DgExpenseRecords" AutoGenerateColumns="False"
                              CanUserAddRows="False" CanUserDeleteRows="False"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              Background="White" RowBackground="#FAFAFA" AlternatingRowBackground="#F0F0F0"
                              BorderBrush="#E0E0E0" BorderThickness="1"
                              FontSize="12" RowHeight="35" MinHeight="400">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المصروف الرئيسي" Binding="{Binding MainExpenseName}" Width="150" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المصروف الفرعي" Binding="{Binding SubExpenseName}" Width="150" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="الفرع" Binding="{Binding BranchName}" Width="120" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المبلغ" Binding="{Binding Amount, StringFormat=N2}" Width="100" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="Foreground" Value="#E74C3C"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="التاريخ" Binding="{Binding DateTime, StringFormat=yyyy-MM-dd}" Width="100" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المسؤول" Binding="{Binding ResponsiblePersonName}" Width="120" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="طريقة السداد" Binding="{Binding PaymentMethod}" Width="100" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="الحالة" Binding="{Binding ExpenseStatus}" Width="80" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Right"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="TextWrapping" Value="Wrap"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="100">
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8"/>
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="✏️" ToolTip="تعديل" Width="25" Height="25" 
                                                    Background="#3498DB" Foreground="White" BorderThickness="0"
                                                    Margin="1" Click="BtnEdit_Click"/>
                                            <Button Content="🗑️" ToolTip="حذف" Width="25" Height="25" 
                                                    Background="#E74C3C" Foreground="White" BorderThickness="0"
                                                    Margin="1" Click="BtnDelete_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
