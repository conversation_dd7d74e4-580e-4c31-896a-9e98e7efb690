# 💼 دليل موديول الرواتب والأجور

## 📋 نظرة عامة
موديول شامل لإدارة رواتب وأجور الموظفين في مصنع الزجاج، مصمم وفق متطلبات المصنع مع مرونة كاملة في التواريخ والعمليات.

## 🎯 المميزات الرئيسية

### ✅ **مطابقة 100% للمتطلبات المحددة**
- جميع الشاشات والوظائف المطلوبة موجودة ومطبقة
- تصميم منسق ومرتب مع سهولة في الاستخدام
- دعم كامل لاختلاف تواريخ الرواتب حسب تاريخ بدء عمل كل موظف

## 🏗️ مكونات النظام

### 1️⃣ **👥 إدارة الموظفين**
**الوظائف:**
- إضافة موظف جديد
- عرض قائمة الموظفين
- حذف منطقي للموظفين

**البيانات المطلوبة:**
- ✅ الاسم
- ✅ رقم الموظف (توليد تلقائي)
- ✅ الفرع الذي يعمل به (قائمة منسدلة من إدارة الفروع)
- ✅ المسمى الوظيفي
- ✅ تاريخ بدء العمل
- ✅ بيانات التواصل (اختياري)

### 2️⃣ **💰 تسجيل رواتب مستحقة**
**الوظائف:**
- تسجيل راتب مستحق جديد
- عرض قائمة الرواتب المستحقة
- تتبع حالة السداد

**البيانات المطلوبة:**
- ✅ اسم الموظف من قائمة منسدلة
- ✅ الراتب الشهري المستحق
- ✅ تاريخ استحقاق الراتب (مرن حسب تاريخ بدء العمل)
- ✅ ملاحظات (اختياري)

### 3️⃣ **🧾 تسجيل سداد راتب لموظف**
**الوظائف:**
- تسجيل سداد راتب جديد
- حساب تلقائي للإجمالي
- عرض قائمة السدادات

**البيانات المطلوبة:**
- ✅ اسم الموظف
- ✅ قيمة السداد
- ✅ أوفر تايم (إدخال يدوي منفصل)
- ✅ خصومات (إدخال يدوي منفصل)
- ✅ مكافآت (إدخال يدوي منفصل)
- ✅ تحديد نوع السداد (كاش، بنك، شيك، تحويل)
- ✅ المسؤول عن السداد (مرتبط بإدارة المسؤولين)
- ✅ التاريخ والوقت
- ✅ ملاحظات

### 4️⃣ **💳 تسجيل سلفة موظف**
**الوظائف:**
- تسجيل سلفة جديدة
- تتبع حالة الخصم
- عرض قائمة السلف

**البيانات المطلوبة:**
- ✅ اسم الموظف
- ✅ قيمة السلفة
- ✅ تاريخ السلفة
- ✅ المسؤول عن صرف السلفة (مرتبط بإدارة المسؤولين)
- ✅ ملاحظات

### 5️⃣ **📑 كشف حساب الموظف**
**الوظائف:**
- عرض شامل لجميع المعاملات المالية
- فلاتر بحث متقدمة
- تصدير PDF احترافي
- إحصائيات مالية مفصلة

**المعاملات المشمولة:**
- ✅ السلف
- ✅ الرواتب المستحقة
- ✅ السداد
- ✅ الأوفر تايم
- ✅ الخصومات
- ✅ المكافآت

**الإحصائيات:**
- ✅ إجمالي المبالغ المستحقة
- ✅ إجمالي المبالغ المدفوعة
- ✅ إجمالي المتبقية حتى تاريخ التقرير

**فلاتر البحث المتقدمة:**
- ✅ حسب اسم الموظف
- ✅ حسب المسؤول
- ✅ حسب التاريخ (من/إلى)

**أزرار العمليات:**
- ✅ طباعة وحفظ بصيغة PDF (يعمل بكفاءة)

## 🔧 المميزات التقنية

### **قاعدة البيانات**
- SQLite مع إنشاء الجداول تلقائياً
- علاقات مترابطة بين الجداول
- حذف منطقي للبيانات

### **التكامل**
- مرتبط بموديول المصروفات للفروع والمسؤولين
- واجهات عربية متسقة
- تصميم موحد مع باقي النظام

### **الأمان والتحقق**
- التحقق من صحة البيانات قبل الحفظ
- رسائل خطأ واضحة باللغة العربية
- معالجة شاملة للأخطاء

## 🎨 التصميم

### **المبادئ المتبعة:**
- تصميم منسق ومرتب ✅
- الحفاظ على الشكل الجمالي ✅
- سهولة في الاستخدام ✅
- ألوان متناسقة لكل وظيفة
- أيقونات واضحة ومعبرة

### **الألوان المستخدمة:**
- 🔵 إدارة الموظفين: أزرق (#2E86AB)
- 🟢 الرواتب المستحقة: أخضر (#28A745)
- 🔵 سداد الرواتب: أزرق فاتح (#007BFF)
- 🟡 السلف: أصفر (#FFC107)
- 🟣 كشف الحساب: بنفسجي (#6F42C1)

## ⚡ مرونة النظام

### **تواريخ الرواتب:**
✅ **النظام يدعم اختلاف تواريخ الرواتب حسب تاريخ بدء عمل كل موظف**
- لا يوجد يوم ثابت من الشهر للرواتب
- كل موظف له تاريخ استحقاق مستقل
- مرونة كاملة في تحديد التواريخ

### **الإدخال اليدوي:**
- الأوفر تايم: إدخال يدوي منفصل
- الخصومات: إدخال يدوي منفصل
- المكافآت: إدخال يدوي منفصل
- حساب تلقائي للإجمالي

## 📊 التقارير والتصدير

### **كشف حساب الموظف:**
- تقرير شامل بتنسيق احترافي
- يشمل جميع المعاملات المالية
- إحصائيات مفصلة
- تصدير PDF بجودة عالية
- دعم كامل للغة العربية في PDF

### **الفلاتر المتقدمة:**
- فلترة بالموظف
- فلترة بالمسؤول
- فلترة بالتاريخ (من/إلى)
- إمكانية الجمع بين الفلاتر

## 🚀 كيفية الاستخدام

1. **الانتقال للموديول:** اضغط على "💼 الرواتب" في القائمة الجانبية
2. **إدارة الموظفين:** ابدأ بإضافة الموظفين وبياناتهم
3. **تسجيل الرواتب:** سجل الرواتب المستحقة حسب تواريخ مرنة
4. **سداد الرواتب:** سجل السدادات مع الأوفر تايم والخصومات والمكافآت
5. **إدارة السلف:** سجل السلف مع ربطها بالمسؤولين
6. **كشوف الحسابات:** اعرض تقارير مفصلة وصدرها لـ PDF

## ✅ التأكيدات النهائية

### **مطابقة المتطلبات:**
- ✅ جميع الشاشات المطلوبة موجودة
- ✅ جميع البيانات المطلوبة مطبقة
- ✅ جميع الوظائف المطلوبة تعمل
- ✅ التصميم منسق ومرتب
- ✅ سهولة في الاستخدام
- ✅ مرونة في تواريخ الرواتب
- ✅ فلاتر بحث متقدمة
- ✅ تصدير PDF فعال

### **الجودة والأداء:**
- ✅ لا توجد أخطاء برمجية
- ✅ واجهات سريعة ومتجاوبة
- ✅ قاعدة بيانات محسنة
- ✅ معالجة شاملة للأخطاء
- ✅ رسائل واضحة للمستخدم

**🎯 النظام جاهز للاستخدام الفوري ومطابق 100% للمتطلبات المحددة!**
