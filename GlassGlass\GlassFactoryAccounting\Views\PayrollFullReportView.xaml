<UserControl x:Class="GlassFactoryAccounting.Views.PayrollFullReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             mc:Ignorable="d"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">

    <UserControl.Resources>
        <!-- الألوان والأنماط -->
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="LightBrush" Color="#F5F5F5"/>

        <!-- نمط البطاقات -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط الأزرار -->
        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,10"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="MinWidth" Value="100"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                CornerRadius="5"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"
                                            Margin="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#0D47A1"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- نمط زر النجاح -->
        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource SuccessBrush}"/>
        </Style>

        <!-- نمط زر التحذير -->
        <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource WarningBrush}"/>
        </Style>

        <!-- نمط زر الخطر -->
        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ButtonStyle}">
            <Setter Property="Background" Value="{StaticResource DangerBrush}"/>
        </Style>

        <!-- نمط موحد لحقول الإدخال (TextBox, ComboBox, DatePicker) -->
        <Style x:Key="UnifiedInputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="12,6"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>
        <Style x:Key="UnifiedDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Right"/>
        </Style>

        <!-- نمط التسميات -->
        <Style x:Key="LabelStyle" TargetType="TextBlock">
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="8,8,8,0"/>
            <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <Grid Background="{StaticResource LightBrush}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}" Margin="10,10,10,5">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="📊" FontSize="28" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <TextBlock Text="التقرير الكامل للرواتب والأجور" FontSize="22" FontWeight="Bold"
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- فلاتر التقرير -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- من تاريخ -->
                <TextBlock Grid.Row="0" Grid.Column="0" Text="من تاريخ:" Style="{StaticResource LabelStyle}"/>
                <DatePicker x:Name="DpFromDate" Grid.Row="1" Grid.Column="0" Margin="8" Style="{StaticResource UnifiedDatePickerStyle}"/>

                <!-- إلى تاريخ -->
                <TextBlock Grid.Row="0" Grid.Column="1" Text="إلى تاريخ:" Style="{StaticResource LabelStyle}"/>
                <DatePicker x:Name="DpToDate" Grid.Row="1" Grid.Column="1" Margin="8" Style="{StaticResource UnifiedDatePickerStyle}"/>

                <!-- الموظف -->
                <TextBlock Grid.Row="0" Grid.Column="2" Text="الموظف (اختياري):" Style="{StaticResource LabelStyle}"/>
                <ComboBox x:Name="CmbEmployee" Grid.Row="1" Grid.Column="2" Margin="8" Style="{StaticResource UnifiedComboBoxStyle}"/>

                <!-- أزرار الفلترة -->
                <StackPanel Grid.Row="1" Grid.Column="3" Orientation="Horizontal" VerticalAlignment="Center" Margin="8">
                    <Button x:Name="BtnFilter" Content="🔍 فلترة" Style="{StaticResource SuccessButtonStyle}" Click="BtnFilter_Click"/>
                    <Button x:Name="BtnReset" Content="🔄 إعادة تعيين" Style="{StaticResource ButtonStyle}" Click="BtnReset_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- التقرير الشامل -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto" Height="680">
                <DataGrid x:Name="FullReportDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="45"
                      SelectionChanged="FullReportDataGrid_SelectionChanged">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="كود الموظف" Binding="{Binding EmployeeCode}" Width="100"/>
                        <DataGridTextColumn Header="اسم الموظف" Binding="{Binding EmployeeName}" Width="150"/>
                        <DataGridTextColumn Header="الوظيفة" Binding="{Binding Position}" Width="120"/>
                        <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary, StringFormat='{}{0:N2}'}" Width="120"/>
                        <DataGridTextColumn Header="الرواتب المستحقة" Binding="{Binding TotalSalaryDue, StringFormat='{}{0:N2}'}" Width="130"/>
                        <DataGridTextColumn Header="السلف" Binding="{Binding TotalAdvances, StringFormat='{}{0:N2}'}" Width="100"/>
                        <DataGridTextColumn Header="المكافآت" Binding="{Binding TotalBonuses, StringFormat='{}{0:N2}'}" Width="100"/>
                        <DataGridTextColumn Header="الخصومات" Binding="{Binding TotalDeductions, StringFormat='{}{0:N2}'}" Width="100"/>
                        <DataGridTextColumn Header="الوقت الإضافي" Binding="{Binding TotalOvertime, StringFormat='{}{0:N2}'}" Width="120"/>
                        <DataGridTextColumn Header="المدفوعات" Binding="{Binding TotalPayments, StringFormat='{}{0:N2}'}" Width="120"/>
                        <DataGridTextColumn Header="المتبقي" Binding="{Binding RemainingBalance, StringFormat='{}{0:N2}'}" Width="100"/>
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="⚖️ تسوية" Style="{StaticResource WarningButtonStyle}"
                                                Click="BtnSettle_Click" Tag="{Binding EmployeeId}" Margin="2"/>
                                        <Button Content="📄 PDF" Style="{StaticResource DangerButtonStyle}"
                                                Click="BtnExportPDF_Click" Tag="{Binding EmployeeId}" Margin="2"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                    </DataGrid>
                </ScrollViewer>
            </Border>

        <!-- الأزرار -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}" Margin="10,5,10,10">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnRefresh" Content="🔄 تحديث البيانات" Style="{StaticResource SuccessButtonStyle}" Click="BtnRefresh_Click"/>
                <Button x:Name="BtnExportAll" Content="📊 تصدير تقرير شامل" Style="{StaticResource WarningButtonStyle}" Click="BtnExportAll_Click"/>
                <Button x:Name="BtnBack" Content="🔙 العودة" Style="{StaticResource ButtonStyle}" Click="BtnBack_Click"/>
            </StackPanel>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
