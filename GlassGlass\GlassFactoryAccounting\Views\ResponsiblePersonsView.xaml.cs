using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class ResponsiblePersonsView : UserControl
    {
        private readonly CompanyManagementService _companyService;
        private List<ResponsiblePerson> _responsiblePersons;

        public ResponsiblePersonsView()
        {
            InitializeComponent();
            _companyService = new CompanyManagementService();
            _responsiblePersons = new List<ResponsiblePerson>();
            LoadResponsiblePersons();
        }

        private void LoadResponsiblePersons()
        {
            try
            {
                _responsiblePersons = _companyService.GetAllResponsiblePersons();
                DgResponsiblePersons.ItemsSource = _responsiblePersons;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المسؤولين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddPerson_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var personName = TxtPersonName.Text.Trim();

                // التحقق من عدم تكرار الاسم
                if (_companyService.IsPersonNameExists(personName))
                {
                    MessageBox.Show($"يوجد مسؤول بنفس الاسم '{personName}' مسبقاً!\nيرجى اختيار اسم آخر.",
                        "اسم مكرر", MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtPersonName.Focus();
                    return;
                }

                var person = new ResponsiblePerson
                {
                    Name = personName,
                    Position = TxtPosition.Text.Trim(),
                    Department = TxtDepartment.Text.Trim(),
                    Phone = TxtPhone.Text.Trim(),
                    CreatedBy = "المستخدم الحالي",
                    CreatedDate = DateTime.Now,
                    IsActive = true
                };

                var result = _companyService.AddResponsiblePerson(person);

                if (result)
                {
                    MessageBox.Show("تم إضافة المسؤول بنجاح!", "نجح الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                    LoadResponsiblePersons();
                }
                else
                {
                    MessageBox.Show($"فشل في إضافة المسؤول '{person.Name}'!\n\nالأسباب المحتملة:\n" +
                        "• قاعدة البيانات غير متاحة\n" +
                        "• خطأ في الاتصال\n" +
                        "• مشكلة في صيغة البيانات\n\n" +
                        "يرجى المحاولة مرة أخرى أو الاتصال بالدعم الفني.",
                        "فشل في الحفظ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المسؤول: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtPersonName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المسؤول", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtPersonName.Focus();
                return false;
            }

            // التحقق من عدم تكرار الاسم
            if (_responsiblePersons.Any(p => p.Name.Equals(TxtPersonName.Text.Trim(), StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("اسم المسؤول موجود بالفعل", "بيانات مكررة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtPersonName.Focus();
                return false;
            }

            return true;
        }

        private void BtnClearForm_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            TxtPersonName.Clear();
            TxtPosition.Clear();
            TxtDepartment.Clear();
            TxtPhone.Clear();
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ResponsiblePerson person)
            {
                // تعبئة النموذج بالبيانات للتعديل
                TxtPersonName.Text = person.Name;
                TxtPosition.Text = person.Position ?? "";
                TxtDepartment.Text = person.Department ?? "";
                TxtPhone.Text = person.Phone ?? "";

                MessageBox.Show($"تم تحديد المسؤول: {person.Name} للتعديل\nيمكنك الآن تعديل البيانات والضغط على إضافة المسؤول", 
                    "تعديل المسؤول", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ResponsiblePerson person)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف المسؤول: {person.Name}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = _companyService.DeleteResponsiblePerson(person.Id);
                        
                        if (deleteResult)
                        {
                            MessageBox.Show("تم حذف المسؤول بنجاح!", "نجح الحذف", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadResponsiblePersons();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المسؤول!", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف المسؤول: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
