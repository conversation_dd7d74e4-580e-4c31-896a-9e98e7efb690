import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, Typography, Grid, Card, CardContent, CardActions, Button } from '@mui/material';
import {
  Receipt,
  People,
  Payment,
  Assessment,
  Add,
  List,
  TrendingUp,
  AccountBalance,
} from '@mui/icons-material';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import SalesInvoicesList from './SalesInvoices/SalesInvoicesList';
import SalesInvoiceForm from './SalesInvoices/SalesInvoiceForm';
import SalesInvoiceView from './SalesInvoices/SalesInvoiceView';
import CustomersList from './Customers/CustomersList';
import CustomerForm from './Customers/CustomerForm';
import CustomerView from './Customers/CustomerView';
import CustomerPaymentsList from './Payments/CustomerPaymentsList';
import CustomerPaymentForm from './Payments/CustomerPaymentForm';
import SalesReports from './Reports/SalesReports';
import SalesDashboard from './Dashboard/SalesDashboard';

const SalesModule: React.FC = () => {
  const navigate = useNavigate();

  const menuItems = [
    {
      title: 'فواتير المبيعات',
      description: 'إدارة فواتير المبيعات وإنشاء فواتير جديدة',
      icon: <Receipt fontSize="large" />,
      color: '#1976d2',
      actions: [
        { label: 'قائمة الفواتير', path: '/sales/invoices', icon: <List /> },
        { label: 'فاتورة جديدة', path: '/sales/invoices/new', icon: <Add /> },
      ]
    },
    {
      title: 'العملاء',
      description: 'إدارة بيانات العملاء وحساباتهم',
      icon: <People fontSize="large" />,
      color: '#2e7d32',
      actions: [
        { label: 'قائمة العملاء', path: '/sales/customers', icon: <List /> },
        { label: 'عميل جديد', path: '/sales/customers/new', icon: <Add /> },
      ]
    },
    {
      title: 'المدفوعات',
      description: 'إدارة مدفوعات العملاء وسندات القبض',
      icon: <Payment fontSize="large" />,
      color: '#ed6c02',
      actions: [
        { label: 'قائمة المدفوعات', path: '/sales/payments', icon: <List /> },
        { label: 'دفعة جديدة', path: '/sales/payments/new', icon: <Add /> },
      ]
    },
    {
      title: 'التقارير',
      description: 'تقارير المبيعات والإحصائيات',
      icon: <Assessment fontSize="large" />,
      color: '#9c27b0',
      actions: [
        { label: 'تقارير المبيعات', path: '/sales/reports', icon: <TrendingUp /> },
        { label: 'كشوف الحسابات', path: '/sales/statements', icon: <AccountBalance /> },
      ]
    },
  ];

  return (
    <>
      <Helmet>
        <title>موديول المبيعات - نظام حسابات مصنع الزجاج</title>
      </Helmet>

      <Routes>
        {/* الصفحة الرئيسية لموديول المبيعات */}
        <Route path="/" element={
          <Box sx={{ p: 3 }}>
            <Typography variant="h4" component="h1" gutterBottom sx={{ mb: 4 }}>
              💰 موديول المبيعات
            </Typography>
            
            <SalesDashboard />

            <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 4, mb: 3 }}>
              الوظائف الرئيسية
            </Typography>

            <Grid container spacing={3}>
              {menuItems.map((item, index) => (
                <Grid item xs={12} md={6} lg={3} key={index}>
                  <Card 
                    sx={{ 
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: 4,
                      }
                    }}
                  >
                    <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                      <Box 
                        sx={{ 
                          color: item.color, 
                          mb: 2,
                          display: 'flex',
                          justifyContent: 'center'
                        }}
                      >
                        {item.icon}
                      </Box>
                      <Typography variant="h6" component="h3" gutterBottom>
                        {item.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {item.description}
                      </Typography>
                    </CardContent>
                    <CardActions sx={{ justifyContent: 'center', flexWrap: 'wrap', gap: 1, p: 2 }}>
                      {item.actions.map((action, actionIndex) => (
                        <Button
                          key={actionIndex}
                          variant="outlined"
                          size="small"
                          startIcon={action.icon}
                          onClick={() => navigate(action.path)}
                          sx={{ 
                            borderColor: item.color,
                            color: item.color,
                            '&:hover': {
                              borderColor: item.color,
                              backgroundColor: `${item.color}10`,
                            }
                          }}
                        >
                          {action.label}
                        </Button>
                      ))}
                    </CardActions>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        } />

        {/* مسارات فواتير المبيعات */}
        <Route path="/invoices" element={<SalesInvoicesList />} />
        <Route path="/invoices/new" element={<SalesInvoiceForm />} />
        <Route path="/invoices/:id" element={<SalesInvoiceView />} />
        <Route path="/invoices/:id/edit" element={<SalesInvoiceForm />} />

        {/* مسارات العملاء */}
        <Route path="/customers" element={<CustomersList />} />
        <Route path="/customers/new" element={<CustomerForm />} />
        <Route path="/customers/:id" element={<CustomerView />} />
        <Route path="/customers/:id/edit" element={<CustomerForm />} />

        {/* مسارات المدفوعات */}
        <Route path="/payments" element={<CustomerPaymentsList />} />
        <Route path="/payments/new" element={<CustomerPaymentForm />} />
        <Route path="/payments/:customerId" element={<CustomerPaymentsList />} />

        {/* مسارات التقارير */}
        <Route path="/reports/*" element={<SalesReports />} />

        {/* إعادة توجيه للصفحة الرئيسية */}
        <Route path="*" element={<Navigate to="/sales" replace />} />
      </Routes>
    </>
  );
};

export default SalesModule;
