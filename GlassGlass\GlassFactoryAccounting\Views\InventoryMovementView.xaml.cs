using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class InventoryMovementView : UserControl
    {
        private readonly InventoryService _inventoryService;
        private readonly ItemService _itemService;
        private readonly WarehouseService _warehouseService;
        private readonly SalesService _salesService;
        private ObservableCollection<InventoryMovement> _movements;
        private ObservableCollection<Item> _items;
        private ObservableCollection<Warehouse> _warehouses;
        private ObservableCollection<Customer> _customers;
        private ObservableCollection<Supplier> _suppliers;
        private MovementType _currentMovementType;
        private Item? _selectedItem;

        public InventoryMovementView()
        {
            InitializeComponent();
            _inventoryService = new InventoryService();
            _itemService = new ItemService();
            _warehouseService = new WarehouseService();
            _salesService = new SalesService();
            _movements = new ObservableCollection<InventoryMovement>();
            _items = new ObservableCollection<Item>();
            _warehouses = new ObservableCollection<Warehouse>();
            _customers = new ObservableCollection<Customer>();
            _suppliers = new ObservableCollection<Supplier>();

            MovementsDataGrid.ItemsSource = _movements;
            CmbItem.ItemsSource = _items;
            CmbWarehouse.ItemsSource = _warehouses;

            // إضافة تلميحات للمستخدم
            TxtUnits.ToolTip = "سيتم تفعيل/تعطيل هذا الحقل حسب نوع الصنف المختار";
            TxtQuantity.ToolTip = "سيتم تفعيل/تعطيل هذا الحقل حسب نوع الصنف المختار";

            LoadData();
        }

        private async void LoadData()
        {
            await LoadWarehouses();
            await LoadCustomers();
            await LoadSuppliers();
            await LoadMovements();
        }

        private async Task LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                _warehouses.Clear();
                foreach (var warehouse in warehouses)
                {
                    _warehouses.Add(warehouse);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المخازن: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadItemsByWarehouse(int warehouseId)
        {
            try
            {
                var items = await _itemService.GetItemsByWarehouseAsync(warehouseId);
                _items.Clear();
                foreach (var item in items)
                {
                    _items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأصناف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadCustomers()
        {
            try
            {
                var customers = await _salesService.GetAllCustomersAsync();
                _customers.Clear();
                foreach (var customer in customers)
                {
                    _customers.Add(customer);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل العملاء: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadSuppliers()
        {
            try
            {
                var suppliers = await _salesService.GetAllSuppliersAsync();
                _suppliers.Clear();
                foreach (var supplier in suppliers)
                {
                    _suppliers.Add(supplier);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الموردين: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadMovements()
        {
            try
            {
                var movements = await _inventoryService.GetAllMovementsAsync();
                _movements.Clear();
                foreach (var movement in movements.Take(50)) // آخر 50 حركة
                {
                    _movements.Add(movement);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحركات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnReceive_Click(object sender, RoutedEventArgs e)
        {
            _currentMovementType = MovementType.استلام;
            LblCustomerSupplier.Text = "المورد:";
            CmbCustomerSupplier.ItemsSource = _suppliers;
            CmbCustomerSupplier.DisplayMemberPath = "Name";
            CmbCustomerSupplier.SelectedValuePath = "Id";

            // تحديث حالة حقل السعر للاستلام (إدخال يدوي)
            TxtUnitPrice.IsReadOnly = false;
            TxtUnitPrice.Background = System.Windows.Media.Brushes.White;
            TxtUnitPrice.BorderBrush = System.Windows.Media.Brushes.Blue;
            TxtUnitPrice.BorderThickness = new System.Windows.Thickness(2);
            TxtUnitPrice.ToolTip = "للاستلام: يرجى إدخال سعر الاستلام يدوياً";
            TxtUnitPrice.Clear();

            MovementFormPanel.Visibility = Visibility.Visible;
            GenerateOrderNumber();
        }

        private void BtnIssue_Click(object sender, RoutedEventArgs e)
        {
            _currentMovementType = MovementType.تسليم;
            LblCustomerSupplier.Text = "العميل:";
            CmbCustomerSupplier.ItemsSource = _customers;
            CmbCustomerSupplier.DisplayMemberPath = "Name";
            CmbCustomerSupplier.SelectedValuePath = "Id";

            // تحديث حالة حقل السعر للتسليم (تلقائي من المتوسط المتحرك)
            TxtUnitPrice.IsReadOnly = true;
            TxtUnitPrice.Background = System.Windows.Media.Brushes.LightGray;
            TxtUnitPrice.BorderBrush = System.Windows.Media.Brushes.Gray;
            TxtUnitPrice.BorderThickness = new System.Windows.Thickness(1);
            TxtUnitPrice.ToolTip = "للتسليم: السعر سيتم جلبه تلقائياً من المتوسط المتحرك";
            TxtUnitPrice.Clear();

            MovementFormPanel.Visibility = Visibility.Visible;
            GenerateOrderNumber();
        }

        private async void GenerateOrderNumber()
        {
            try
            {
                var movements = await _inventoryService.GetAllMovementsAsync();
                var lastOrderNumber = movements
                    .Where(m => !string.IsNullOrEmpty(m.OrderNumber) && m.OrderNumber.StartsWith("ORD"))
                    .Select(m => m.OrderNumber)
                    .OrderByDescending(o => o)
                    .FirstOrDefault();

                if (lastOrderNumber != null && lastOrderNumber.Length > 3)
                {
                    var numberPart = lastOrderNumber.Substring(3);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        TxtOrderNumber.Text = $"ORD{(lastNumber + 1):D6}";
                        return;
                    }
                }

                TxtOrderNumber.Text = "ORD000001";
            }
            catch
            {
                TxtOrderNumber.Text = "ORD000001";
            }
        }

        private async void CmbWarehouse_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbWarehouse.SelectedValue is int warehouseId)
            {
                await LoadItemsByWarehouse(warehouseId);
                CmbItem.SelectedIndex = -1;
                _selectedItem = null;
                TxtQuantity.Clear();
                TxtUnitPrice.Clear();
                TxtTotalValue.Clear();
                TxtBoxCount.Clear();
            }
        }

        private async void CmbItem_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbItem.SelectedItem is Item item)
            {
                _selectedItem = item;

                // تحديد حالة حقول الإدخال حسب نوع الصنف
                if (item.HasDimensions)
                {
                    // للأصناف ذات الأبعاد: تفعيل العدد وتعطيل الكمية
                    TxtUnits.IsEnabled = true;
                    TxtUnits.Background = System.Windows.Media.Brushes.White;
                    TxtUnits.BorderBrush = System.Windows.Media.Brushes.Blue;
                    TxtUnits.BorderThickness = new System.Windows.Thickness(2);

                    TxtQuantity.IsReadOnly = true;
                    TxtQuantity.Background = System.Windows.Media.Brushes.LightGray;
                    TxtQuantity.BorderBrush = System.Windows.Media.Brushes.Gray;
                    TxtQuantity.BorderThickness = new System.Windows.Thickness(1);

                    LblQuantity.Text = "الكمية (م²) - محسوبة تلقائياً:";
                    LblUnits.Text = "العدد - مُفعل:";

                    // مسح الحقول
                    TxtUnits.Clear();
                    TxtQuantity.Clear();
                }
                else
                {
                    // للأصناف بدون أبعاد: تعطيل العدد وتفعيل الكمية
                    TxtUnits.IsEnabled = false;
                    TxtUnits.Background = System.Windows.Media.Brushes.LightGray;
                    TxtUnits.BorderBrush = System.Windows.Media.Brushes.Gray;
                    TxtUnits.BorderThickness = new System.Windows.Thickness(1);
                    TxtUnits.Clear();

                    TxtQuantity.IsReadOnly = false;
                    TxtQuantity.Background = System.Windows.Media.Brushes.White;
                    TxtQuantity.BorderBrush = System.Windows.Media.Brushes.Blue;
                    TxtQuantity.BorderThickness = new System.Windows.Thickness(2);

                    LblQuantity.Text = "الكمية - مُفعل:";
                    LblUnits.Text = "العدد - معطل:";

                    // مسح الحقول
                    TxtQuantity.Clear();
                }

                // تحديد آلية إدخال السعر حسب نوع الحركة
                if (_currentMovementType == MovementType.تسليم)
                {
                    // للتسليم: جلب السعر تلقائياً من المتوسط المتحرك
                    var averagePrice = await _itemService.GetItemAveragePriceAsync(item.Id);
                    TxtUnitPrice.Text = averagePrice.ToString("F2");
                    TxtUnitPrice.IsReadOnly = true;
                    TxtUnitPrice.Background = System.Windows.Media.Brushes.LightGray;
                    TxtUnitPrice.BorderBrush = System.Windows.Media.Brushes.Gray;
                    TxtUnitPrice.BorderThickness = new System.Windows.Thickness(1);
                    TxtUnitPrice.ToolTip = $"السعر محسوب تلقائياً من المتوسط المتحرك: {averagePrice:F2}";
                }
                else if (_currentMovementType == MovementType.استلام)
                {
                    // للاستلام: إدخال السعر يدوياً
                    TxtUnitPrice.Clear();
                    TxtUnitPrice.IsReadOnly = false;
                    TxtUnitPrice.Background = System.Windows.Media.Brushes.White;
                    TxtUnitPrice.BorderBrush = System.Windows.Media.Brushes.Blue;
                    TxtUnitPrice.BorderThickness = new System.Windows.Thickness(2);
                    TxtUnitPrice.ToolTip = "يرجى إدخال سعر الاستلام يدوياً";
                }

                CalculateQuantityAndBoxes();
            }
        }

        private void TxtUnits_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateQuantityFromUnits();
        }

        private void CalculateQuantityFromUnits()
        {
            if (_selectedItem?.HasDimensions == true && decimal.TryParse(TxtUnits.Text, out decimal units))
            {
                var quantity = units * _selectedItem.Area;
                TxtQuantity.Text = quantity.ToString("F4");

                // إظهار رسالة توضيحية
                TxtQuantity.ToolTip = $"محسوبة تلقائياً: {units} عدد × {_selectedItem.Area:F4} م² = {quantity:F4}";
            }
            else if (_selectedItem?.HasDimensions == true)
            {
                TxtQuantity.Clear();
                TxtQuantity.ToolTip = "سيتم حساب الكمية تلقائياً عند إدخال العدد";
            }

            CalculateBoxCount();
        }

        private void CalculateQuantityAndBoxes()
        {
            if (_selectedItem != null)
            {
                CalculateBoxCount();
            }
        }

        private void CalculateBoxCount()
        {
            if (_selectedItem != null && decimal.TryParse(TxtQuantity.Text, out decimal quantity))
            {
                if (_selectedItem.BoxContent > 0)
                {
                    var boxCount = quantity / _selectedItem.BoxContent;
                    TxtBoxCount.Text = boxCount.ToString("F2");
                }
                else
                {
                    TxtBoxCount.Text = "0";
                }
            }
            else
            {
                TxtBoxCount.Clear();
            }
        }

        private void TxtQuantity_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateBoxCount();
            CalculateTotalValue();
        }

        private void TxtUnitPrice_TextChanged(object sender, TextChangedEventArgs e)
        {
            CalculateTotalValue();
        }

        private void CalculateTotalValue()
        {
            if (decimal.TryParse(TxtQuantity.Text, out decimal quantity) &&
                decimal.TryParse(TxtUnitPrice.Text, out decimal unitPrice))
            {
                var totalValue = quantity * unitPrice;
                TxtTotalValue.Text = totalValue.ToString("F2");
            }
            else
            {
                TxtTotalValue.Clear();
            }
        }

        private async void BtnSaveMovement_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateForm())
                    return;

                var selectedItem = (Item)CmbItem.SelectedItem;
                var movement = new InventoryMovement
                {
                    OrderNumber = TxtOrderNumber.Text,
                    InvoiceNumber = TxtInvoiceNumber.Text.Trim(),
                    Date = DpDate.SelectedDate ?? DateTime.Now,
                    MovementType = _currentMovementType,
                    WarehouseId = (int)CmbWarehouse.SelectedValue,
                    WarehouseName = ((Warehouse)CmbWarehouse.SelectedItem).Name,
                    ItemId = (int)CmbItem.SelectedValue,
                    ItemName = selectedItem.Name,
                    UnitOfMeasure = selectedItem.UnitOfMeasure,
                    BoxContent = selectedItem.BoxContent,
                    Length = selectedItem.Length,
                    Width = selectedItem.Width,
                    Area = selectedItem.Area,
                    Notes = TxtNotes.Text.Trim()
                };

                if (decimal.TryParse(TxtUnits.Text, out decimal units))
                    movement.Units = units;
                if (decimal.TryParse(TxtQuantity.Text, out decimal quantity))
                    movement.Quantity = quantity;
                if (decimal.TryParse(TxtUnitPrice.Text, out decimal unitPrice))
                    movement.UnitPrice = unitPrice;
                if (decimal.TryParse(TxtBoxCount.Text, out decimal boxCount))
                    movement.BoxCount = boxCount;

                if (_currentMovementType == MovementType.استلام)
                {
                    movement.SupplierId = (int)CmbCustomerSupplier.SelectedValue;
                    movement.SupplierName = ((Supplier)CmbCustomerSupplier.SelectedItem).Name;
                }
                else
                {
                    movement.CustomerId = (int)CmbCustomerSupplier.SelectedValue;
                    movement.CustomerName = ((Customer)CmbCustomerSupplier.SelectedItem).Name;
                }

                var success = await _inventoryService.SaveMovementAsync(movement);
                if (success)
                {
                    MessageBox.Show("تم حفظ الحركة بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    ClearForm();
                    await LoadMovements();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الحركة", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الحركة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateForm()
        {
            if (CmbWarehouse.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار المخزن", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (CmbItem.SelectedValue == null)
            {
                MessageBox.Show("يرجى اختيار الصنف", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (CmbCustomerSupplier.SelectedValue == null)
            {
                MessageBox.Show($"يرجى اختيار {(_currentMovementType == MovementType.استلام ? "المورد" : "العميل")}", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtQuantity.Text) || !decimal.TryParse(TxtQuantity.Text, out _))
            {
                MessageBox.Show("يرجى إدخال كمية صحيحة", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtUnitPrice.Text) || !decimal.TryParse(TxtUnitPrice.Text, out _))
            {
                MessageBox.Show("يرجى إدخال سعر صحيح", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return false;
            }

            return true;
        }

        private void BtnClearForm_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            TxtInvoiceNumber.Clear();
            CmbWarehouse.SelectedIndex = -1;
            CmbCustomerSupplier.SelectedIndex = -1;
            CmbItem.SelectedIndex = -1;
            TxtUnits.Clear();
            TxtQuantity.Clear();
            TxtUnitPrice.Clear();
            TxtTotalValue.Clear();
            TxtBoxCount.Clear();
            TxtNotes.Clear();
            _selectedItem = null;

            // إعادة تعيين حالة الحقول إلى الحالة الافتراضية
            TxtUnits.IsEnabled = true;
            TxtUnits.Background = System.Windows.Media.Brushes.White;
            TxtUnits.BorderBrush = System.Windows.Media.Brushes.Gray;
            TxtUnits.BorderThickness = new System.Windows.Thickness(1);
            TxtUnits.ToolTip = "سيتم تفعيل/تعطيل هذا الحقل حسب نوع الصنف المختار";

            TxtQuantity.IsReadOnly = false;
            TxtQuantity.Background = System.Windows.Media.Brushes.White;
            TxtQuantity.BorderBrush = System.Windows.Media.Brushes.Gray;
            TxtQuantity.BorderThickness = new System.Windows.Thickness(1);
            TxtQuantity.ToolTip = "سيتم تفعيل/تعطيل هذا الحقل حسب نوع الصنف المختار";

            LblQuantity.Text = "الكمية:";
            LblUnits.Text = "العدد:";

            // مسح قائمة الأصناف
            _items.Clear();

            GenerateOrderNumber();
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            await LoadMovements();
        }
    }
}
