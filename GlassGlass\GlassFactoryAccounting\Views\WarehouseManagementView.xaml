<UserControl x:Class="GlassFactoryAccounting.Views.WarehouseManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#F44336"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect" Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Auto"
                  PanningMode="VerticalOnly"
                  Background="#F5F5F5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🏠" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="إدارة المخازن" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                    <TextBlock Text="إضافة وإدارة المخازن" FontSize="14" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- نموذج إضافة مخزن جديد -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- الصف الأول -->
                <Grid Grid.Row="0" Margin="15,15,15,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                    </Grid.ColumnDefinitions>

                    <!-- كود المخزن -->
                    <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="كود المخزن:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtWarehouseCode" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" IsReadOnly="True"
                                     Background="#F8F9FA" BorderBrush="#BDC3C7" BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- اسم المخزن -->
                    <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="اسم المخزن:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtWarehouseName" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#3498DB" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- موقع المخزن -->
                    <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="موقع المخزن:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtWarehouseLocation" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#3498DB" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- وصف المخزن -->
                    <Border Grid.Column="3" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="وصف المخزن:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtWarehouseDescription" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#3498DB" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- الصف الثاني - الأزرار -->
                <Border Grid.Row="1" Margin="10,0,10,10" Padding="15" Background="#F8F9FA"
                        BorderBrush="#E0E0E0" BorderThickness="1" CornerRadius="8">
                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                        <Button x:Name="BtnGenerateCode" Content="🔄 توليد كود جديد"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Margin="0,0,15,0" Click="BtnGenerateCode_Click"
                                Background="#3498DB" Foreground="White" BorderThickness="0"/>
                        <Button x:Name="BtnAddWarehouse" Content="➕ إضافة مخزن"
                                Style="{StaticResource SuccessButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Margin="0,0,15,0" Click="BtnAddWarehouse_Click"
                                Background="#27AE60" Foreground="White" BorderThickness="0"/>
                        <Button x:Name="BtnClearForm" Content="🗑️ مسح النموذج"
                                Style="{StaticResource PrimaryButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Background="#95A5A6" Foreground="White" BorderThickness="0"
                                Click="BtnClearForm_Click"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>

        <!-- قائمة المخازن -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 قائمة المخازن" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                    <Button x:Name="BtnRefresh" Content="🔄 تحديث" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="20,0,0,0" Click="BtnRefresh_Click"/>
                </StackPanel>

                <DataGrid Grid.Row="1" x:Name="WarehousesDataGrid" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="المعرف" Binding="{Binding Id}" Width="60"/>
                        <DataGridTextColumn Header="كود المخزن" Binding="{Binding Code}" Width="100"/>
                        <DataGridTextColumn Header="اسم المخزن" Binding="{Binding Name}" Width="150"/>
                        <DataGridTextColumn Header="موقع المخزن" Binding="{Binding Location}" Width="150"/>
                        <DataGridTextColumn Header="وصف المخزن" Binding="{Binding Description}" Width="200"/>
                        <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=dd/MM/yyyy}" Width="120"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="✏️ تعديل" 
                                                Style="{StaticResource PrimaryButtonStyle}"
                                                Margin="0,0,5,0" Padding="10,5"
                                                Click="BtnEditWarehouse_Click"
                                                Tag="{Binding}"/>
                                        <Button Content="🗑️ حذف" 
                                                Style="{StaticResource DangerButtonStyle}"
                                                Padding="10,5"
                                                Click="BtnDeleteWarehouse_Click"
                                                Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
