using System.Windows;
using System.Windows.Controls;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة تقرير العملاء
/// </summary>
public partial class CustomersReportView : UserControl
{
    private readonly MainWindow _mainWindow;

    public CustomersReportView(MainWindow mainWindow)
    {
        InitializeComponent();
        _mainWindow = mainWindow;
    }

    private void BtnBack_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _mainWindow.GoBack();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnComingSoon_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("سيتم إشعارك عند إتاحة تقارير العملاء.\nيمكنك التواصل مع فريق التطوير لمعرفة المزيد.", 
                "قريباً", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
