using System.Windows;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة إضافة عميل جديد
/// </summary>
public partial class AddCustomerWindow : Window
{
    public Customer? NewCustomer { get; private set; }

    public AddCustomerWindow()
    {
        InitializeComponent();
        TxtCustomerName.Focus();
    }

    private async void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(TxtCustomerName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم العميل", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                TxtCustomerName.Focus();
                return;
            }

            // إنشاء عميل جديد
            var customer = new Customer
            {
                Name = TxtCustomerName.Text.Trim(),
                Phone = TxtCustomerPhone.Text.Trim(),
                Email = TxtCustomerEmail.Text.Trim(),
                Address = TxtCustomerAddress.Text.Trim(),
                ContactPerson = TxtContactPerson.Text.Trim(),
                Notes = TxtCustomerNotes.Text.Trim()
            };

            // حفظ العميل
            var archiveService = new ArchiveService();
            int customerId = await archiveService.SaveCustomerAsync(customer);

            if (customerId > 0)
            {
                customer.Id = customerId;
                NewCustomer = customer;
                
                MessageBox.Show("تم حفظ العميل بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء حفظ العميل", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ العميل: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            DialogResult = false;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
