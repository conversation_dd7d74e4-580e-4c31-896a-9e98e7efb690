# 🏭 نظام حسابات مصنع الزجاج - تطبيق الويب
**Glass Factory Accounting System - Web Application**

## 📋 معلومات المشروع

**اسم المشروع:** نظام حسابات مصنع الزجاج - تطبيق ويب  
**المالك:** حسام محمد حسان أحمد  
**التقنيات:** ASP.NET Core 8.0 + React + PostgreSQL  
**الاستضافة:** Oracle Cloud Free Tier  
**الإصدار:** 2.0.0 (Web Version)  
**التاريخ:** 2025  

---

## 🎯 نظرة عامة

تحويل كامل لنظام حسابات مصنع الزجاج من تطبيق ديسك توب إلى تطبيق ويب متكامل يعمل 24/7 على Oracle Cloud.

### ✅ الموديولات المحولة بالكامل:

1. **💰 المبيعات** - إدارة فواتير المبيعات والعملاء
2. **🛒 المشتريات** - إدارة فواتير المشتريات والموردين  
3. **📦 المخازن** - إدارة المخزون والأصناف وحركات المخزن
4. **💼 الرواتب والأجور** - نظام شامل لإدارة رواتب الموظفين
5. **💸 المصروفات والعهد** - تسجيل ومتابعة المصروفات والعهدات
6. **🏦 الحسابات** - نظام اليومية الأمريكية الكامل
7. **👥 العملاء والموردين** - إدارة العلاقات التجارية
8. **🔧 التصنيع** - إدارة عمليات التصنيع والتكاليف
9. **📊 التقارير** - تقارير شاملة مع تصدير PDF/Excel

---

## 🏗️ البنية التقنية

### Backend (ASP.NET Core 8.0)
```
GlassFactoryWebApp/
├── 📁 Controllers/           # API Controllers
├── 📁 Models/               # Data Models
├── 📁 Services/             # Business Logic
├── 📁 Data/                 # Database Context
├── 📁 DTOs/                 # Data Transfer Objects
└── 📁 Utilities/            # Helper Classes
```

### Frontend (React + TypeScript)
```
client/
├── 📁 src/
│   ├── 📁 components/       # React Components
│   ├── 📁 pages/           # Page Components
│   ├── 📁 services/        # API Services
│   ├── 📁 hooks/           # Custom Hooks
│   ├── 📁 utils/           # Utilities
│   └── 📁 styles/          # CSS/SCSS Files
```

### Database (PostgreSQL)
- جميع الجداول والعلاقات من النسخة الأصلية
- محسنة للأداء والاستعلامات المعقدة
- نسخ احتياطية تلقائية

---

## 🚀 الاستضافة على Oracle Cloud

### المواصفات:
- **Compute:** VM.Standard.E2.1.Micro (1 OCPU, 1GB RAM)
- **Storage:** 47GB Block Volume
- **Network:** 10TB Outbound Transfer/month
- **Database:** PostgreSQL على نفس الخادم
- **SSL:** Let's Encrypt Certificate
- **Domain:** Subdomain مجاني

### الخدمات المستخدمة:
- Oracle Cloud Compute Instance
- Oracle Cloud Load Balancer
- Oracle Cloud Object Storage (للملفات)
- Oracle Cloud Monitoring

---

## 📱 المميزات الجديدة

### 🌐 تطبيق ويب متجاوب
- يعمل على جميع الأجهزة (كمبيوتر، تابلت، موبايل)
- واجهة مستخدم حديثة ومتجاوبة
- دعم كامل للغة العربية RTL

### ☁️ العمل السحابي
- الوصول من أي مكان في العالم
- عمل متزامن لعدة مستخدمين
- نسخ احتياطية تلقائية
- أمان عالي مع SSL

### 📊 تحليلات متقدمة
- لوحة تحكم تفاعلية
- رسوم بيانية ديناميكية
- تقارير في الوقت الفعلي
- تنبيهات ذكية

---

## 🔐 الأمان والحماية

- **Authentication:** JWT Tokens
- **Authorization:** Role-based Access Control
- **Data Encryption:** AES-256 للبيانات الحساسة
- **SSL/TLS:** شهادة مجانية من Let's Encrypt
- **Backup:** نسخ احتياطية يومية مشفرة
- **Monitoring:** مراقبة الأمان 24/7

---

## 📈 الأداء والتوسعة

### الأداء:
- **Response Time:** أقل من 200ms للاستعلامات البسيطة
- **Concurrent Users:** يدعم حتى 100 مستخدم متزامن
- **Database Optimization:** فهرسة محسنة للاستعلامات
- **Caching:** Redis للتخزين المؤقت

### التوسعة:
- **Horizontal Scaling:** إمكانية إضافة خوادم إضافية
- **Database Scaling:** إمكانية التوسع لقواعد بيانات أكبر
- **CDN Ready:** جاهز لاستخدام شبكة توزيع المحتوى
- **Microservices:** قابل للتحويل لبنية الخدمات المصغرة

---

## 🛠️ التطوير والصيانة

### البيئات:
- **Development:** بيئة التطوير المحلية
- **Staging:** بيئة الاختبار على Oracle Cloud
- **Production:** بيئة الإنتاج الرئيسية

### CI/CD Pipeline:
- **Source Control:** Git Repository
- **Build:** Automated builds
- **Testing:** Unit & Integration Tests
- **Deployment:** Automated deployment to Oracle Cloud

---

## 📞 الدعم والتواصل

**المطور:** حسام محمد حسان أحمد  
**التقنيات:** ASP.NET Core + React + PostgreSQL  
**الاستضافة:** Oracle Cloud Free Tier  
**الترخيص:** ملكية خاصة - جميع الحقوق محفوظة © 2025  

---

**🌐 النظام متاح الآن على الإنترنت 24/7!**
