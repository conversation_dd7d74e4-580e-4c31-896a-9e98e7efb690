# 🏭 نظام حسابات مصنع الزجاج - Glass Factory Accounting System

## 🌐 **الروابط المباشرة (بعد النشر):**
- **🏠 التطبيق:** https://glassfactorywebapp-production.up.railway.app
- **💰 موديول المبيعات:** https://glassfactorywebapp-production.up.railway.app/sales
- **📋 API:** https://glassfactorywebapp-production.up.railway.app/swagger
- **🔍 Health Check:** https://glassfactorywebapp-production.up.railway.app/health

## 📋 **وصف المشروع**
نظام حسابات متكامل مصمم خصيصاً لمصانع الزجاج، يوفر إدارة شاملة لجميع العمليات المحاسبية والتجارية.

## 🚀 **المميزات الرئيسية**

### **💰 موديول المبيعات (مكتمل):**
✅ إدارة العملاء - إضافة وتعديل وحذف العملاء  
✅ فواتير المبيعات - إنشاء وطباعة وترحيل الفواتير  
✅ مدفوعات العملاء - تسجيل ومتابعة المدفوعات  
✅ التقارير - تقارير مفصلة وإحصائيات  
✅ البحث والفلترة - بحث متقدم في جميع البيانات  
✅ الطباعة - طباعة الفواتير PDF  
✅ التصدير - تصدير التقارير Excel/PDF  

## 🛠️ **التقنيات المستخدمة**

### **Backend:**
- ASP.NET Core 8.0
- Entity Framework Core
- PostgreSQL
- JWT Authentication
- AutoMapper
- Swagger/OpenAPI

### **Frontend:**
- React 18
- TypeScript
- Material-UI (MUI)
- Arabic RTL Support

### **Deployment:**
- Railway.app
- Docker
- PostgreSQL Database

## 🚀 **النشر على Railway**

### **متطلبات النشر:**
- حساب GitHub
- حساب Railway.app (مجاني)

### **خطوات النشر:**
1. رفع المشروع على GitHub
2. ربط Railway بـ GitHub
3. إنشاء مشروع جديد على Railway
4. إضافة PostgreSQL database
5. إعداد متغيرات البيئة
6. انتظار النشر

### **متغيرات البيئة المطلوبة:**
```
JWT_SECRET_KEY=GlassFactorySecretKey2025VeryLongAndSecureForProduction!
ASPNETCORE_ENVIRONMENT=Production
```

## 📞 **معلومات المطور**
- **👤 المطور:** حسام محمد حسان أحمد
- **📧 الدعم:** <EMAIL>
- **🔢 الإصدار:** 2.0.0
- **📅 تاريخ النشر:** 6 ديسمبر 2024

## 🎉 **الحالة**
✅ جاهز للنشر على Railway.app  
✅ جميع الملفات محضرة  
✅ Docker configuration جاهز  
✅ Database setup جاهز  

**🌐 سيكون متاح على:** https://glassfactorywebapp-production.up.railway.app
