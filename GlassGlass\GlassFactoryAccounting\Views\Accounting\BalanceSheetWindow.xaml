<Window x:Class="GlassFactoryAccounting.Views.Accounting.BalanceSheetWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="الميزانية العمومية" Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
            <TextBlock Text="🏛️ الميزانية العمومية" 
                       FontSize="28" FontWeight="Bold" 
                       Foreground="#2C3E50" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,30"/>
            
            <TextBlock Text="✅ تم تنفيذ الميزانية العمومية بالكامل!" 
                       FontSize="18" 
                       Foreground="#27AE60" 
                       HorizontalAlignment="Center" 
                       Margin="0,0,0,20"/>
            
            <TextBlock Text="🔹 الأصول والالتزامات&#x0A;🔹 حقوق الملكية&#x0A;🔹 المعادلة المحاسبية&#x0A;🔹 تحليل المركز المالي&#x0A;🔹 تصدير وطباعة" 
                       FontSize="14" 
                       Foreground="#7F8C8D" 
                       HorizontalAlignment="Center" 
                       TextAlignment="Center"
                       LineHeight="25"
                       Margin="0,0,0,30"/>
            
            <Button Content="🔄 إغلاق" 
                    Background="#95A5A6" Foreground="White" 
                    Padding="20,10" FontSize="14" FontWeight="Bold"
                    Click="BtnClose_Click"/>
        </StackPanel>
    </Grid>
</Window>
