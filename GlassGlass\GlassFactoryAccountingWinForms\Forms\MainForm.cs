using System;
using System.Drawing;
using System.Windows.Forms;
using GlassFactoryAccountingWinForms.Forms.AccountingForms;
using GlassFactoryAccountingWinForms.Services;

namespace GlassFactoryAccountingWinForms.Forms
{
    /// <summary>
    /// النموذج الرئيسي للتطبيق
    /// </summary>
    public partial class MainForm : Form
    {
        private readonly AccountingService _accountingService;

        public MainForm()
        {
            InitializeComponent();
            _accountingService = new AccountingService();
            
            // تخصيص النموذج
            CustomizeForm();
            
            // تحميل البيانات الأولية
            LoadInitialData();
        }

        /// <summary>
        /// تخصيص النموذج
        /// </summary>
        private void CustomizeForm()
        {
            // إعدادات النموذج الأساسية
            this.Text = "🏭 نظام حسابات مصنع الزجاج - Glass Factory Accounting System";
            this.WindowState = FormWindowState.Maximized;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 244, 247);
            this.Font = new Font("Segoe UI", 10F, FontStyle.Regular);

            // تمكين الـ RTL للعربية
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
        }

        /// <summary>
        /// تحميل البيانات الأولية
        /// </summary>
        private void LoadInitialData()
        {
            try
            {
                // تحديث عدد الحسابات في الواجهة
                var accounts = _accountingService.GetAllAccounts();
                lblAccountsCount.Text = $"عدد الحسابات: {accounts.Count}";
                
                // تحديث حالة النظام
                lblSystemStatus.Text = "✅ النظام جاهز للعمل";
                lblSystemStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                lblSystemStatus.Text = "❌ خطأ في النظام";
                lblSystemStatus.ForeColor = Color.Red;
                
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فتح نموذج شجرة الحسابات
        /// </summary>
        private void btnChartOfAccounts_Click(object sender, EventArgs e)
        {
            try
            {
                var chartForm = new ChartOfAccountsForm();
                chartForm.ShowDialog();
                
                // تحديث البيانات بعد إغلاق النموذج
                LoadInitialData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح شجرة الحسابات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فتح نموذج قيود اليومية
        /// </summary>
        private void btnJournalEntries_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("نموذج قيود اليومية سيكون متاحاً في التحديث القادم.", "قريباً", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// فتح التقارير المحاسبية
        /// </summary>
        private void btnReports_Click(object sender, EventArgs e)
        {
            try
            {
                MessageBox.Show("التقارير المحاسبية ستكون متاحة في التحديث القادم.", "قريباً", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// عرض معلومات حول البرنامج
        /// </summary>
        private void btnAbout_Click(object sender, EventArgs e)
        {
            var aboutMessage = @"🏭 نظام حسابات مصنع الزجاج
Glass Factory Accounting System

الإصدار: 1.0.0
المطور: حسام محمد حسان أحمد
التاريخ: 2025

نظام محاسبي متكامل مصمم خصيصاً لإدارة حسابات مصانع الزجاج
يتضمن إدارة الحسابات، قيود اليومية، والتقارير المحاسبية

جميع الحقوق محفوظة © 2025";

            MessageBox.Show(aboutMessage, "حول البرنامج", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// إغلاق التطبيق
        /// </summary>
        private void btnExit_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        /// <summary>
        /// معالج إغلاق النموذج
        /// </summary>
        private void MainForm_FormClosing(object sender, FormClosingEventArgs e)
        {
            var result = MessageBox.Show("هل تريد إغلاق البرنامج؟", "تأكيد الإغلاق", 
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            
            if (result == DialogResult.No)
            {
                e.Cancel = true;
            }
        }
    }
}
