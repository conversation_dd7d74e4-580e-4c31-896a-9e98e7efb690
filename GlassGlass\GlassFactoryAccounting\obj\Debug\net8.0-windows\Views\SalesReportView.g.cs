﻿#pragma checksum "..\..\..\..\Views\SalesReportView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1CB9CFAA4E473665AC42A723ECD4BEA5776A822A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// SalesReportView
    /// </summary>
    public partial class SalesReportView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 44 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportExcel;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrintReport;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateFrom;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateTo;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCustomerSearch;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearFilters;
        
        #line default
        #line hidden
        
        
        #line 118 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbServiceFilter;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbGlassTypeFilter;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbGlassThicknessFilter;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbPaymentStatusFilter;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFilteredCount;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFilteredTotal;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFilteredDiscount;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFilteredNet;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefreshData;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Views\SalesReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid SalesReportDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/salesreportview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SalesReportView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\Views\SalesReportView.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnExportExcel = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\Views\SalesReportView.xaml"
            this.BtnExportExcel.Click += new System.Windows.RoutedEventHandler(this.BtnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnPrintReport = ((System.Windows.Controls.Button)(target));
            
            #line 60 "..\..\..\..\Views\SalesReportView.xaml"
            this.BtnPrintReport.Click += new System.Windows.RoutedEventHandler(this.BtnPrintReport_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DateFrom = ((System.Windows.Controls.DatePicker)(target));
            
            #line 83 "..\..\..\..\Views\SalesReportView.xaml"
            this.DateFrom.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DateTo = ((System.Windows.Controls.DatePicker)(target));
            
            #line 88 "..\..\..\..\Views\SalesReportView.xaml"
            this.DateTo.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TxtCustomerSearch = ((System.Windows.Controls.TextBox)(target));
            
            #line 94 "..\..\..\..\Views\SalesReportView.xaml"
            this.TxtCustomerSearch.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtCustomerSearch_TextChanged);
            
            #line default
            #line hidden
            
            #line 96 "..\..\..\..\Views\SalesReportView.xaml"
            this.TxtCustomerSearch.GotFocus += new System.Windows.RoutedEventHandler(this.TxtCustomerSearch_GotFocus);
            
            #line default
            #line hidden
            
            #line 96 "..\..\..\..\Views\SalesReportView.xaml"
            this.TxtCustomerSearch.LostFocus += new System.Windows.RoutedEventHandler(this.TxtCustomerSearch_LostFocus);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnClearFilters = ((System.Windows.Controls.Button)(target));
            
            #line 103 "..\..\..\..\Views\SalesReportView.xaml"
            this.BtnClearFilters.Click += new System.Windows.RoutedEventHandler(this.BtnClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CmbServiceFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 119 "..\..\..\..\Views\SalesReportView.xaml"
            this.CmbServiceFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.ServiceFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CmbGlassTypeFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 126 "..\..\..\..\Views\SalesReportView.xaml"
            this.CmbGlassTypeFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.GlassTypeFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 10:
            this.CmbGlassThicknessFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 132 "..\..\..\..\Views\SalesReportView.xaml"
            this.CmbGlassThicknessFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.GlassThicknessFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CmbPaymentStatusFilter = ((System.Windows.Controls.ComboBox)(target));
            
            #line 138 "..\..\..\..\Views\SalesReportView.xaml"
            this.CmbPaymentStatusFilter.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.PaymentStatusFilter_Changed);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TxtFilteredCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtFilteredTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtFilteredDiscount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TxtFilteredNet = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.BtnRefreshData = ((System.Windows.Controls.Button)(target));
            
            #line 195 "..\..\..\..\Views\SalesReportView.xaml"
            this.BtnRefreshData.Click += new System.Windows.RoutedEventHandler(this.BtnRefreshData_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SalesReportDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 18:
            
            #line 227 "..\..\..\..\Views\SalesReportView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnViewInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 19:
            
            #line 232 "..\..\..\..\Views\SalesReportView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 20:
            
            #line 237 "..\..\..\..\Views\SalesReportView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnPrintInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 21:
            
            #line 242 "..\..\..\..\Views\SalesReportView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteInvoice_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

