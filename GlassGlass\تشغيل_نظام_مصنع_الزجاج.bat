@echo off
chcp 65001 >nul
title 🏭 نظام حسابات مصنع الزجاج - الإصدار النهائي
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🏭 نظام حسابات مصنع الزجاج                              ║
echo ║                     Glass Factory Accounting System                          ║
echo ║                                                                              ║
echo ║                    المالك: حسام محمد حسان أحمد                             ║
echo ║                    الإصدار: 1.0.0 - 2025                                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔄 جاري تحضير النظام...
echo.

REM إيقاف أي نسخة تعمل من البرنامج
echo [1/5] إيقاف النسخ السابقة...
taskkill /f /im "GlassFactoryAccounting.exe" 2>nul >nul
timeout /t 1 /nobreak >nul

REM التحقق من وجود المجلد
echo [2/5] التحقق من ملفات النظام...
if not exist "GlassFactoryAccounting" (
    echo ❌ خطأ: مجلد المشروع غير موجود!
    echo يرجى التأكد من وجود مجلد GlassFactoryAccounting
    pause
    exit /b 1
)

cd "GlassFactoryAccounting"

REM التحقق من وجود الملف التنفيذي
echo [3/5] البحث عن الملف التنفيذي...
set "EXE_PATH="
if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
    set "EXE_PATH=bin\Release\net8.0-windows\GlassFactoryAccounting.exe"
    echo ✅ تم العثور على النسخة المحدثة: Release
) else if exist "bin\Debug\net8.0-windows\GlassFactoryAccounting.exe" (
    set "EXE_PATH=bin\Debug\net8.0-windows\GlassFactoryAccounting.exe"
    echo ⚠️  تم العثور على نسخة التطوير: Debug
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي!
    echo.
    echo 🔧 محاولة بناء المشروع...
    dotnet build --configuration Release --verbosity quiet
    if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
        set "EXE_PATH=bin\Release\net8.0-windows\GlassFactoryAccounting.exe"
        echo ✅ تم بناء المشروع بنجاح!
    ) else (
        echo ❌ فشل في بناء المشروع!
        echo يرجى التحقق من تثبيت .NET 8.0 SDK
        pause
        exit /b 1
    )
)

REM إنشاء مجلد البيانات إذا لم يكن موجود
echo [4/5] تحضير قاعدة البيانات...
if not exist "bin\Release\net8.0-windows\Data" (
    mkdir "bin\Release\net8.0-windows\Data" 2>nul
)
if not exist "bin\Debug\net8.0-windows\Data" (
    mkdir "bin\Debug\net8.0-windows\Data" 2>nul
)

REM تشغيل البرنامج
echo [5/5] تشغيل النظام...
echo.
echo 🚀 جاري تشغيل نظام حسابات مصنع الزجاج...
echo.

start "" "%EXE_PATH%"

REM انتظار قصير للتأكد من التشغيل
timeout /t 2 /nobreak >nul

REM التحقق من تشغيل البرنامج
tasklist /fi "imagename eq GlassFactoryAccounting.exe" 2>nul | find /i "GlassFactoryAccounting.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ تم تشغيل النظام بنجاح!
) else (
    echo ⚠️  تم إطلاق النظام، يرجى التحقق من النافذة
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                           🎯 مميزات النظام المحدث                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 📋 موديول التصنيع المحدث:
echo   ✅ جدول ألواح الزجاج مع حساب السعر والقيمة تلقائياً
echo   ✅ خدمة دبل جلاس مع إجمالي المتر الطولي
echo   ✅ المقاسات المطلوبة مع التحديث الفوري للمجاميع
echo   ✅ التكاليف الإضافية مع عرض المجموع تلقائياً
echo   ✅ ملخص التكاليف الشامل والمحدث
echo.
echo 🏦 النظام المحاسبي الكامل:
echo   ✅ شجرة الحسابات الشاملة
echo   ✅ قيود اليومية (النظام الأمريكي)
echo   ✅ ميزان المراجعة
echo   ✅ قائمة الدخل والميزانية العمومية
echo   ✅ كشوف الحسابات التفصيلية
echo   ✅ التقارير المحاسبية المتقدمة
echo.
echo 💼 موديولات إدارية متكاملة:
echo   ✅ إدارة المبيعات والعملاء
echo   ✅ إدارة المشتريات والموردين
echo   ✅ إدارة المخازن والمخزون
echo   ✅ إدارة المصروفات والنفقات
echo   ✅ نظام الرواتب والأجور
echo   ✅ عهدات الموظفين والسلف
echo   ✅ الخدمات والصيانة
echo   ✅ التقارير والتحليلات
echo.
echo 🎯 نصائح للاستخدام:
echo   1️⃣  ابدأ بإعداد بيانات الشركة من قائمة الإعدادات
echo   2️⃣  أضف العملاء والموردين من القوائم المخصصة
echo   3️⃣  أدخل أصناف الزجاج في نظام المخازن
echo   4️⃣  استخدم موديول التصنيع لحساب التكاليف
echo   5️⃣  راجع التقارير المالية بانتظام
echo.
echo 📞 الدعم الفني:
echo   المطور: حسام محمد حسان أحمد
echo   جميع الحقوق محفوظة © 2025
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🎉 النظام جاهز للاستخدام التجاري! 🎉                   ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo اضغط أي مفتاح للإغلاق...
pause >nul
