﻿#pragma checksum "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4C76B591A8D2FE1255F19A6CAAEBA1B19086E0A7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views.Accounting {
    
    
    /// <summary>
    /// AddEditAccountWindow
    /// </summary>
    public partial class AddEditAccountWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 20 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTitle;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAccountCode;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtAccountName;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbAccountType;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkIsParent;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel pnlParentAccount;
        
        #line default
        #line hidden
        
        
        #line 73 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock lblParentAccount;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbParentAccount;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox txtNotes;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSave;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/GlassFactoryAccounting;V1.0.0.0;component/views/accounting/addeditaccountwindow." +
                    "xaml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.txtAccountCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.txtAccountName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.cmbAccountType = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.chkIsParent = ((System.Windows.Controls.CheckBox)(target));
            
            #line 68 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
            this.chkIsParent.Checked += new System.Windows.RoutedEventHandler(this.ChkIsParent_Checked);
            
            #line default
            #line hidden
            
            #line 68 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
            this.chkIsParent.Unchecked += new System.Windows.RoutedEventHandler(this.ChkIsParent_Unchecked);
            
            #line default
            #line hidden
            return;
            case 6:
            this.pnlParentAccount = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 7:
            this.lblParentAccount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.cmbParentAccount = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.txtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.btnSave = ((System.Windows.Controls.Button)(target));
            
            #line 95 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
            this.btnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.btnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\..\..\Views\Accounting\AddEditAccountWindow.xaml"
            this.btnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

