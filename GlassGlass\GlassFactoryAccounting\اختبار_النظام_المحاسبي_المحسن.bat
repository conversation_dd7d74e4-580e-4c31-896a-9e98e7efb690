@echo off
chcp 65001 >nul
title 🏦 اختبار النظام المحاسبي المحسن
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🏦 اختبار النظام المحاسبي المحسن                        ║
echo ║                     Enhanced Accounting System Test                          ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔄 جاري تشغيل النظام لاختبار التحسينات المحاسبية...
echo.

REM إيقاف أي نسخة تعمل
echo [1/5] إيقاف النسخ السابقة...
taskkill /f /im "GlassFactoryAccounting.exe" 2>nul >nul
timeout /t 1 /nobreak >nul

REM التحقق من وجود الملف التنفيذي
echo [2/5] التحقق من ملفات النظام...
set "EXE_PATH="
if exist "Release\GlassFactoryAccounting.exe" (
    set "EXE_PATH=Release\GlassFactoryAccounting.exe"
    echo ✅ تم العثور على النظام في مجلد Release
) else if exist "bin\Release\net8.0-windows\GlassFactoryAccounting.exe" (
    set "EXE_PATH=bin\Release\net8.0-windows\GlassFactoryAccounting.exe"
    echo ✅ تم العثور على النظام في مجلد bin\Release
) else if exist "bin\Debug\net8.0-windows\GlassFactoryAccounting.exe" (
    set "EXE_PATH=bin\Debug\net8.0-windows\GlassFactoryAccounting.exe"
    echo ⚠️  تم العثور على نسخة Debug
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي!
    echo يرجى بناء المشروع أولاً باستخدام:
    echo dotnet build --configuration Release
    pause
    exit /b 1
)

REM إنشاء مجلد البيانات
echo [3/5] تحضير قاعدة البيانات...
for %%d in ("Release\Data" "bin\Release\net8.0-windows\Data" "bin\Debug\net8.0-windows\Data") do (
    if not exist "%%d" (
        mkdir "%%d" 2>nul
    )
)
echo ✅ تم تحضير مجلدات البيانات

REM تشغيل النظام
echo [4/5] تشغيل النظام المحاسبي المحسن...
echo المسار: %EXE_PATH%
start "" "%EXE_PATH%"

REM انتظار قصير
echo [5/5] التحقق من التشغيل...
timeout /t 3 /nobreak >nul

REM التحقق من تشغيل البرنامج
tasklist /fi "imagename eq GlassFactoryAccounting.exe" 2>nul | find /i "GlassFactoryAccounting.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ تم تشغيل النظام بنجاح!
) else (
    echo ⚠️  تم إطلاق النظام، يرجى التحقق من النافذة
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                        🧪 دليل اختبار التحسينات                           ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🌳 **اختبار شجرة الحسابات المحسنة:**
echo   1️⃣  اذهب إلى: الحسابات ← شجرة الحسابات
echo   2️⃣  لاحظ المستويات المتعددة للحسابات
echo   3️⃣  جرب إضافة حساب فرعي جديد
echo   4️⃣  لاحظ حساب المستوى تلقائياً
echo.

echo 📝 **اختبار قيود اليومية المحسنة:**
echo   1️⃣  اذهب إلى: الحسابات ← قيد اليومية
echo   2️⃣  أدخل قيد تجريبي:
echo       • الوصف: "اختبار القيد المحسن"
echo       • مدين: حساب النقدية (1110) - مبلغ 1000
echo       • دائن: حساب مبيعات الزجاج (4100) - مبلغ 1000
echo   3️⃣  لاحظ مؤشر التوازن (أخضر = متوازن)
echo   4️⃣  جرب استخدام حساب رئيسي ولاحظ رسالة الخطأ
echo   5️⃣  احفظ القيد أو احفظ مع ترحيل
echo.

echo 🔍 **اختبار نافذة اختيار الحسابات:**
echo   1️⃣  في نافذة قيد اليومية، اضغط على اختيار حساب
echo   2️⃣  جرب البحث بكود الحساب أو الاسم
echo   3️⃣  جرب فلترة حسب نوع الحساب
echo   4️⃣  فعل "الحسابات الفرعية فقط" ولاحظ الفرق
echo   5️⃣  لاحظ الأيقونات والمؤشرات البصرية
echo.

echo 🔗 **اختبار التكامل مع الموديولات:**
echo   1️⃣  اذهب إلى موديول المبيعات
echo   2️⃣  أنشئ فاتورة مبيعات جديدة
echo   3️⃣  احفظ الفاتورة
echo   4️⃣  ارجع للنظام المحاسبي وتحقق من القيود التلقائية
echo   5️⃣  كرر نفس الاختبار مع الرواتب والمصروفات
echo.

echo 📊 **اختبار التقارير المحاسبية:**
echo   1️⃣  اذهب إلى: الحسابات ← ميزان المراجعة
echo   2️⃣  اذهب إلى: الحسابات ← قائمة الدخل
echo   3️⃣  اذهب إلى: الحسابات ← الميزانية العمومية
echo   4️⃣  تحقق من دقة الأرقام والحسابات
echo.

echo ⚠️  **اختبارات الأمان والتحقق:**
echo   1️⃣  جرب إدخال قيد غير متوازن ولاحظ منع الحفظ
echo   2️⃣  جرب استخدام حساب رئيسي في القيد
echo   3️⃣  جرب حذف حساب له حركات
echo   4️⃣  جرب إدخال بيانات ناقصة
echo.

echo 🎯 **نصائح للاختبار:**
echo   • استخدم بيانات تجريبية فقط
echo   • اختبر كل ميزة على حدة
echo   • لاحظ رسائل الخطأ والتحذيرات
echo   • تأكد من تحديث الأرصدة تلقائياً
echo   • جرب السيناريوهات المختلفة
echo.

echo 📋 **ملفات التوثيق:**
echo   📄 ACCOUNTING_SYSTEM_ENHANCEMENTS.md - دليل التحسينات الكامل
echo   📄 README.md - دليل النظام العام
echo   📄 دليل_المستخدم.md - دليل المستخدم
echo.

echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    ✅ النظام المحاسبي المحسن جاهز للاختبار!                ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔄 لإعادة تشغيل هذا الاختبار: اضغط أي مفتاح
echo 🚪 للخروج: أغلق هذه النافذة
echo.
pause >nul

REM إعادة تشغيل الاختبار
goto :eof
