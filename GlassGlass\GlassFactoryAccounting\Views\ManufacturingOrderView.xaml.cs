using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using GlassFactoryAccounting.Helpers;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة أمر التصنيع الجديد
    /// </summary>
    public partial class ManufacturingOrderView : UserControl
    {
        private readonly Services.ManufacturingService _manufacturingService;
        private NewManufacturingOrder _currentOrder;
        private bool _areNavigationToolsVisible = true;
        
        // مجموعات البيانات
        private ObservableCollection<GlassPanelViewModel> _glassPanels;
        private ObservableCollection<RequiredSizeViewModel> _requiredSizes;
        private ObservableCollection<ServiceCostViewModel> _serviceCosts;
        private ObservableCollection<AdditionalCostViewModel> _additionalCosts;
        private ObservableCollection<FilmServiceViewModel> _filmServices;
        private ObservableCollection<DoubleGlassServiceViewModel> _doubleGlassServices;

        public ManufacturingOrderView()
        {
            try
            {
                InitializeComponent();
                _manufacturingService = new Services.ManufacturingService();

                InitializeCollections();
                InitializeControls();
                LoadData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة النافذة: {ex.Message}\n\nتفاصيل:\n{ex.StackTrace}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// Constructor للتعديل
        /// </summary>
        public ManufacturingOrderView(NewManufacturingOrder existingOrder) : this()
        {
            _currentOrder = existingOrder;
            LoadExistingOrderData();
        }

        /// <summary>
        /// تحميل بيانات الأمر الموجود
        /// </summary>
        private void LoadExistingOrderData()
        {
            if (_currentOrder != null)
            {
                try
                {
                    // تحميل البيانات الأساسية
                    txtOrderNumber.Text = _currentOrder.OrderNumber;
                    txtCustomerName.Text = _currentOrder.CustomerName ?? "";
                    txtInvoiceNumber.Text = _currentOrder.InvoiceNumber ?? "";
                    dpOrderDate.SelectedDate = _currentOrder.OrderDate;
                    txtNotes.Text = _currentOrder.Notes ?? "";

                    // تحديد الحالة
                    foreach (ComboBoxItem item in cmbOrderStatus.Items)
                    {
                        if (item.Content.ToString() == _currentOrder.OrderStatus)
                        {
                            cmbOrderStatus.SelectedItem = item;
                            break;
                        }
                    }

                    // تحميل ألواح الزجاج
                    LoadGlassPanelsData();

                    // تحميل المقاسات المطلوبة
                    LoadRequiredSizesData();

                    // تحميل تكاليف الخدمات
                    LoadServiceCostsData();

                    // تحميل التكاليف الإضافية
                    LoadAdditionalCostsData();

                    // تحديث جميع الحسابات
                    UpdateAllCalculations();
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تحميل بيانات الأمر: {ex.Message}", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// تحميل ألواح الزجاج من قاعدة البيانات
        /// </summary>
        private void LoadGlassPanelsData()
        {
            try
            {
                var glassPanels = _manufacturingService.GetGlassPanels(_currentOrder.Id);
                _glassPanels.Clear();

                int rowNumber = 1;
                foreach (var panel in glassPanels)
                {
                    _glassPanels.Add(new GlassPanelViewModel
                    {
                        RowNumber = rowNumber++,
                        GlassType = panel.GlassType,
                        Thickness = panel.Thickness,
                        Length = panel.Length,
                        Width = panel.Width,
                        SquareMeters = panel.SquareMeters,
                        Quantity = panel.Quantity,
                        TotalSquareMeters = panel.TotalSquareMeters,
                        Price = 0, // سيتم حسابها من القيمة الإجمالية
                        TotalValue = 0, // سيتم تحديثها
                        Notes = panel.Notes
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading glass panels: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل المقاسات المطلوبة من قاعدة البيانات
        /// </summary>
        private void LoadRequiredSizesData()
        {
            try
            {
                var requiredSizes = _manufacturingService.GetRequiredSizes(_currentOrder.Id);
                _requiredSizes.Clear();

                int rowNumber = 1;
                foreach (var size in requiredSizes)
                {
                    _requiredSizes.Add(new RequiredSizeViewModel
                    {
                        RowNumber = rowNumber++,
                        RefCode = size.RefCode,
                        GlassType = size.GlassType,
                        Thickness = size.Thickness,
                        Length = size.Length,
                        Width = size.Width,
                        SquareMeters = size.SquareMeters,
                        Quantity = size.Quantity,
                        TotalSquareMeters = size.TotalSquareMeters,
                        LinearMeters = 0, // سيتم حسابها
                        TotalLinearMeters = 0, // سيتم حسابها
                        IsDelivered = size.IsDelivered
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading required sizes: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل تكاليف الخدمات من قاعدة البيانات
        /// </summary>
        private void LoadServiceCostsData()
        {
            try
            {
                var serviceCosts = _manufacturingService.GetServiceCosts(_currentOrder.Id);
                _serviceCosts.Clear();

                int rowNumber = 1;
                foreach (var cost in serviceCosts)
                {
                    _serviceCosts.Add(new ServiceCostViewModel
                    {
                        RowNumber = rowNumber++,
                        ServiceName = cost.ServiceName,
                        Description = cost.Description,
                        Quantity = cost.Quantity,
                        Price = cost.Price,
                        Value = cost.Value
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading service costs: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل التكاليف الإضافية من قاعدة البيانات
        /// </summary>
        private void LoadAdditionalCostsData()
        {
            try
            {
                var additionalCosts = _manufacturingService.GetAdditionalCosts(_currentOrder.Id);
                _additionalCosts.Clear();

                int rowNumber = 1;
                foreach (var cost in additionalCosts)
                {
                    _additionalCosts.Add(new AdditionalCostViewModel
                    {
                        RowNumber = rowNumber++,
                        Description = cost.Description,
                        Value = cost.Value,
                        Notes = cost.Notes
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading additional costs: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث جميع الحسابات
        /// </summary>
        private void UpdateAllCalculations()
        {
            UpdateGlassPanelCalculations();
            UpdateRequiredSizeCalculations();
            UpdateServiceCostCalculations();
            UpdateAdditionalCostCalculations();
            UpdateCostSummary();
            CalculateWaste();
        }

        #region Initialization

        /// <summary>
        /// تهيئة المجموعات
        /// </summary>
        private void InitializeCollections()
        {
            try
            {
                _glassPanels = new ObservableCollection<GlassPanelViewModel>();
                _requiredSizes = new ObservableCollection<RequiredSizeViewModel>();
                _serviceCosts = new ObservableCollection<ServiceCostViewModel>();
                _additionalCosts = new ObservableCollection<AdditionalCostViewModel>();
                _filmServices = new ObservableCollection<FilmServiceViewModel>();
                _doubleGlassServices = new ObservableCollection<DoubleGlassServiceViewModel>();

                // ربط البيانات فقط إذا كانت العناصر موجودة
                if (dgGlassPanels != null)
                    dgGlassPanels.ItemsSource = _glassPanels;
                if (dgRequiredSizes != null)
                    dgRequiredSizes.ItemsSource = _requiredSizes;
                if (dgServiceCosts != null)
                    dgServiceCosts.ItemsSource = _serviceCosts;
                if (dgAdditionalCosts != null)
                    dgAdditionalCosts.ItemsSource = _additionalCosts;

                // ربط أحداث تغيير البيانات للحسابات التلقائية
                _glassPanels.CollectionChanged += (s, e) => CalculateGlassPanelsTotals();
                _requiredSizes.CollectionChanged += (s, e) => CalculateRequiredSizesTotals();
                _serviceCosts.CollectionChanged += (s, e) => CalculateServiceCostsTotals();
                _additionalCosts.CollectionChanged += (s, e) => CalculateAdditionalCostsTotals();

                // ربط أحداث تغيير خصائص العناصر للحسابات الفورية
                foreach (var panel in _glassPanels)
                {
                    panel.PropertyChanged += (s, e) =>
                    {
                        if (e.PropertyName == nameof(panel.Length) || e.PropertyName == nameof(panel.Width) ||
                            e.PropertyName == nameof(panel.Quantity) || e.PropertyName == nameof(panel.Price))
                        {
                            UpdateGlassPanelCalculations();
                        }
                    };
                }

                foreach (var size in _requiredSizes)
                {
                    size.PropertyChanged += (s, e) =>
                    {
                        if (e.PropertyName == nameof(size.Length) || e.PropertyName == nameof(size.Width) ||
                            e.PropertyName == nameof(size.Quantity))
                        {
                            UpdateRequiredSizeCalculations();
                        }
                    };
                }

                foreach (var cost in _serviceCosts)
                {
                    cost.PropertyChanged += (s, e) =>
                    {
                        if (e.PropertyName == nameof(cost.Quantity) || e.PropertyName == nameof(cost.Price))
                        {
                            UpdateServiceCostCalculations();
                        }
                    };
                }

                foreach (var cost in _additionalCosts)
                {
                    cost.PropertyChanged += (s, e) =>
                    {
                        if (e.PropertyName == nameof(cost.Value))
                        {
                            UpdateAdditionalCostCalculations();
                        }
                    };
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in InitializeCollections: {ex.Message}");

                // إنشاء مجموعات فارغة على الأقل
                _glassPanels = new ObservableCollection<GlassPanelViewModel>();
                _requiredSizes = new ObservableCollection<RequiredSizeViewModel>();
                _serviceCosts = new ObservableCollection<ServiceCostViewModel>();
                _additionalCosts = new ObservableCollection<AdditionalCostViewModel>();
                _filmServices = new ObservableCollection<FilmServiceViewModel>();
                _doubleGlassServices = new ObservableCollection<DoubleGlassServiceViewModel>();
            }
        }

        /// <summary>
        /// تهيئة العناصر
        /// </summary>
        private void InitializeControls()
        {
            try
            {
                // تعيين رقم أمر التصنيع
                var orderNumber = _manufacturingService.GenerateOrderNumber();
                if (txtOrderNumber != null)
                    txtOrderNumber.Text = orderNumber;

                // تعيين التاريخ الحالي
                if (dpOrderDate != null)
                    dpOrderDate.SelectedDate = DateTime.Now;

                // إنشاء أمر جديد
                _currentOrder = new NewManufacturingOrder
                {
                    OrderNumber = orderNumber,
                    OrderDate = DateTime.Now,
                    CreatedBy = "النظام",
                    CreatedDate = DateTime.Now
                };

                // تطبيق تحويل الأرقام على جميع TextBox
                ApplyEnglishNumbersToAllTextBoxes();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in InitializeControls: {ex.Message}");

                // إنشاء أمر افتراضي
                _currentOrder = new NewManufacturingOrder
                {
                    OrderNumber = $"MO{DateTime.Now:yyyyMMddHHmmss}",
                    OrderDate = DateTime.Now,
                    CreatedBy = "النظام",
                    CreatedDate = DateTime.Now
                };
            }
        }

        /// <summary>
        /// تطبيق تحويل الأرقام على جميع TextBox في النافذة
        /// </summary>
        private void ApplyEnglishNumbersToAllTextBoxes()
        {
            try
            {
                // البحث عن جميع TextBox في النافذة وتطبيق التحويل عليها
                var textBoxes = FindVisualChildren<TextBox>(this);
                foreach (var textBox in textBoxes)
                {
                    NumberFormatHelper.ApplyEnglishNumbersToTextBox(textBox);
                }

                // تطبيق التحويل على DataGrid أيضاً
                ApplyEnglishNumbersToDataGrids();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying English numbers: {ex.Message}");
            }
        }

        /// <summary>
        /// تطبيق تحويل الأرقام على DataGrid
        /// </summary>
        private void ApplyEnglishNumbersToDataGrids()
        {
            try
            {
                var dataGrids = new[] { dgGlassPanels, dgRequiredSizes, dgServiceCosts, dgAdditionalCosts };

                foreach (var dataGrid in dataGrids)
                {
                    if (dataGrid != null)
                    {
                        dataGrid.CellEditEnding += (sender, e) =>
                        {
                            if (e.EditingElement is TextBox textBox)
                            {
                                textBox.Text = NumberFormatHelper.ConvertArabicToEnglishNumbers(textBox.Text);
                            }
                        };
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error applying English numbers to DataGrids: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن العناصر الفرعية من نوع معين
        /// </summary>
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                // تم إلغاء تحميل القوائم المنسدلة لتجنب التعطل
                // المستخدم سيدخل البيانات يدوياً

                // تحميل البيانات الموجودة إذا كان هذا تعديل
                LoadExistingOrderData();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل أسماء العملاء - تم إلغاؤها لتجنب مشاكل القوائم المنسدلة
        /// </summary>
        private void LoadCustomers()
        {
            // تم إلغاء تحميل القوائم المنسدلة لتجنب التعطل
            // العميل سيدخل الاسم يدوياً في TextBox
        }

        /// <summary>
        /// تحميل أنواع الزجاج والسماكات
        /// </summary>
        private void LoadGlassTypesAndThickness()
        {
            // أنواع الزجاج
            _glassTypes = new List<string>
            {
                "شفاف",
                "ملون",
                "عاكس",
                "مقسى",
                "لاميناتد",
                "دبل جلاس"
            };

            // السماكات من 1 إلى 100 ملم بزيادة 0.5
            _thicknesses = new List<string>();
            for (decimal thickness = 1.0m; thickness <= 100.0m; thickness += 0.5m)
            {
                _thicknesses.Add($"{thickness:0.0} ملم");
            }
        }

        private List<string> _glassTypes = new List<string>();
        private List<string> _thicknesses = new List<string>();

        /// <summary>
        /// تحميل أسماء الخدمات من موديول المبيعات
        /// </summary>
        private void LoadServiceNames()
        {
            try
            {
                var servicesService = new ServicesService();
                var services = servicesService.GetAllServices();

                _serviceNames = services.Select(s => s.ServiceName).ToList();
            }
            catch (Exception ex)
            {
                // في حالة عدم وجود خدمات، إضافة بيانات تجريبية
                _serviceNames = new List<string>
                {
                    "قص",
                    "تشطيب",
                    "تركيب",
                    "نقل",
                    "تعبئة",
                    "فحص جودة"
                };

                System.Diagnostics.Debug.WriteLine($"Error loading services: {ex.Message}");
            }
        }

        private List<string> _serviceNames = new List<string>();

        #endregion

        #region Glass Panels

        /// <summary>
        /// إضافة لوح زجاج جديد
        /// </summary>
        private void BtnAddGlassPanel_Click(object sender, RoutedEventArgs e)
        {
            var newPanel = new GlassPanelViewModel
            {
                RowNumber = _glassPanels.Count + 1,
                GlassType = "",
                Thickness = "",
                Length = 0,
                Width = 0,
                Quantity = 1
            };

            _glassPanels.Add(newPanel);
            UpdateGlassPanelCalculations();
        }

        /// <summary>
        /// تحديث حسابات ألواح الزجاج
        /// </summary>
        private void UpdateGlassPanelCalculations()
        {
            foreach (var panel in _glassPanels)
            {
                // حساب المساحة بالمتر المربع
                panel.SquareMeters = (panel.Length * panel.Width) / 1000000;

                // حساب إجمالي المساحة
                panel.TotalSquareMeters = panel.SquareMeters * panel.Quantity;

                // حساب إجمالي القيمة
                panel.TotalValue = panel.Price * panel.TotalSquareMeters;
            }

            // تحديث العرض
            dgGlassPanels.ItemsSource = null;
            dgGlassPanels.ItemsSource = _glassPanels;

            // تحديث المجاميع
            CalculateGlassPanelsTotals();
        }

        /// <summary>
        /// حساب مجاميع ألواح الزجاج
        /// </summary>
        private void CalculateGlassPanelsTotals()
        {
            var totalSquareMeters = _glassPanels.Sum(p => p.TotalSquareMeters);
            var totalValue = _glassPanels.Sum(p => p.TotalValue);

            if (txtTotalGlassSquareMeters != null)
                txtTotalGlassSquareMeters.Text = $"مجموع إجمالي المتر المربع: {NumberFormatHelper.FormatDecimal(totalSquareMeters)} م²";

            if (txtTotalGlassValue != null)
                txtTotalGlassValue.Text = $"مجموع إجمالي القيمة: {NumberFormatHelper.FormatCurrency(totalValue)} ج.م";

            // تحديث ملخص التكاليف
            UpdateCostSummary();
        }

        /// <summary>
        /// تعديل لوح زجاج
        /// </summary>
        private void BtnEditGlassPanel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var panel = button?.DataContext as GlassPanelViewModel;
                if (panel != null)
                {
                    // يمكن للمستخدم التعديل مباشرة في الجدول
                    MessageBox.Show("يمكنك التعديل مباشرة في خلايا الجدول", "معلومات",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل لوح الزجاج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف لوح زجاج
        /// </summary>
        private void BtnDeleteGlassPanel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var panel = button?.DataContext as GlassPanelViewModel;
                if (panel != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف لوح الزجاج رقم {panel.RowNumber}؟",
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _glassPanels.Remove(panel);

                        // إعادة ترقيم الصفوف
                        for (int i = 0; i < _glassPanels.Count; i++)
                        {
                            _glassPanels[i].RowNumber = i + 1;
                        }

                        UpdateGlassPanelCalculations();
                        MessageBox.Show("تم حذف لوح الزجاج بنجاح", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف لوح الزجاج: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Required Sizes

        /// <summary>
        /// إضافة مقاس مطلوب جديد
        /// </summary>
        private void BtnAddRequiredSize_Click(object sender, RoutedEventArgs e)
        {
            var newSize = new RequiredSizeViewModel
            {
                RowNumber = _requiredSizes.Count + 1,
                RefCode = GenerateRefCode(),
                GlassType = "",
                Thickness = "",
                Length = 0,
                Width = 0,
                Quantity = 1
            };

            _requiredSizes.Add(newSize);
            UpdateRequiredSizeCalculations();
        }

        /// <summary>
        /// تحديث حسابات المقاسات المطلوبة
        /// </summary>
        private void UpdateRequiredSizeCalculations()
        {
            foreach (var size in _requiredSizes)
            {
                // حساب المساحة بالمتر المربع
                size.SquareMeters = (size.Length * size.Width) / 1000000;

                // حساب إجمالي المساحة
                size.TotalSquareMeters = size.SquareMeters * size.Quantity;
            }

            // تحديث العرض
            dgRequiredSizes.ItemsSource = null;
            dgRequiredSizes.ItemsSource = _requiredSizes;

            // تحديث المجاميع
            CalculateRequiredSizesTotals();
        }

        /// <summary>
        /// حساب مجاميع المقاسات المطلوبة
        /// </summary>
        private void CalculateRequiredSizesTotals()
        {
            var totalRequiredMeters = _requiredSizes.Sum(s => s.TotalSquareMeters);

            if (txtTotalRequiredMeters != null)
                txtTotalRequiredMeters.Text = $"إجمالي الأمتار المطلوبة فعلياً: {NumberFormatHelper.FormatDecimal(totalRequiredMeters)} م²";

            // تحديث المتر الطولي
            UpdateTotalLinearMeters();

            // تحديث حساب الهالك
            CalculateWaste();

            // تحديث ملخص التكاليف
            UpdateCostSummary();
        }

        /// <summary>
        /// توليد كود مرجعي للمقاس
        /// </summary>
        private string GenerateRefCode()
        {
            return $"A{_requiredSizes.Count + 1:D3}";
        }

        /// <summary>
        /// تعديل مقاس مطلوب
        /// </summary>
        private void BtnEditRequiredSize_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var size = button?.DataContext as RequiredSizeViewModel;
                if (size != null)
                {
                    MessageBox.Show("يمكنك التعديل مباشرة في خلايا الجدول", "معلومات",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المقاس: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف مقاس مطلوب
        /// </summary>
        private void BtnDeleteRequiredSize_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var size = button?.DataContext as RequiredSizeViewModel;
                if (size != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف المقاس {size.RefCode}؟",
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _requiredSizes.Remove(size);

                        // إعادة ترقيم الصفوف وإعادة توليد أكواد المرجع
                        for (int i = 0; i < _requiredSizes.Count; i++)
                        {
                            _requiredSizes[i].RowNumber = i + 1;
                            _requiredSizes[i].RefCode = $"A{i + 1:D3}";
                        }

                        UpdateRequiredSizeCalculations();
                        MessageBox.Show("تم حذف المقاس بنجاح", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المقاس: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Services

        /// <summary>
        /// تفعيل/إلغاء تفعيل خدمة
        /// </summary>
        private void ServiceCheckBox_Checked(object sender, RoutedEventArgs e)
        {
            var checkBox = sender as CheckBox;
            if (checkBox != null)
            {
                ShowServiceDetails(checkBox.Name, true);
            }
        }

        /// <summary>
        /// إلغاء تفعيل خدمة
        /// </summary>
        private void ServiceCheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            var checkBox = sender as CheckBox;
            if (checkBox != null)
            {
                ShowServiceDetails(checkBox.Name, false);
            }
        }

        /// <summary>
        /// عرض تفاصيل الخدمة
        /// </summary>
        private void ShowServiceDetails(string serviceName, bool show)
        {
            // إزالة التفاصيل الموجودة لهذه الخدمة
            RemoveServiceDetails(serviceName);

            if (show)
            {
                switch (serviceName)
                {
                    case "chkFilmService":
                        AddFilmServiceDetails();
                        break;
                    case "chkDoubleGlassService":
                        AddDoubleGlassServiceDetails();
                        break;

                    case "chkBevelService":
                        AddBevelServiceDetails();
                        break;
                }
            }
        }

        /// <summary>
        /// إزالة تفاصيل الخدمة
        /// </summary>
        private void RemoveServiceDetails(string serviceName)
        {
            var elementsToRemove = new List<UIElement>();
            
            foreach (UIElement element in spServiceDetails.Children)
            {
                if (element is GroupBox groupBox && groupBox.Tag?.ToString() == serviceName)
                {
                    elementsToRemove.Add(element);
                }
            }

            foreach (var element in elementsToRemove)
            {
                spServiceDetails.Children.Remove(element);
            }
        }

        /// <summary>
        /// إضافة تفاصيل خدمة الفيلم مع جدول البيانات
        /// </summary>
        private void AddFilmServiceDetails()
        {
            var groupBox = new GroupBox
            {
                Header = "🎬 تفاصيل خدمة الفيلم",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 30), // زيادة المسافة السفلية
                Tag = "chkFilmService"
            };

            var stackPanel = new StackPanel { Margin = new Thickness(10) };

            // زر إضافة فيلم
            var btnAddFilm = new Button
            {
                Content = "➕ إضافة فيلم",
                Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)),
                Foreground = Brushes.White,
                Padding = new Thickness(10, 5, 10, 5),
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            btnAddFilm.Click += BtnAddFilm_Click;

            // جدول بيانات الفيلم
            var dgFilm = new DataGrid
            {
                Name = "dgFilmService",
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                CanUserDeleteRows = true,
                GridLinesVisibility = DataGridGridLinesVisibility.All,
                HeadersVisibility = DataGridHeadersVisibility.All,
                FontSize = 12,
                MinHeight = 150
            };

            // أعمدة جدول الفيلم
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "ت", Binding = new Binding("RowNumber"), Width = 40, IsReadOnly = true });
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "نوع الفيلم", Binding = new Binding("FilmType"), Width = 120 });
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "الطول (ملم)", Binding = new Binding("Length"), Width = 100 });
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "العرض (ملم)", Binding = new Binding("Width"), Width = 100 });
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "م²", Binding = new Binding("SquareMeters"), Width = 80, IsReadOnly = true });
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "العدد", Binding = new Binding("Count"), Width = 80 });
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "إجمالي م²", Binding = new Binding("TotalSquareMeters"), Width = 100, IsReadOnly = true });
            dgFilm.Columns.Add(new DataGridTextColumn { Header = "اتجاه العاكس", Binding = new Binding("ReflectiveDirection"), Width = 120 });

            // ربط البيانات
            dgFilm.ItemsSource = _filmServices;

            // إضافة العناصر للمجموعة
            stackPanel.Children.Add(btnAddFilm);
            stackPanel.Children.Add(dgFilm);

            groupBox.Content = stackPanel;
            spServiceDetails.Children.Add(groupBox);
        }

        /// <summary>
        /// إضافة فيلم جديد
        /// </summary>
        private void BtnAddFilm_Click(object sender, RoutedEventArgs e)
        {
            var newFilm = new FilmServiceViewModel
            {
                RowNumber = _filmServices.Count + 1,
                FilmType = "",
                Length = 0,
                Width = 0,
                Count = 1,
                ReflectiveDirection = "من الداخل"
            };

            _filmServices.Add(newFilm);
        }

        /// <summary>
        /// إضافة تفاصيل خدمة الدبل جلاس مع جدول البيانات
        /// </summary>
        private void AddDoubleGlassServiceDetails()
        {
            var groupBox = new GroupBox
            {
                Header = "🔄 تفاصيل خدمة الدبل جلاس",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 30), // زيادة المسافة السفلية
                Tag = "chkDoubleGlassService"
            };

            var stackPanel = new StackPanel { Margin = new Thickness(10) };

            // زر إضافة دبل جلاس
            var btnAddDoubleGlass = new Button
            {
                Content = "➕ إضافة دبل جلاس",
                Background = new SolidColorBrush(Color.FromRgb(23, 162, 184)),
                Foreground = Brushes.White,
                Padding = new Thickness(10, 5, 10, 5),
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 10)
            };
            btnAddDoubleGlass.Click += BtnAddDoubleGlass_Click;

            // جدول بيانات الدبل جلاس
            var dgDoubleGlass = new DataGrid
            {
                Name = "dgDoubleGlassService",
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                CanUserDeleteRows = true,
                GridLinesVisibility = DataGridGridLinesVisibility.All,
                HeadersVisibility = DataGridHeadersVisibility.All,
                FontSize = 12,
                MinHeight = 150
            };

            // أعمدة جدول الدبل جلاس
            dgDoubleGlass.Columns.Add(new DataGridTextColumn { Header = "ت", Binding = new Binding("RowNumber"), Width = 40, IsReadOnly = true });
            dgDoubleGlass.Columns.Add(new DataGridTextColumn { Header = "نوع السبيسر", Binding = new Binding("SpacerType"), Width = 120 });
            dgDoubleGlass.Columns.Add(new DataGridTextColumn { Header = "طول السبيسر", Binding = new Binding("SpacerLength"), Width = 100 });
            dgDoubleGlass.Columns.Add(new DataGridTextColumn { Header = "العدد", Binding = new Binding("Count"), Width = 80 });
            dgDoubleGlass.Columns.Add(new DataGridTextColumn { Header = "إجمالي المتر الطولي", Binding = new Binding("TotalLinearMeters"), Width = 120, IsReadOnly = true });
            dgDoubleGlass.Columns.Add(new DataGridTextColumn { Header = "اتجاه العاكس", Binding = new Binding("ReflectiveDirection"), Width = 120 });
            dgDoubleGlass.Columns.Add(new DataGridTextColumn { Header = "ملاحظات", Binding = new Binding("Notes"), Width = 200 });

            // ربط البيانات
            dgDoubleGlass.ItemsSource = _doubleGlassServices;

            // مجموع إجمالي المتر الطولي
            var borderTotal = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(227, 242, 253)),
                Padding = new Thickness(10),
                Margin = new Thickness(0, 10, 0, 0),
                CornerRadius = new CornerRadius(5)
            };

            var txtTotalLinearMeters = new TextBlock
            {
                Name = "txtTotalDoubleGlassLinearMeters",
                Text = "مجموع إجمالي المتر الطولي: 0.00 م",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                HorizontalAlignment = HorizontalAlignment.Center
            };

            borderTotal.Child = txtTotalLinearMeters;

            // إضافة العناصر للمجموعة
            stackPanel.Children.Add(btnAddDoubleGlass);
            stackPanel.Children.Add(dgDoubleGlass);
            stackPanel.Children.Add(borderTotal);

            groupBox.Content = stackPanel;
            spServiceDetails.Children.Add(groupBox);
        }

        /// <summary>
        /// إضافة دبل جلاس جديد
        /// </summary>
        private void BtnAddDoubleGlass_Click(object sender, RoutedEventArgs e)
        {
            var newDoubleGlass = new DoubleGlassServiceViewModel
            {
                RowNumber = _doubleGlassServices.Count + 1,
                SpacerType = "",
                SpacerLength = 0,
                Count = 1,
                ReflectiveDirection = "من الداخل",
                Notes = ""
            };

            _doubleGlassServices.Add(newDoubleGlass);
            UpdateDoubleGlassCalculations();
        }

        /// <summary>
        /// تحديث حسابات الدبل جلاس
        /// </summary>
        private void UpdateDoubleGlassCalculations()
        {
            decimal totalLinearMeters = 0;

            foreach (var doubleGlass in _doubleGlassServices)
            {
                // حساب إجمالي المتر الطولي = العدد × طول السبيسر (بالمتر)
                doubleGlass.TotalLinearMeters = (doubleGlass.Count * doubleGlass.SpacerLength) / 1000;
                totalLinearMeters += doubleGlass.TotalLinearMeters;
            }

            // البحث عن TextBlock المجموع وتحديثه
            foreach (UIElement element in spServiceDetails.Children)
            {
                if (element is GroupBox groupBox && groupBox.Tag?.ToString() == "chkDoubleGlassService")
                {
                    var stackPanel = groupBox.Content as StackPanel;
                    if (stackPanel != null)
                    {
                        foreach (UIElement child in stackPanel.Children)
                        {
                            if (child is Border border && border.Child is TextBlock textBlock &&
                                textBlock.Name == "txtTotalDoubleGlassLinearMeters")
                            {
                                textBlock.Text = $"مجموع إجمالي المتر الطولي: {totalLinearMeters:F2} م";
                                break;
                            }
                        }
                    }
                    break;
                }
            }
        }



        /// <summary>
        /// إضافة تفاصيل خدمة الشطف
        /// </summary>
        private void AddBevelServiceDetails()
        {
            var groupBox = new GroupBox
            {
                Header = "✂️ تفاصيل خدمة الشطف",
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 10),
                Tag = "chkBevelService"
            };

            var stackPanel = new StackPanel { Margin = new Thickness(10) };
            
            var lblBevelValue = new TextBlock { Text = "قيمة الشطف (ملم):", FontWeight = FontWeights.Bold };
            var txtBevelValue = new TextBox { Margin = new Thickness(0, 5, 0, 0) };

            stackPanel.Children.Add(lblBevelValue);
            stackPanel.Children.Add(txtBevelValue);

            groupBox.Content = stackPanel;
            spServiceDetails.Children.Add(groupBox);
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// حدث انتهاء تحرير خلية في جدول ألواح الزجاج
        /// </summary>
        private void DgGlassPanels_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            // تأخير التحديث قليلاً للسماح بحفظ القيمة الجديدة
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateGlassPanelCalculations();
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        /// <summary>
        /// حدث انتهاء تحرير خلية في جدول المقاسات المطلوبة
        /// </summary>
        private void DgRequiredSizes_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            // تأخير التحديث قليلاً للسماح بحفظ القيمة الجديدة
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateRequiredSizeCalculations();
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        /// <summary>
        /// حدث انتهاء تحرير خلية في جدول تكلفة الخدمات
        /// </summary>
        private void DgServiceCosts_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            // تأخير التحديث قليلاً للسماح بحفظ القيمة الجديدة
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateServiceCostCalculations();
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        /// <summary>
        /// حدث انتهاء تحرير خلية في جدول التكاليف الإضافية
        /// </summary>
        private void DgAdditionalCosts_CellEditEnding(object sender, DataGridCellEditEndingEventArgs e)
        {
            // تأخير التحديث قليلاً للسماح بحفظ القيمة الجديدة
            Dispatcher.BeginInvoke(new Action(() =>
            {
                UpdateAdditionalCostCalculations();
            }), System.Windows.Threading.DispatcherPriority.Background);
        }

        #endregion

        #region Waste Calculations

        /// <summary>
        /// حساب هالك الزجاج والفيلم
        /// </summary>
        private void CalculateWaste()
        {
            try
            {
                // حساب مجموع إجمالي المتر المربع من جدول ألواح الزجاج المستخدمة
                decimal totalGlassMeters = _glassPanels.Sum(g => g.TotalSquareMeters);

                // حساب مجموع الأمتار المطلوبة فعلياً من جدول المقاسات
                decimal totalRequiredMeters = _requiredSizes.Sum(r => r.TotalSquareMeters);

                // حساب هالك الزجاج = مجموع إجمالي المتر المربع من ألواح الزجاج - مجموع الأمتار المطلوبة فعلياً
                decimal glassWaste = totalGlassMeters - totalRequiredMeters;

                // حساب نسبة الهالك (%) = (هالك الزجاج ÷ إجمالي الزجاج المستخدم) × 100
                decimal glassWastePercentage = totalGlassMeters > 0 ? (glassWaste / totalGlassMeters) * 100 : 0;

                // حساب مجموع إجمالي المتر المربع من جدول الفيلم
                decimal totalFilmMeters = _filmServices.Sum(f => f.TotalSquareMeters);

                // حساب الأمتار المطلوبة فعلياً من المقاسات الخاصة بالفيلم فقط
                // (يجب أن تكون المقاسات التي تحتاج فيلم محددة بطريقة ما - سنفترض جميع المقاسات تحتاج فيلم للآن)
                decimal totalRequiredFilmMeters = _requiredSizes
                    .Where(r => !string.IsNullOrEmpty(r.GlassType) &&
                               (r.GlassType.Contains("فيلم") || r.GlassType.Contains("Film") ||
                                r.GlassType.Contains("مزدوج") || r.GlassType.Contains("Double")))
                    .Sum(r => r.TotalSquareMeters);

                // إذا لم توجد مقاسات فيلم محددة، استخدم جميع المقاسات
                if (totalRequiredFilmMeters == 0)
                    totalRequiredFilmMeters = totalRequiredMeters;

                // حساب هالك الفيلم = مجموع إجمالي المتر المربع من الفيلم - الأمتار المطلوبة فعلياً للفيلم
                decimal filmWaste = totalFilmMeters - totalRequiredFilmMeters;

                // حساب نسبة هالك الفيلم = (هالك الفيلم ÷ إجمالي الفيلم المستخدم) × 100
                decimal filmWastePercentage = totalFilmMeters > 0 ? (filmWaste / totalFilmMeters) * 100 : 0;

                // تحديث النصوص بالتنسيق الإنجليزي
                if (txtGlassWaste != null)
                    txtGlassWaste.Text = $"{NumberFormatHelper.FormatDecimal(glassWaste)} م²";

                if (txtGlassWastePercentage != null)
                    txtGlassWastePercentage.Text = $"{NumberFormatHelper.FormatDecimal(glassWastePercentage)}%";

                if (txtFilmWaste != null)
                    txtFilmWaste.Text = $"{NumberFormatHelper.FormatDecimal(filmWaste)} م²";

                if (txtFilmWastePercentage != null)
                    txtFilmWastePercentage.Text = $"{NumberFormatHelper.FormatDecimal(filmWastePercentage)}%";

                // تسجيل للتشخيص
                System.Diagnostics.Debug.WriteLine($"Glass Waste Calculation:");
                System.Diagnostics.Debug.WriteLine($"  Total Glass Meters: {totalGlassMeters}");
                System.Diagnostics.Debug.WriteLine($"  Total Required Meters: {totalRequiredMeters}");
                System.Diagnostics.Debug.WriteLine($"  Glass Waste: {glassWaste}");
                System.Diagnostics.Debug.WriteLine($"  Glass Waste Percentage: {glassWastePercentage}%");
                System.Diagnostics.Debug.WriteLine($"Film Waste Calculation:");
                System.Diagnostics.Debug.WriteLine($"  Total Film Meters: {totalFilmMeters}");
                System.Diagnostics.Debug.WriteLine($"  Total Required Film Meters: {totalRequiredFilmMeters}");
                System.Diagnostics.Debug.WriteLine($"  Film Waste: {filmWaste}");
                System.Diagnostics.Debug.WriteLine($"  Film Waste Percentage: {filmWastePercentage}%");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in CalculateWaste: {ex.Message}");
                MessageBox.Show($"خطأ في حساب الهالك: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث إجمالي المتر الطولي
        /// </summary>
        private void UpdateTotalLinearMeters()
        {
            try
            {
                decimal totalLinearMeters = _requiredSizes.Sum(r => r.LinearMeters * r.Quantity);
                txtTotalLinearMeters.Text = $"إجمالي المتر الطولي: {totalLinearMeters:F2} م";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب المتر الطولي: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Service Costs

        /// <summary>
        /// عرض قسم تكلفة الخدمات
        /// </summary>
        private void BtnCalculateServiceCosts_Click(object sender, RoutedEventArgs e)
        {
            spServiceCosts.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// إضافة تكلفة خدمة جديدة
        /// </summary>
        private void BtnAddServiceCost_Click(object sender, RoutedEventArgs e)
        {
            var newCost = new ServiceCostViewModel
            {
                RowNumber = _serviceCosts.Count + 1,
                ServiceName = "",
                Description = "",
                Quantity = 1,
                Price = 0
            };

            _serviceCosts.Add(newCost);
            UpdateServiceCostCalculations();
        }

        /// <summary>
        /// تحديث حسابات تكلفة الخدمات - تلقائياً
        /// </summary>
        private void UpdateServiceCostCalculations()
        {
            decimal totalServiceCosts = 0;

            foreach (var cost in _serviceCosts)
            {
                // حساب القيمة تلقائياً
                cost.Value = cost.Quantity * cost.Price;
                totalServiceCosts += cost.Value;
            }

            // تحديث إجمالي تكلفة الخدمات فوراً
            txtTotalServiceCosts.Text = $"إجمالي تكلفة الخدمات: {totalServiceCosts:F2} ج.م";

            // تحديث العرض
            dgServiceCosts.ItemsSource = null;
            dgServiceCosts.ItemsSource = _serviceCosts;

            // تحديث ملخص التكاليف فوراً
            UpdateCostSummary();
        }





        /// <summary>
        /// حسابات تكلفة الخدمات التلقائية
        /// </summary>
        private void CalculateServiceCostsTotals()
        {
            decimal totalServiceCosts = 0;

            foreach (var cost in _serviceCosts)
            {
                // حساب القيمة تلقائياً: الكمية × السعر
                cost.Value = cost.Quantity * cost.Price;
                totalServiceCosts += cost.Value;
            }

            // تحديث إجمالي تكلفة الخدمات فوراً
            txtTotalServiceCosts.Text = $"إجمالي تكلفة الخدمات: {NumberFormatHelper.FormatCurrency(totalServiceCosts)} ج.م";

            dgServiceCosts.ItemsSource = null;
            dgServiceCosts.ItemsSource = _serviceCosts;
            UpdateCostSummary();
        }

        /// <summary>
        /// حسابات التكاليف الإضافية التلقائية
        /// </summary>
        private void CalculateAdditionalCostsTotals()
        {
            decimal totalAdditionalCosts = 0;

            foreach (var cost in _additionalCosts)
            {
                totalAdditionalCosts += cost.Value;
            }

            // تحديث إجمالي التكاليف الإضافية فوراً
            txtTotalAdditionalCosts.Text = $"إجمالي التكاليف المحملة: {NumberFormatHelper.FormatCurrency(totalAdditionalCosts)} ج.م";

            dgAdditionalCosts.ItemsSource = null;
            dgAdditionalCosts.ItemsSource = _additionalCosts;
            UpdateCostSummary();
        }

        /// <summary>
        /// تعديل تكلفة خدمة
        /// </summary>
        private void BtnEditServiceCost_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var cost = button?.DataContext as ServiceCostViewModel;
                if (cost != null)
                {
                    MessageBox.Show("يمكنك التعديل مباشرة في خلايا الجدول", "معلومات",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل تكلفة الخدمة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف تكلفة خدمة
        /// </summary>
        private void BtnDeleteServiceCost_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var cost = button?.DataContext as ServiceCostViewModel;
                if (cost != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف خدمة {cost.ServiceName}؟",
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _serviceCosts.Remove(cost);

                        // إعادة ترقيم الصفوف
                        for (int i = 0; i < _serviceCosts.Count; i++)
                        {
                            _serviceCosts[i].RowNumber = i + 1;
                        }

                        UpdateServiceCostCalculations();
                        MessageBox.Show("تم حذف تكلفة الخدمة بنجاح", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف تكلفة الخدمة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Additional Costs

        /// <summary>
        /// إضافة تكلفة إضافية جديدة
        /// </summary>
        private void BtnAddAdditionalCost_Click(object sender, RoutedEventArgs e)
        {
            var newCost = new AdditionalCostViewModel
            {
                RowNumber = _additionalCosts.Count + 1,
                Description = "",
                Value = 0,
                Notes = ""
            };

            _additionalCosts.Add(newCost);
            UpdateAdditionalCostCalculations();
        }

        /// <summary>
        /// تحديث حسابات التكاليف الإضافية
        /// </summary>
        private void UpdateAdditionalCostCalculations()
        {
            decimal totalAdditionalCosts = _additionalCosts.Sum(c => c.Value);

            // تحديث إجمالي التكاليف الإضافية
            txtTotalAdditionalCosts.Text = $"إجمالي التكاليف المحملة: {totalAdditionalCosts:F2} ج.م";

            // تحديث العرض
            dgAdditionalCosts.ItemsSource = null;
            dgAdditionalCosts.ItemsSource = _additionalCosts;
            
            // تحديث ملخص التكاليف
            UpdateCostSummary();
        }

        /// <summary>
        /// تعديل تكلفة إضافية
        /// </summary>
        private void BtnEditAdditionalCost_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var cost = button?.DataContext as AdditionalCostViewModel;
                if (cost != null)
                {
                    MessageBox.Show("يمكنك التعديل مباشرة في خلايا الجدول", "معلومات",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل التكلفة الإضافية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حذف تكلفة إضافية
        /// </summary>
        private void BtnDeleteAdditionalCost_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var button = sender as Button;
                var cost = button?.DataContext as AdditionalCostViewModel;
                if (cost != null)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف التكلفة الإضافية: {cost.Description}؟",
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _additionalCosts.Remove(cost);

                        // إعادة ترقيم الصفوف
                        for (int i = 0; i < _additionalCosts.Count; i++)
                        {
                            _additionalCosts[i].RowNumber = i + 1;
                        }

                        UpdateAdditionalCostCalculations();
                        MessageBox.Show("تم حذف التكلفة الإضافية بنجاح", "نجح الحذف",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف التكلفة الإضافية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Cost Summary

        /// <summary>
        /// تحديث ملخص التكاليف حسب المتطلبات المحددة
        /// </summary>
        private void UpdateCostSummary()
        {
            try
            {
                // 1. إجمالي تكلفة الخدمات
                decimal totalServiceCosts = _serviceCosts.Sum(c => c.Value);

                // 2. إجمالي التكاليف الإضافية
                decimal totalAdditionalCosts = _additionalCosts.Sum(c => c.Value);

                // 3. إجمالي قيمة الزجاج المستخدم
                decimal totalGlassCosts = _glassPanels.Sum(p => p.TotalValue);

                // 4. المجموع الكلي = الخدمات + التكاليف الإضافية + قيمة الزجاج
                decimal grandTotal = totalServiceCosts + totalAdditionalCosts + totalGlassCosts;

                // 5. إجمالي الأمتار المطلوبة فعلياً
                decimal totalRequiredMeters = _requiredSizes.Sum(s => s.TotalSquareMeters);

                // 6. سعر المتر = المجموع الكلي ÷ الأمتار المطلوبة فعلياً
                decimal pricePerMeter = totalRequiredMeters > 0 ? grandTotal / totalRequiredMeters : 0;

                // تحديث النصوص في الملخص بالترتيب المطلوب:
                if (txtSummaryServiceCosts != null)
                    txtSummaryServiceCosts.Text = $"{NumberFormatHelper.FormatCurrency(totalServiceCosts)} ج.م";

                if (txtSummaryAdditionalCosts != null)
                    txtSummaryAdditionalCosts.Text = $"{NumberFormatHelper.FormatCurrency(totalAdditionalCosts)} ج.م";

                if (txtSummaryGlassCosts != null)
                    txtSummaryGlassCosts.Text = $"{NumberFormatHelper.FormatCurrency(totalGlassCosts)} ج.م";

                if (txtSummaryTotalCost != null)
                    txtSummaryTotalCost.Text = $"{NumberFormatHelper.FormatCurrency(grandTotal)} ج.م";

                if (txtSummaryTotalMeters != null)
                    txtSummaryTotalMeters.Text = $"{NumberFormatHelper.FormatDecimal(totalRequiredMeters)} م²";

                if (txtSummaryPricePerMeter != null)
                    txtSummaryPricePerMeter.Text = $"{NumberFormatHelper.FormatCurrency(pricePerMeter)} ج.م/م²";

                // تحديث الأمر الحالي
                _currentOrder.TotalServicesCost = totalServiceCosts;
                _currentOrder.TotalAdditionalCosts = totalAdditionalCosts;
                _currentOrder.TotalGlassCosts = totalGlassCosts;
                _currentOrder.TotalCost = grandTotal;
                _currentOrder.TotalSquareMeters = totalRequiredMeters;
                _currentOrder.PricePerMeter = pricePerMeter;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating cost summary: {ex.Message}");
            }
        }

        #endregion

        #region Navigation

        /// <summary>
        /// الرجوع للقائمة الرئيسية
        /// </summary>
        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // البحث عن النافذة الرئيسية
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    // العودة لصفحة التصنيع الرئيسية
                    var manufacturingView = new ManufacturingView();
                    mainWindow.ShowView(manufacturingView, "وحدة التصنيع");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة للقائمة الرئيسية: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إظهار/إخفاء أدوات التنقل
        /// </summary>
        private void BtnToggleNavigationTools_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_areNavigationToolsVisible)
                {
                    // إخفاء أدوات التنقل
                    BasicDataPanel.Visibility = Visibility.Collapsed;
                    ServicesPanel.Visibility = Visibility.Collapsed;
                    ServiceCostsPanel.Visibility = Visibility.Collapsed;
                    AdditionalCostsPanel.Visibility = Visibility.Collapsed;
                    CostSummaryPanel.Visibility = Visibility.Collapsed;
                    WasteCalculationPanel.Visibility = Visibility.Collapsed;

                    btnToggleNavigationTools.Content = "✅ إظهار أدوات التنقل";
                    btnToggleNavigationTools.Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)); // أخضر
                    btnToggleNavigationTools.ToolTip = "إظهار الأقسام الفرعية";
                    _areNavigationToolsVisible = false;
                }
                else
                {
                    // إظهار أدوات التنقل
                    BasicDataPanel.Visibility = Visibility.Visible;
                    ServicesPanel.Visibility = Visibility.Visible;
                    ServiceCostsPanel.Visibility = Visibility.Visible;
                    AdditionalCostsPanel.Visibility = Visibility.Visible;
                    CostSummaryPanel.Visibility = Visibility.Visible;
                    WasteCalculationPanel.Visibility = Visibility.Visible;

                    btnToggleNavigationTools.Content = "❌ إخفاء أدوات التنقل";
                    btnToggleNavigationTools.Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)); // رمادي
                    btnToggleNavigationTools.ToolTip = "إخفاء الأقسام الفرعية لتوسيع مساحة العمل";
                    _areNavigationToolsVisible = true;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إظهار/إخفاء أدوات التنقل: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Main Actions

        /// <summary>
        /// حفظ أمر التصنيع
        /// </summary>
        private void BtnSaveOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات الأساسية
                if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "بيانات ناقصة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    txtCustomerName.Focus();
                    return;
                }

                // التحقق من وجود بيانات للحفظ
                if (!_glassPanels.Any() && !_requiredSizes.Any() && !_serviceCosts.Any() && !_additionalCosts.Any())
                {
                    MessageBox.Show("يرجى إضافة بيانات للحفظ (ألواح زجاج، مقاسات مطلوبة، خدمات، أو تكاليف إضافية)",
                        "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تحديث بيانات الأمر
                _currentOrder.CustomerName = txtCustomerName.Text.Trim();
                _currentOrder.InvoiceNumber = NumberFormatHelper.CleanAndConvertNumbers(txtInvoiceNumber.Text?.Trim() ?? "");
                _currentOrder.OrderDate = dpOrderDate.SelectedDate ?? DateTime.Now;
                _currentOrder.OrderStatus = ((ComboBoxItem)cmbOrderStatus.SelectedItem)?.Content?.ToString() ?? "تحت التشغيل";
                _currentOrder.Notes = txtNotes.Text?.Trim() ?? "";
                _currentOrder.ModifiedDate = DateTime.Now;
                _currentOrder.ModifiedBy = "النظام";

                // تحديث ملخص التكاليف قبل الحفظ
                UpdateCostSummary();

                // تحديث بيانات التكلفة في الأمر
                var totalServiceCosts = _serviceCosts.Sum(c => c.Value);
                var totalAdditionalCosts = _additionalCosts.Sum(c => c.Value);
                var totalRequiredMeters = _requiredSizes.Sum(s => s.TotalSquareMeters);

                _currentOrder.TotalServicesCost = totalServiceCosts;
                _currentOrder.TotalAdditionalCosts = totalAdditionalCosts;
                _currentOrder.TotalCost = totalServiceCosts + totalAdditionalCosts;
                _currentOrder.TotalSquareMeters = totalRequiredMeters;
                _currentOrder.PricePerMeter = totalRequiredMeters > 0 ? _currentOrder.TotalCost / totalRequiredMeters : 0;

                // حفظ الأمر الأساسي أولاً
                bool success = _manufacturingService.SaveManufacturingOrder(_currentOrder);

                if (success)
                {
                    bool allDetailsSaved = true;
                    string errorMessage = "";

                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"Saving details for order ID: {_currentOrder.Id}");

                        // حفظ ألواح الزجاج
                        if (_glassPanels.Any())
                        {
                            var glassPanelsList = _glassPanels.Select(panel => new GlassPanel
                            {
                                ManufacturingOrderId = _currentOrder.Id,
                                GlassType = panel.GlassType ?? "",
                                Thickness = panel.Thickness ?? "",
                                Length = panel.Length,
                                Width = panel.Width,
                                SquareMeters = panel.SquareMeters,
                                Quantity = panel.Quantity,
                                TotalSquareMeters = panel.TotalSquareMeters,
                                Notes = panel.Notes ?? ""
                            }).ToList();

                            System.Diagnostics.Debug.WriteLine($"Saving {glassPanelsList.Count} glass panels");
                            if (!_manufacturingService.SaveGlassPanels(_currentOrder.Id, glassPanelsList))
                            {
                                allDetailsSaved = false;
                                errorMessage += "فشل في حفظ ألواح الزجاج. ";
                                System.Diagnostics.Debug.WriteLine("Failed to save glass panels");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("Glass panels saved successfully");
                            }
                        }

                        // حفظ المقاسات المطلوبة
                        if (_requiredSizes.Any())
                        {
                            var requiredSizesList = _requiredSizes.Select(size => new RequiredSize
                            {
                                ManufacturingOrderId = _currentOrder.Id,
                                RefCode = size.RefCode ?? "",
                                GlassType = size.GlassType ?? "",
                                Thickness = size.Thickness ?? "",
                                Length = size.Length,
                                Width = size.Width,
                                SquareMeters = size.SquareMeters,
                                Quantity = size.Quantity,
                                TotalSquareMeters = size.TotalSquareMeters,
                                IsDelivered = false
                            }).ToList();

                            System.Diagnostics.Debug.WriteLine($"Saving {requiredSizesList.Count} required sizes");
                            if (!_manufacturingService.SaveRequiredSizes(_currentOrder.Id, requiredSizesList))
                            {
                                allDetailsSaved = false;
                                errorMessage += "فشل في حفظ المقاسات المطلوبة. ";
                                System.Diagnostics.Debug.WriteLine("Failed to save required sizes");
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("Required sizes saved successfully");
                            }
                        }

                        // حفظ تكاليف الخدمات
                        if (_serviceCosts.Any())
                        {
                            var serviceCostsList = _serviceCosts.Select(cost => new ServiceCost
                            {
                                ManufacturingOrderId = _currentOrder.Id,
                                ServiceName = cost.ServiceName,
                                Description = cost.Description ?? "",
                                Quantity = cost.Quantity,
                                Price = cost.Price,
                                Value = cost.Value
                            }).ToList();

                            if (!_manufacturingService.SaveServiceCosts(_currentOrder.Id, serviceCostsList))
                            {
                                allDetailsSaved = false;
                                errorMessage += "فشل في حفظ تكاليف الخدمات. ";
                            }
                        }

                        // حفظ التكاليف الإضافية
                        if (_additionalCosts.Any())
                        {
                            var additionalCostsList = _additionalCosts.Select(cost => new AdditionalCost
                            {
                                ManufacturingOrderId = _currentOrder.Id,
                                Description = cost.Description,
                                Value = cost.Value,
                                Notes = cost.Notes ?? ""
                            }).ToList();

                            if (!_manufacturingService.SaveAdditionalCosts(_currentOrder.Id, additionalCostsList))
                            {
                                allDetailsSaved = false;
                                errorMessage += "فشل في حفظ التكاليف الإضافية. ";
                            }
                        }

                        if (allDetailsSaved)
                        {
                            MessageBox.Show("تم حفظ أمر التصنيع وجميع تفاصيله بنجاح!", "نجح الحفظ",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show($"تم حفظ الأمر الأساسي ولكن حدثت أخطاء في حفظ التفاصيل:\n{errorMessage}",
                                "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                        }
                    }
                    catch (Exception detailsEx)
                    {
                        MessageBox.Show($"تم حفظ الأمر الأساسي ولكن حدث خطأ في حفظ التفاصيل: {detailsEx.Message}",
                            "تحذير", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("فشل في حفظ أمر التصنيع الأساسي!", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ أمر التصنيع: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض أمر التصنيع
        /// </summary>
        private void BtnViewOrder_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير صفحة عرض أمر التصنيع قريباً", "قيد التطوير",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// ترحيل إلى أمر التسليم
        /// </summary>
        private void BtnTransferToDelivery_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود أمر محفوظ
                if (_currentOrder.Id <= 0)
                {
                    MessageBox.Show("يرجى حفظ أمر التصنيع أولاً قبل الترحيل إلى أمر التسليم",
                        "أمر غير محفوظ", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // التحقق من وجود مقاسات مطلوبة
                if (!_requiredSizes.Any())
                {
                    MessageBox.Show("لا توجد مقاسات مطلوبة للترحيل إلى أمر التسليم",
                        "لا توجد بيانات", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // التحقق من وجود مقاسات غير مسلمة
                var undeliveredSizes = _requiredSizes.Where(s => !s.IsDelivered).ToList();
                if (!undeliveredSizes.Any())
                {
                    MessageBox.Show("جميع المقاسات تم تسليمها بالفعل",
                        "لا توجد مقاسات للتسليم", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                // عرض رسالة تأكيد
                var result = MessageBox.Show(
                    $"سيتم ترحيل {undeliveredSizes.Count} مقاس إلى أمر التسليم.\nهل تريد المتابعة؟",
                    "تأكيد الترحيل",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // فتح صفحة أمر التسليم مع البيانات المرحلة
                    var deliveryOrderView = new DeliveryOrderView(_currentOrder);

                    // إخفاء النافذة الحالية
                    var parentWindow = Window.GetWindow(this);
                    if (parentWindow != null)
                    {
                        parentWindow.Hide();
                    }

                    // عرض نافذة أمر التسليم
                    var deliveryWindow = new Window
                    {
                        Title = "أمر التسليم",
                        Content = deliveryOrderView,
                        WindowState = WindowState.Maximized,
                        WindowStartupLocation = WindowStartupLocation.CenterScreen
                    };

                    deliveryWindow.Closed += (s, args) =>
                    {
                        // إعادة عرض النافذة الحالية عند إغلاق نافذة التسليم
                        if (parentWindow != null)
                        {
                            parentWindow.Show();
                            // إعادة تحميل البيانات لتحديث حالة التسليم
                            LoadExistingOrderData();
                        }
                    };

                    deliveryWindow.ShowDialog();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في ترحيل البيانات إلى أمر التسليم: {ex.Message}",
                    "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة PDF احترافية باستخدام QuestPDF
        /// </summary>
        private void BtnPrintPDF_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من وجود بيانات للطباعة
                if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
                {
                    MessageBox.Show("يرجى إدخال بيانات الأمر أولاً", "بيانات ناقصة",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // مربع حوار حفظ الملف
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "حفظ تقرير أمر التصنيع",
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    FileName = $"Manufacturing_Order_{NumberFormatHelper.EnsureEnglishNumbers(_currentOrder.OrderNumber)}_{DateTime.Now:yyyyMMdd}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    CreateProfessionalManufacturingReport(saveDialog.FileName);
                    MessageBox.Show($"تم إنشاء تقرير أمر التصنيع الاحترافي بنجاح في:\n{saveDialog.FileName}",
                        "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);

                    // فتح الملف تلقائياً
                    try
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = saveDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                    catch
                    {
                        // في حالة فشل فتح الملف، لا نعرض خطأ
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تسليم الطلب
        /// </summary>
        private void BtnDeliveryOrder_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير صفحة تسليم الطلب قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// إنشاء تقرير أمر التصنيع الشامل
        /// </summary>
        private void CreateComprehensiveManufacturingReport(string fileName)
        {
            try
            {
                using (var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4))
                {
                    using (var writer = iTextSharp.text.pdf.PdfWriter.GetInstance(document, new FileStream(fileName, FileMode.Create)))
                    {
                        document.Open();

                        // إضافة الخط العربي
                        var arabicFont = iTextSharp.text.pdf.BaseFont.CreateFont("c:\\windows\\fonts\\arial.ttf",
                            iTextSharp.text.pdf.BaseFont.IDENTITY_H, iTextSharp.text.pdf.BaseFont.EMBEDDED);
                        var titleFont = new iTextSharp.text.Font(arabicFont, 18, iTextSharp.text.Font.BOLD);
                        var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
                        var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);

                        // العنوان الرئيسي
                        var title = new iTextSharp.text.Paragraph($"أمر تصنيع رقم: {_currentOrder.OrderNumber}", titleFont);
                        title.Alignment = iTextSharp.text.Element.ALIGN_CENTER;
                        title.SpacingAfter = 20f;
                        document.Add(title);

                        // معلومات الأمر الأساسية
                        AddBasicOrderInfoToReport(document, headerFont, normalFont);

                        // ألواح الزجاج
                        if (_glassPanels.Any())
                        {
                            AddGlassPanelsToReport(document, headerFont, normalFont);
                        }

                        // المقاسات المطلوبة
                        if (_requiredSizes.Any())
                        {
                            AddRequiredSizesToReport(document, headerFont, normalFont);
                        }

                        // تكاليف الخدمات
                        if (_serviceCosts.Any())
                        {
                            AddServiceCostsToReport(document, headerFont, normalFont);
                        }

                        // التكاليف الإضافية
                        if (_additionalCosts.Any())
                        {
                            AddAdditionalCostsToReport(document, headerFont, normalFont);
                        }

                        // حساب الهالك
                        AddWasteCalculationToReport(document, headerFont, normalFont);

                        // ملخص التكاليف النهائي
                        AddFinalCostSummaryToReport(document, headerFont, normalFont);

                        document.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة معلومات الأمر الأساسية للتقرير
        /// </summary>
        private void AddBasicOrderInfoToReport(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var infoTable = new iTextSharp.text.pdf.PdfPTable(2);
            infoTable.WidthPercentage = 100;
            infoTable.SpacingAfter = 15f;

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("اسم العميل:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(txtCustomerName.Text, normalFont)) { Border = 0 });

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("رقم الفاتورة:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(txtInvoiceNumber.Text, normalFont)) { Border = 0 });

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("تاريخ الأمر:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase((dpOrderDate.SelectedDate ?? DateTime.Now).ToString("dd/MM/yyyy"), normalFont)) { Border = 0 });

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("حالة الطلب:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(((ComboBoxItem)cmbOrderStatus.SelectedItem)?.Content?.ToString() ?? "تحت التشغيل", normalFont)) { Border = 0 });

            document.Add(infoTable);
        }

        /// <summary>
        /// إضافة جدول ألواح الزجاج للتقرير
        /// </summary>
        private void AddGlassPanelsToReport(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var header = new iTextSharp.text.Paragraph("ألواح الزجاج المستخدمة", headerFont);
            header.SpacingBefore = 15f;
            header.SpacingAfter = 10f;
            document.Add(header);

            var table = new iTextSharp.text.pdf.PdfPTable(8);
            table.WidthPercentage = 100;
            table.SpacingAfter = 15f;

            // رؤوس الأعمدة
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("ت", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("نوع الزجاج", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("السمك", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الطول", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العرض", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("م²", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العدد", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي م²", headerFont)));

            // البيانات
            for (int i = 0; i < _glassPanels.Count; i++)
            {
                var panel = _glassPanels[i];
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase((i + 1).ToString(), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.GlassType, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Thickness, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Length.ToString("F0"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Width.ToString("F0"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.SquareMeters.ToString("F2"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Quantity.ToString(), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.TotalSquareMeters.ToString("F2"), normalFont)));
            }

            // إضافة صف المجموع
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("المجموع:", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_glassPanels.Sum(p => p.TotalSquareMeters).ToString("F2"), headerFont)));

            document.Add(table);
        }

        /// <summary>
        /// إضافة جدول المقاسات المطلوبة للتقرير
        /// </summary>
        private void AddRequiredSizesToReport(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var header = new iTextSharp.text.Paragraph("المقاسات المطلوبة للقص", headerFont);
            header.SpacingBefore = 15f;
            header.SpacingAfter = 10f;
            document.Add(header);

            var table = new iTextSharp.text.pdf.PdfPTable(8);
            table.WidthPercentage = 100;
            table.SpacingAfter = 15f;

            // رؤوس الأعمدة
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("Ref", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("نوع الزجاج", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("السمك", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الطول", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العرض", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("م²", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العدد", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي م²", headerFont)));

            // البيانات
            foreach (var size in _requiredSizes)
            {
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.RefCode, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.GlassType, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.Thickness, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.Length.ToString("F0"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.Width.ToString("F0"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.SquareMeters.ToString("F2"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.Quantity.ToString(), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.TotalSquareMeters.ToString("F2"), normalFont)));
            }

            // إضافة صف المجموع
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("المجموع:", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_requiredSizes.Sum(s => s.TotalSquareMeters).ToString("F2"), headerFont)));

            document.Add(table);
        }

        /// <summary>
        /// إضافة جدول تكاليف الخدمات للتقرير
        /// </summary>
        private void AddServiceCostsToReport(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var header = new iTextSharp.text.Paragraph("تكاليف الخدمات", headerFont);
            header.SpacingBefore = 15f;
            header.SpacingAfter = 10f;
            document.Add(header);

            var table = new iTextSharp.text.pdf.PdfPTable(5);
            table.WidthPercentage = 100;
            table.SpacingAfter = 15f;

            // رؤوس الأعمدة
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("اسم الخدمة", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الوصف", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الكمية", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("السعر", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("القيمة", headerFont)));

            // البيانات
            foreach (var cost in _serviceCosts)
            {
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.ServiceName, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Description, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Quantity.ToString("F2"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Price.ToString("F2"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Value.ToString("F2"), normalFont)));
            }

            // إضافة صف المجموع
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("المجموع:", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_serviceCosts.Sum(c => c.Value).ToString("F2"), headerFont)));

            document.Add(table);
        }

        /// <summary>
        /// إضافة جدول التكاليف الإضافية للتقرير
        /// </summary>
        private void AddAdditionalCostsToReport(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var header = new iTextSharp.text.Paragraph("التكاليف الإضافية", headerFont);
            header.SpacingBefore = 15f;
            header.SpacingAfter = 10f;
            document.Add(header);

            var table = new iTextSharp.text.pdf.PdfPTable(3);
            table.WidthPercentage = 100;
            table.SpacingAfter = 15f;

            // رؤوس الأعمدة
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("وصف التكلفة", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("القيمة", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("ملاحظات", headerFont)));

            // البيانات
            foreach (var cost in _additionalCosts)
            {
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Description, normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Value.ToString("F2"), normalFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Notes, normalFont)));
            }

            // إضافة صف المجموع
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("المجموع:", headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_additionalCosts.Sum(c => c.Value).ToString("F2"), headerFont)));
            table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", headerFont)));

            document.Add(table);
        }

        /// <summary>
        /// إضافة حساب الهالك للتقرير
        /// </summary>
        private void AddWasteCalculationToReport(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var header = new iTextSharp.text.Paragraph("حساب هالك الزجاج والفيلم", headerFont);
            header.SpacingBefore = 15f;
            header.SpacingAfter = 10f;
            document.Add(header);

            // حساب الهالك
            decimal totalGlassMeters = _glassPanels.Sum(g => g.TotalSquareMeters);
            decimal totalRequiredMeters = _requiredSizes.Sum(r => r.TotalSquareMeters);
            decimal totalFilmMeters = _filmServices.Sum(f => f.TotalSquareMeters);

            decimal glassWaste = totalGlassMeters - totalRequiredMeters;
            decimal glassWastePercentage = totalGlassMeters > 0 ? (glassWaste / totalGlassMeters) * 100 : 0;

            decimal filmWaste = totalFilmMeters - totalRequiredMeters;
            decimal filmWastePercentage = totalFilmMeters > 0 ? (filmWaste / totalFilmMeters) * 100 : 0;

            var wasteTable = new iTextSharp.text.pdf.PdfPTable(2);
            wasteTable.WidthPercentage = 100;
            wasteTable.SpacingAfter = 15f;

            // هالك الزجاج
            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("هالك الزجاج (م²):", normalFont)) { Border = 0 });
            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(glassWaste.ToString("F2"), normalFont)) { Border = 0 });

            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("نسبة هالك الزجاج (%):", normalFont)) { Border = 0 });
            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(glassWastePercentage.ToString("F2") + "%", normalFont)) { Border = 0 });

            // هالك الفيلم
            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("هالك الفيلم (م²):", normalFont)) { Border = 0 });
            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(filmWaste.ToString("F2"), normalFont)) { Border = 0 });

            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("نسبة هالك الفيلم (%):", normalFont)) { Border = 0 });
            wasteTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(filmWastePercentage.ToString("F2") + "%", normalFont)) { Border = 0 });

            document.Add(wasteTable);
        }

        /// <summary>
        /// إضافة ملخص التكاليف النهائي للتقرير
        /// </summary>
        private void AddFinalCostSummaryToReport(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var header = new iTextSharp.text.Paragraph("ملخص التكاليف النهائي", headerFont);
            header.SpacingBefore = 15f;
            header.SpacingAfter = 10f;
            document.Add(header);

            // حساب المجاميع
            decimal totalServiceCosts = _serviceCosts.Sum(c => c.Value);
            decimal totalAdditionalCosts = _additionalCosts.Sum(c => c.Value);
            decimal totalGlassCosts = _glassPanels.Sum(p => p.TotalValue);
            decimal grandTotal = totalServiceCosts + totalAdditionalCosts + totalGlassCosts;
            decimal totalRequiredMeters = _requiredSizes.Sum(s => s.TotalSquareMeters);
            decimal pricePerMeter = totalRequiredMeters > 0 ? grandTotal / totalRequiredMeters : 0;

            var summaryTable = new iTextSharp.text.pdf.PdfPTable(2);
            summaryTable.WidthPercentage = 100;
            summaryTable.SpacingAfter = 15f;

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي تكلفة الخدمات:", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalServiceCosts.ToString("F2") + " ج.م", normalFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي التكاليف الإضافية:", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalAdditionalCosts.ToString("F2") + " ج.م", normalFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي قيمة الزجاج المستخدم:", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalGlassCosts.ToString("F2") + " ج.م", normalFont)) { Border = 0 });

            // خط فاصل
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("", normalFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("المجموع الكلي:", headerFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(grandTotal.ToString("F2") + " ج.م", headerFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي الأمتار المطلوبة فعلياً:", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalRequiredMeters.ToString("F2") + " م²", normalFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("سعر المتر:", headerFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(pricePerMeter.ToString("F2") + " ج.م/م²", headerFont)) { Border = 0 });

            document.Add(summaryTable);

            // إضافة تاريخ الطباعة
            var printDate = new iTextSharp.text.Paragraph($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}", normalFont);
            printDate.Alignment = iTextSharp.text.Element.ALIGN_LEFT;
            printDate.SpacingBefore = 20f;
            document.Add(printDate);
        }

        /// <summary>
        /// إنشاء تقرير أمر التصنيع الاحترافي باستخدام QuestPDF
        /// </summary>
        private void CreateProfessionalManufacturingReport(string filePath)
        {
            try
            {
                // تحديث بيانات الأمر
                _currentOrder.CustomerName = txtCustomerName.Text;
                _currentOrder.InvoiceNumber = txtInvoiceNumber.Text;
                _currentOrder.OrderDate = dpOrderDate.SelectedDate ?? DateTime.Now;
                _currentOrder.OrderStatus = ((ComboBoxItem)cmbOrderStatus.SelectedItem)?.Content?.ToString() ?? "تحت التشغيل";
                _currentOrder.Notes = txtNotes.Text;

                // تحويل البيانات للنماذج المطلوبة
                var glassPanelsList = _glassPanels.Select(panel => new GlassPanel
                {
                    GlassType = panel.GlassType,
                    Thickness = panel.Thickness,
                    Length = panel.Length,
                    Width = panel.Width,
                    SquareMeters = panel.SquareMeters,
                    Quantity = panel.Quantity,
                    TotalSquareMeters = panel.TotalSquareMeters,
                    Notes = panel.Notes ?? ""
                }).ToList();

                var requiredSizesList = _requiredSizes.Select(size => new RequiredSize
                {
                    RefCode = size.RefCode,
                    GlassType = size.GlassType,
                    Thickness = size.Thickness,
                    Length = size.Length,
                    Width = size.Width,
                    SquareMeters = size.SquareMeters,
                    Quantity = size.Quantity,
                    TotalSquareMeters = size.TotalSquareMeters
                }).ToList();

                var serviceCostsList = _serviceCosts.Select(cost => new ServiceCost
                {
                    ServiceName = cost.ServiceName,
                    Description = cost.Description ?? "",
                    Quantity = cost.Quantity,
                    Price = cost.Price,
                    Value = cost.Value
                }).ToList();

                var additionalCostsList = _additionalCosts.Select(cost => new AdditionalCost
                {
                    Description = cost.Description,
                    Value = cost.Value,
                    Notes = cost.Notes ?? ""
                }).ToList();

                // إنشاء التقرير باستخدام الخدمة الاحترافية
                var printService = new ProfessionalPrintService();
                printService.CreateManufacturingOrderReport(
                    _currentOrder,
                    glassPanelsList,
                    requiredSizesList,
                    serviceCostsList,
                    additionalCostsList,
                    filePath
                );
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء التقرير الاحترافي: {ex.Message}", ex);
            }
        }

        #endregion


    }

    #region ViewModels

    /// <summary>
    /// نموذج عرض لوح الزجاج
    /// </summary>
    public class GlassPanelViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        public int RowNumber { get; set; }
        public string GlassType { get; set; } = "";
        public string Thickness { get; set; } = "";

        private decimal _length;
        public decimal Length
        {
            get => _length;
            set
            {
                _length = value;
                OnPropertyChanged();
                CalculateSquareMeters();
            }
        }

        private decimal _width;
        public decimal Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged();
                CalculateSquareMeters();
            }
        }

        private decimal _squareMeters;
        public decimal SquareMeters
        {
            get => _squareMeters;
            set
            {
                _squareMeters = value;
                OnPropertyChanged();
                CalculateTotalSquareMeters();
            }
        }

        private int _quantity = 1;
        public int Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged();
                CalculateTotalSquareMeters();
            }
        }

        private decimal _totalSquareMeters;
        public decimal TotalSquareMeters
        {
            get => _totalSquareMeters;
            set
            {
                _totalSquareMeters = value;
                OnPropertyChanged();
                CalculateTotalValue();
            }
        }

        private decimal _price;
        public decimal Price
        {
            get => _price;
            set
            {
                _price = value;
                OnPropertyChanged();
                CalculateTotalValue();
            }
        }

        private decimal _totalValue;
        public decimal TotalValue
        {
            get => _totalValue;
            set
            {
                _totalValue = value;
                OnPropertyChanged();
            }
        }

        public string Notes { get; set; } = "";

        private void CalculateSquareMeters()
        {
            SquareMeters = (Length * Width) / 1000000;
        }

        private void CalculateTotalSquareMeters()
        {
            TotalSquareMeters = SquareMeters * Quantity;
        }

        private void CalculateTotalValue()
        {
            TotalValue = Price * TotalSquareMeters;
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض المقاس المطلوب
    /// </summary>
    public class RequiredSizeViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        public int RowNumber { get; set; }
        public string RefCode { get; set; } = "";
        public string GlassType { get; set; } = "";
        public string Thickness { get; set; } = "";

        private decimal _length;
        public decimal Length
        {
            get => _length;
            set
            {
                _length = value;
                OnPropertyChanged();
                CalculateSquareMeters();
            }
        }

        private decimal _width;
        public decimal Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged();
                CalculateSquareMeters();
            }
        }

        private decimal _squareMeters;
        public decimal SquareMeters
        {
            get => _squareMeters;
            set
            {
                _squareMeters = value;
                OnPropertyChanged();
                CalculateTotalSquareMeters();
            }
        }

        private int _quantity = 1;
        public int Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged();
                CalculateTotalSquareMeters();
            }
        }

        private decimal _totalSquareMeters;
        public decimal TotalSquareMeters
        {
            get => _totalSquareMeters;
            set
            {
                _totalSquareMeters = value;
                OnPropertyChanged();
            }
        }

        private decimal _linearMeters;
        public decimal LinearMeters
        {
            get => _linearMeters;
            set
            {
                _linearMeters = value;
                OnPropertyChanged();
                CalculateTotalLinearMeters();
            }
        }

        private decimal _totalLinearMeters;
        public decimal TotalLinearMeters
        {
            get => _totalLinearMeters;
            set
            {
                _totalLinearMeters = value;
                OnPropertyChanged();
            }
        }

        private bool _isDelivered;
        public bool IsDelivered
        {
            get => _isDelivered;
            set
            {
                _isDelivered = value;
                OnPropertyChanged();
            }
        }

        private void CalculateSquareMeters()
        {
            SquareMeters = (Length * Width) / 1000000;
            CalculateLinearMeters();
        }

        private void CalculateTotalSquareMeters()
        {
            TotalSquareMeters = SquareMeters * Quantity;
            CalculateTotalLinearMeters();
        }

        private void CalculateLinearMeters()
        {
            // المتر الطولي = (الطول / 1000 * 2) + (العرض / 1000 * 2)
            LinearMeters = ((Length / 1000) * 2) + ((Width / 1000) * 2);
            CalculateTotalLinearMeters();
        }

        private void CalculateTotalLinearMeters()
        {
            // إجمالي المتر الطولي = المتر الطولي × العدد
            TotalLinearMeters = LinearMeters * Quantity;
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض تكلفة الخدمة
    /// </summary>
    public class ServiceCostViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        public int RowNumber { get; set; }
        public string ServiceName { get; set; } = "";
        public string Description { get; set; } = "";

        private decimal _quantity = 1;
        public decimal Quantity
        {
            get => _quantity;
            set
            {
                _quantity = value;
                OnPropertyChanged();
                CalculateValue();
            }
        }

        private decimal _price;
        public decimal Price
        {
            get => _price;
            set
            {
                _price = value;
                OnPropertyChanged();
                CalculateValue();
            }
        }

        private decimal _value;
        public decimal Value
        {
            get => _value;
            set
            {
                _value = value;
                OnPropertyChanged();
            }
        }

        private void CalculateValue()
        {
            Value = Quantity * Price;
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض التكلفة الإضافية
    /// </summary>
    public class AdditionalCostViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        public int RowNumber { get; set; }
        public string Description { get; set; } = "";

        private decimal _value;
        public decimal Value
        {
            get => _value;
            set
            {
                _value = value;
                OnPropertyChanged();
            }
        }

        public string Notes { get; set; } = "";

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض خدمة الفيلم
    /// </summary>
    public class FilmServiceViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        public int RowNumber { get; set; }
        public string FilmType { get; set; } = "";

        private decimal _length;
        public decimal Length
        {
            get => _length;
            set
            {
                _length = value;
                OnPropertyChanged();
                CalculateSquareMeters();
            }
        }

        private decimal _width;
        public decimal Width
        {
            get => _width;
            set
            {
                _width = value;
                OnPropertyChanged();
                CalculateSquareMeters();
            }
        }

        private decimal _squareMeters;
        public decimal SquareMeters
        {
            get => _squareMeters;
            set
            {
                _squareMeters = value;
                OnPropertyChanged();
                CalculateTotalSquareMeters();
            }
        }

        private int _count = 1;
        public int Count
        {
            get => _count;
            set
            {
                _count = value;
                OnPropertyChanged();
                CalculateTotalSquareMeters();
            }
        }

        private decimal _totalSquareMeters;
        public decimal TotalSquareMeters
        {
            get => _totalSquareMeters;
            set
            {
                _totalSquareMeters = value;
                OnPropertyChanged();
            }
        }

        public string ReflectiveDirection { get; set; } = "";

        private void CalculateSquareMeters()
        {
            SquareMeters = (Length * Width) / 1000000;
        }

        private void CalculateTotalSquareMeters()
        {
            TotalSquareMeters = SquareMeters * Count;
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// نموذج عرض خدمة الدبل جلاس
    /// </summary>
    public class DoubleGlassServiceViewModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler? PropertyChanged;

        public int RowNumber { get; set; }
        public string SpacerType { get; set; } = "";

        private decimal _spacerLength;
        public decimal SpacerLength
        {
            get => _spacerLength;
            set
            {
                _spacerLength = value;
                OnPropertyChanged();
                CalculateTotalLinearMeters();
            }
        }

        private int _count = 1;
        public int Count
        {
            get => _count;
            set
            {
                _count = value;
                OnPropertyChanged();
                CalculateTotalLinearMeters();
            }
        }

        private decimal _totalLinearMeters;
        public decimal TotalLinearMeters
        {
            get => _totalLinearMeters;
            set
            {
                _totalLinearMeters = value;
                OnPropertyChanged();
            }
        }

        public string ReflectiveDirection { get; set; } = "";
        public string Notes { get; set; } = "";

        private void CalculateTotalLinearMeters()
        {
            TotalLinearMeters = (Count * SpacerLength) / 1000;
        }

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    #endregion
}
