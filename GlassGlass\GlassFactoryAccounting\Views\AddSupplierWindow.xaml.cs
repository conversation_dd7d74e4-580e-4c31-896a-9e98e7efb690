using System.Windows;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة إضافة مورد جديد
/// </summary>
public partial class AddSupplierWindow : Window
{
    public Supplier? NewSupplier { get; private set; }

    public AddSupplierWindow()
    {
        InitializeComponent();
        TxtSupplierName.Focus();
    }

    private async void BtnSave_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // التحقق من صحة البيانات
            if (string.IsNullOrWhiteSpace(TxtSupplierName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المورد", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                TxtSupplierName.Focus();
                return;
            }

            // إنشاء مورد جديد
            var supplier = new Supplier
            {
                Name = TxtSupplierName.Text.Trim(),
                Phone = TxtSupplierPhone.Text.Trim(),
                Email = TxtSupplierEmail.Text.Trim(),
                Address = TxtSupplierAddress.Text.Trim(),
                ContactPerson = TxtContactPerson.Text.Trim(),
                Notes = TxtSupplierNotes.Text.Trim()
            };

            // حفظ المورد
            var archiveService = new ArchiveService();
            int supplierId = await archiveService.SaveSupplierAsync(supplier);

            if (supplierId > 0)
            {
                supplier.Id = supplierId;
                NewSupplier = supplier;
                
                MessageBox.Show("تم حفظ المورد بنجاح", "نجح", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
                
                DialogResult = true;
                Close();
            }
            else
            {
                MessageBox.Show("حدث خطأ أثناء حفظ المورد", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حفظ المورد: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnCancel_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            DialogResult = false;
            Close();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
