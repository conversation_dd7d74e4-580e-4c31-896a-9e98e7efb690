using System.Windows;
using System.Windows.Controls;

namespace GlassFactoryAccounting.Views
{
    public partial class InventoryMainView : UserControl
    {
        public InventoryMainView()
        {
            InitializeComponent();
        }

        private void BtnWarehouseManagement_Click(object sender, RoutedEventArgs e)
        {
            // الانتقال لإدارة المخازن
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.ShowWarehouseManagement();
        }

        private void BtnItemManagement_Click(object sender, RoutedEventArgs e)
        {
            // الانتقال لإدارة الأصناف
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.ShowItemManagement();
        }

        private void BtnInventoryMovement_Click(object sender, RoutedEventArgs e)
        {
            // الانتقال لحركة المخزون
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.ShowInventoryMovement();
        }

        private void BtnMovementReport_Click(object sender, RoutedEventArgs e)
        {
            // الانتقال لتقرير الحركات
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.ShowMovementReport();
        }

        private void BtnInventoryBalance_Click(object sender, RoutedEventArgs e)
        {
            // الانتقال لأرصدة المخزون
            var mainWindow = Window.GetWindow(this) as MainWindow;
            mainWindow?.ShowInventoryBalance();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            // العودة للرئيسية
            if (Window.GetWindow(this) is MainWindow mainWindow)
            {
                mainWindow.ShowDashboard();
            }
        }
    }
}
