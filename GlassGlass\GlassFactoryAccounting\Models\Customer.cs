namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج العملاء
    /// </summary>
    public class Customer : BaseEntity
    {
        private string _name = string.Empty;
        private string _phone = string.Empty;
        private string _address = string.Empty;
        private string _email = string.Empty;
        private decimal _balance;
        private string _notes = string.Empty;
        private string _contactPerson = string.Empty;

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Phone
        {
            get => _phone;
            set => SetProperty(ref _phone, value);
        }

        public string Address
        {
            get => _address;
            set => SetProperty(ref _address, value);
        }

        public string Email
        {
            get => _email;
            set => SetProperty(ref _email, value);
        }

        public decimal Balance
        {
            get => _balance;
            set => SetProperty(ref _balance, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public string ContactPerson
        {
            get => _contactPerson;
            set => SetProperty(ref _contactPerson, value);
        }
    }
}
