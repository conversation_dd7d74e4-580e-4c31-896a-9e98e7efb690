@echo off
chcp 65001 >nul
title 🏭 نظام حسابات مصنع الزجاج - Windows Forms
color 0A

echo.
echo 🏭 نظام حسابات مصنع الزجاج - Windows Forms Edition
echo ═══════════════════════════════════════════════════════════
echo Framework: .NET Framework 4.7.2
echo UI: Windows Forms + Bunifu UI
echo Database: SQLite
echo.

echo 🚀 جاري تشغيل النظام...

REM إيقاف أي نسخة تعمل
taskkill /f /im "GlassFactoryAccountingWinForms.exe" 2>nul >nul

REM إنشاء مجلدات البيانات
if not exist "Data" mkdir "Data" 2>nul
if not exist "bin\Release\Data" mkdir "bin\Release\Data" 2>nul
if not exist "bin\Debug\Data" mkdir "bin\Debug\Data" 2>nul

REM محاولة تشغيل البرنامج من مواقع مختلفة
if exist "bin\Release\GlassFactoryAccountingWinForms.exe" (
    echo ✅ تشغيل من bin\Release
    cd "bin\Release"
    start "" "GlassFactoryAccountingWinForms.exe"
    cd ..\..
    goto :success
)

if exist "bin\Debug\GlassFactoryAccountingWinForms.exe" (
    echo ⚠️  تشغيل من bin\Debug
    cd "bin\Debug"
    start "" "GlassFactoryAccountingWinForms.exe"
    cd ..\..
    goto :success
)

if exist "GlassFactoryAccountingWinForms.exe" (
    echo ✅ تشغيل من المجلد الحالي
    start "" "GlassFactoryAccountingWinForms.exe"
    goto :success
)

echo ❌ لم يتم العثور على الملف التنفيذي!
echo.
echo يرجى بناء المشروع أولاً باستخدام Visual Studio:
echo 1. افتح ملف GlassFactoryAccountingWinForms.sln في Visual Studio
echo 2. اختر Build → Build Solution
echo 3. شغل هذا الملف مرة أخرى
echo.
pause
exit /b 1

:success
echo ✅ تم تشغيل النظام بنجاح!
echo.
echo 🎯 المميزات المتاحة:
echo • شجرة الحسابات مع المستويات المتعددة
echo • إضافة وتعديل الحسابات
echo • عرض تفاصيل الحسابات
echo • إحصائيات شاملة
echo.
echo للوصول: انقر على "شجرة الحسابات" في الواجهة الرئيسية
echo.
timeout /t 3 /nobreak >nul
