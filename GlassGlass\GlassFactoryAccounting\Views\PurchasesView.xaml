<UserControl x:Class="GlassFactoryAccounting.Views.PurchasesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="ActionButtonStyle" TargetType="Button">
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان والإحصائيات -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0">
                    <TextBlock Text="🛒 إدارة المشتريات" FontSize="24" FontWeight="Bold" 
                             Foreground="{StaticResource PrimaryBrush}" Margin="0,0,0,10"/>
                    
                    <StackPanel Orientation="Horizontal">
                        <Border Background="{StaticResource PrimaryBrush}" CornerRadius="5" Padding="10,5" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📊" FontSize="16" Margin="0,0,5,0"/>
                                <TextBlock x:Name="TxtTotalPurchases" Text="إجمالي المشتريات: 0" 
                                         Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>
                        
                        <Border Background="{StaticResource SuccessBrush}" CornerRadius="5" Padding="10,5" Margin="0,0,10,0">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💰" FontSize="16" Margin="0,0,5,0"/>
                                <TextBlock x:Name="TxtTotalAmount" Text="إجمالي القيمة: 0.00 ج.م" 
                                         Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>
                        
                        <Border Background="{StaticResource WarningBrush}" CornerRadius="5" Padding="10,5">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🏪" FontSize="16" Margin="0,0,5,0"/>
                                <TextBlock x:Name="TxtTotalSuppliers" Text="عدد الموردين: 0" 
                                         Foreground="White" FontWeight="Bold"/>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </StackPanel>
                
                <Button x:Name="BtnRefresh" Grid.Column="1" Content="🔄 تحديث" 
                        Background="{StaticResource PrimaryBrush}" Style="{StaticResource ActionButtonStyle}"
                        Click="BtnRefresh_Click"/>
            </Grid>
        </Border>
        
        <!-- أزرار الإجراءات -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnNewPurchase" Content="➕ فاتورة مشتريات جديدة" 
                        Background="{StaticResource SuccessBrush}" Style="{StaticResource ActionButtonStyle}"
                        Padding="20,10" FontSize="16" Click="BtnNewPurchase_Click"/>
                
                <Button x:Name="BtnManageSuppliers" Content="🏪 إدارة الموردين" 
                        Background="{StaticResource WarningBrush}" Style="{StaticResource ActionButtonStyle}"
                        Padding="20,10" FontSize="16" Click="BtnManageSuppliers_Click"/>
                
                <Button x:Name="BtnPurchaseReports" Content="📊 تقارير المشتريات" 
                        Background="{StaticResource PrimaryBrush}" Style="{StaticResource ActionButtonStyle}"
                        Padding="20,10" FontSize="16" Click="BtnPurchaseReports_Click"/>
            </StackPanel>
        </Border>
        
        <!-- قائمة فواتير المشتريات -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="📋 فواتير المشتريات المحفوظة" FontSize="18" FontWeight="Bold" 
                         Margin="0,0,0,15" Foreground="{StaticResource PrimaryBrush}"/>
                
                <DataGrid x:Name="PurchasesDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="120"/>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding PurchaseDate, StringFormat='{}{0:yyyy/MM/dd}'}" Width="100"/>
                        <DataGridTextColumn Header="اسم المورد" Binding="{Binding Supplier.Name}" Width="150"/>
                        <DataGridTextColumn Header="الخدمات" Binding="{Binding ServicesText}" Width="200"/>
                        <DataGridTextColumn Header="إجمالي الفاتورة" Binding="{Binding TotalAmount, StringFormat='{}{0:F2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="الخصم" Binding="{Binding Discount, StringFormat='{}{0:F2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="الإجمالي النهائي" Binding="{Binding NetAmount, StringFormat='{}{0:F2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="حالة الدفع" Binding="{Binding PaymentStatus}" Width="100"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="200">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="👁️ عرض" Background="{StaticResource PrimaryBrush}" 
                                                Style="{StaticResource ActionButtonStyle}" FontSize="10"
                                                Click="BtnView_Click" Tag="{Binding}"/>
                                        
                                        <Button Content="✏️ تعديل" Background="{StaticResource WarningBrush}" 
                                                Style="{StaticResource ActionButtonStyle}" FontSize="10"
                                                Click="BtnEdit_Click" Tag="{Binding}"/>
                                        
                                        <Button Content="🗑️ حذف" Background="{StaticResource DangerBrush}" 
                                                Style="{StaticResource ActionButtonStyle}" FontSize="10"
                                                Click="BtnDelete_Click" Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
