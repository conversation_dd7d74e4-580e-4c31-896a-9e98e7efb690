using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نماذج موديول التصنيع
    /// </summary>
    
    /// <summary>
    /// أمر التصنيع الرئيسي الجديد
    /// </summary>
    public class NewManufacturingOrder
    {
        public int Id { get; set; }
        
        [Required]
        public string OrderNumber { get; set; } = string.Empty; // رقم أمر التصنيع
        
        public string CustomerName { get; set; } = string.Empty; // اسم العميل
        
        public string InvoiceNumber { get; set; } = string.Empty; // رقم الفاتورة
        
        public DateTime OrderDate { get; set; } = DateTime.Now; // التاريخ
        
        public string OrderStatus { get; set; } = "تحت التشغيل"; // حالة الطلب
        
        public decimal TotalServicesCost { get; set; } = 0; // إجمالي تكلفة الخدمات
        
        public decimal TotalAdditionalCosts { get; set; } = 0; // إجمالي التكاليف الإضافية

        public decimal TotalGlassCosts { get; set; } = 0; // إجمالي قيمة الزجاج المستخدم

        public decimal TotalCost { get; set; } = 0; // المجموع الكلي
        
        public decimal TotalSquareMeters { get; set; } = 0; // إجمالي الأمتار
        
        public decimal PricePerMeter { get; set; } = 0; // سعر المتر
        
        public string Notes { get; set; } = string.Empty; // ملاحظات
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public string CreatedBy { get; set; } = string.Empty;
        
        public DateTime? ModifiedDate { get; set; }
        
        public string? ModifiedBy { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        // العلاقات
        public virtual ICollection<GlassPanel> GlassPanels { get; set; } = new List<GlassPanel>();
        public virtual ICollection<NewManufacturingService> Services { get; set; } = new List<NewManufacturingService>();
        public virtual ICollection<RequiredSize> RequiredSizes { get; set; } = new List<RequiredSize>();
        public virtual ICollection<ServiceCost> ServiceCosts { get; set; } = new List<ServiceCost>();
        public virtual ICollection<AdditionalCost> AdditionalCosts { get; set; } = new List<AdditionalCost>();
        public virtual ICollection<FilmService> FilmServices { get; set; } = new List<FilmService>();
        public virtual ICollection<DoubleGlassService> DoubleGlassServices { get; set; } = new List<DoubleGlassService>();
    }

    /// <summary>
    /// ألواح الزجاج
    /// </summary>
    public class GlassPanel
    {
        public int Id { get; set; }
        
        public int ManufacturingOrderId { get; set; }
        
        public string GlassType { get; set; } = string.Empty; // نوع الزجاج
        
        public string Thickness { get; set; } = string.Empty; // السمك
        
        public decimal Length { get; set; } = 0; // الطول بالملم
        
        public decimal Width { get; set; } = 0; // العرض بالملم
        
        public decimal SquareMeters { get; set; } = 0; // المساحة بالمتر المربع
        
        public int Quantity { get; set; } = 1; // العدد
        
        public decimal TotalSquareMeters { get; set; } = 0; // إجمالي م²

        public decimal Price { get; set; } = 0; // السعر

        public decimal TotalValue { get; set; } = 0; // إجمالي القيمة = السعر × إجمالي المتر المربع

        public string Notes { get; set; } = string.Empty; // ملاحظات
        
        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
    }

    /// <summary>
    /// الخدمات المرتبطة بالتصنيع
    /// </summary>
    public class NewManufacturingService
    {
        public int Id { get; set; }
        
        public int ManufacturingOrderId { get; set; }
        
        public string ServiceType { get; set; } = string.Empty; // نوع الخدمة
        
        public bool IsEnabled { get; set; } = false; // مفعل أم لا
        
        public string ServiceData { get; set; } = string.Empty; // بيانات الخدمة (JSON)
        
        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
    }

    /// <summary>
    /// خدمة الفيلم
    /// </summary>
    public class FilmService
    {
        public int Id { get; set; }
        public int ManufacturingOrderId { get; set; }
        public string FilmType { get; set; } = string.Empty; // نوع الفيلم
        public decimal Length { get; set; } = 0; // الطول (ملم)
        public decimal Width { get; set; } = 0; // العرض (ملم)
        public decimal SquareMeters { get; set; } = 0; // م²
        public int Quantity { get; set; } = 1; // العدد
        public decimal TotalSquareMeters { get; set; } = 0; // الإجمالي
        public string ReflectionDirection { get; set; } = "من الداخل"; // اتجاه العاكس

        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
    }

    /// <summary>
    /// خدمة الدبل جلاس
    /// </summary>
    public class DoubleGlassService
    {
        public int Id { get; set; }
        public int ManufacturingOrderId { get; set; }
        public string SpacerType { get; set; } = string.Empty; // نوع السبيسر
        public decimal SpacerLength { get; set; } = 0; // طول السبيسر (ملم)
        public int Quantity { get; set; } = 1; // العدد
        public decimal TotalLinearMeters { get; set; } = 0; // إجمالي المتر الطولي = العدد × طول السبيسر
        public string ReflectionDirection { get; set; } = "من الداخل"; // اتجاه العاكس
        public string Notes { get; set; } = string.Empty; // ملاحظات

        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
    }

    /// <summary>
    /// خدمة التركيبة
    /// </summary>
    public class CompositeService
    {
        public string FirstGlassType { get; set; } = string.Empty; // نوع الزجاج الأول
        public string SecondGlassType { get; set; } = string.Empty; // نوع الزجاج الثاني
        public string FinalResult { get; set; } = string.Empty; // الناتج النهائي
    }

    /// <summary>
    /// خدمة الشطف
    /// </summary>
    public class BevelService
    {
        public decimal BevelValue { get; set; } = 0; // قيمة الشطف بالملم
    }

    /// <summary>
    /// المقاسات المطلوبة للقص
    /// </summary>
    public class RequiredSize
    {
        public int Id { get; set; }
        
        public int ManufacturingOrderId { get; set; }
        
        [Required]
        public string RefCode { get; set; } = string.Empty; // كود المقاس الفريد
        
        public string GlassType { get; set; } = string.Empty; // نوع الزجاج
        
        public string Thickness { get; set; } = string.Empty; // السمك
        
        public decimal Length { get; set; } = 0; // الطول
        
        public decimal Width { get; set; } = 0; // العرض
        
        public decimal SquareMeters { get; set; } = 0; // المساحة م²
        
        public int Quantity { get; set; } = 1; // العدد
        
        public decimal TotalSquareMeters { get; set; } = 0; // إجمالي م²
        
        public bool IsDelivered { get; set; } = false; // تم التسليم
        
        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
    }

    /// <summary>
    /// تكلفة الخدمات
    /// </summary>
    public class ServiceCost
    {
        public int Id { get; set; }
        
        public int ManufacturingOrderId { get; set; }
        
        public string ServiceName { get; set; } = string.Empty; // اسم الخدمة
        
        public string Description { get; set; } = string.Empty; // الوصف
        
        public decimal Quantity { get; set; } = 1; // الكمية/العدد
        
        public decimal Price { get; set; } = 0; // السعر
        
        public decimal Value { get; set; } = 0; // القيمة
        
        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
    }

    /// <summary>
    /// التكاليف الإضافية
    /// </summary>
    public class AdditionalCost
    {
        public int Id { get; set; }
        
        public int ManufacturingOrderId { get; set; }
        
        public string Description { get; set; } = string.Empty; // وصف التكلفة
        
        public decimal Value { get; set; } = 0; // القيمة
        
        public string Notes { get; set; } = string.Empty; // ملاحظات
        
        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
    }

    /// <summary>
    /// أمر التسليم
    /// </summary>
    public class DeliveryOrder
    {
        public int Id { get; set; }
        
        public string DeliveryOrderNumber { get; set; } = string.Empty; // رقم أمر التسليم
        
        public int ManufacturingOrderId { get; set; }
        
        public string CustomerName { get; set; } = string.Empty; // اسم العميل
        
        public string InvoiceNumber { get; set; } = string.Empty; // رقم الفاتورة
        
        public string WorkOrderNumber { get; set; } = string.Empty; // رقم أمر العمل
        
        public string ProjectName { get; set; } = string.Empty; // اسم المشروع
        
        public int TotalPieces { get; set; } = 0; // إجمالي عدد القطع
        
        public decimal TotalSquareMeters { get; set; } = 0; // إجمالي م²
        
        public string DeliveryResponsible { get; set; } = string.Empty; // مسؤول التسليم
        
        public string ReceiverSignature { get; set; } = string.Empty; // توقيع المستلم
        
        public DateTime DeliveryDate { get; set; } = DateTime.Now; // تاريخ التسليم
        
        public string CreatedBy { get; set; } = string.Empty;
        
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        
        public bool IsActive { get; set; } = true;
        
        public virtual NewManufacturingOrder ManufacturingOrder { get; set; } = null!;
        public virtual ICollection<DeliveredSize> DeliveredSizes { get; set; } = new List<DeliveredSize>();
    }

    /// <summary>
    /// المقاسات المسلمة
    /// </summary>
    public class DeliveredSize
    {
        public int Id { get; set; }
        
        public int DeliveryOrderId { get; set; }
        
        public string RefCode { get; set; } = string.Empty; // كود المقاس
        
        public string GlassType { get; set; } = string.Empty; // نوع الزجاج
        
        public string Thickness { get; set; } = string.Empty; // السمك
        
        public decimal Length { get; set; } = 0; // الطول
        
        public decimal Width { get; set; } = 0; // العرض
        
        public int Quantity { get; set; } = 1; // الكمية
        
        public virtual DeliveryOrder DeliveryOrder { get; set; } = null!;
    }

    /// <summary>
    /// عداد كود المقاسات
    /// </summary>
    public class SizeCodeCounter
    {
        public int Id { get; set; }
        
        public string Prefix { get; set; } = "A"; // البادئة الحالية
        
        public int CurrentNumber { get; set; } = 1; // الرقم الحالي
        
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }
}
