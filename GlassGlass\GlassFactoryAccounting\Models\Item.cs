using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// وحدات القياس
    /// </summary>
    public enum UnitOfMeasure
    {
        متر_مربع,
        كيلو,
        وحدة,
        متر,
        لتر,
        قطعة
    }

    /// <summary>
    /// نموذج الصنف
    /// </summary>
    public class Item : INotifyPropertyChanged
    {
        private int _id;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private int _warehouseId;
        private string _warehouseName = string.Empty;
        private UnitOfMeasure _unitOfMeasure;
        private bool _hasDimensions;
        private decimal _length; // بالملم
        private decimal _width;  // بالملم
        private decimal _area;   // بالمتر المربع
        private decimal _boxContent; // محتوى الصندوق أو العبوة
        private string _description = string.Empty;
        private DateTime _createdDate;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public int WarehouseId
        {
            get => _warehouseId;
            set => SetProperty(ref _warehouseId, value);
        }

        public string WarehouseName
        {
            get => _warehouseName;
            set => SetProperty(ref _warehouseName, value);
        }

        public UnitOfMeasure UnitOfMeasure
        {
            get => _unitOfMeasure;
            set => SetProperty(ref _unitOfMeasure, value);
        }

        public bool HasDimensions
        {
            get => _hasDimensions;
            set
            {
                SetProperty(ref _hasDimensions, value);
                if (!value)
                {
                    Length = 0;
                    Width = 0;
                    Area = 0;
                }
            }
        }

        public decimal Length
        {
            get => _length;
            set
            {
                SetProperty(ref _length, value);
                CalculateArea();
            }
        }

        public decimal Width
        {
            get => _width;
            set
            {
                SetProperty(ref _width, value);
                CalculateArea();
            }
        }

        public decimal Area
        {
            get => _area;
            private set => SetProperty(ref _area, value);
        }

        public decimal BoxContent
        {
            get => _boxContent;
            set => SetProperty(ref _boxContent, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }

        /// <summary>
        /// حساب المساحة بالمتر المربع
        /// </summary>
        private void CalculateArea()
        {
            if (HasDimensions && Length > 0 && Width > 0)
            {
                Area = (Length * Width) / 1_000_000; // تحويل من ملم² إلى متر²
            }
            else
            {
                Area = 0;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        public Item()
        {
            CreatedDate = DateTime.Now;
        }
    }
}
