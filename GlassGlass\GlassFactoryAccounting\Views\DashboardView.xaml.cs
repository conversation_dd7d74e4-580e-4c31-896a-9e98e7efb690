using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة الرئيسية
/// </summary>
public partial class DashboardView : UserControl
{
    private readonly SettingsService _settingsService;

    public DashboardView()
    {
        InitializeComponent();
        _settingsService = new SettingsService();
        LoadDashboardData();
        LoadCompanyData();
    }

    private void LoadDashboardData()
    {
        // سيتم تحميل البيانات الفعلية لاحقاً
        TxtTodaySales.Text = "0.00 ج.م";
        TxtTodayInvoices.Text = "0";
        TxtLowStockProducts.Text = "0";
        TxtTotalCustomers.Text = "0";
    }

    private void BtnNewSale_Click(object sender, RoutedEventArgs e)
    {
        // البحث عن النافذة الرئيسية وتغيير المحتوى
        var mainWindow = Application.Current.MainWindow as MainWindow;
        if (mainWindow != null)
        {
            mainWindow.ShowNewInvoice();
        }
    }

    private void BtnNewProduct_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح صفحة إضافة منتج جديد", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnViewReports_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح صفحة التقارير", "معلومات",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void LoadCompanyData()
    {
        try
        {
            var settings = _settingsService.GetSettings();
            TxtCompanyName.Text = settings.CompanyName;
            TxtCompanyAddress.Text = settings.CompanyAddress;
            TxtCompanyPhone.Text = settings.CompanyPhone;
            TxtCompanyEmail.Text = settings.CompanyEmail;
        }
        catch (Exception)
        {
            // في حالة الخطأ، استخدام القيم الافتراضية
        }
    }

    private void BtnEditCompanyInfo_Click(object sender, RoutedEventArgs e)
    {
        var companySettingsWindow = new CompanySettingsWindow();
        if (companySettingsWindow.ShowDialog() == true)
        {
            // تحديث البيانات المعروضة
            LoadCompanyData();
        }
    }
}
