<UserControl x:Class="GlassFactoryAccounting.Views.CompanyBranchesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             Background="#F8F9FA">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="10"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#007ACC"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="5" 
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#005A9E"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Padding" Value="10"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#DDD"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Background" Value="White"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
            <Setter Property="TextAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0"/>
                    <GradientStop Color="#764ba2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="🏢" FontSize="32" Margin="0,0,15,0" VerticalAlignment="Center"/>
                <TextBlock Text="إدارة فروع الشركة" 
                          FontSize="28" 
                          FontWeight="Bold" 
                          Foreground="White" 
                          VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="2*"/>
            </Grid.ColumnDefinitions>

            <!-- نموذج إضافة فرع جديد -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}">
                <StackPanel>
                    <TextBlock Text="إضافة فرع جديد" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="#333" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,20"/>

                    <TextBlock Text="اسم الفرع:" FontWeight="SemiBold" Margin="5"/>
                    <TextBox x:Name="TxtBranchName" Style="{StaticResource ModernTextBox}" />

                    <TextBlock Text="العنوان:" FontWeight="SemiBold" Margin="5"/>
                    <TextBox x:Name="TxtAddress" Style="{StaticResource ModernTextBox}" />

                    <TextBlock Text="رقم الهاتف:" FontWeight="SemiBold" Margin="5"/>
                    <TextBox x:Name="TxtPhone" Style="{StaticResource ModernTextBox}" />

                    <TextBlock Text="اسم المدير:" FontWeight="SemiBold" Margin="5"/>
                    <TextBox x:Name="TxtManager" Style="{StaticResource ModernTextBox}" />

                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button x:Name="BtnAddBranch" 
                               Content="إضافة الفرع" 
                               Style="{StaticResource ModernButton}"
                               Background="#28a745"
                               Click="BtnAddBranch_Click"/>
                        <Button x:Name="BtnClearForm" 
                               Content="مسح النموذج" 
                               Style="{StaticResource ModernButton}"
                               Background="#6c757d"
                               Click="BtnClearForm_Click"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- قائمة الفروع -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" 
                              Text="قائمة فروع الشركة" 
                              FontSize="20" 
                              FontWeight="Bold" 
                              Foreground="#333" 
                              HorizontalAlignment="Center" 
                              Margin="0,0,0,20"/>

                    <DataGrid Grid.Row="1" 
                             x:Name="DgBranches" 
                             AutoGenerateColumns="False" 
                             CanUserAddRows="False" 
                             CanUserDeleteRows="False" 
                             IsReadOnly="True"
                             GridLinesVisibility="Horizontal" 
                             HeadersVisibility="Column"
                             Background="White"
                             AlternatingRowBackground="#F8F9FA"
                             FontSize="12">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60"/>
                            <DataGridTextColumn Header="اسم الفرع" Binding="{Binding Name}" Width="150"/>
                            <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="200"/>
                            <DataGridTextColumn Header="الهاتف" Binding="{Binding Phone}" Width="120"/>
                            <DataGridTextColumn Header="المدير" Binding="{Binding Manager}" Width="120"/>
                            <DataGridTextColumn Header="تاريخ الإنشاء" Binding="{Binding CreatedDate, StringFormat=yyyy-MM-dd}" Width="100"/>
                            <DataGridTemplateColumn Header="العمليات" Width="150">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="تعديل" 
                                                   Background="#ffc107" 
                                                   Foreground="White" 
                                                   BorderThickness="0" 
                                                   Padding="8,4" 
                                                   Margin="2"
                                                   FontSize="10"
                                                   Click="BtnEdit_Click"/>
                                            <Button Content="حذف" 
                                                   Background="#dc3545" 
                                                   Foreground="White" 
                                                   BorderThickness="0" 
                                                   Padding="8,4" 
                                                   Margin="2"
                                                   FontSize="10"
                                                   Click="BtnDelete_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</UserControl>
