using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة تسجيل مكافأة الموظف
    /// </summary>
    public partial class EmployeeBonusView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;

        public EmployeeBonusView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadBonuses();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ اليوم كافتراضي
            DpBonusDate.SelectedDate = DateTime.Now;

            // تحميل أسباب المكافآت الشائعة
            CmbBonusReason.Items.Add("تميز في الأداء");
            CmbBonusReason.Items.Add("إنجاز مشروع مهم");
            CmbBonusReason.Items.Add("مكافأة شهرية");
            CmbBonusReason.Items.Add("مكافأة سنوية");
            CmbBonusReason.Items.Add("مكافأة عيد");
            CmbBonusReason.Items.Add("مكافأة إضافية");
            CmbBonusReason.Items.Add("تقدير للجهود المبذولة");
            CmbBonusReason.Items.Add("مكافأة إنتاجية");
            CmbBonusReason.SelectedIndex = 0;
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.ItemsSource = employees;
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadBonuses()
        {
            try
            {
                var bonuses = _payrollService.GetAllEmployeeBonuses();
                BonusesDataGrid.ItemsSource = bonuses;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المكافآت: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbEmployee_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbEmployee.SelectedItem is Employee selectedEmployee)
            {
                TxtEmployeeCode.Text = selectedEmployee.EmployeeCode;
                TxtPosition.Text = selectedEmployee.Position;
                TxtBranch.Text = selectedEmployee.Branch;
            }
            else
            {
                ClearEmployeeFields();
            }
        }

        private void ClearEmployeeFields()
        {
            TxtEmployeeCode.Clear();
            TxtPosition.Clear();
            TxtBranch.Clear();
        }

        private void TxtBonusAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التحقق من صحة المبلغ المدخل
            if (sender is TextBox textBox)
            {
                if (!string.IsNullOrEmpty(textBox.Text))
                {
                    if (!decimal.TryParse(textBox.Text, out _))
                    {
                        // إزالة الأحرف غير الصحيحة
                        int caretIndex = textBox.CaretIndex;
                        textBox.Text = System.Text.RegularExpressions.Regex.Replace(textBox.Text, @"[^\d.]", "");
                        textBox.CaretIndex = Math.Min(caretIndex, textBox.Text.Length);
                    }
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedEmployee = CmbEmployee.SelectedItem as Employee;

                var bonus = new EmployeeBonus
                {
                    EmployeeId = selectedEmployee.Id,
                    EmployeeName = selectedEmployee.Name,
                    EmployeeCode = selectedEmployee.EmployeeCode,
                    BonusAmount = decimal.Parse(TxtBonusAmount.Text),
                    BonusReason = CmbBonusReason.Text,
                    BonusDate = DpBonusDate.SelectedDate ?? DateTime.Now,
                    Notes = TxtNotes.Text.Trim()
                };

                // التحقق من وضع التعديل
                if (BtnSave.Tag != null && int.TryParse(BtnSave.Tag.ToString(), out int bonusId))
                {
                    // وضع التعديل
                    bonus.Id = bonusId;
                    if (_payrollService.UpdateEmployeeBonus(bonus))
                    {
                        MessageBox.Show("تم تحديث المكافأة بنجاح!", "نجح التحديث",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        ClearForm();
                        LoadBonuses();

                        BtnSave.Content = "💾 حفظ المكافأة";
                        BtnSave.Tag = null;
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث المكافأة!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    // وضع الإضافة
                    if (_payrollService.AddEmployeeBonus(bonus))
                    {
                        MessageBox.Show("تم حفظ المكافأة بنجاح!", "نجح الحفظ",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        ClearForm();
                        LoadBonuses();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ المكافأة!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المكافأة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbEmployee.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbEmployee.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtBonusAmount.Text))
            {
                MessageBox.Show("يرجى إدخال قيمة المكافأة", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtBonusAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtBonusAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للمكافأة", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtBonusAmount.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CmbBonusReason.Text))
            {
                MessageBox.Show("يرجى إدخال سبب المكافأة", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbBonusReason.Focus();
                return false;
            }

            if (!DpBonusDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ المكافأة", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpBonusDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            CmbEmployee.SelectedIndex = -1;
            ClearEmployeeFields();
            TxtBonusAmount.Clear();
            CmbBonusReason.SelectedIndex = 0;
            DpBonusDate.SelectedDate = DateTime.Now;
            TxtNotes.Clear();
            CmbEmployee.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditBonus_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int bonusId)
                {
                    var bonuses = _payrollService.GetAllEmployeeBonuses();
                    var bonus = bonuses.FirstOrDefault(b => b.Id == bonusId);

                    if (bonus != null)
                    {
                        // البحث عن الموظف وتحديده
                        var employees = _payrollService.GetAllEmployees();
                        var employee = employees.FirstOrDefault(emp => emp.Id == bonus.EmployeeId);
                        if (employee != null)
                        {
                            CmbEmployee.SelectedItem = employee;
                        }

                        TxtBonusAmount.Text = bonus.BonusAmount.ToString("N2");
                        CmbBonusReason.Text = bonus.BonusReason;
                        DpBonusDate.SelectedDate = bonus.BonusDate;
                        TxtNotes.Text = bonus.Notes;

                        BtnSave.Content = "🔄 تحديث المكافأة";
                        BtnSave.Tag = bonusId;

                        MessageBox.Show("تم تحميل بيانات المكافأة للتعديل", "تعديل المكافأة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المكافأة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeleteBonus_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int bonusId)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذه المكافأة؟", "تأكيد الحذف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (_payrollService.DeleteEmployeeBonus(bonusId))
                        {
                            MessageBox.Show("تم حذف المكافأة بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadBonuses();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المكافأة!", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المكافأة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
