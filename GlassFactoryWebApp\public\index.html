<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏭 نظام حسابات مصنع الزجاج - Glass Factory Accounting System</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', 'Segoe UI', '<PERSON>hom<PERSON>', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .navbar {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #2c3e50 !important;
        }

        .container-fluid {
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header h2 {
            font-size: 1.5rem;
            color: #7f8c8d;
            margin-bottom: 20px;
            font-weight: 400;
        }

        .status {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }

        .demo-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }

        .sales-module {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }

        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }

        .api-demo {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        .tech-item {
            background: #f8f9fa;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #2c3e50;
            border: 1px solid #dee2e6;
        }

        .modal-content {
            direction: rtl;
            text-align: right;
        }

        .table {
            direction: rtl;
            text-align: right;
        }

        .form-control {
            text-align: right;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .container-fluid {
                padding: 10px;
            }
        }
    </style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container-fluid {
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 3rem;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header h2 {
            font-size: 1.5rem;
            color: #7f8c8d;
            margin-bottom: 20px;
            font-weight: 400;
        }
        
        .status {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            font-size: 1.1rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        }
        
        .demo-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 2rem;
            text-align: center;
        }
        
        .sales-module {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .btn-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .btn-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
            color: white;
        }
        
        .api-demo {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .tech-stack {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .tech-item {
            background: #f8f9fa;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: #2c3e50;
            border: 1px solid #dee2e6;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .container-fluid {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="header">
            <h1>🏭 نظام حسابات مصنع الزجاج</h1>
            <h2>Glass Factory Accounting System</h2>
            <div class="status">
                <i class="fas fa-check-circle"></i>
                النظام متاح الآن - System Live!
            </div>
        </div>

        <div class="demo-section">
            <h3>🚀 النسخة التجريبية المباشرة</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="sales-module">
                        <h4 class="text-center mb-4">💰 موديول المبيعات</h4>
                        
                        <div class="feature-card">
                            <h5><i class="fas fa-users"></i> إدارة العملاء</h5>
                            <p>إضافة وتعديل وحذف العملاء مع تتبع كامل للحسابات</p>
                            <button class="btn btn-custom" onclick="showCustomers()">
                                <i class="fas fa-eye"></i> عرض العملاء
                            </button>
                        </div>
                        
                        <div class="feature-card">
                            <h5><i class="fas fa-file-invoice"></i> فواتير المبيعات</h5>
                            <p>إنشاء وطباعة وترحيل فواتير المبيعات</p>
                            <button class="btn btn-custom" onclick="showInvoices()">
                                <i class="fas fa-plus"></i> فاتورة جديدة
                            </button>
                        </div>
                        
                        <div class="feature-card">
                            <h5><i class="fas fa-credit-card"></i> المدفوعات</h5>
                            <p>تسجيل ومتابعة مدفوعات العملاء</p>
                            <button class="btn btn-custom" onclick="showPayments()">
                                <i class="fas fa-money-bill"></i> تسجيل دفعة
                            </button>
                        </div>
                        
                        <div class="feature-card">
                            <h5><i class="fas fa-chart-bar"></i> التقارير</h5>
                            <p>تقارير مفصلة وإحصائيات شاملة</p>
                            <button class="btn btn-custom" onclick="showReports()">
                                <i class="fas fa-chart-line"></i> عرض التقارير
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="api-demo">
                        <h4 class="text-center mb-4">📋 API Documentation</h4>
                        <p class="text-center">واجهة برمجة التطبيقات الموثقة مع Swagger UI</p>
                        
                        <div class="text-center">
                            <button class="btn btn-custom" onclick="openSwagger()">
                                <i class="fas fa-code"></i> فتح Swagger UI
                            </button>
                        </div>
                        
                        <div class="mt-4">
                            <h6>🔧 المميزات التقنية:</h6>
                            <ul class="list-unstyled">
                                <li>✅ ASP.NET Core 8.0</li>
                                <li>✅ React 18 + TypeScript</li>
                                <li>✅ PostgreSQL Database</li>
                                <li>✅ JWT Authentication</li>
                                <li>✅ Arabic RTL Support</li>
                                <li>✅ Material-UI Components</li>
                                <li>✅ PDF Generation</li>
                                <li>✅ Excel Export</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>🎯 اختبار النظام</h3>
            <div class="row">
                <div class="col-md-12">
                    <div id="demo-area" class="p-4 bg-light rounded">
                        <h5 class="text-center">اختر وظيفة لتجربتها:</h5>
                        <p class="text-center text-muted">انقر على أي زر أعلاه لتجربة الوظائف</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 معلومات المشروع</h3>
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h5>👤 المطور</h5>
                        <p>حسام محمد حسان أحمد</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5>🔢 الإصدار</h5>
                        <p>2.0.0</p>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5>📅 تاريخ النشر</h5>
                        <p>6 ديسمبر 2024</p>
                    </div>
                </div>
            </div>
            
            <div class="tech-stack">
                <div class="tech-item">ASP.NET Core 8.0</div>
                <div class="tech-item">React 18</div>
                <div class="tech-item">TypeScript</div>
                <div class="tech-item">PostgreSQL</div>
                <div class="tech-item">Material-UI</div>
                <div class="tech-item">JWT Auth</div>
                <div class="tech-item">Arabic RTL</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Demo functions
        function showCustomers() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>👥 إدارة العملاء</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>شركة الزجاج المتطور</h6>
                                <p>الهاتف: 0501234567</p>
                                <p>البريد: <EMAIL></p>
                                <p>الرصيد: 15,000 ريال</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>مؤسسة البناء الحديث</h6>
                                <p>الهاتف: 0507654321</p>
                                <p>البريد: <EMAIL></p>
                                <p>الرصيد: 8,500 ريال</p>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="btn btn-success mt-3" onclick="alert('سيتم إضافة عميل جديد')">
                    <i class="fas fa-plus"></i> إضافة عميل جديد
                </button>
            `;
        }
        
        function showInvoices() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>📋 فواتير المبيعات</h5>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>التاريخ</th>
                                <th>المبلغ</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>INV-001</td>
                                <td>شركة الزجاج المتطور</td>
                                <td>2024-12-06</td>
                                <td>5,000 ريال</td>
                                <td><span class="badge bg-success">مدفوعة</span></td>
                            </tr>
                            <tr>
                                <td>INV-002</td>
                                <td>مؤسسة البناء الحديث</td>
                                <td>2024-12-05</td>
                                <td>3,200 ريال</td>
                                <td><span class="badge bg-warning">معلقة</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <button class="btn btn-primary mt-3" onclick="alert('سيتم إنشاء فاتورة جديدة')">
                    <i class="fas fa-plus"></i> فاتورة جديدة
                </button>
            `;
        }
        
        function showPayments() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>💳 مدفوعات العملاء</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>دفعة نقدية</h6>
                                <p>العميل: شركة الزجاج المتطور</p>
                                <p>المبلغ: 5,000 ريال</p>
                                <p>التاريخ: 2024-12-06</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6>تحويل بنكي</h6>
                                <p>العميل: مؤسسة البناء الحديث</p>
                                <p>المبلغ: 2,000 ريال</p>
                                <p>التاريخ: 2024-12-05</p>
                            </div>
                        </div>
                    </div>
                </div>
                <button class="btn btn-info mt-3" onclick="alert('سيتم تسجيل دفعة جديدة')">
                    <i class="fas fa-money-bill"></i> تسجيل دفعة جديدة
                </button>
            `;
        }
        
        function showReports() {
            const demoArea = document.getElementById('demo-area');
            demoArea.innerHTML = `
                <h5>📊 التقارير والإحصائيات</h5>
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary">25</h3>
                                <p>إجمالي الفواتير</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">85,000</h3>
                                <p>إجمالي المبيعات (ريال)</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info">12</h3>
                                <p>عدد العملاء</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-warning">15,000</h3>
                                <p>المبالغ المعلقة (ريال)</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-outline-primary me-2" onclick="alert('سيتم تصدير تقرير PDF')">
                        <i class="fas fa-file-pdf"></i> تصدير PDF
                    </button>
                    <button class="btn btn-outline-success" onclick="alert('سيتم تصدير تقرير Excel')">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </button>
                </div>
            `;
        }
        
        function openSwagger() {
            alert('📋 API Documentation سيكون متاحاً قريباً!\n\nسيتم فتح Swagger UI مع جميع APIs الموثقة');
        }
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.demo-section');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(30px)';
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
