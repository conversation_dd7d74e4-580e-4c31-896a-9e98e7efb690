using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using QuestPDF.Fluent;
using QuestPDF.Helpers;
using QuestPDF.Infrastructure;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Helpers;
using System.Globalization;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة الطباعة الاحترافية باستخدام QuestPDF
    /// </summary>
    public class ProfessionalPrintService
    {
        static ProfessionalPrintService()
        {
            // تفعيل QuestPDF للاستخدام المجاني
            QuestPDF.Settings.License = LicenseType.Community;
        }

        /// <summary>
        /// إنشاء تقرير أمر التصنيع الاحترافي
        /// </summary>
        public void CreateManufacturingOrderReport(
            NewManufacturingOrder order,
            List<GlassPanel> glassPanels,
            List<RequiredSize> requiredSizes,
            List<ServiceCost> serviceCosts,
            List<AdditionalCost> additionalCosts,
            string filePath)
        {
            try
            {
                var document = Document.Create(container =>
                {
                    container.Page(page =>
                    {
                        page.Size(PageSizes.A4);
                        page.Margin(1.5f, Unit.Centimetre);
                        page.PageColor(Colors.White);
                        page.DefaultTextStyle(x => x
                            .FontSize(12)
                            .FontFamily("Arial Unicode MS")
                            .FontColor(Colors.Black)
                            .LineHeight(1.2f));

                        page.Header()
                            .Height(100)
                            .Background(Colors.Blue.Darken1)
                            .Padding(20)
                            .Column(column =>
                            {
                                column.Item()
                                    .AlignCenter()
                                    .Text("تقرير أمر التصنيع")
                                    .FontSize(24)
                                    .FontColor(Colors.White)
                                    .Bold()
                                    .FontFamily("Arial Unicode MS");

                                column.Item()
                                    .AlignCenter()
                                    .Text($"رقم الأمر: {NumberFormatHelper.EnsureEnglishNumbers(order.OrderNumber)}")
                                    .FontSize(16)
                                    .FontColor(Colors.White)
                                    .FontFamily("Arial Unicode MS");
                            });

                        page.Content()
                            .PaddingVertical(1, Unit.Centimetre)
                            .Column(column =>
                            {
                                // معلومات الأمر الأساسية
                                column.Item().Element(CreateBasicInfo);

                                column.Item().PaddingTop(20).Element(CreateGlassPanelsTable);

                                column.Item().PaddingTop(20).Element(CreateRequiredSizesTable);

                                if (serviceCosts.Any())
                                    column.Item().PaddingTop(20).Element(CreateServiceCostsTable);

                                if (additionalCosts.Any())
                                    column.Item().PaddingTop(20).Element(CreateAdditionalCostsTable);

                                column.Item().PaddingTop(20).Element(CreateWasteCalculation);

                                column.Item().PaddingTop(20).Element(CreateCostSummary);
                            });

                        page.Footer()
                            .AlignCenter()
                            .Text($"تاريخ الطباعة: {NumberFormatHelper.FormatDateTime(DateTime.Now)}")
                            .FontSize(8);
                    });

                    // دوال إنشاء الأقسام
                    void CreateBasicInfo(IContainer container)
                    {
                        container
                            .Border(1)
                            .BorderColor(Colors.Grey.Medium)
                            .Padding(15)
                            .Column(column =>
                            {
                                column.Item()
                                    .Text("معلومات الأمر الأساسية")
                                    .FontSize(16)
                                    .Bold()
                                    .FontFamily("Tahoma")
                                    .FontColor(Colors.Blue.Darken2);

                                column.Item().PaddingTop(15).Row(row =>
                                {
                                    row.RelativeItem().Text($"اسم العميل: {order.CustomerName ?? ""}")
                                        .FontFamily("Tahoma").FontSize(12);
                                    row.RelativeItem().Text($"رقم الفاتورة: {NumberFormatHelper.EnsureEnglishNumbers(order.InvoiceNumber ?? "")}")
                                        .FontFamily("Tahoma").FontSize(12);
                                });

                                column.Item().PaddingTop(8).Row(row =>
                                {
                                    row.RelativeItem().Text($"تاريخ الأمر: {NumberFormatHelper.FormatDate(order.OrderDate)}")
                                        .FontFamily("Tahoma").FontSize(12);
                                    row.RelativeItem().Text($"حالة الطلب: {order.OrderStatus ?? ""}")
                                        .FontFamily("Tahoma").FontSize(12);
                                });

                                if (!string.IsNullOrEmpty(order.Notes))
                                {
                                    column.Item().PaddingTop(8)
                                        .Text($"ملاحظات: {order.Notes}")
                                        .FontFamily("Tahoma")
                                        .FontSize(12);
                                }
                            });
                    }

                    void CreateGlassPanelsTable(IContainer container)
                    {
                        if (!glassPanels.Any()) return;

                        container
                            .Border(1)
                            .BorderColor(Colors.Grey.Medium)
                            .Column(column =>
                            {
                                // عنوان الجدول
                                column.Item()
                                    .Background(Colors.Blue.Lighten3)
                                    .Padding(10)
                                    .Text("ألواح الزجاج المستخدمة")
                                    .FontSize(14)
                                    .Bold()
                                    .FontFamily("Arial Unicode MS")
                                    .FontColor(Colors.Blue.Darken2);

                                // الجدول
                                column.Item()
                                    .Table(table =>
                                    {
                                        table.ColumnsDefinition(columns =>
                                        {
                                            columns.ConstantColumn(40);  // ت
                                            columns.RelativeColumn(3);  // نوع الزجاج
                                            columns.RelativeColumn(2);  // السمك
                                            columns.RelativeColumn(2);  // الطول
                                            columns.RelativeColumn(2);  // العرض
                                            columns.RelativeColumn(2);  // م²
                                            columns.RelativeColumn(2);  // العدد
                                            columns.RelativeColumn(2);  // إجمالي م²
                                        });

                                        // رؤوس الأعمدة
                                        table.Header(header =>
                                        {
                                            header.Cell().Background(Colors.Grey.Lighten2).Padding(8).Text("ت").Bold().FontSize(11).FontFamily("Arial Unicode MS");
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("نوع الزجاج").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("السمك").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("الطول").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("العرض").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("م²").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("العدد").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("إجمالي م²").Bold();
                                });

                                // البيانات
                                for (int i = 0; i < glassPanels.Count; i++)
                                {
                                    var panel = glassPanels[i];
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatInteger(i + 1));
                                    table.Cell().Padding(5).Text(panel.GlassType);
                                    table.Cell().Padding(5).Text(panel.Thickness);
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(panel.Length, 0));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(panel.Width, 0));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(panel.SquareMeters));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatInteger(panel.Quantity));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(panel.TotalSquareMeters));
                                }

                                        // المجموع
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("المجموع:").Bold();
                                        table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text(NumberFormatHelper.FormatDecimal(glassPanels.Sum(p => p.TotalSquareMeters))).Bold();
                                    });
                            });
                    }

                    void CreateRequiredSizesTable(IContainer container)
                    {
                        if (!requiredSizes.Any()) return;

                        container
                            .Border(1)
                            .Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.ConstantColumn(40);  // Ref
                                    columns.RelativeColumn(2);  // نوع الزجاج
                                    columns.RelativeColumn(1);  // السمك
                                    columns.RelativeColumn(1);  // الطول
                                    columns.RelativeColumn(1);  // العرض
                                    columns.RelativeColumn(1);  // م²
                                    columns.RelativeColumn(1);  // العدد
                                    columns.RelativeColumn(1);  // إجمالي م²
                                });

                                // العنوان
                                table.Header(header =>
                                {
                                    header.Cell().Background(Colors.Grey.Lighten2).Padding(5).Text("المقاسات المطلوبة للقص").Bold().FontSize(12);
                                    header.Cell().ColumnSpan(7);
                                });

                                // رؤوس الأعمدة
                                table.Header(header =>
                                {
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("Ref").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("نوع الزجاج").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("السمك").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("الطول").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("العرض").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("م²").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("العدد").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("إجمالي م²").Bold();
                                });

                                // البيانات
                                foreach (var size in requiredSizes)
                                {
                                    table.Cell().Padding(5).Text(size.RefCode);
                                    table.Cell().Padding(5).Text(size.GlassType);
                                    table.Cell().Padding(5).Text(size.Thickness);
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(size.Length, 0));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(size.Width, 0));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(size.SquareMeters));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatInteger(size.Quantity));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(size.TotalSquareMeters));
                                }

                                // المجموع
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("المجموع:").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text(NumberFormatHelper.FormatDecimal(requiredSizes.Sum(s => s.TotalSquareMeters))).Bold();
                            });
                    }

                    void CreateServiceCostsTable(IContainer container)
                    {
                        container
                            .Border(1)
                            .Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(3);  // اسم الخدمة
                                    columns.RelativeColumn(3);  // الوصف
                                    columns.RelativeColumn(1);  // الكمية
                                    columns.RelativeColumn(1);  // السعر
                                    columns.RelativeColumn(1);  // القيمة
                                });

                                // العنوان
                                table.Header(header =>
                                {
                                    header.Cell().Background(Colors.Grey.Lighten2).Padding(5).Text("تكاليف الخدمات").Bold().FontSize(12);
                                    header.Cell().ColumnSpan(4);
                                });

                                // رؤوس الأعمدة
                                table.Header(header =>
                                {
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("اسم الخدمة").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("الوصف").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("الكمية").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("السعر").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("القيمة").Bold();
                                });

                                // البيانات
                                foreach (var cost in serviceCosts)
                                {
                                    table.Cell().Padding(5).Text(cost.ServiceName);
                                    table.Cell().Padding(5).Text(cost.Description);
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatDecimal(cost.Quantity));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatCurrency(cost.Price));
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatCurrency(cost.Value));
                                }

                                // المجموع
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("المجموع:").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text(NumberFormatHelper.FormatCurrency(serviceCosts.Sum(c => c.Value))).Bold();
                            });
                    }

                    void CreateAdditionalCostsTable(IContainer container)
                    {
                        container
                            .Border(1)
                            .Table(table =>
                            {
                                table.ColumnsDefinition(columns =>
                                {
                                    columns.RelativeColumn(3);  // وصف التكلفة
                                    columns.RelativeColumn(1);  // القيمة
                                    columns.RelativeColumn(2);  // ملاحظات
                                });

                                // العنوان
                                table.Header(header =>
                                {
                                    header.Cell().Background(Colors.Grey.Lighten2).Padding(5).Text("التكاليف الإضافية").Bold().FontSize(12);
                                    header.Cell().ColumnSpan(2);
                                });

                                // رؤوس الأعمدة
                                table.Header(header =>
                                {
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("وصف التكلفة").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("القيمة").Bold();
                                    header.Cell().Background(Colors.Grey.Lighten3).Padding(5).Text("ملاحظات").Bold();
                                });

                                // البيانات
                                foreach (var cost in additionalCosts)
                                {
                                    table.Cell().Padding(5).Text(cost.Description);
                                    table.Cell().Padding(5).Text(NumberFormatHelper.FormatCurrency(cost.Value));
                                    table.Cell().Padding(5).Text(cost.Notes ?? "");
                                }

                                // المجموع
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("المجموع:").Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text(NumberFormatHelper.FormatCurrency(additionalCosts.Sum(c => c.Value))).Bold();
                                table.Cell().Background(Colors.Grey.Lighten4).Padding(5).Text("").Bold();
                            });
                    }

                    void CreateWasteCalculation(IContainer container)
                    {
                        var totalGlassMeters = glassPanels.Sum(g => g.TotalSquareMeters);
                        var totalRequiredMeters = requiredSizes.Sum(r => r.TotalSquareMeters);
                        var glassWaste = totalGlassMeters - totalRequiredMeters;
                        var glassWastePercentage = totalGlassMeters > 0 ? (glassWaste / totalGlassMeters) * 100 : 0;

                        container
                            .Border(1)
                            .Padding(10)
                            .Column(column =>
                            {
                                column.Item().Text("حساب هالك الزجاج").FontSize(14).Bold();
                                column.Item().PaddingTop(10).Row(row =>
                                {
                                    row.RelativeItem().Text($"هالك الزجاج (م²): {NumberFormatHelper.FormatDecimal(glassWaste)}");
                                    row.RelativeItem().Text($"نسبة هالك الزجاج (%): {NumberFormatHelper.FormatPercentage(glassWastePercentage)}");
                                });
                            });
                    }

                    void CreateCostSummary(IContainer container)
                    {
                        var totalServiceCosts = serviceCosts.Sum(c => c.Value);
                        var totalAdditionalCosts = additionalCosts.Sum(c => c.Value);
                        var grandTotal = totalServiceCosts + totalAdditionalCosts;
                        var totalRequiredMeters = requiredSizes.Sum(s => s.TotalSquareMeters);
                        var pricePerMeter = totalRequiredMeters > 0 ? grandTotal / totalRequiredMeters : 0;

                        container
                            .Border(1)
                            .Padding(10)
                            .Column(column =>
                            {
                                column.Item().Text("ملخص التكاليف النهائي").FontSize(14).Bold();
                                column.Item().PaddingTop(10).Row(row =>
                                {
                                    row.RelativeItem().Text($"إجمالي تكلفة الخدمات: {NumberFormatHelper.FormatCurrency(totalServiceCosts)} ج.م");
                                    row.RelativeItem().Text($"إجمالي التكاليف الإضافية: {NumberFormatHelper.FormatCurrency(totalAdditionalCosts)} ج.م");
                                });
                                column.Item().PaddingTop(5).Row(row =>
                                {
                                    row.RelativeItem().Text($"المجموع الكلي: {NumberFormatHelper.FormatCurrency(grandTotal)} ج.م").Bold();
                                    row.RelativeItem().Text($"سعر المتر: {NumberFormatHelper.FormatCurrency(pricePerMeter)} ج.م/م²").Bold();
                                });
                            });
                    }
                });

                document.GeneratePdf(filePath);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء تقرير PDF: {ex.Message}", ex);
            }
        }
    }
}
