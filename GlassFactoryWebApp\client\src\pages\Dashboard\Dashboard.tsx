import React from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  Paper,
  IconButton,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  TrendingUp,
  TrendingDown,
  ShoppingCart,
  Inventory,
  People,
  AccountBalance,
  Factory,
  Assessment,
  MonetizationOn,
  Warning,
  CheckCircle,
  Schedule,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import { Helmet } from 'react-helmet-async';
import { dashboardService } from '../../services/dashboardService';
import LoadingSpinner from '../../components/Common/LoadingSpinner';
import ErrorMessage from '../../components/Common/ErrorMessage';
import StatCard from '../../components/Dashboard/StatCard';
import QuickActions from '../../components/Dashboard/QuickActions';
import RecentActivities from '../../components/Dashboard/RecentActivities';
import AlertsPanel from '../../components/Dashboard/AlertsPanel';

const Dashboard: React.FC = () => {
  // جلب بيانات لوحة التحكم
  const { data: dashboardData, isLoading, error } = useQuery(
    'dashboard-data',
    dashboardService.getDashboardData,
    {
      refetchInterval: 5 * 60 * 1000, // تحديث كل 5 دقائق
    }
  );

  const { data: salesChart } = useQuery(
    'sales-chart',
    () => dashboardService.getSalesChartData(30), // آخر 30 يوم
    {
      refetchInterval: 10 * 60 * 1000, // تحديث كل 10 دقائق
    }
  );

  const { data: recentActivities } = useQuery(
    'recent-activities',
    () => dashboardService.getRecentActivities(10),
    {
      refetchInterval: 2 * 60 * 1000, // تحديث كل دقيقتين
    }
  );

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message="حدث خطأ في تحميل بيانات لوحة التحكم" />;

  const stats = dashboardData?.stats || {};
  const alerts = dashboardData?.alerts || [];

  // بيانات الرسوم البيانية
  const pieChartData = [
    { name: 'المبيعات', value: stats.totalSales || 0, color: '#0088FE' },
    { name: 'المشتريات', value: stats.totalPurchases || 0, color: '#00C49F' },
    { name: 'المصروفات', value: stats.totalExpenses || 0, color: '#FFBB28' },
    { name: 'الرواتب', value: stats.totalPayroll || 0, color: '#FF8042' },
  ];

  const monthlyData = salesChart || [];

  return (
    <>
      <Helmet>
        <title>لوحة التحكم - نظام حسابات مصنع الزجاج</title>
      </Helmet>

      <Box sx={{ flexGrow: 1, p: 3 }}>
        {/* العنوان الرئيسي */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom>
            🏭 لوحة التحكم - نظام حسابات مصنع الزجاج
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            مرحباً بك في نظام إدارة مصنع الزجاج المتكامل
          </Typography>
        </Box>

        {/* الإحصائيات الرئيسية */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="إجمالي المبيعات"
              value={stats.totalSales || 0}
              icon={<MonetizationOn />}
              color="primary"
              trend={stats.salesTrend || 0}
              period="هذا الشهر"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="عدد الفواتير"
              value={stats.totalInvoices || 0}
              icon={<ShoppingCart />}
              color="success"
              trend={stats.invoicesTrend || 0}
              period="هذا الشهر"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="عدد العملاء"
              value={stats.totalCustomers || 0}
              icon={<People />}
              color="info"
              trend={stats.customersTrend || 0}
              period="إجمالي"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="المخزون الحالي"
              value={stats.totalInventoryValue || 0}
              icon={<Inventory />}
              color="warning"
              trend={stats.inventoryTrend || 0}
              period="القيمة الإجمالية"
            />
          </Grid>
        </Grid>

        {/* الإجراءات السريعة والتنبيهات */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} md={8}>
            <QuickActions />
          </Grid>
          <Grid item xs={12} md={4}>
            <AlertsPanel alerts={alerts} />
          </Grid>
        </Grid>

        {/* الرسوم البيانية */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          {/* رسم بياني للمبيعات الشهرية */}
          <Grid item xs={12} lg={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📈 المبيعات الشهرية
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={monthlyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <Tooltip formatter={(value) => [`${value} ريال`, 'المبيعات']} />
                      <Area
                        type="monotone"
                        dataKey="sales"
                        stroke="#1976d2"
                        fill="#1976d2"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* رسم دائري للتوزيع المالي */}
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  💰 التوزيع المالي
                </Typography>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={pieChartData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {pieChartData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value} ريال`} />
                    </PieChart>
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* الأنشطة الحديثة والمهام */}
        <Grid container spacing={3}>
          <Grid item xs={12} lg={8}>
            <RecentActivities activities={recentActivities || []} />
          </Grid>
          <Grid item xs={12} lg={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  📊 ملخص سريع
                </Typography>
                <Box sx={{ mt: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">الفواتير المدفوعة</Typography>
                    <Typography variant="body2" color="success.main">
                      {stats.paidInvoicesPercentage || 0}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stats.paidInvoicesPercentage || 0}
                    color="success"
                    sx={{ mb: 2 }}
                  />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">مستوى المخزون</Typography>
                    <Typography variant="body2" color="warning.main">
                      {stats.inventoryLevelPercentage || 0}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stats.inventoryLevelPercentage || 0}
                    color="warning"
                    sx={{ mb: 2 }}
                  />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2">أوامر التصنيع المكتملة</Typography>
                    <Typography variant="body2" color="info.main">
                      {stats.completedOrdersPercentage || 0}%
                    </Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={stats.completedOrdersPercentage || 0}
                    color="info"
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default Dashboard;
