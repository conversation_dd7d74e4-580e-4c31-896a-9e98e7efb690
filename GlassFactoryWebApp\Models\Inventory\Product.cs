using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج المنتج
    /// </summary>
    public class Product : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string ProductCode { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string ProductName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string ProductDescription { get; set; } = string.Empty;

        [Required]
        public int CategoryId { get; set; }

        [MaxLength(50)]
        public string Unit { get; set; } = "قطعة";

        [Column(TypeName = "decimal(18,2)")]
        public decimal SalePrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CostPrice { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MinimumStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MaximumStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal CurrentStock { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ReorderLevel { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        public bool IsManufactured { get; set; } = false;

        public bool IsPurchased { get; set; } = true;

        public bool IsSold { get; set; } = true;

        [MaxLength(100)]
        public string? Barcode { get; set; }

        [MaxLength(200)]
        public string? ImagePath { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal Weight { get; set; } = 0;

        [MaxLength(50)]
        public string? WeightUnit { get; set; }

        // Glass-specific properties
        [MaxLength(50)]
        public string? GlassType { get; set; } // شفاف، ملون، مقسى، مصفح

        [MaxLength(50)]
        public string? GlassColor { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? StandardWidth { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? StandardHeight { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? StandardThickness { get; set; }

        [MaxLength(100)]
        public string? FinishingOptions { get; set; } // صقل، حفر، طباعة

        public bool IsCustomSize { get; set; } = false;

        [MaxLength(100)]
        public string? Supplier { get; set; }

        [MaxLength(100)]
        public string? Brand { get; set; }

        [MaxLength(50)]
        public string? QualityGrade { get; set; } // ممتاز، جيد، عادي

        // Foreign Keys
        [ForeignKey("CategoryId")]
        public virtual ProductCategory Category { get; set; } = null!;

        // Navigation Properties
        public virtual ICollection<SalesInvoiceItem> SalesItems { get; set; } = new List<SalesInvoiceItem>();
        public virtual ICollection<PurchaseInvoiceItem> PurchaseItems { get; set; } = new List<PurchaseInvoiceItem>();
        public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();
        public virtual ICollection<ManufacturingOrderItem> ManufacturingItems { get; set; } = new List<ManufacturingOrderItem>();
        public virtual ICollection<ProductionBOM> BOMItems { get; set; } = new List<ProductionBOM>();
    }
}
