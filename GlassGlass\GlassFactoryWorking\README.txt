Glass Factory Accounting System - WORKING PROJECT
================================================

HOW TO RUN:
1. Double-click: OPEN_PROJECT.bat
2. Visual Studio will open
3. Press F5 to run

OR:
1. Double-click: GlassFactoryWorking.sln
2. Press F5 in Visual Studio

WHAT YOU WILL SEE:
- Window with Arabic title
- 3 colored buttons
- Green status text at bottom
- Working SQLite database

TEST THE APP:
- Click green button: Shows success message
- Click blue button: Shows accounts from database
- Click red button: Exits the application

GUARANTEED TO WORK with:
- Visual Studio 2019+
- .NET Framework 4.8
- Windows 10

Project includes:
- Complete .sln and .csproj files
- MainForm with Designer
- SQLite database integration
- Arabic RTL support
