﻿#pragma checksum "..\..\..\..\Views\ItemEditWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "96B44B5624BD32C474FA0C1D89147FB49E906863"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ItemEditWindow
    /// </summary>
    public partial class ItemEditWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 62 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtName;
        
        #line default
        #line hidden
        
        
        #line 68 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbWarehouse;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbUnitOfMeasure;
        
        #line default
        #line hidden
        
        
        #line 87 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkHasDimensions;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DimensionsPanel;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtLength;
        
        #line default
        #line hidden
        
        
        #line 108 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWidth;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtArea;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDescription;
        
        #line default
        #line hidden
        
        
        #line 128 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\ItemEditWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/itemeditwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ItemEditWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.CmbWarehouse = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.CmbUnitOfMeasure = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.ChkHasDimensions = ((System.Windows.Controls.CheckBox)(target));
            
            #line 88 "..\..\..\..\Views\ItemEditWindow.xaml"
            this.ChkHasDimensions.Checked += new System.Windows.RoutedEventHandler(this.ChkHasDimensions_Checked);
            
            #line default
            #line hidden
            
            #line 88 "..\..\..\..\Views\ItemEditWindow.xaml"
            this.ChkHasDimensions.Unchecked += new System.Windows.RoutedEventHandler(this.ChkHasDimensions_Unchecked);
            
            #line default
            #line hidden
            return;
            case 5:
            this.DimensionsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 6:
            this.TxtLength = ((System.Windows.Controls.TextBox)(target));
            
            #line 103 "..\..\..\..\Views\ItemEditWindow.xaml"
            this.TxtLength.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Dimensions_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TxtWidth = ((System.Windows.Controls.TextBox)(target));
            
            #line 109 "..\..\..\..\Views\ItemEditWindow.xaml"
            this.TxtWidth.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Dimensions_TextChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TxtArea = ((System.Windows.Controls.TextBox)(target));
            return;
            case 9:
            this.TxtDescription = ((System.Windows.Controls.TextBox)(target));
            return;
            case 10:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 130 "..\..\..\..\Views\ItemEditWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 133 "..\..\..\..\Views\ItemEditWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

