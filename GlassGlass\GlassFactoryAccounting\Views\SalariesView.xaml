<UserControl x:Class="GlassFactoryAccounting.Views.SalariesView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900"
             FlowDirection="RightToLeft">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
        <!-- نمط موحد لحقول الإدخال (TextBox, ComboBox, DatePicker) -->
        <Style x:Key="UnifiedInputStyle" TargetType="TextBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderBrush" Value="#CCCCCC"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>
        <Style x:Key="UnifiedComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Padding" Value="12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>
        <Style x:Key="UnifiedDatePickerStyle" TargetType="DatePicker">
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Height" Value="40"/>
            <Setter Property="VerticalContentAlignment" Value="Center"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- شريط الأدوات -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="BtnNewEmployee" Content="موظف جديد" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="15,8" Margin="0,0,10,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnNewEmployee_Click"/>
                
                <Button x:Name="BtnNewSalary" Content="راتب جديد" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,8" Margin="0,0,10,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnNewSalary_Click"/>
                
                <Button x:Name="BtnRefresh" Content="تحديث" 
                        Background="{StaticResource WarningBrush}" Foreground="White"
                        Padding="15,8" Margin="0,0,10,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnRefresh_Click"/>
                
                <ComboBox x:Name="CmbMonth" Width="120" Height="35" Margin="0,0,10,0"
                          Style="{StaticResource UnifiedComboBoxStyle}" VerticalContentAlignment="Center"/>
                
                <ComboBox x:Name="CmbYear" Width="80" Height="35" Margin="0,0,10,0"
                          Style="{StaticResource UnifiedComboBoxStyle}" VerticalContentAlignment="Center"/>
                
                <Button x:Name="BtnSearch" Content="بحث" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,8" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSearch_Click"/>
            </StackPanel>
        </Border>
        
        <!-- إحصائيات سريعة -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <UniformGrid Columns="4">
                <!-- إجمالي الموظفين -->
                <StackPanel Margin="10">
                    <TextBlock Text="👥" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="إجمالي الموظفين" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtTotalEmployees" Text="0" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource PrimaryBrush}"/>
                </StackPanel>
                
                <!-- رواتب هذا الشهر -->
                <StackPanel Margin="10">
                    <TextBlock Text="💵" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="رواتب هذا الشهر" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtMonthSalaries" Text="0.00 ج.م" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource SuccessBrush}"/>
                </StackPanel>
                
                <!-- رواتب مدفوعة -->
                <StackPanel Margin="10">
                    <TextBlock Text="✅" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="رواتب مدفوعة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtPaidSalaries" Text="0.00 ج.م" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource SuccessBrush}"/>
                </StackPanel>
                
                <!-- رواتب معلقة -->
                <StackPanel Margin="10">
                    <TextBlock Text="⏳" FontSize="24" HorizontalAlignment="Center"/>
                    <TextBlock Text="رواتب معلقة" FontSize="12" HorizontalAlignment="Center" Margin="0,5,0,0"/>
                    <TextBlock x:Name="TxtPendingSalaries" Text="0.00 ج.م" FontSize="16" FontWeight="Bold" 
                             HorizontalAlignment="Center" Foreground="{StaticResource WarningBrush}"/>
                </StackPanel>
            </UniformGrid>
        </Border>
        
        <!-- جدول الرواتب -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- عنوان القائمة -->
                <TextBlock Grid.Row="0" Text="قائمة الرواتب والأجور" 
                         FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
                
                <!-- جدول البيانات -->
                <DataGrid x:Name="SalariesDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40"
                          FontSize="14">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="كود الموظف" Binding="{Binding Employee.EmployeeCode}" Width="100"/>
                        <DataGridTextColumn Header="اسم الموظف" Binding="{Binding Employee.Name}" Width="150"/>
                        <DataGridTextColumn Header="المنصب" Binding="{Binding Employee.Position}" Width="120"/>
                        <DataGridTextColumn Header="الشهر" Binding="{Binding SalaryMonth, StringFormat='{}{0:yyyy/MM}'}" Width="100"/>
                        <DataGridTextColumn Header="الراتب الأساسي" Binding="{Binding BasicSalary, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="البدلات" Binding="{Binding Allowances, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="الإضافي" Binding="{Binding Overtime, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="المكافآت" Binding="{Binding Bonus, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="الخصومات" Binding="{Binding Deductions, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                        <DataGridTextColumn Header="الصافي" Binding="{Binding NetSalary, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridCheckBoxColumn Header="مدفوع" Binding="{Binding IsPaid}" Width="60"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal">
                                        <Button Content="عرض" Background="{StaticResource PrimaryBrush}" 
                                                Foreground="White" Padding="8,4" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnViewSalary_Click"/>
                                        <Button Content="دفع" Background="{StaticResource SuccessBrush}" 
                                                Foreground="White" Padding="8,4" Margin="2"
                                                BorderThickness="0" Cursor="Hand"
                                                Click="BtnPaySalary_Click"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
