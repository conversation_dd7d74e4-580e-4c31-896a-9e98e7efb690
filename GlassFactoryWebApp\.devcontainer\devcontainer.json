{"name": "Glass Factory Accounting System", "image": "mcr.microsoft.com/devcontainers/dotnet:8.0", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "18"}, "ghcr.io/devcontainers/features/github-cli:1": {}}, "customizations": {"vscode": {"extensions": ["ms-dotnettools.csharp", "ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode"]}}, "forwardPorts": [5000, 3000], "postCreateCommand": "dotnet restore && cd client && npm install", "postStartCommand": "echo 'Glass Factory Accounting System - Development Environment Ready!'"}