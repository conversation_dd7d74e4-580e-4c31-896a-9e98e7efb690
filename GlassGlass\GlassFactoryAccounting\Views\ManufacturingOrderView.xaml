<UserControl x:Class="GlassFactoryAccounting.Views.ManufacturingOrderView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             Background="White">

    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- العنوان مع زر الرجوع -->
            <Border Grid.Row="0" Background="#2E86AB" CornerRadius="10" Padding="15" Margin="0,0,0,20">
                <Grid>
                    <Button x:Name="btnBack" Content="⬅️ رجوع"
                           Background="#1E5A73" Foreground="White" Padding="10,5"
                           FontSize="14" FontWeight="Bold" HorizontalAlignment="Left"
                           Click="BtnBack_Click"/>

                    <TextBlock Text="🏭 أمر تصنيع جديد"
                              FontSize="24" FontWeight="Bold"
                              Foreground="White" HorizontalAlignment="Center"/>

                    <Button x:Name="btnToggleNavigationTools"
                           Content="❌ إخفاء أدوات التنقل"
                           Background="#6C757D" Foreground="White"
                           FontSize="12" FontWeight="Bold"
                           Padding="12,6" HorizontalAlignment="Right"
                           BorderThickness="0" Cursor="Hand"
                           ToolTip="إخفاء/إظهار الأقسام الفرعية لتوسيع مساحة العمل"
                           Click="BtnToggleNavigationTools_Click"/>
                </Grid>
            </Border>

            <!-- البيانات الأساسية -->
            <GroupBox x:Name="BasicDataPanel" Grid.Row="1" Header="📋 البيانات الأساسية" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الصف الأول -->
                    <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                        <TextBlock Text="رقم أمر التصنيع:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtOrderNumber" IsReadOnly="True" Background="#F0F0F0" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                        <TextBlock Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtCustomerName" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                        <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtInvoiceNumber" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <StackPanel Grid.Row="0" Grid.Column="3" Margin="5">
                        <TextBlock Text="التاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <DatePicker x:Name="dpOrderDate" Padding="8" FontSize="14"/>
                    </StackPanel>

                    <!-- الصف الثاني -->
                    <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                        <TextBlock Text="حالة الطلب:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <ComboBox x:Name="cmbOrderStatus" Padding="8" FontSize="14">
                            <ComboBoxItem Content="تحت التشغيل" IsSelected="True"/>
                            <ComboBoxItem Content="مكتمل"/>
                            <ComboBoxItem Content="مؤجل"/>
                        </ComboBox>
                    </StackPanel>

                    <StackPanel Grid.Row="1" Grid.Column="1" Grid.ColumnSpan="3" Margin="5">
                        <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="txtNotes" Padding="8" FontSize="14"/>
                    </StackPanel>
                </Grid>
            </GroupBox>

            <!-- بيانات ألواح الزجاج -->
            <GroupBox Grid.Row="2" Header="🪟 بيانات ألواح الزجاج" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <Button x:Name="btnAddGlassPanel" Content="➕ إضافة لوح زجاج" 
                           Background="#28A745" Foreground="White" Padding="10,5" 
                           FontSize="14" FontWeight="Bold" Margin="0,0,0,10"
                           Click="BtnAddGlassPanel_Click"/>
                    
                    <DataGrid x:Name="dgGlassPanels" AutoGenerateColumns="False"
                             CanUserAddRows="False" CanUserDeleteRows="True"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="150"
                             CellEditEnding="DgGlassPanels_CellEditEnding">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40" IsReadOnly="True"/>
                            <DataGridTextColumn Header="نوع الزجاج" Binding="{Binding GlassType}" Width="120"/>
                            <DataGridTextColumn Header="السمك" Binding="{Binding Thickness}" Width="80"/>
                            <DataGridTextColumn Header="الطول (ملم)" Binding="{Binding Length}" Width="100"/>
                            <DataGridTextColumn Header="العرض (ملم)" Binding="{Binding Width}" Width="100"/>
                            <DataGridTextColumn Header="المساحة م²" Binding="{Binding SquareMeters}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="العدد" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="إجمالي م²" Binding="{Binding TotalSquareMeters}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="السعر" Binding="{Binding Price}" Width="100"/>
                            <DataGridTextColumn Header="إجمالي القيمة" Binding="{Binding TotalValue}" Width="120" IsReadOnly="True"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="150"/>

                            <!-- أعمدة الإجراءات -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="✏️" Background="#FFC107" Foreground="Black"
                                                   Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                   ToolTip="تعديل" Click="BtnEditGlassPanel_Click"/>
                                            <Button Content="🗑️" Background="#DC3545" Foreground="White"
                                                   Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                   ToolTip="حذف" Click="BtnDeleteGlassPanel_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- مجموع ألواح الزجاج -->
                    <Border Background="#E3F2FD" Padding="10" Margin="0,10,0,0" CornerRadius="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <TextBlock Grid.Column="0" x:Name="txtTotalGlassSquareMeters" Text="مجموع إجمالي المتر المربع: 0.00 م²"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Left"/>
                            <TextBlock Grid.Column="1" x:Name="txtTotalGlassValue" Text="مجموع إجمالي القيمة: 0.00 ج.م"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Right"/>
                        </Grid>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- الخدمات المرتبطة -->
            <GroupBox x:Name="ServicesPanel" Grid.Row="3" Header="⚙️ الخدمات المرتبطة بالزجاج" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <!-- صف الخدمات الأول -->
                    <WrapPanel Margin="0,0,0,10">
                        <CheckBox x:Name="chkFilmService" Content="🎬 فيلم" FontSize="14" FontWeight="Bold"
                                 Margin="0,0,20,0" Checked="ServiceCheckBox_Checked" Unchecked="ServiceCheckBox_Unchecked"/>
                        <CheckBox x:Name="chkDoubleGlassService" Content="🔄 دبل جلاس" FontSize="14" FontWeight="Bold"
                                 Margin="0,0,20,0" Checked="ServiceCheckBox_Checked" Unchecked="ServiceCheckBox_Unchecked"/>
                        <CheckBox x:Name="chkBevelService" Content="✂️ شطف" FontSize="14" FontWeight="Bold"
                                 Margin="0,0,20,0" Checked="ServiceCheckBox_Checked" Unchecked="ServiceCheckBox_Unchecked"/>
                    </WrapPanel>

                    <!-- صف الخدمات الثاني -->
                    <WrapPanel Margin="0,0,0,10">
                        <CheckBox x:Name="chkThermalService" Content="🔥 حراري" FontSize="14" FontWeight="Bold" Margin="0,0,20,0"/>
                        <CheckBox x:Name="chkSGPService" Content="🛡️ SGP" FontSize="14" FontWeight="Bold" Margin="0,0,20,0"/>
                        <CheckBox x:Name="chkPrintService" Content="🖨️ طبع" FontSize="14" FontWeight="Bold" Margin="0,0,20,0"/>
                        <CheckBox x:Name="chkCNCService" Content="⚙️ CNC" FontSize="14" FontWeight="Bold" Margin="0,0,20,0"/>
                        <CheckBox x:Name="chkSprayService" Content="💨 بخ" FontSize="14" FontWeight="Bold" Margin="0,0,20,0"/>
                        <CheckBox x:Name="chkDrawingService" Content="🎨 رسم طباعة" FontSize="14" FontWeight="Bold" Margin="0,0,20,0"/>
                    </WrapPanel>

                    <!-- جداول الخدمات المفصلة -->
                    <StackPanel x:Name="spServiceDetails" Margin="0,10,0,0"/>
                </StackPanel>
            </GroupBox>

            <!-- جدول المقاسات المطلوبة -->
            <GroupBox Grid.Row="4" Header="📏 جدول المقاسات المطلوبة للقص" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <Button x:Name="btnAddRequiredSize" Content="➕ إضافة مقاس" 
                           Background="#17A2B8" Foreground="White" Padding="10,5" 
                           FontSize="14" FontWeight="Bold" Margin="0,0,0,10"
                           Click="BtnAddRequiredSize_Click"/>
                    
                    <DataGrid x:Name="dgRequiredSizes" AutoGenerateColumns="False"
                             CanUserAddRows="False" CanUserDeleteRows="True"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="150"
                             CellEditEnding="DgRequiredSizes_CellEditEnding">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40" IsReadOnly="True"/>
                            <DataGridTextColumn Header="Ref كود" Binding="{Binding RefCode}" Width="80" IsReadOnly="True"/>
                            <DataGridTextColumn Header="نوع الزجاج" Binding="{Binding GlassType}" Width="120"/>
                            <DataGridTextColumn Header="السمك" Binding="{Binding Thickness}" Width="80"/>
                            <DataGridTextColumn Header="الطول" Binding="{Binding Length}" Width="100"/>
                            <DataGridTextColumn Header="العرض" Binding="{Binding Width}" Width="100"/>
                            <DataGridTextColumn Header="المساحة م²" Binding="{Binding SquareMeters}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="العدد" Binding="{Binding Quantity}" Width="80"/>
                            <DataGridTextColumn Header="إجمالي م²" Binding="{Binding TotalSquareMeters}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="المتر الطولي" Binding="{Binding LinearMeters}" Width="100" IsReadOnly="True"/>
                            <DataGridTextColumn Header="إجمالي المتر الطولي" Binding="{Binding TotalLinearMeters}" Width="120" IsReadOnly="True"/>

                            <!-- أعمدة الإجراءات -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="✏️" Background="#FFC107" Foreground="Black"
                                                   Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                   ToolTip="تعديل" Click="BtnEditRequiredSize_Click"/>
                                            <Button Content="🗑️" Background="#DC3545" Foreground="White"
                                                   Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                   ToolTip="حذف" Click="BtnDeleteRequiredSize_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <!-- مجاميع المقاسات المطلوبة -->
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Border Grid.Column="0" Background="#E9ECEF" Padding="10" CornerRadius="5" Margin="0,0,5,0">
                            <TextBlock x:Name="txtTotalRequiredMeters" Text="إجمالي الأمتار المطلوبة فعلياً: 0.00 م²"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>

                        <Border Grid.Column="1" Background="#D1ECF1" Padding="10" CornerRadius="5" Margin="5,0,0,0">
                            <TextBlock x:Name="txtTotalLinearMeters" Text="إجمالي المتر الطولي: 0.00 م"
                                      FontSize="14" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                    </Grid>
                </StackPanel>
            </GroupBox>

            <!-- احتساب تكلفة الخدمات -->
            <GroupBox x:Name="ServiceCostsPanel" Grid.Row="5" Header="💰 احتساب تكلفة الخدمات" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <Button x:Name="btnCalculateServiceCosts" Content="🧮 احتساب تكلفة الخدمات" 
                           Background="#FFC107" Foreground="Black" Padding="10,5" 
                           FontSize="14" FontWeight="Bold" Margin="0,0,0,10"
                           Click="BtnCalculateServiceCosts_Click"/>
                    
                    <StackPanel x:Name="spServiceCosts" Visibility="Collapsed">
                        <Button x:Name="btnAddServiceCost" Content="➕ إضافة خدمة" 
                               Background="#28A745" Foreground="White" Padding="8,4" 
                               FontSize="12" FontWeight="Bold" Margin="0,0,0,10"
                               Click="BtnAddServiceCost_Click"/>
                        
                        <DataGrid x:Name="dgServiceCosts" AutoGenerateColumns="False"
                                 CanUserAddRows="False" CanUserDeleteRows="True"
                                 GridLinesVisibility="All" HeadersVisibility="All"
                                 FontSize="12" MinHeight="120"
                             CellEditEnding="DgServiceCosts_CellEditEnding">
                            <DataGrid.Columns>
                                <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40" IsReadOnly="True"/>
                                <DataGridTextColumn Header="اسم الخدمة" Binding="{Binding ServiceName}" Width="150"/>
                                <DataGridTextColumn Header="الوصف" Binding="{Binding Description}" Width="200"/>
                                <DataGridTextColumn Header="الكمية/العدد" Binding="{Binding Quantity}" Width="100"/>
                                <DataGridTextColumn Header="السعر" Binding="{Binding Price}" Width="100"/>
                                <DataGridTextColumn Header="القيمة" Binding="{Binding Value}" Width="100" IsReadOnly="True"/>

                                <!-- أعمدة الإجراءات -->
                                <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal">
                                                <Button Content="✏️" Background="#FFC107" Foreground="Black"
                                                       Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                       ToolTip="تعديل" Click="BtnEditServiceCost_Click"/>
                                                <Button Content="🗑️" Background="#DC3545" Foreground="White"
                                                       Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                       ToolTip="حذف" Click="BtnDeleteServiceCost_Click"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>
                        </DataGrid>

                        <Border Background="#D4EDDA" Padding="10" Margin="0,10,0,0" CornerRadius="5">
                            <TextBlock x:Name="txtTotalServiceCosts" Text="إجمالي تكلفة الخدمات: 0.00 ج.م" 
                                      FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                        </Border>
                    </StackPanel>
                </StackPanel>
            </GroupBox>

            <!-- التكاليف الإضافية -->
            <GroupBox x:Name="AdditionalCostsPanel" Grid.Row="6" Header="📊 التكاليف الإضافية / المحملة" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <StackPanel Margin="10">
                    <Button x:Name="btnAddAdditionalCost" Content="➕ إضافة تكلفة إضافية" 
                           Background="#6F42C1" Foreground="White" Padding="10,5" 
                           FontSize="14" FontWeight="Bold" Margin="0,0,0,10"
                           Click="BtnAddAdditionalCost_Click"/>
                    
                    <DataGrid x:Name="dgAdditionalCosts" AutoGenerateColumns="False"
                             CanUserAddRows="False" CanUserDeleteRows="True"
                             GridLinesVisibility="All" HeadersVisibility="All"
                             FontSize="12" MinHeight="120"
                             CellEditEnding="DgAdditionalCosts_CellEditEnding">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="ت" Binding="{Binding RowNumber}" Width="40" IsReadOnly="True"/>
                            <DataGridTextColumn Header="وصف التكلفة" Binding="{Binding Description}" Width="300"/>
                            <DataGridTextColumn Header="القيمة" Binding="{Binding Value}" Width="150"/>
                            <DataGridTextColumn Header="ملاحظات" Binding="{Binding Notes}" Width="200"/>

                            <!-- أعمدة الإجراءات -->
                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal">
                                            <Button Content="✏️" Background="#FFC107" Foreground="Black"
                                                   Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                   ToolTip="تعديل" Click="BtnEditAdditionalCost_Click"/>
                                            <Button Content="🗑️" Background="#DC3545" Foreground="White"
                                                   Padding="5,2" Margin="2" FontSize="10" FontWeight="Bold"
                                                   ToolTip="حذف" Click="BtnDeleteAdditionalCost_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>

                    <Border Background="#F8D7DA" Padding="10" Margin="0,10,0,0" CornerRadius="5">
                        <TextBlock x:Name="txtTotalAdditionalCosts" Text="إجمالي التكاليف المحملة: 0.00 ج.م" 
                                  FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                    </Border>
                </StackPanel>
            </GroupBox>

            <!-- ملخص التكاليف -->
            <GroupBox x:Name="CostSummaryPanel" Grid.Row="7" Header="📋 ملخص التكاليف" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Row="0" Grid.Column="0" Text="إجمالي تكلفة الخدمات:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtSummaryServiceCosts" Text="0.00 ج.م" FontSize="14" Margin="5"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="إجمالي التكاليف الإضافية:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtSummaryAdditionalCosts" Text="0.00 ج.م" FontSize="14" Margin="5"/>

                    <TextBlock Grid.Row="2" Grid.Column="0" Text="إجمالي قيمة الزجاج المستخدم:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="2" Grid.Column="1" x:Name="txtSummaryGlassCosts" Text="0.00 ج.م" FontSize="14" Margin="5"/>

                    <Separator Grid.Row="3" Grid.ColumnSpan="2" Margin="5"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="المجموع الكلي:" FontSize="16" FontWeight="Bold" Margin="5" Foreground="#DC3545"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" x:Name="txtSummaryTotalCost" Text="0.00 ج.م" FontSize="16" FontWeight="Bold" Margin="5" Foreground="#DC3545"/>

                    <TextBlock Grid.Row="5" Grid.Column="0" Text="إجمالي الأمتار المطلوبة فعلياً:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="5" Grid.Column="1" x:Name="txtSummaryTotalMeters" Text="0.00 م²" FontSize="14" Margin="5"/>

                    <TextBlock Grid.Row="6" Grid.Column="0" Text="سعر المتر:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="6" Grid.Column="1" x:Name="txtSummaryPricePerMeter" Text="0.00 ج.م/م²" FontSize="14" Margin="5"/>
                </Grid>
            </GroupBox>

            <!-- هالك الزجاج والفيلم -->
            <GroupBox x:Name="WasteCalculationPanel" Grid.Row="8" Header="📊 هالك الزجاج والفيلم" FontSize="16" FontWeight="Bold" Margin="0,0,0,15">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- هالك الزجاج -->
                    <TextBlock Grid.Row="0" Grid.Column="0" Text="هالك الزجاج (م²):" FontSize="14" FontWeight="Bold" Margin="5" Foreground="#DC3545"/>
                    <TextBlock Grid.Row="0" Grid.Column="1" x:Name="txtGlassWaste" Text="0.00 م²" FontSize="14" Margin="5" Foreground="#DC3545"/>

                    <TextBlock Grid.Row="1" Grid.Column="0" Text="نسبة هالك الزجاج:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="1" Grid.Column="1" x:Name="txtGlassWastePercentage" Text="0.00%" FontSize="14" Margin="5"/>

                    <Separator Grid.Row="2" Grid.ColumnSpan="2" Margin="5"/>

                    <!-- هالك الفيلم -->
                    <TextBlock Grid.Row="3" Grid.Column="0" Text="هالك الفيلم (م²):" FontSize="14" FontWeight="Bold" Margin="5" Foreground="#FFC107"/>
                    <TextBlock Grid.Row="3" Grid.Column="1" x:Name="txtFilmWaste" Text="0.00 م²" FontSize="14" Margin="5" Foreground="#FFC107"/>

                    <TextBlock Grid.Row="4" Grid.Column="0" Text="نسبة هالك الفيلم:" FontSize="14" FontWeight="Bold" Margin="5"/>
                    <TextBlock Grid.Row="4" Grid.Column="1" x:Name="txtFilmWastePercentage" Text="0.00%" FontSize="14" Margin="5"/>
                </Grid>
            </GroupBox>

            <!-- الأزرار الرئيسية -->
            <StackPanel Grid.Row="9" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                <Button x:Name="btnSaveOrder" Content="💾 حفظ أمر التصنيع" 
                       Background="#28A745" Foreground="White" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnSaveOrder_Click"/>
                
                <Button x:Name="btnViewOrder" Content="📄 عرض أمر التصنيع" 
                       Background="#17A2B8" Foreground="White" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnViewOrder_Click"/>
                
                <Button x:Name="btnPrintPDF" Content="🖨️ طباعة PDF" 
                       Background="#FFC107" Foreground="Black" Padding="15,8" 
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnPrintPDF_Click"/>
                
                <Button x:Name="btnTransferToDelivery" Content="🚚 ترحيل إلى أمر التسليم"
                       Background="#FF6B35" Foreground="White" Padding="15,8"
                       FontSize="16" FontWeight="Bold" Margin="5"
                       Click="BtnTransferToDelivery_Click"/>
            </StackPanel>
        </Grid>
    </ScrollViewer>
</UserControl>
