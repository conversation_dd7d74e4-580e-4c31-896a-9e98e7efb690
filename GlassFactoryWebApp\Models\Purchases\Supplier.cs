using System.ComponentModel.DataAnnotations;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج المورد
    /// </summary>
    public class Supplier : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string SupplierCode { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string SupplierName { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? ContactPerson { get; set; }

        [MaxLength(20)]
        public string? Phone { get; set; }

        [MaxLength(20)]
        public string? Mobile { get; set; }

        [MaxLength(100)]
        public string? Email { get; set; }

        [MaxLength(500)]
        public string? Address { get; set; }

        [MaxLength(100)]
        public string? City { get; set; }

        [MaxLength(100)]
        public string? Country { get; set; }

        [MaxLength(20)]
        public string? TaxNumber { get; set; }

        [MaxLength(20)]
        public string? CommercialRegister { get; set; }

        public decimal CreditLimit { get; set; } = 0;

        public int PaymentTermDays { get; set; } = 30;

        public decimal CurrentBalance { get; set; } = 0;

        public bool IsActive { get; set; } = true;

        [MaxLength(50)]
        public string SupplierType { get; set; } = "عادي"; // محلي، مستورد، وكيل

        [MaxLength(100)]
        public string? BankAccount { get; set; }

        [MaxLength(100)]
        public string? BankName { get; set; }

        public decimal DiscountPercentage { get; set; } = 0;

        // Glass industry specific
        [MaxLength(200)]
        public string? Specialization { get; set; } // زجاج خام، أدوات، مواد كيميائية

        [MaxLength(100)]
        public string? QualityCertification { get; set; }

        public bool IsPreferredSupplier { get; set; } = false;

        // Navigation Properties
        public virtual ICollection<PurchaseInvoice> PurchaseInvoices { get; set; } = new List<PurchaseInvoice>();
        public virtual ICollection<SupplierPayment> Payments { get; set; } = new List<SupplierPayment>();
    }
}
