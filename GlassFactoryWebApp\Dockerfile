# Glass Factory Accounting System - Railway Deployment
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080

# Install Node.js for building React app
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs

WORKDIR /src

# Copy csproj and restore dependencies
COPY ["GlassFactoryWebApp.csproj", "."]
RUN dotnet restore "GlassFactoryWebApp.csproj"

# Copy everything else
COPY . .

# Use simplified Program.cs for Railway
RUN cp Program.Railway.Simple.cs Program.cs

# Build React frontend
WORKDIR "/src/client"
RUN npm install
RUN npm run build

# Build .NET backend
WORKDIR "/src"
RUN dotnet build "GlassFactoryWebApp.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "GlassFactoryWebApp.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app

# Copy published app
COPY --from=publish /app/publish .

# Copy React build to wwwroot
COPY --from=build /src/client/build ./wwwroot

# Create directories
RUN mkdir -p /app/uploads /app/reports /app/logs

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:8080

ENTRYPOINT ["dotnet", "GlassFactoryWebApp.dll"]
