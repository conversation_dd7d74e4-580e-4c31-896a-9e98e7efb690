﻿#pragma checksum "..\..\..\..\Views\PayrollView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "76CB90165A79A3A09E751F0CF334C8E96C6D1C9A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// PayrollView
    /// </summary>
    public partial class PayrollView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 217 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnEmployeeData;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSalaryDue;
        
        #line default
        #line hidden
        
        
        #line 233 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAdvance;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBonus;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeduction;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnOvertime;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSimpleSalaryPayment;
        
        #line default
        #line hidden
        
        
        #line 273 "..\..\..\..\Views\PayrollView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFullReport;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/payrollview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\PayrollView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnEmployeeData = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnEmployeeData.Click += new System.Windows.RoutedEventHandler(this.BtnEmployeeData_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnSalaryDue = ((System.Windows.Controls.Button)(target));
            
            #line 225 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnSalaryDue.Click += new System.Windows.RoutedEventHandler(this.BtnSalaryDue_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.BtnAdvance = ((System.Windows.Controls.Button)(target));
            
            #line 233 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnAdvance.Click += new System.Windows.RoutedEventHandler(this.BtnAdvance_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.BtnBonus = ((System.Windows.Controls.Button)(target));
            
            #line 241 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnBonus.Click += new System.Windows.RoutedEventHandler(this.BtnBonus_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnDeduction = ((System.Windows.Controls.Button)(target));
            
            #line 249 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnDeduction.Click += new System.Windows.RoutedEventHandler(this.BtnDeduction_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnOvertime = ((System.Windows.Controls.Button)(target));
            
            #line 257 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnOvertime.Click += new System.Windows.RoutedEventHandler(this.BtnOvertime_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnSimpleSalaryPayment = ((System.Windows.Controls.Button)(target));
            
            #line 265 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnSimpleSalaryPayment.Click += new System.Windows.RoutedEventHandler(this.BtnSimpleSalaryPayment_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnFullReport = ((System.Windows.Controls.Button)(target));
            
            #line 273 "..\..\..\..\Views\PayrollView.xaml"
            this.BtnFullReport.Click += new System.Windows.RoutedEventHandler(this.BtnFullReport_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

