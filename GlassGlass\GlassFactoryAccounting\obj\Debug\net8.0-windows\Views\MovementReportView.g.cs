﻿#pragma checksum "..\..\..\..\Views\MovementReportView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "35F637BF2D6383D408E028FEC0AD3B6B32E4A29E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// MovementReportView
    /// </summary>
    public partial class MovementReportView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 81 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpFromDate;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpToDate;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbMovementType;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbItem;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnFilter;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearFilter;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportExcel;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtRecordCount;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid MovementsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalReceived;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalIssued;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtReceiveCount;
        
        #line default
        #line hidden
        
        
        #line 198 "..\..\..\..\Views\MovementReportView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtIssueCount;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/movementreportview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\MovementReportView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 2:
            this.DpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.CmbMovementType = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.CmbItem = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.BtnFilter = ((System.Windows.Controls.Button)(target));
            
            #line 105 "..\..\..\..\Views\MovementReportView.xaml"
            this.BtnFilter.Click += new System.Windows.RoutedEventHandler(this.BtnFilter_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnClearFilter = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\..\Views\MovementReportView.xaml"
            this.BtnClearFilter.Click += new System.Windows.RoutedEventHandler(this.BtnClearFilter_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnExportExcel = ((System.Windows.Controls.Button)(target));
            
            #line 111 "..\..\..\..\Views\MovementReportView.xaml"
            this.BtnExportExcel.Click += new System.Windows.RoutedEventHandler(this.BtnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TxtRecordCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.MovementsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 10:
            this.TxtTotalReceived = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtTotalIssued = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtReceiveCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtIssueCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

