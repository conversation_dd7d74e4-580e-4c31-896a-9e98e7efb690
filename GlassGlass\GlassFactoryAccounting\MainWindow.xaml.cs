﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using System.Windows.Media;
using System.Windows.Input;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Views;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using GlassFactoryAccounting.Helpers;

namespace GlassFactoryAccounting;

/// <summary>
/// النافذة الرئيسية للبرنامج
/// </summary>
public partial class MainWindow : Window
{
    private readonly DatabaseContext _databaseContext;
    private DispatcherTimer _timer;
    private UserControl? _previousContent;
    private bool _isSideMenuVisible = true;

    public MainWindow()
    {
        try
        {
            // تطبيق الثقافة الإنجليزية للأرقام
            NumberFormatHelper.SetEnglishCulture();

            InitializeComponent();

            // إضافة معالج للأخطاء غير المعالجة
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            Application.Current.DispatcherUnhandledException += Current_DispatcherUnhandledException;

            _databaseContext = new DatabaseContext();
            InitializeAsync();

            // تهيئة مؤقت الوقت
            InitializeTimer();

            // عرض الصفحة الرئيسية افتراضياً
            ShowDashboard();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"MainWindow initialization error: {ex.Message}");
            MessageBox.Show($"حدث خطأ أثناء تشغيل البرنامج: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            var ex = e.ExceptionObject as Exception;
            System.Diagnostics.Debug.WriteLine($"Unhandled exception: {ex?.Message}");

            if (!e.IsTerminating)
            {
                MessageBox.Show($"حدث خطأ غير متوقع: {ex?.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch
        {
            // تجنب الأخطاء في معالج الأخطاء
        }
    }

    private void Current_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"Dispatcher unhandled exception: {e.Exception.Message}");

            MessageBox.Show($"حدث خطأ في الواجهة: {e.Exception.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);

            // منع إغلاق البرنامج
            e.Handled = true;

            // العودة للصفحة الرئيسية
            try
            {
                ShowDashboard();
            }
            catch
            {
                // في حالة فشل العودة للرئيسية
                TxtPageTitle.Text = "الرئيسية";
                MainContentArea.Content = new DashboardView();
            }
        }
        catch
        {
            // تجنب الأخطاء في معالج الأخطاء
            e.Handled = true;
        }
    }

    private async void InitializeAsync()
    {
        try
        {
            if (_databaseContext != null)
            {
                await _databaseContext.InitializeDatabaseAsync();

                // حذف البيانات التجريبية للاختبار
                var payrollService = new PayrollService();
                payrollService.ClearAllTestData();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Database initialization error: {ex.Message}");
            // لا نعرض رسالة خطأ للمستخدم، البرنامج سيعمل بدون قاعدة البيانات
        }
    }

    private void InitializeTimer()
    {
        _timer = new DispatcherTimer();
        _timer.Interval = TimeSpan.FromSeconds(1);
        _timer.Tick += Timer_Tick;
        _timer.Start();

        // تحديث الوقت فوراً
        UpdateTime();
    }

    private void Timer_Tick(object? sender, EventArgs e)
    {
        UpdateTime();
    }

    private void UpdateTime()
    {
        TxtCurrentTime.Text = DateTime.Now.ToString("HH:mm:ss");
    }

    #region Navigation Methods

    private void BtnDashboard_Click(object sender, RoutedEventArgs e)
    {
        ShowDashboard();
    }

    private void BtnSales_Click(object sender, RoutedEventArgs e)
    {
        ShowSales();
    }

    private void BtnPurchases_Click(object sender, RoutedEventArgs e)
    {
        ShowPurchases();
    }

    private void BtnInventory_Click(object sender, RoutedEventArgs e)
    {
        ShowInventory();
    }

    private void BtnManufacturing_Click(object sender, RoutedEventArgs e)
    {
        ShowManufacturing();
    }

    private void BtnExpenses_Click(object sender, RoutedEventArgs e)
    {
        ShowExpenses();
    }

    private void BtnEmployeeAdvances_Click(object sender, RoutedEventArgs e)
    {
        ShowEmployeeAdvances();
    }

    private void BtnSalaries_Click(object sender, RoutedEventArgs e)
    {
        ShowSalaries();
    }

    private void BtnServices_Click(object sender, RoutedEventArgs e)
    {
        ShowServices();
    }

    private void BtnAccounting_Click(object sender, RoutedEventArgs e)
    {
        ShowAccounting();
    }

    private void BtnReports_Click(object sender, RoutedEventArgs e)
    {
        ShowReports();
    }

    private void BtnArchive_Click(object sender, RoutedEventArgs e)
    {
        ShowArchive();
    }

    private void BtnSettings_Click(object sender, RoutedEventArgs e)
    {
        ShowSettings();
    }

    private void BtnCompanyBranches_Click(object sender, RoutedEventArgs e)
    {
        ShowCompanyBranches();
    }

    private void BtnResponsiblePersons_Click(object sender, RoutedEventArgs e)
    {
        ShowResponsiblePersons();
    }

    /// <summary>
    /// إخفاء/إظهار القائمة الجانبية
    /// </summary>
    private void BtnToggleSideMenu_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_isSideMenuVisible)
            {
                // إخفاء القائمة الجانبية
                SideMenuColumn.Width = new GridLength(0);
                SideMenuBorder.Visibility = Visibility.Collapsed;
                BtnToggleSideMenu.Content = "☰";
                BtnToggleSideMenu.ToolTip = "إظهار القائمة الجانبية";
                _isSideMenuVisible = false;
            }
            else
            {
                // إظهار القائمة الجانبية
                SideMenuColumn.Width = new GridLength(250);
                SideMenuBorder.Visibility = Visibility.Visible;
                BtnToggleSideMenu.Content = "✕";
                BtnToggleSideMenu.ToolTip = "إخفاء القائمة الجانبية";
                _isSideMenuVisible = true;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إخفاء/إظهار القائمة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    #endregion

    #region Page Display Methods

    public void ShowDashboard()
    {
        TxtPageTitle.Text = "الرئيسية";
        MainContentArea.Content = new DashboardView();
    }

    private void ShowSales()
    {
        TxtPageTitle.Text = "المبيعات";
        MainContentArea.Content = new SalesView();
    }

    private void ShowPurchases()
    {
        TxtPageTitle.Text = "المشتريات";
        MainContentArea.Content = new PurchasesView();
    }

    private void ShowInventory()
    {
        TxtPageTitle.Text = "المخازن";
        MainContentArea.Content = new InventoryMainView();
    }

    public void ShowWarehouseManagement()
    {
        TxtPageTitle.Text = "إدارة المخازن";
        MainContentArea.Content = new WarehouseManagementView();
    }

    public void ShowItemManagement()
    {
        TxtPageTitle.Text = "إدارة الأصناف";
        MainContentArea.Content = new ItemManagementView();
    }

    public void ShowInventoryMovement()
    {
        TxtPageTitle.Text = "حركة المخزون";
        MainContentArea.Content = new InventoryMovementView();
    }

    public void ShowMovementReport()
    {
        TxtPageTitle.Text = "تقرير الحركات";
        MainContentArea.Content = new MovementReportView();
    }

    public void ShowInventoryBalance()
    {
        TxtPageTitle.Text = "الأصناف المتوفرة";
        MainContentArea.Content = new InventoryBalanceView();
    }

    private void ShowManufacturing()
    {
        TxtPageTitle.Text = "التصنيع";
        MainContentArea.Content = new ManufacturingView();
    }

    /// <summary>
    /// عرض واجهة في المنطقة الرئيسية مع زر رجوع
    /// </summary>
    public void ShowView(UserControl view, string title)
    {
        try
        {
            // إنشاء Grid للمحتوى مع زر الرجوع
            var containerGrid = new Grid();
            containerGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            containerGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });

            // شريط العنوان مع زر الرجوع
            var headerBorder = new Border
            {
                Background = new SolidColorBrush(Color.FromRgb(232, 62, 140)), // #E83E8C
                CornerRadius = new CornerRadius(10, 10, 0, 0),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 0)
            };

            var headerGrid = new Grid();
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // عنوان الصفحة
            var titleText = new TextBlock
            {
                Text = $"🏭 {title}",
                FontSize = 20,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(titleText, 0);

            // زر الرجوع
            var backButton = new Button
            {
                Content = "🔙 رجوع",
                Background = Brushes.White,
                Foreground = new SolidColorBrush(Color.FromRgb(232, 62, 140)),
                FontSize = 14,
                FontWeight = FontWeights.Bold,
                Padding = new Thickness(15, 8, 15, 8),
                BorderThickness = new Thickness(0),
                Cursor = Cursors.Hand
            };
            backButton.Click += (s, e) => ShowManufacturing(); // العودة لصفحة التصنيع
            Grid.SetColumn(backButton, 1);

            headerGrid.Children.Add(titleText);
            headerGrid.Children.Add(backButton);
            headerBorder.Child = headerGrid;

            Grid.SetRow(headerBorder, 0);
            containerGrid.Children.Add(headerBorder);

            // المحتوى
            var contentBorder = new Border
            {
                Background = Brushes.White,
                CornerRadius = new CornerRadius(0, 0, 10, 10),
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1)
            };
            contentBorder.Child = view;
            Grid.SetRow(contentBorder, 1);
            containerGrid.Children.Add(contentBorder);

            // تحديث العنوان والمحتوى
            TxtPageTitle.Text = title;
            MainContentArea.Content = containerGrid;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض الواجهة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ShowExpenses()
    {
        TxtPageTitle.Text = "المصروفات";
        MainContentArea.Content = new Views.ExpensesMainView();
    }

    private void ShowEmployeeAdvances()
    {
        TxtPageTitle.Text = "عهدات الموظفين";
        MainContentArea.Content = new TextBlock { Text = "صفحة عهدات الموظفين - قيد التطوير",
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center, FontSize = 18 };
    }

    public void ShowSalaries()
    {
        TxtPageTitle.Text = "الرواتب والأجور";
        MainContentArea.Content = new PayrollView();
    }

    private void ShowServices()
    {
        TxtPageTitle.Text = "الخدمات";
        MainContentArea.Content = new ServicesView();
    }

    private void ShowAccounting()
    {
        TxtPageTitle.Text = "الحسابات";
        MainContentArea.Content = new Views.Accounting.AccountingMainView();
    }

    private void ShowReports()
    {
        TxtPageTitle.Text = "التقارير";
        MainContentArea.Content = new ReportsView();
    }

    private void ShowArchive()
    {
        TxtPageTitle.Text = "الأرشيف والتحليلات";
        MainContentArea.Content = new ArchiveView();
    }

    private void ShowSettings()
    {
        var companySettingsWindow = new CompanySettingsWindow();
        companySettingsWindow.ShowDialog();
    }

    private void ShowCompanyBranches()
    {
        TxtPageTitle.Text = "إدارة فروع الشركة";
        MainContentArea.Content = new CompanyBranchesView();
    }

    private void ShowResponsiblePersons()
    {
        TxtPageTitle.Text = "إدارة المسؤولين";
        MainContentArea.Content = new ResponsiblePersonsView();
    }

    public void ShowNewInvoice()
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = "فاتورة مبيعات جديدة";

        // إنشاء صفحة الفاتورة مع زر العودة
        var invoiceView = new NewSaleInvoiceView(this);
        MainContentArea.Content = invoiceView;
    }

    public void ShowEditInvoice(Sale saleToEdit)
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = $"تعديل الفاتورة - {saleToEdit.InvoiceNumber}";

        // إنشاء صفحة تعديل الفاتورة
        var editInvoiceView = new EditSaleInvoiceView(this, saleToEdit);
        MainContentArea.Content = editInvoiceView;
    }

    public void ShowSalesReport()
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = "تقرير المبيعات";

        // إنشاء صفحة تقرير المبيعات
        var salesReportView = new SalesReportView(this);
        MainContentArea.Content = salesReportView;
    }

    public void ShowManufacturingReport()
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = "تقرير التصنيع";

        // إنشاء صفحة تقرير التصنيع
        var manufacturingReportView = new ManufacturingReportView(this);
        MainContentArea.Content = manufacturingReportView;
    }

    public void ShowServicesReport()
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = "تقرير خدمات المبيعات";

        // إنشاء صفحة تقرير الخدمات
        var servicesReportView = new ServicesReportView(this);
        MainContentArea.Content = servicesReportView;
    }

    public void ShowCustomersReport()
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = "تقرير العملاء";

        // إنشاء صفحة تقرير العملاء
        var customersReportView = new CustomersReportView(this);
        MainContentArea.Content = customersReportView;
    }

    public void ShowNewPurchaseInvoice()
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = "فاتورة مشتريات جديدة";

        // إنشاء صفحة فاتورة المشتريات مع زر العودة
        var purchaseInvoiceView = new NewPurchaseInvoiceView(this);
        MainContentArea.Content = purchaseInvoiceView;
    }

    public void ShowNewSaleInvoice()
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = "فاتورة مبيعات جديدة";

        // إنشاء صفحة فاتورة المبيعات مع زر العودة
        var saleInvoiceView = new NewSaleInvoiceView(this);
        MainContentArea.Content = saleInvoiceView;
    }

    public void ShowEditSaleInvoice(Sale sale)
    {
        // حفظ المحتوى الحالي للعودة إليه
        _previousContent = MainContentArea.Content as UserControl;

        TxtPageTitle.Text = $"تعديل فاتورة مبيعات - {sale.InvoiceNumber}";

        // إنشاء صفحة تعديل فاتورة المبيعات
        var editSaleView = new EditSaleInvoiceView(this, sale);
        MainContentArea.Content = editSaleView;
    }



    public void GoBack()
    {
        try
        {
            if (_previousContent != null)
            {
                // تحديث المحتوى
                MainContentArea.Content = _previousContent;

                // تحديد العنوان حسب نوع الصفحة
                if (_previousContent is DashboardView)
                    TxtPageTitle.Text = "الرئيسية";
                else if (_previousContent is SalesView)
                    TxtPageTitle.Text = "المبيعات";
                else if (_previousContent is ReportsView)
                    TxtPageTitle.Text = "التقارير";
                else if (_previousContent is ArchiveView)
                    TxtPageTitle.Text = "الأرشيف والتحليلات";
                else
                    TxtPageTitle.Text = "الرئيسية";

                // مسح المحتوى السابق
                _previousContent = null;
            }
            else
            {
                // العودة للصفحة الرئيسية كافتراضي
                ShowDashboard();
            }
        }
        catch (Exception ex)
        {
            // في حالة الخطأ، العودة للصفحة الرئيسية
            System.Diagnostics.Debug.WriteLine($"Error in GoBack: {ex.Message}");
            try
            {
                ShowDashboard();
            }
            catch (Exception ex2)
            {
                System.Diagnostics.Debug.WriteLine($"Error in ShowDashboard: {ex2.Message}");
                // كحل أخير، إنشاء صفحة رئيسية جديدة
                TxtPageTitle.Text = "الرئيسية";
                MainContentArea.Content = new DashboardView();
            }
        }
    }

    #endregion
}