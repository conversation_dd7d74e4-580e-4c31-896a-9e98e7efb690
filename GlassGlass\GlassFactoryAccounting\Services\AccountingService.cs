using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models.Accounting;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة النظام المحاسبي - اليومية الأمريكية
    /// </summary>
    public class AccountingService
    {
        private readonly DatabaseContext _context;
        
        public AccountingService()
        {
            _context = new DatabaseContext();
            InitializeDatabase();
        }
        
        #region Database Initialization
        
        /// <summary>
        /// تهيئة قاعدة البيانات المحاسبية
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();
                
                // إنشاء جدول الحسابات المحسن
                var createAccountsTable = @"
                    CREATE TABLE IF NOT EXISTS Accounts (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        AccountCode TEXT NOT NULL UNIQUE,
                        AccountName TEXT NOT NULL,
                        AccountType INTEGER NOT NULL,
                        IsParent INTEGER NOT NULL DEFAULT 0,
                        ParentAccountId INTEGER,
                        AccountLevel INTEGER NOT NULL DEFAULT 1,
                        Balance DECIMAL NOT NULL DEFAULT 0,
                        DebitBalance DECIMAL NOT NULL DEFAULT 0,
                        CreditBalance DECIMAL NOT NULL DEFAULT 0,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT NOT NULL,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT,
                        Notes TEXT,
                        FOREIGN KEY (ParentAccountId) REFERENCES Accounts(Id)
                    )";
                
                // إنشاء جدول قيود اليومية
                var createJournalEntriesTable = @"
                    CREATE TABLE IF NOT EXISTS JournalEntries (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        EntryNumber TEXT NOT NULL UNIQUE,
                        EntryDate TEXT NOT NULL,
                        Description TEXT NOT NULL,
                        ReferenceNumber TEXT,
                        ReferenceType TEXT,
                        ReferenceId INTEGER,
                        IsPosted INTEGER NOT NULL DEFAULT 0,
                        PostedDate TEXT,
                        PostedBy TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT NOT NULL,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT,
                        Notes TEXT
                    )";
                
                // إنشاء جدول تفاصيل قيود اليومية
                var createJournalEntryDetailsTable = @"
                    CREATE TABLE IF NOT EXISTS JournalEntryDetails (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        JournalEntryId INTEGER NOT NULL,
                        AccountId INTEGER NOT NULL,
                        DebitAmount DECIMAL NOT NULL DEFAULT 0,
                        CreditAmount DECIMAL NOT NULL DEFAULT 0,
                        Description TEXT,
                        LineNumber INTEGER NOT NULL,
                        CreatedDate TEXT NOT NULL,
                        FOREIGN KEY (JournalEntryId) REFERENCES JournalEntries(Id),
                        FOREIGN KEY (AccountId) REFERENCES Accounts(Id)
                    )";
                
                using var command = new SQLiteCommand(createAccountsTable, connection);
                command.ExecuteNonQuery();

                command.CommandText = createJournalEntriesTable;
                command.ExecuteNonQuery();

                command.CommandText = createJournalEntryDetailsTable;
                command.ExecuteNonQuery();

                // تحديث الجداول الموجودة لإضافة العمود الجديد
                UpdateExistingAccountsTable(connection);

                // إنشاء الحسابات الأساسية إذا لم تكن موجودة
                CreateDefaultAccounts();

                System.Diagnostics.Debug.WriteLine("Accounting database initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing accounting database: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث الجداول الموجودة لإضافة العمود الجديد
        /// </summary>
        private void UpdateExistingAccountsTable(SQLiteConnection connection)
        {
            try
            {
                // التحقق من وجود عمود AccountLevel
                var checkColumnSql = "PRAGMA table_info(Accounts)";
                using var checkCommand = new SQLiteCommand(checkColumnSql, connection);
                using var reader = checkCommand.ExecuteReader();

                bool hasAccountLevel = false;
                while (reader.Read())
                {
                    var columnName = reader["name"]?.ToString();
                    if (columnName == "AccountLevel")
                    {
                        hasAccountLevel = true;
                        break;
                    }
                }
                reader.Close();

                // إضافة العمود إذا لم يكن موجود
                if (!hasAccountLevel)
                {
                    var addColumnSql = "ALTER TABLE Accounts ADD COLUMN AccountLevel INTEGER NOT NULL DEFAULT 1";
                    using var addCommand = new SQLiteCommand(addColumnSql, connection);
                    addCommand.ExecuteNonQuery();

                    // تحديث المستويات للحسابات الموجودة
                    UpdateAccountLevels(connection);

                    System.Diagnostics.Debug.WriteLine("AccountLevel column added successfully");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating accounts table: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث مستويات الحسابات الموجودة
        /// </summary>
        private void UpdateAccountLevels(SQLiteConnection connection)
        {
            try
            {
                // تحديث الحسابات الرئيسية (بدون أب) إلى المستوى 1
                var updateRootSql = "UPDATE Accounts SET AccountLevel = 1 WHERE ParentAccountId IS NULL";
                using var updateRootCommand = new SQLiteCommand(updateRootSql, connection);
                updateRootCommand.ExecuteNonQuery();

                // تحديث الحسابات الفرعية بناءً على مستوى الأب
                for (int level = 2; level <= 5; level++) // حتى 5 مستويات
                {
                    var updateChildSql = @"
                        UPDATE Accounts
                        SET AccountLevel = @Level
                        WHERE ParentAccountId IN (
                            SELECT Id FROM Accounts WHERE AccountLevel = @ParentLevel
                        ) AND AccountLevel != @Level";

                    using var updateChildCommand = new SQLiteCommand(updateChildSql, connection);
                    updateChildCommand.Parameters.AddWithValue("@Level", level);
                    updateChildCommand.Parameters.AddWithValue("@ParentLevel", level - 1);

                    var rowsAffected = updateChildCommand.ExecuteNonQuery();
                    if (rowsAffected == 0) break; // لا توجد حسابات أخرى لتحديثها
                }

                System.Diagnostics.Debug.WriteLine("Account levels updated successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating account levels: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء الحسابات الأساسية
        /// </summary>
        public void CreateDefaultAccounts()
        {
            try
            {
                var accounts = GetAllAccounts();
                if (accounts.Count > 0) return; // الحسابات موجودة بالفعل
                
                // الحسابات الرئيسية
                var defaultAccounts = new List<Account>
                {
                    // الأصول
                    new Account { AccountCode = "1000", AccountName = "الأصول", AccountType = AccountType.Asset, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "1100", AccountName = "الأصول المتداولة", AccountType = AccountType.Asset, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "1110", AccountName = "النقدية", AccountType = AccountType.Asset, IsParent = false, CreatedBy = "النظام" },
                    new Account { AccountCode = "1120", AccountName = "العملاء", AccountType = AccountType.Asset, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "1130", AccountName = "المخزون", AccountType = AccountType.Asset, IsParent = true, CreatedBy = "النظام" },
                    
                    // الالتزامات
                    new Account { AccountCode = "2000", AccountName = "الالتزامات", AccountType = AccountType.Liability, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "2100", AccountName = "الالتزامات المتداولة", AccountType = AccountType.Liability, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "2110", AccountName = "الموردين", AccountType = AccountType.Liability, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "2120", AccountName = "الرواتب المستحقة", AccountType = AccountType.Liability, IsParent = false, CreatedBy = "النظام" },
                    
                    // حقوق الملكية
                    new Account { AccountCode = "3000", AccountName = "حقوق الملكية", AccountType = AccountType.Equity, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "3100", AccountName = "رأس المال", AccountType = AccountType.Equity, IsParent = false, CreatedBy = "النظام" },
                    new Account { AccountCode = "3200", AccountName = "الأرباح المحتجزة", AccountType = AccountType.Equity, IsParent = false, CreatedBy = "النظام" },
                    
                    // الإيرادات
                    new Account { AccountCode = "4000", AccountName = "الإيرادات", AccountType = AccountType.Revenue, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "4100", AccountName = "مبيعات الزجاج", AccountType = AccountType.Revenue, IsParent = false, CreatedBy = "النظام" },
                    new Account { AccountCode = "4200", AccountName = "مبيعات الخدمات", AccountType = AccountType.Revenue, IsParent = false, CreatedBy = "النظام" },
                    
                    // المصروفات
                    new Account { AccountCode = "5000", AccountName = "المصروفات", AccountType = AccountType.Expense, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "5100", AccountName = "تكلفة البضاعة المباعة", AccountType = AccountType.Expense, IsParent = false, CreatedBy = "النظام" },
                    new Account { AccountCode = "5200", AccountName = "مصروفات التشغيل", AccountType = AccountType.Expense, IsParent = true, CreatedBy = "النظام" },
                    new Account { AccountCode = "5210", AccountName = "الرواتب والأجور", AccountType = AccountType.Expense, IsParent = false, CreatedBy = "النظام" },
                    new Account { AccountCode = "5220", AccountName = "مصروفات إدارية", AccountType = AccountType.Expense, IsParent = false, CreatedBy = "النظام" }
                };
                
                foreach (var account in defaultAccounts)
                {
                    SaveAccount(account);
                }
                
                System.Diagnostics.Debug.WriteLine("Default accounts created successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating default accounts: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Account Management
        
        /// <summary>
        /// حفظ حساب جديد أو تحديث موجود
        /// </summary>
        public bool SaveAccount(Account account)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();
                
                string sql;
                if (account.Id == 0)
                {
                    sql = @"
                        INSERT INTO Accounts (
                            AccountCode, AccountName, AccountType, IsParent, ParentAccountId, AccountLevel,
                            Balance, DebitBalance, CreditBalance, IsActive, CreatedDate, CreatedBy, Notes
                        ) VALUES (
                            @AccountCode, @AccountName, @AccountType, @IsParent, @ParentAccountId, @AccountLevel,
                            @Balance, @DebitBalance, @CreditBalance, @IsActive, @CreatedDate, @CreatedBy, @Notes
                        )";
                }
                else
                {
                    sql = @"
                        UPDATE Accounts SET
                            AccountCode = @AccountCode, AccountName = @AccountName, AccountType = @AccountType,
                            IsParent = @IsParent, ParentAccountId = @ParentAccountId, AccountLevel = @AccountLevel,
                            Balance = @Balance, DebitBalance = @DebitBalance, CreditBalance = @CreditBalance,
                            IsActive = @IsActive, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy, Notes = @Notes
                        WHERE Id = @Id";
                }
                
                // حساب مستوى الحساب تلقائياً
                if (account.ParentAccountId.HasValue)
                {
                    var parentAccount = GetAccountById(account.ParentAccountId.Value);
                    account.AccountLevel = parentAccount?.AccountLevel + 1 ?? 1;
                }
                else
                {
                    account.AccountLevel = 1;
                }

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                command.Parameters.AddWithValue("@AccountName", account.AccountName);
                command.Parameters.AddWithValue("@AccountType", (int)account.AccountType);
                command.Parameters.AddWithValue("@IsParent", account.IsParent ? 1 : 0);
                command.Parameters.AddWithValue("@ParentAccountId", account.ParentAccountId);
                command.Parameters.AddWithValue("@AccountLevel", account.AccountLevel);
                command.Parameters.AddWithValue("@Balance", account.Balance);
                command.Parameters.AddWithValue("@DebitBalance", account.DebitBalance);
                command.Parameters.AddWithValue("@CreditBalance", account.CreditBalance);
                command.Parameters.AddWithValue("@IsActive", account.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@Notes", account.Notes ?? "");
                command.Parameters.AddWithValue("@CreatedBy", account.CreatedBy);
                
                if (account.Id == 0)
                {
                    command.Parameters.AddWithValue("@CreatedDate", account.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                }
                else
                {
                    command.Parameters.AddWithValue("@Id", account.Id);
                    command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    command.Parameters.AddWithValue("@ModifiedBy", account.ModifiedBy ?? "");
                }
                
                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving account: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع الحسابات
        /// </summary>
        public List<Account> GetAllAccounts()
        {
            var accounts = new List<Account>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var sql = @"
                    SELECT Id, AccountCode, AccountName, AccountType, IsParent, ParentAccountId, AccountLevel,
                           Balance, DebitBalance, CreditBalance, IsActive, CreatedDate, CreatedBy,
                           ModifiedDate, ModifiedBy, Notes
                    FROM Accounts
                    WHERE IsActive = 1
                    ORDER BY AccountCode";

                using var command = new SQLiteCommand(sql, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var account = new Account
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        AccountCode = reader["AccountCode"]?.ToString() ?? "",
                        AccountName = reader["AccountName"]?.ToString() ?? "",
                        AccountType = (AccountType)Convert.ToInt32(reader["AccountType"]),
                        IsParent = Convert.ToInt32(reader["IsParent"]) == 1,
                        ParentAccountId = reader["ParentAccountId"] == DBNull.Value ? null : Convert.ToInt32(reader["ParentAccountId"]),
                        AccountLevel = reader["AccountLevel"] == DBNull.Value ? 1 : Convert.ToInt32(reader["AccountLevel"]),
                        Balance = Convert.ToDecimal(reader["Balance"]),
                        DebitBalance = Convert.ToDecimal(reader["DebitBalance"]),
                        CreditBalance = Convert.ToDecimal(reader["CreditBalance"]),
                        IsActive = Convert.ToInt32(reader["IsActive"]) == 1,
                        CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                        ModifiedBy = reader["ModifiedBy"]?.ToString(),
                        Notes = reader["Notes"]?.ToString()
                    };

                    if (DateTime.TryParse(reader["CreatedDate"]?.ToString(), out DateTime createdDate))
                        account.CreatedDate = createdDate;

                    if (reader["ModifiedDate"] != DBNull.Value &&
                        DateTime.TryParse(reader["ModifiedDate"]?.ToString(), out DateTime modifiedDate))
                        account.ModifiedDate = modifiedDate;

                    accounts.Add(account);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting accounts: {ex.Message}");
            }

            return accounts;
        }

        /// <summary>
        /// الحصول على حساب بالمعرف
        /// </summary>
        public Account? GetAccountById(int id)
        {
            var accounts = GetAllAccounts();
            return accounts.FirstOrDefault(a => a.Id == id);
        }

        /// <summary>
        /// الحصول على حساب بالكود
        /// </summary>
        public Account? GetAccountByCode(string code)
        {
            var accounts = GetAllAccounts();
            return accounts.FirstOrDefault(a => a.AccountCode == code);
        }

        /// <summary>
        /// الحصول على الحسابات الفرعية لحساب معين
        /// </summary>
        public List<Account> GetSubAccounts(int parentAccountId)
        {
            var accounts = GetAllAccounts();
            return accounts.Where(a => a.ParentAccountId == parentAccountId).ToList();
        }

        /// <summary>
        /// الحصول على الحسابات الرئيسية فقط
        /// </summary>
        public List<Account> GetParentAccounts()
        {
            var accounts = GetAllAccounts();
            return accounts.Where(a => a.IsParent).ToList();
        }

        /// <summary>
        /// الحصول على الحسابات التي يمكن استخدامها في القيود (الفرعية فقط)
        /// </summary>
        public List<Account> GetUsableAccounts()
        {
            var accounts = GetAllAccounts();
            return accounts.Where(a => !a.IsParent).ToList();
        }

        /// <summary>
        /// التحقق من إمكانية استخدام الحساب في القيود
        /// </summary>
        public bool CanAccountBeUsedInEntries(int accountId)
        {
            var account = GetAccountById(accountId);
            return account != null && !account.IsParent;
        }

        /// <summary>
        /// الحصول على الحسابات حسب النوع
        /// </summary>
        public List<Account> GetAccountsByType(AccountType accountType)
        {
            var accounts = GetAllAccounts();
            return accounts.Where(a => a.AccountType == accountType).ToList();
        }

        /// <summary>
        /// حذف حساب
        /// </summary>
        public bool DeleteAccount(int accountId)
        {
            try
            {
                // التحقق من عدم وجود حركات على الحساب
                if (HasTransactions(accountId))
                {
                    return false; // لا يمكن حذف حساب له حركات
                }

                // التحقق من عدم وجود حسابات فرعية
                var subAccounts = GetSubAccounts(accountId);
                if (subAccounts.Count > 0)
                {
                    return false; // لا يمكن حذف حساب له حسابات فرعية
                }

                using var connection = _context.GetConnection();
                connection.Open();

                var sql = "UPDATE Accounts SET IsActive = 0 WHERE Id = @Id";
                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", accountId);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting account: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود حركات على الحساب
        /// </summary>
        private bool HasTransactions(int accountId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var sql = "SELECT COUNT(*) FROM JournalEntryDetails WHERE AccountId = @AccountId";
                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@AccountId", accountId);

                return Convert.ToInt32(command.ExecuteScalar()) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking account transactions: {ex.Message}");
                return true; // في حالة الخطأ، نفترض وجود حركات لمنع الحذف
            }
        }

        #endregion

        #region Journal Entry Management

        /// <summary>
        /// إنشاء قيد يومية جديد مع التحقق المحسن
        /// </summary>
        public bool CreateJournalEntry(JournalEntry entry)
        {
            try
            {
                // التحقق من توازن القيد
                if (!entry.IsBalanced)
                {
                    System.Diagnostics.Debug.WriteLine("Journal entry is not balanced");
                    return false;
                }

                // التحقق من صحة الحسابات المستخدمة
                foreach (var detail in entry.Details)
                {
                    if (!CanAccountBeUsedInEntries(detail.AccountId))
                    {
                        var account = GetAccountById(detail.AccountId);
                        System.Diagnostics.Debug.WriteLine($"Cannot use parent account '{account?.AccountName}' in journal entries. Please use sub-accounts only.");
                        return false;
                    }
                }

                using var connection = _context.GetConnection();
                connection.Open();
                using var transaction = connection.BeginTransaction();

                try
                {
                    // إنشاء رقم قيد تلقائي إذا لم يكن موجود
                    if (string.IsNullOrEmpty(entry.EntryNumber))
                    {
                        entry.EntryNumber = GenerateEntryNumber();
                    }

                    // حفظ قيد اليومية
                    var insertEntrySql = @"
                        INSERT INTO JournalEntries (
                            EntryNumber, EntryDate, Description, ReferenceNumber, ReferenceType, ReferenceId,
                            IsPosted, IsActive, CreatedDate, CreatedBy, Notes
                        ) VALUES (
                            @EntryNumber, @EntryDate, @Description, @ReferenceNumber, @ReferenceType, @ReferenceId,
                            @IsPosted, @IsActive, @CreatedDate, @CreatedBy, @Notes
                        )";

                    using var entryCommand = new SQLiteCommand(insertEntrySql, connection, transaction);
                    entryCommand.Parameters.AddWithValue("@EntryNumber", entry.EntryNumber);
                    entryCommand.Parameters.AddWithValue("@EntryDate", entry.EntryDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    entryCommand.Parameters.AddWithValue("@Description", entry.Description);
                    entryCommand.Parameters.AddWithValue("@ReferenceNumber", entry.ReferenceNumber ?? "");
                    entryCommand.Parameters.AddWithValue("@ReferenceType", entry.ReferenceType ?? "");
                    entryCommand.Parameters.AddWithValue("@ReferenceId", entry.ReferenceId);
                    entryCommand.Parameters.AddWithValue("@IsPosted", entry.IsPosted ? 1 : 0);
                    entryCommand.Parameters.AddWithValue("@IsActive", entry.IsActive ? 1 : 0);
                    entryCommand.Parameters.AddWithValue("@CreatedDate", entry.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                    entryCommand.Parameters.AddWithValue("@CreatedBy", entry.CreatedBy);
                    entryCommand.Parameters.AddWithValue("@Notes", entry.Notes ?? "");

                    entryCommand.ExecuteNonQuery();

                    // الحصول على معرف القيد المحفوظ
                    var getIdSql = "SELECT last_insert_rowid()";
                    using var getIdCommand = new SQLiteCommand(getIdSql, connection, transaction);
                    entry.Id = Convert.ToInt32(getIdCommand.ExecuteScalar());

                    // حفظ تفاصيل القيد
                    var insertDetailSql = @"
                        INSERT INTO JournalEntryDetails (
                            JournalEntryId, AccountId, DebitAmount, CreditAmount, Description, LineNumber, CreatedDate
                        ) VALUES (
                            @JournalEntryId, @AccountId, @DebitAmount, @CreditAmount, @Description, @LineNumber, @CreatedDate
                        )";

                    for (int i = 0; i < entry.Details.Count; i++)
                    {
                        var detail = entry.Details[i];
                        detail.LineNumber = i + 1;
                        detail.JournalEntryId = entry.Id;

                        using var detailCommand = new SQLiteCommand(insertDetailSql, connection, transaction);
                        detailCommand.Parameters.AddWithValue("@JournalEntryId", detail.JournalEntryId);
                        detailCommand.Parameters.AddWithValue("@AccountId", detail.AccountId);
                        detailCommand.Parameters.AddWithValue("@DebitAmount", detail.DebitAmount);
                        detailCommand.Parameters.AddWithValue("@CreditAmount", detail.CreditAmount);
                        detailCommand.Parameters.AddWithValue("@Description", detail.Description ?? "");
                        detailCommand.Parameters.AddWithValue("@LineNumber", detail.LineNumber);
                        detailCommand.Parameters.AddWithValue("@CreatedDate", detail.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));

                        detailCommand.ExecuteNonQuery();
                    }

                    // ترحيل القيد إذا كان مطلوب
                    if (entry.IsPosted)
                    {
                        PostJournalEntry(entry.Id, entry.CreatedBy, connection, transaction);
                    }

                    transaction.Commit();
                    return true;
                }
                catch
                {
                    transaction.Rollback();
                    throw;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating journal entry: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// توليد رقم قيد تلقائي
        /// </summary>
        private string GenerateEntryNumber()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var sql = "SELECT COUNT(*) FROM JournalEntries WHERE EntryDate >= @StartOfYear";
                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@StartOfYear", new DateTime(DateTime.Now.Year, 1, 1).ToString("yyyy-MM-dd"));

                var count = Convert.ToInt32(command.ExecuteScalar());
                return $"JE{DateTime.Now:yyyy}{(count + 1):D4}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating entry number: {ex.Message}");
                return $"JE{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// ترحيل قيد اليومية
        /// </summary>
        private void PostJournalEntry(int entryId, string postedBy, SQLiteConnection connection, SQLiteTransaction transaction)
        {
            try
            {
                // تحديث أرصدة الحسابات
                var getDetailsSql = @"
                    SELECT AccountId, DebitAmount, CreditAmount
                    FROM JournalEntryDetails
                    WHERE JournalEntryId = @EntryId";

                using var getDetailsCommand = new SQLiteCommand(getDetailsSql, connection, transaction);
                getDetailsCommand.Parameters.AddWithValue("@EntryId", entryId);
                using var reader = getDetailsCommand.ExecuteReader();

                var accountUpdates = new List<(int AccountId, decimal DebitAmount, decimal CreditAmount)>();
                while (reader.Read())
                {
                    accountUpdates.Add((
                        Convert.ToInt32(reader["AccountId"]),
                        Convert.ToDecimal(reader["DebitAmount"]),
                        Convert.ToDecimal(reader["CreditAmount"])
                    ));
                }
                reader.Close();

                // تحديث أرصدة الحسابات
                foreach (var update in accountUpdates)
                {
                    var updateAccountSql = @"
                        UPDATE Accounts SET
                            DebitBalance = DebitBalance + @DebitAmount,
                            CreditBalance = CreditBalance + @CreditAmount,
                            Balance = Balance + @DebitAmount - @CreditAmount
                        WHERE Id = @AccountId";

                    using var updateCommand = new SQLiteCommand(updateAccountSql, connection, transaction);
                    updateCommand.Parameters.AddWithValue("@AccountId", update.AccountId);
                    updateCommand.Parameters.AddWithValue("@DebitAmount", update.DebitAmount);
                    updateCommand.Parameters.AddWithValue("@CreditAmount", update.CreditAmount);
                    updateCommand.ExecuteNonQuery();
                }

                // تحديث حالة القيد
                var updateEntrySql = @"
                    UPDATE JournalEntries SET
                        IsPosted = 1, PostedDate = @PostedDate, PostedBy = @PostedBy
                    WHERE Id = @Id";

                using var updateEntryCommand = new SQLiteCommand(updateEntrySql, connection, transaction);
                updateEntryCommand.Parameters.AddWithValue("@Id", entryId);
                updateEntryCommand.Parameters.AddWithValue("@PostedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                updateEntryCommand.Parameters.AddWithValue("@PostedBy", postedBy);
                updateEntryCommand.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error posting journal entry: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Reports

        /// <summary>
        /// إنشاء ميزان المراجعة
        /// </summary>
        public TrialBalance GenerateTrialBalance(DateTime asOfDate)
        {
            var trialBalance = new TrialBalance { AsOfDate = asOfDate };

            try
            {
                var accounts = GetAllAccounts();

                foreach (var account in accounts.Where(a => !a.IsParent))
                {
                    var balance = GetAccountBalance(account.Id, asOfDate);

                    if (balance.DebitBalance != 0 || balance.CreditBalance != 0)
                    {
                        trialBalance.Items.Add(new TrialBalanceItem
                        {
                            AccountCode = account.AccountCode,
                            AccountName = account.AccountName,
                            AccountType = account.AccountType,
                            DebitBalance = balance.DebitBalance,
                            CreditBalance = balance.CreditBalance
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating trial balance: {ex.Message}");
            }

            return trialBalance;
        }

        /// <summary>
        /// إنشاء قائمة الدخل
        /// </summary>
        public IncomeStatement GenerateIncomeStatement(DateTime fromDate, DateTime toDate)
        {
            var incomeStatement = new IncomeStatement
            {
                FromDate = fromDate,
                ToDate = toDate
            };

            try
            {
                var accounts = GetAllAccounts();

                // الإيرادات
                var revenueAccounts = accounts.Where(a => a.AccountType == AccountType.Revenue && !a.IsParent);
                foreach (var account in revenueAccounts)
                {
                    var balance = GetAccountBalanceForPeriod(account.Id, fromDate, toDate);
                    if (balance.CreditBalance > 0)
                    {
                        incomeStatement.RevenueItems.Add(new IncomeStatementItem
                        {
                            AccountCode = account.AccountCode,
                            AccountName = account.AccountName,
                            Amount = balance.CreditBalance,
                            AccountType = account.AccountType
                        });
                    }
                }

                // المصروفات
                var expenseAccounts = accounts.Where(a => a.AccountType == AccountType.Expense && !a.IsParent);
                foreach (var account in expenseAccounts)
                {
                    var balance = GetAccountBalanceForPeriod(account.Id, fromDate, toDate);
                    if (balance.DebitBalance > 0)
                    {
                        incomeStatement.ExpenseItems.Add(new IncomeStatementItem
                        {
                            AccountCode = account.AccountCode,
                            AccountName = account.AccountName,
                            Amount = balance.DebitBalance,
                            AccountType = account.AccountType
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating income statement: {ex.Message}");
            }

            return incomeStatement;
        }

        /// <summary>
        /// إنشاء الميزانية العمومية
        /// </summary>
        public BalanceSheet GenerateBalanceSheet(DateTime asOfDate)
        {
            var balanceSheet = new BalanceSheet { AsOfDate = asOfDate };

            try
            {
                var accounts = GetAllAccounts();

                // الأصول
                var assetAccounts = accounts.Where(a => a.AccountType == AccountType.Asset && !a.IsParent);
                foreach (var account in assetAccounts)
                {
                    var balance = GetAccountBalance(account.Id, asOfDate);
                    if (balance.DebitBalance > 0)
                    {
                        balanceSheet.Assets.Add(new BalanceSheetItem
                        {
                            AccountCode = account.AccountCode,
                            AccountName = account.AccountName,
                            Amount = balance.DebitBalance,
                            AccountType = account.AccountType
                        });
                    }
                }

                // الالتزامات
                var liabilityAccounts = accounts.Where(a => a.AccountType == AccountType.Liability && !a.IsParent);
                foreach (var account in liabilityAccounts)
                {
                    var balance = GetAccountBalance(account.Id, asOfDate);
                    if (balance.CreditBalance > 0)
                    {
                        balanceSheet.Liabilities.Add(new BalanceSheetItem
                        {
                            AccountCode = account.AccountCode,
                            AccountName = account.AccountName,
                            Amount = balance.CreditBalance,
                            AccountType = account.AccountType
                        });
                    }
                }

                // حقوق الملكية
                var equityAccounts = accounts.Where(a => a.AccountType == AccountType.Equity && !a.IsParent);
                foreach (var account in equityAccounts)
                {
                    var balance = GetAccountBalance(account.Id, asOfDate);
                    if (balance.CreditBalance > 0)
                    {
                        balanceSheet.Equity.Add(new BalanceSheetItem
                        {
                            AccountCode = account.AccountCode,
                            AccountName = account.AccountName,
                            Amount = balance.CreditBalance,
                            AccountType = account.AccountType
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating balance sheet: {ex.Message}");
            }

            return balanceSheet;
        }

        /// <summary>
        /// الحصول على رصيد حساب في تاريخ معين
        /// </summary>
        public (decimal DebitBalance, decimal CreditBalance) GetAccountBalance(int accountId, DateTime asOfDate)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var sql = @"
                    SELECT
                        COALESCE(SUM(DebitAmount), 0) as TotalDebit,
                        COALESCE(SUM(CreditAmount), 0) as TotalCredit
                    FROM JournalEntryDetails jed
                    INNER JOIN JournalEntries je ON jed.JournalEntryId = je.Id
                    WHERE jed.AccountId = @AccountId
                    AND je.EntryDate <= @AsOfDate
                    AND je.IsPosted = 1
                    AND je.IsActive = 1";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@AccountId", accountId);
                command.Parameters.AddWithValue("@AsOfDate", asOfDate.ToString("yyyy-MM-dd 23:59:59"));

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    return (
                        Convert.ToDecimal(reader["TotalDebit"]),
                        Convert.ToDecimal(reader["TotalCredit"])
                    );
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting account balance: {ex.Message}");
            }

            return (0, 0);
        }

        /// <summary>
        /// الحصول على رصيد حساب لفترة معينة
        /// </summary>
        private (decimal DebitBalance, decimal CreditBalance) GetAccountBalanceForPeriod(int accountId, DateTime fromDate, DateTime toDate)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var sql = @"
                    SELECT
                        COALESCE(SUM(DebitAmount), 0) as TotalDebit,
                        COALESCE(SUM(CreditAmount), 0) as TotalCredit
                    FROM JournalEntryDetails jed
                    INNER JOIN JournalEntries je ON jed.JournalEntryId = je.Id
                    WHERE jed.AccountId = @AccountId
                    AND je.EntryDate >= @FromDate
                    AND je.EntryDate <= @ToDate
                    AND je.IsPosted = 1
                    AND je.IsActive = 1";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@AccountId", accountId);
                command.Parameters.AddWithValue("@FromDate", fromDate.ToString("yyyy-MM-dd 00:00:00"));
                command.Parameters.AddWithValue("@ToDate", toDate.ToString("yyyy-MM-dd 23:59:59"));

                using var reader = command.ExecuteReader();
                if (reader.Read())
                {
                    return (
                        Convert.ToDecimal(reader["TotalDebit"]),
                        Convert.ToDecimal(reader["TotalCredit"])
                    );
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting account balance for period: {ex.Message}");
            }

            return (0, 0);
        }

        #endregion

        #region إدارة الحسابات

        /// <summary>
        /// إنشاء حساب جديد
        /// </summary>
        public bool CreateAccount(Account account)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var sql = @"INSERT INTO Accounts (AccountCode, AccountName, AccountType, IsParent, ParentAccountId, Notes, IsActive, CreatedDate, ModifiedDate)
                           VALUES (@AccountCode, @AccountName, @AccountType, @IsParent, @ParentAccountId, @Notes, @IsActive, @CreatedDate, @ModifiedDate)";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                command.Parameters.AddWithValue("@AccountName", account.AccountName);
                command.Parameters.AddWithValue("@AccountType", account.AccountType.ToString());
                command.Parameters.AddWithValue("@IsParent", account.IsParent);
                command.Parameters.AddWithValue("@ParentAccountId", account.ParentAccountId);
                command.Parameters.AddWithValue("@Notes", account.Notes);
                command.Parameters.AddWithValue("@IsActive", account.IsActive);
                command.Parameters.AddWithValue("@CreatedDate", account.CreatedDate);
                command.Parameters.AddWithValue("@ModifiedDate", account.ModifiedDate);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating account: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث حساب موجود
        /// </summary>
        public bool UpdateAccount(Account account)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var sql = @"UPDATE Accounts SET
                           AccountCode = @AccountCode,
                           AccountName = @AccountName,
                           AccountType = @AccountType,
                           IsParent = @IsParent,
                           ParentAccountId = @ParentAccountId,
                           Notes = @Notes,
                           IsActive = @IsActive,
                           ModifiedDate = @ModifiedDate
                           WHERE Id = @Id";

                using var command = new SQLiteCommand(sql, connection);
                command.Parameters.AddWithValue("@Id", account.Id);
                command.Parameters.AddWithValue("@AccountCode", account.AccountCode);
                command.Parameters.AddWithValue("@AccountName", account.AccountName);
                command.Parameters.AddWithValue("@AccountType", account.AccountType.ToString());
                command.Parameters.AddWithValue("@IsParent", account.IsParent);
                command.Parameters.AddWithValue("@ParentAccountId", account.ParentAccountId);
                command.Parameters.AddWithValue("@Notes", account.Notes);
                command.Parameters.AddWithValue("@IsActive", account.IsActive);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating account: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على رصيد حساب (مع تاريخ افتراضي)
        /// </summary>
        public decimal GetAccountBalance(int accountId)
        {
            var balance = GetAccountBalance(accountId, DateTime.Now);
            return balance.DebitBalance - balance.CreditBalance;
        }

        #endregion
    }
}
