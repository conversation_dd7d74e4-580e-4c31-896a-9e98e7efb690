// Glass Factory Accounting System - Express.js Server
// نظام حسابات مصنع الزجاج - خادم Node.js

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Sample data - قاعدة بيانات تجريبية
let customers = [
    {
        id: 1,
        customerCode: 'CUST-001',
        customerName: 'شركة الزجاج المتطور',
        phone: '**********',
        email: '<EMAIL>',
        address: 'الرياض، المملكة العربية السعودية',
        creditLimit: 50000,
        currentBalance: 15000,
        paymentTermDays: 30,
        customerType: 'شركة',
        isActive: true,
        createdDate: '2024-01-15'
    },
    {
        id: 2,
        customerCode: 'CUST-002',
        customerName: 'مؤسسة البناء الحديث',
        phone: '0507654321',
        email: '<EMAIL>',
        address: 'جدة، المملكة العربية السعودية',
        creditLimit: 75000,
        currentBalance: 8500,
        paymentTermDays: 45,
        customerType: 'مؤسسة',
        isActive: true,
        createdDate: '2024-02-10'
    },
    {
        id: 3,
        customerCode: 'CUST-003',
        customerName: 'شركة المباني الذكية',
        phone: '0551234567',
        email: '<EMAIL>',
        address: 'الدمام، المملكة العربية السعودية',
        creditLimit: 30000,
        currentBalance: 5200,
        paymentTermDays: 30,
        customerType: 'شركة',
        isActive: true,
        createdDate: '2024-03-05'
    }
];

let invoices = [
    {
        id: 1,
        invoiceNumber: 'INV-001',
        customerId: 1,
        customerName: 'شركة الزجاج المتطور',
        invoiceDate: '2024-12-06',
        dueDate: '2024-12-20',
        totalAmount: 5000,
        paidAmount: 5000,
        remainingAmount: 0,
        status: 'مدفوعة',
        items: [
            { productName: 'زجاج شفاف 6مم', quantity: 20, unitPrice: 150, total: 3000 },
            { productName: 'زجاج مقسى 8مم', quantity: 8, unitPrice: 250, total: 2000 }
        ],
        notes: 'فاتورة مبيعات زجاج للمشروع الجديد'
    },
    {
        id: 2,
        invoiceNumber: 'INV-002',
        customerId: 2,
        customerName: 'مؤسسة البناء الحديث',
        invoiceDate: '2024-12-05',
        dueDate: '2024-12-19',
        totalAmount: 3200,
        paidAmount: 1200,
        remainingAmount: 2000,
        status: 'معلقة',
        items: [
            { productName: 'زجاج عاكس 10مم', quantity: 12, unitPrice: 200, total: 2400 },
            { productName: 'زجاج ملون 6مم', quantity: 8, unitPrice: 100, total: 800 }
        ],
        notes: 'فاتورة مبيعات للمبنى التجاري'
    },
    {
        id: 3,
        invoiceNumber: 'INV-003',
        customerId: 3,
        customerName: 'شركة المباني الذكية',
        invoiceDate: '2024-12-04',
        dueDate: '2024-12-18',
        totalAmount: 4500,
        paidAmount: 4500,
        remainingAmount: 0,
        status: 'مدفوعة',
        items: [
            { productName: 'زجاج ذكي 12مم', quantity: 15, unitPrice: 300, total: 4500 }
        ],
        notes: 'فاتورة زجاج ذكي للمشروع السكني'
    }
];

let payments = [
    {
        id: 1,
        paymentNumber: 'PAY-001',
        customerId: 1,
        customerName: 'شركة الزجاج المتطور',
        invoiceNumber: 'INV-001',
        paymentDate: '2024-12-06',
        amount: 5000,
        paymentMethod: 'نقدي',
        referenceNumber: 'CASH-001',
        notes: 'دفعة نقدية كاملة للفاتورة',
        status: 'مؤكد'
    },
    {
        id: 2,
        paymentNumber: 'PAY-002',
        customerId: 2,
        customerName: 'مؤسسة البناء الحديث',
        invoiceNumber: 'INV-002',
        paymentDate: '2024-12-05',
        amount: 1200,
        paymentMethod: 'تحويل بنكي',
        referenceNumber: 'TRF-12345',
        notes: 'دفعة جزئية - تحويل بنكي',
        status: 'مؤكد'
    },
    {
        id: 3,
        paymentNumber: 'PAY-003',
        customerId: 3,
        customerName: 'شركة المباني الذكية',
        invoiceNumber: 'INV-003',
        paymentDate: '2024-12-04',
        amount: 4500,
        paymentMethod: 'شيك',
        referenceNumber: 'CHK-789',
        notes: 'دفعة بشيك مصرفي',
        status: 'مؤكد'
    }
];

let products = [
    {
        id: 1,
        productCode: 'GLASS-001',
        productName: 'زجاج شفاف 6مم',
        category: 'زجاج شفاف',
        unit: 'متر مربع',
        costPrice: 100,
        salePrice: 150,
        currentStock: 200,
        minimumStock: 50,
        isActive: true
    },
    {
        id: 2,
        productCode: 'GLASS-002',
        productName: 'زجاج مقسى 8مم',
        category: 'زجاج مقسى',
        unit: 'متر مربع',
        costPrice: 180,
        salePrice: 250,
        currentStock: 100,
        minimumStock: 30,
        isActive: true
    },
    {
        id: 3,
        productCode: 'GLASS-003',
        productName: 'زجاج عاكس 10مم',
        category: 'زجاج عاكس',
        unit: 'متر مربع',
        costPrice: 150,
        salePrice: 200,
        currentStock: 80,
        minimumStock: 25,
        isActive: true
    }
];

// API Routes

// Health Check
app.get('/health', (req, res) => {
    res.json({
        status: 'healthy',
        message: 'Glass Factory Accounting System is running',
        timestamp: new Date().toISOString(),
        version: '2.0.0'
    });
});

// Customers API
app.get('/api/customers', (req, res) => {
    res.json({
        success: true,
        data: customers,
        total: customers.length,
        message: 'تم جلب قائمة العملاء بنجاح'
    });
});

app.get('/api/customers/:id', (req, res) => {
    const customer = customers.find(c => c.id === parseInt(req.params.id));
    if (customer) {
        res.json({ success: true, data: customer });
    } else {
        res.status(404).json({ success: false, message: 'العميل غير موجود' });
    }
});

app.post('/api/customers', (req, res) => {
    const newCustomer = {
        id: customers.length + 1,
        customerCode: `CUST-${String(customers.length + 1).padStart(3, '0')}`,
        ...req.body,
        isActive: true,
        createdDate: new Date().toISOString().split('T')[0]
    };
    customers.push(newCustomer);
    res.json({
        success: true,
        data: newCustomer,
        message: 'تم إضافة العميل بنجاح'
    });
});

// Invoices API
app.get('/api/invoices', (req, res) => {
    res.json({
        success: true,
        data: invoices,
        total: invoices.length,
        message: 'تم جلب قائمة الفواتير بنجاح'
    });
});

app.get('/api/invoices/:id', (req, res) => {
    const invoice = invoices.find(i => i.id === parseInt(req.params.id));
    if (invoice) {
        res.json({ success: true, data: invoice });
    } else {
        res.status(404).json({ success: false, message: 'الفاتورة غير موجودة' });
    }
});

app.post('/api/invoices', (req, res) => {
    const newInvoice = {
        id: invoices.length + 1,
        invoiceNumber: `INV-${String(invoices.length + 1).padStart(3, '0')}`,
        invoiceDate: new Date().toISOString().split('T')[0],
        status: 'معلقة',
        paidAmount: 0,
        ...req.body
    };
    newInvoice.remainingAmount = newInvoice.totalAmount - newInvoice.paidAmount;
    invoices.push(newInvoice);
    res.json({
        success: true,
        data: newInvoice,
        message: 'تم إنشاء الفاتورة بنجاح'
    });
});

// Payments API
app.get('/api/payments', (req, res) => {
    res.json({
        success: true,
        data: payments,
        total: payments.length,
        message: 'تم جلب قائمة المدفوعات بنجاح'
    });
});

app.post('/api/payments', (req, res) => {
    const newPayment = {
        id: payments.length + 1,
        paymentNumber: `PAY-${String(payments.length + 1).padStart(3, '0')}`,
        paymentDate: new Date().toISOString().split('T')[0],
        status: 'مؤكد',
        ...req.body
    };
    payments.push(newPayment);
    
    // Update invoice payment
    const invoice = invoices.find(i => i.invoiceNumber === newPayment.invoiceNumber);
    if (invoice) {
        invoice.paidAmount += newPayment.amount;
        invoice.remainingAmount = invoice.totalAmount - invoice.paidAmount;
        if (invoice.remainingAmount <= 0) {
            invoice.status = 'مدفوعة';
        }
    }
    
    res.json({
        success: true,
        data: newPayment,
        message: 'تم تسجيل الدفعة بنجاح'
    });
});

// Products API
app.get('/api/products', (req, res) => {
    res.json({
        success: true,
        data: products,
        total: products.length,
        message: 'تم جلب قائمة المنتجات بنجاح'
    });
});

// Reports API
app.get('/api/reports/sales-summary', (req, res) => {
    const totalSales = invoices.reduce((sum, inv) => sum + inv.totalAmount, 0);
    const totalPaid = invoices.reduce((sum, inv) => sum + inv.paidAmount, 0);
    const totalPending = invoices.reduce((sum, inv) => sum + inv.remainingAmount, 0);
    const paidInvoices = invoices.filter(inv => inv.status === 'مدفوعة').length;
    const pendingInvoices = invoices.filter(inv => inv.status === 'معلقة').length;
    
    res.json({
        success: true,
        data: {
            totalInvoices: invoices.length,
            totalCustomers: customers.length,
            totalSales: totalSales,
            totalPaid: totalPaid,
            totalPending: totalPending,
            paidInvoices: paidInvoices,
            pendingInvoices: pendingInvoices,
            averageInvoiceValue: Math.round(totalSales / invoices.length),
            paymentRate: Math.round((totalPaid / totalSales) * 100)
        },
        message: 'تم جلب ملخص المبيعات بنجاح'
    });
});

// Serve React app
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Glass Factory Accounting System is running on port ${PORT}`);
    console.log(`🌐 Access the application at: http://localhost:${PORT}`);
    console.log(`📋 API Health Check: http://localhost:${PORT}/health`);
    console.log(`👥 Customers API: http://localhost:${PORT}/api/customers`);
    console.log(`📄 Invoices API: http://localhost:${PORT}/api/invoices`);
    console.log(`💳 Payments API: http://localhost:${PORT}/api/payments`);
});

module.exports = app;
