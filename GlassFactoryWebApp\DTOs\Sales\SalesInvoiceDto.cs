using System.ComponentModel.DataAnnotations;

namespace GlassFactoryWebApp.DTOs
{
    /// <summary>
    /// DTO فاتورة المبيعات
    /// </summary>
    public class SalesInvoiceDto
    {
        public int Id { get; set; }
        public string InvoiceNumber { get; set; } = string.Empty;
        public DateTime InvoiceDate { get; set; }
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerAddress { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public decimal SubTotal { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal PaidAmount { get; set; }
        public decimal RemainingAmount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public string InvoiceStatus { get; set; } = string.Empty;
        public string SalesmanName { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? InvoiceNotes { get; set; }
        public bool IsPrinted { get; set; }
        public bool IsPosted { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;

        public List<SalesInvoiceItemDto> Items { get; set; } = new();
        public CustomerDto? Customer { get; set; }
    }

    /// <summary>
    /// DTO إنشاء فاتورة مبيعات
    /// </summary>
    public class CreateSalesInvoiceDto
    {
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Required(ErrorMessage = "العميل مطلوب")]
        public int CustomerId { get; set; }

        public string CustomerName { get; set; } = string.Empty;
        public string CustomerAddress { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;

        [Range(0, double.MaxValue, ErrorMessage = "نسبة الخصم يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "نسبة الضريبة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal TaxPercentage { get; set; } = 0;

        public string PaymentMethod { get; set; } = "نقدي";
        public string SalesmanName { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? InvoiceNotes { get; set; }

        [Required(ErrorMessage = "يجب إضافة عنصر واحد على الأقل")]
        public List<CreateSalesInvoiceItemDto> Items { get; set; } = new();
    }

    /// <summary>
    /// DTO تحديث فاتورة مبيعات
    /// </summary>
    public class UpdateSalesInvoiceDto
    {
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "تاريخ الفاتورة مطلوب")]
        public DateTime InvoiceDate { get; set; }

        [Required(ErrorMessage = "العميل مطلوب")]
        public int CustomerId { get; set; }

        public string CustomerName { get; set; } = string.Empty;
        public string CustomerAddress { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;

        [Range(0, double.MaxValue, ErrorMessage = "نسبة الخصم يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Range(0, double.MaxValue, ErrorMessage = "نسبة الضريبة يجب أن تكون أكبر من أو تساوي صفر")]
        public decimal TaxPercentage { get; set; } = 0;

        public string PaymentMethod { get; set; } = "نقدي";
        public string SalesmanName { get; set; } = string.Empty;
        public DateTime? DueDate { get; set; }
        public string? ReferenceNumber { get; set; }
        public string? InvoiceNotes { get; set; }

        public List<UpdateSalesInvoiceItemDto> Items { get; set; } = new();
    }

    /// <summary>
    /// DTO عنصر فاتورة المبيعات
    /// </summary>
    public class SalesInvoiceItemDto
    {
        public int Id { get; set; }
        public int SalesInvoiceId { get; set; }
        public int ProductId { get; set; }
        public string ProductCode { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ProductDescription { get; set; } = string.Empty;
        public string Unit { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public decimal DiscountPercentage { get; set; }
        public decimal DiscountAmount { get; set; }
        public decimal TaxPercentage { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal TotalPrice { get; set; }
        public int LineNumber { get; set; }
        public string? ItemNotes { get; set; }

        // Glass-specific properties
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public decimal? Thickness { get; set; }
        public string? GlassType { get; set; }
        public string? GlassColor { get; set; }
        public string? FinishingType { get; set; }
    }

    /// <summary>
    /// DTO إنشاء عنصر فاتورة مبيعات
    /// </summary>
    public class CreateSalesInvoiceItemDto
    {
        [Required(ErrorMessage = "المنتج مطلوب")]
        public int ProductId { get; set; }

        public string ProductCode { get; set; } = string.Empty;
        public string ProductName { get; set; } = string.Empty;
        public string ProductDescription { get; set; } = string.Empty;
        public string Unit { get; set; } = "قطعة";

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercentage { get; set; } = 0;

        [Range(0, 100, ErrorMessage = "نسبة الضريبة يجب أن تكون بين 0 و 100")]
        public decimal TaxPercentage { get; set; } = 0;

        public string? ItemNotes { get; set; }

        // Glass-specific properties
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public decimal? Thickness { get; set; }
        public string? GlassType { get; set; }
        public string? GlassColor { get; set; }
        public string? FinishingType { get; set; }
    }

    /// <summary>
    /// DTO تحديث عنصر فاتورة مبيعات
    /// </summary>
    public class UpdateSalesInvoiceItemDto : CreateSalesInvoiceItemDto
    {
        public int Id { get; set; }
    }

    /// <summary>
    /// DTO إحصائيات المبيعات
    /// </summary>
    public class SalesStatisticsDto
    {
        public decimal TotalSales { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal TotalRemaining { get; set; }
        public int TotalInvoices { get; set; }
        public int PaidInvoices { get; set; }
        public int PendingInvoices { get; set; }
        public decimal AverageInvoiceValue { get; set; }
        public decimal TotalProfit { get; set; }
        public decimal ProfitMargin { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
    }

    /// <summary>
    /// DTO بيانات الرسم البياني للمبيعات
    /// </summary>
    public class SalesChartDataDto
    {
        public string Label { get; set; } = string.Empty;
        public decimal Value { get; set; }
        public DateTime Date { get; set; }
        public string Color { get; set; } = string.Empty;
    }

    /// <summary>
    /// DTO أفضل العملاء
    /// </summary>
    public class TopCustomerDto
    {
        public int CustomerId { get; set; }
        public string CustomerName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public int InvoiceCount { get; set; }
        public decimal AverageInvoiceValue { get; set; }
    }

    /// <summary>
    /// DTO أفضل المنتجات
    /// </summary>
    public class TopProductDto
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public decimal TotalSales { get; set; }
        public decimal TotalQuantity { get; set; }
        public int InvoiceCount { get; set; }
    }
}
