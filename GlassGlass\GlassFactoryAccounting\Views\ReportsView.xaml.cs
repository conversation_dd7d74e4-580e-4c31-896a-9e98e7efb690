using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة التقارير الرئيسية
/// </summary>
public partial class ReportsView : UserControl
{
    private readonly ArchiveService _archiveService;

    public ReportsView()
    {
        InitializeComponent();
        _archiveService = new ArchiveService();
        LoadReportsStatistics();
    }

    private void LoadReportsStatistics()
    {
        try
        {
            Task.Run(async () =>
            {
                try
                {
                    var statistics = await _archiveService.GetArchiveStatisticsAsync();
                    
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        TxtTotalSales.Text = $"{statistics.TotalSalesAmount:N2} ج.م";
                        TxtTotalInvoices.Text = statistics.TotalSales.ToString();
                        TxtTotalCustomers.Text = statistics.TotalCustomers.ToString();
                        
                        if (statistics.LastSaleDate != DateTime.MinValue)
                        {
                            TxtLastInvoiceDate.Text = statistics.LastSaleDate.ToString("yyyy/MM/dd");
                        }
                        else
                        {
                            TxtLastInvoiceDate.Text = "لا توجد فواتير";
                        }
                    });
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل إحصائيات التقارير: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل إحصائيات التقارير: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefreshReports_Click(object sender, RoutedEventArgs e)
    {
        LoadReportsStatistics();
        MessageBox.Show("تم تحديث إحصائيات التقارير", "تحديث", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnSalesReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // البحث عن النافذة الرئيسية وفتح تقرير المبيعات
            var mainWindow = Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.ShowSalesReport();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تقرير المبيعات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnManufacturingReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var mainWindow = Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.ShowManufacturingReport();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تقرير التصنيع: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnServicesReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var mainWindow = Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.ShowServicesReport();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تقرير الخدمات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnCustomersReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var mainWindow = Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.ShowCustomersReport();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تقرير العملاء: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnCustomReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("ميزة التقارير المخصصة ستكون متاحة قريباً", "قريباً", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnAddNewReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = MessageBox.Show("هل تريد إضافة نوع تقرير جديد؟\nيمكنك التواصل مع فريق التطوير لإضافة تقارير مخصصة.", 
                "إضافة تقرير جديد", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                MessageBox.Show("يرجى التواصل مع فريق التطوير لإضافة أنواع تقارير جديدة حسب احتياجاتك", 
                    "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
