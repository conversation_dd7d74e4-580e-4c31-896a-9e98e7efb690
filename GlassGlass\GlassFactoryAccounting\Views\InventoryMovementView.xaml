<UserControl x:Class="GlassFactoryAccounting.Views.InventoryMovementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <Style x:Key="ReceiveButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <Style x:Key="IssueButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#FF9800"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect" Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Auto"
                  PanningMode="VerticalOnly"
                  Background="#F5F5F5">
        <Grid Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="🔄" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="حركة المخزون" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                    <TextBlock Text="استلام وتسليم الأصناف بنظام المتوسط المتحرك" FontSize="14" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- اختيار نوع الحركة -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <StackPanel>
                <TextBlock Text="نوع الحركة:" FontSize="16" FontWeight="Bold" Margin="0,0,0,10"/>
                <StackPanel Orientation="Horizontal">
                    <Button x:Name="BtnReceive" Content="📥 استلام" 
                            Style="{StaticResource ReceiveButtonStyle}"
                            Margin="0,0,15,0" Click="BtnReceive_Click"/>
                    <Button x:Name="BtnIssue" Content="📤 تسليم" 
                            Style="{StaticResource IssueButtonStyle}"
                            Click="BtnIssue_Click"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- نموذج الحركة -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}" x:Name="MovementFormPanel" Visibility="Collapsed">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <!-- معلومات الحركة الأساسية -->
                <Grid Grid.Row="0" Margin="12,12,12,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                    </Grid.ColumnDefinitions>

                    <!-- التاريخ -->
                    <Border Grid.Column="0" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="التاريخ:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <DatePicker x:Name="DpDate" Height="45" VerticalContentAlignment="Center"
                                        FontSize="15" SelectedDate="{x:Static sys:DateTime.Now}"
                                        xmlns:sys="clr-namespace:System;assembly=mscorlib"
                                        HorizontalContentAlignment="Center"
                                        BorderBrush="#3498DB" BorderThickness="3"/>
                        </StackPanel>
                    </Border>

                    <!-- رقم الأمر -->
                    <Border Grid.Column="1" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="رقم الأمر:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtOrderNumber" Height="45"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="15" Padding="12,8" IsReadOnly="True"
                                     Background="#F8F9FA" BorderBrush="#BDC3C7" BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- رقم الفاتورة -->
                    <Border Grid.Column="2" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtInvoiceNumber" Height="45"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="15" Padding="12,8"
                                     BorderBrush="#3498DB" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- المخزن -->
                    <Border Grid.Column="3" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="المخزن:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <ComboBox x:Name="CmbWarehouse" Height="45"
                                      VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                      FontSize="15" Padding="12,8" DisplayMemberPath="Name" SelectedValuePath="Id"
                                      SelectionChanged="CmbWarehouse_SelectionChanged"
                                      BorderBrush="#3498DB" BorderThickness="3"
                                      Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- العميل/المورد -->
                    <Border Grid.Column="4" Margin="6" Padding="12" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}" x:Name="CustomerSupplierPanel">
                        <StackPanel>
                            <TextBlock x:Name="LblCustomerSupplier" Text="العميل/المورد:" FontWeight="Bold" FontSize="14"
                                       Margin="0,0,0,10" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <ComboBox x:Name="CmbCustomerSupplier" Height="45"
                                      VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                      FontSize="15" Padding="12,8" DisplayMemberPath="Name" SelectedValuePath="Id"
                                      BorderBrush="#3498DB" BorderThickness="3"
                                      Background="White"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- تفاصيل الصنف -->
                <Grid Grid.Row="1" Margin="12,0,12,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                        <ColumnDefinition Width="1*" MinWidth="200"/>
                    </Grid.ColumnDefinitions>

                    <!-- الصنف -->
                    <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="الصنف:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <ComboBox x:Name="CmbItem" Height="50"
                                      VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                      FontSize="16" Padding="15,10" DisplayMemberPath="Name" SelectedValuePath="Id"
                                      SelectionChanged="CmbItem_SelectionChanged"
                                      BorderBrush="#E67E22" BorderThickness="3"
                                      Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- العدد -->
                    <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock x:Name="LblUnits" Text="العدد:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtUnits" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" TextChanged="TxtUnits_TextChanged"
                                     BorderBrush="#27AE60" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- الكمية -->
                    <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock x:Name="LblQuantity" Text="الكمية:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtQuantity" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" TextChanged="TxtQuantity_TextChanged"
                                     BorderBrush="#27AE60" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>

                    <!-- السعر -->
                    <Border Grid.Column="3" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="السعر:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtUnitPrice" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" TextChanged="TxtUnitPrice_TextChanged"
                                     BorderBrush="#F39C12" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- القيمة الإجمالية والملاحظات -->
                <Grid Grid.Row="2" Margin="12,0,12,25">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="1*" MinWidth="250"/>
                        <ColumnDefinition Width="2*" MinWidth="400"/>
                    </Grid.ColumnDefinitions>

                    <!-- إجمالي القيمة -->
                    <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="إجمالي القيمة:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtTotalValue" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" IsReadOnly="True"
                                     Background="#F8F9FA" BorderBrush="#BDC3C7" BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- عدد الصناديق -->
                    <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="عدد الصناديق:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtBoxCount" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10" IsReadOnly="True"
                                     Background="#F8F9FA" BorderBrush="#BDC3C7" BorderThickness="2"/>
                        </StackPanel>
                    </Border>

                    <!-- الملاحظات -->
                    <Border Grid.Column="2" Margin="8" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="الملاحظات:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtNotes" Height="50"
                                     VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#9B59B6" BorderThickness="3"
                                     Background="White"/>
                        </StackPanel>
                    </Border>
                </Grid>

                <!-- الأزرار -->
                <StackPanel Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button x:Name="BtnSaveMovement" Content="💾 حفظ الحركة" 
                            Style="{StaticResource SuccessButtonStyle}"
                            Click="BtnSaveMovement_Click"/>
                    <Button x:Name="BtnClearForm" Content="🗑️ مسح النموذج" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="10,0,0,0" Click="BtnClearForm_Click"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- قائمة الحركات الأخيرة -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 الحركات الأخيرة" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                    <Button x:Name="BtnRefresh" Content="🔄 تحديث" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="20,0,0,0" Click="BtnRefresh_Click"/>
                </StackPanel>

                <DataGrid Grid.Row="1" x:Name="MovementsDataGrid" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="35">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="التاريخ" Binding="{Binding Date, StringFormat=dd/MM/yyyy}" Width="60"/>
                        <DataGridTextColumn Header="رقم الأمر" Binding="{Binding OrderNumber}" Width="70"/>
                        <DataGridTextColumn Header="رقم الفاتورة" Binding="{Binding InvoiceNumber}" Width="70"/>
                        <DataGridTextColumn Header="نوع الحركة" Binding="{Binding MovementType}" Width="60"/>
                        <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="70"/>
                        <DataGridTextColumn Header="العميل/المورد" Width="90">
                            <DataGridTextColumn.Binding>
                                <MultiBinding StringFormat="{}{0}{1}">
                                    <Binding Path="CustomerName"/>
                                    <Binding Path="SupplierName"/>
                                </MultiBinding>
                            </DataGridTextColumn.Binding>
                        </DataGridTextColumn>
                        <DataGridTextColumn Header="الصنف" Binding="{Binding ItemName}" Width="90"/>
                        <DataGridTextColumn Header="وحدة القياس" Binding="{Binding UnitOfMeasure}" Width="70"/>
                        <DataGridTextColumn Header="محتوى الصندوق" Binding="{Binding BoxContent, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="الطول (مم)" Binding="{Binding Length}" Width="70"/>
                        <DataGridTextColumn Header="العرض (مم)" Binding="{Binding Width}" Width="70"/>
                        <DataGridTextColumn Header="المتر المربع" Binding="{Binding Area, StringFormat=F4}" Width="80"/>
                        <DataGridTextColumn Header="العدد" Binding="{Binding Units, StringFormat=F2}" Width="60"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Quantity, StringFormat=F2}" Width="60"/>
                        <DataGridTextColumn Header="عدد الصناديق" Binding="{Binding BoxCount, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, StringFormat=F2}" Width="60"/>
                        <DataGridTextColumn Header="إجمالي القيمة" Binding="{Binding TotalValue, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="الملاحظات" Binding="{Binding Notes}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        </Grid>
    </ScrollViewer>
</UserControl>
