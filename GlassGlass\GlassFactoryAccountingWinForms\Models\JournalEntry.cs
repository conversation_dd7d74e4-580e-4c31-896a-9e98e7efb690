using System;
using System.Collections.Generic;
using System.Linq;

namespace GlassFactoryAccountingWinForms.Models
{
    /// <summary>
    /// نموذج قيد اليومية
    /// </summary>
    public class JournalEntry
    {
        public int Id { get; set; }
        public string EntryNumber { get; set; } = "";
        public DateTime EntryDate { get; set; } = DateTime.Now;
        public string Description { get; set; } = "";
        public decimal TotalAmount { get; set; }
        public bool IsPosted { get; set; }
        public DateTime? PostedDate { get; set; }
        public string PostedBy { get; set; } = "";
        public string CreatedBy { get; set; } = "";
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string Reference { get; set; } = "";
        public string Notes { get; set; } = "";

        public List<JournalEntryDetail> Details { get; set; } = new List<JournalEntryDetail>();

        /// <summary>
        /// التحقق من توازن القيد
        /// </summary>
        public bool IsBalanced
        {
            get
            {
                var totalDebit = Details.Sum(d => d.DebitAmount);
                var totalCredit = Details.Sum(d => d.CreditAmount);
                return Math.Abs(totalDebit - totalCredit) < 0.01m;
            }
        }

        /// <summary>
        /// إجمالي المدين
        /// </summary>
        public decimal TotalDebit => Details.Sum(d => d.DebitAmount);

        /// <summary>
        /// إجمالي الدائن
        /// </summary>
        public decimal TotalCredit => Details.Sum(d => d.CreditAmount);
    }

    /// <summary>
    /// تفاصيل قيد اليومية
    /// </summary>
    public class JournalEntryDetail
    {
        public int Id { get; set; }
        public int JournalEntryId { get; set; }
        public int AccountId { get; set; }
        public decimal DebitAmount { get; set; }
        public decimal CreditAmount { get; set; }
        public string Description { get; set; } = "";
        public int LineNumber { get; set; }
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // Navigation properties
        public JournalEntry JournalEntry { get; set; }
        public Account Account { get; set; }
    }
}
