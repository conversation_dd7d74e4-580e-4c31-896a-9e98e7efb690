{"name": "glass-factory-client", "version": "2.0.0", "description": "Glass Factory Accounting System - Web Client", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.11.47", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.7.4", "web-vitals": "^2.1.4", "react-router-dom": "^6.8.1", "axios": "^1.3.4", "react-query": "^3.39.3", "@mui/material": "^5.11.10", "@mui/icons-material": "^5.11.9", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/x-data-grid": "^5.17.26", "@mui/x-date-pickers": "^5.0.20", "@mui/lab": "^5.0.0-alpha.120", "react-hook-form": "^7.43.5", "@hookform/resolvers": "^2.9.11", "yup": "^1.0.2", "react-toastify": "^9.1.1", "recharts": "^2.5.0", "jspdf": "^2.5.1", "xlsx": "^0.18.5", "date-fns": "^2.29.3", "lodash": "^4.17.21", "@types/lodash": "^4.14.191", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.3", "react-select": "^5.7.0", "react-number-format": "^5.1.3", "react-datepicker": "^4.10.0", "@types/react-datepicker": "^4.10.0", "formik": "^2.2.9", "react-pdf": "^6.2.2", "@react-pdf/renderer": "^3.1.9", "html2canvas": "^1.4.1", "react-to-print": "^2.14.12", "react-helmet-async": "^1.3.0", "i18next": "^22.4.13", "react-i18next": "^12.2.0", "i18next-browser-languagedetector": "^7.0.1", "react-virtualized": "^9.22.5", "@types/react-virtualized": "^9.21.21"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write src/**/*.{ts,tsx,css,scss,json}", "analyze": "npm run build && npx bundle-analyzer build/static/js/*.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "eslint": "^8.36.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.8.4", "sass": "^1.58.3", "bundle-analyzer": "^0.1.0"}, "homepage": "."}