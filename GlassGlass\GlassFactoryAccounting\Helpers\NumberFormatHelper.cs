using System;
using System.Globalization;
using System.Text.RegularExpressions;

namespace GlassFactoryAccounting.Helpers
{
    /// <summary>
    /// مساعد لتنسيق الأرقام وتحويلها للإنجليزية
    /// </summary>
    public static class NumberFormatHelper
    {
        /// <summary>
        /// تحويل الأرقام العربية إلى إنجليزية
        /// </summary>
        public static string ConvertArabicToEnglishNumbers(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // تحويل الأرقام العربية (٠١٢٣٤٥٦٧٨٩) إلى إنجليزية (**********)
            string result = input;
            result = result.Replace('٠', '0');
            result = result.Replace('١', '1');
            result = result.Replace('٢', '2');
            result = result.Replace('٣', '3');
            result = result.Replace('٤', '4');
            result = result.Replace('٥', '5');
            result = result.Replace('٦', '6');
            result = result.Replace('٧', '7');
            result = result.Replace('٨', '8');
            result = result.Replace('٩', '9');

            return result;
        }

        /// <summary>
        /// تنسيق الأرقام العشرية بالإنجليزية
        /// </summary>
        public static string FormatDecimal(decimal value, int decimalPlaces = 2)
        {
            var culture = new CultureInfo("en-US");
            return value.ToString($"F{decimalPlaces}", culture);
        }

        /// <summary>
        /// تنسيق الأرقام الصحيحة بالإنجليزية
        /// </summary>
        public static string FormatInteger(int value)
        {
            var culture = new CultureInfo("en-US");
            return value.ToString(culture);
        }

        /// <summary>
        /// تنسيق العملة بالإنجليزية
        /// </summary>
        public static string FormatCurrency(decimal value)
        {
            var culture = new CultureInfo("en-US");
            return value.ToString("F2", culture);
        }

        /// <summary>
        /// تنسيق النسبة المئوية بالإنجليزية
        /// </summary>
        public static string FormatPercentage(decimal value)
        {
            var culture = new CultureInfo("en-US");
            return value.ToString("F2", culture) + "%";
        }

        /// <summary>
        /// تنسيق التاريخ بالإنجليزية
        /// </summary>
        public static string FormatDate(DateTime date)
        {
            var culture = new CultureInfo("en-US");
            return date.ToString("dd/MM/yyyy", culture);
        }

        /// <summary>
        /// تنسيق التاريخ والوقت بالإنجليزية
        /// </summary>
        public static string FormatDateTime(DateTime dateTime)
        {
            var culture = new CultureInfo("en-US");
            return dateTime.ToString("dd/MM/yyyy HH:mm", culture);
        }

        /// <summary>
        /// تحويل النص المختلط (عربي وإنجليزي) لأرقام إنجليزية فقط
        /// </summary>
        public static string EnsureEnglishNumbers(string text)
        {
            if (string.IsNullOrEmpty(text))
                return text;

            return ConvertArabicToEnglishNumbers(text);
        }

        /// <summary>
        /// تحليل الرقم من النص مع ضمان الأرقام الإنجليزية
        /// </summary>
        public static bool TryParseDecimal(string input, out decimal result)
        {
            result = 0;
            if (string.IsNullOrEmpty(input))
                return false;

            string englishNumbers = ConvertArabicToEnglishNumbers(input);
            var culture = new CultureInfo("en-US");
            
            return decimal.TryParse(englishNumbers, NumberStyles.Number, culture, out result);
        }

        /// <summary>
        /// تحليل الرقم الصحيح من النص مع ضمان الأرقام الإنجليزية
        /// </summary>
        public static bool TryParseInt(string input, out int result)
        {
            result = 0;
            if (string.IsNullOrEmpty(input))
                return false;

            string englishNumbers = ConvertArabicToEnglishNumbers(input);
            var culture = new CultureInfo("en-US");
            
            return int.TryParse(englishNumbers, NumberStyles.Number, culture, out result);
        }

        /// <summary>
        /// إعداد الثقافة الإنجليزية للتطبيق
        /// </summary>
        public static void SetEnglishCulture()
        {
            try
            {
                var culture = new CultureInfo("en-US");

                // تطبيق على الخيط الحالي
                System.Threading.Thread.CurrentThread.CurrentCulture = culture;
                System.Threading.Thread.CurrentThread.CurrentUICulture = culture;

                // تطبيق على التطبيق بالكامل
                CultureInfo.DefaultThreadCurrentCulture = culture;
                CultureInfo.DefaultThreadCurrentUICulture = culture;

                // تطبيق على WPF
                System.Windows.FrameworkElement.LanguageProperty.OverrideMetadata(
                    typeof(System.Windows.FrameworkElement),
                    new System.Windows.FrameworkPropertyMetadata(
                        System.Windows.Markup.XmlLanguage.GetLanguage(culture.IetfLanguageTag)));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting English culture: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف النص من الأرقام العربية وتحويلها للإنجليزية
        /// </summary>
        public static string CleanAndConvertNumbers(string input)
        {
            if (string.IsNullOrEmpty(input))
                return input;

            // تحويل الأرقام العربية للإنجليزية
            string result = ConvertArabicToEnglishNumbers(input);

            // إزالة أي أحرف غير مرغوب فيها (اختياري)
            // يمكن إضافة المزيد من التنظيف هنا حسب الحاجة

            return result;
        }

        /// <summary>
        /// تطبيق تحويل الأرقام على TextBox
        /// </summary>
        public static void ApplyEnglishNumbersToTextBox(System.Windows.Controls.TextBox textBox)
        {
            if (textBox == null) return;

            textBox.TextChanged += (sender, e) =>
            {
                var tb = sender as System.Windows.Controls.TextBox;
                if (tb != null)
                {
                    int selectionStart = tb.SelectionStart;
                    string originalText = tb.Text;
                    string convertedText = ConvertArabicToEnglishNumbers(originalText);

                    if (originalText != convertedText)
                    {
                        tb.Text = convertedText;
                        tb.SelectionStart = Math.Min(selectionStart, convertedText.Length);
                    }
                }
            };
        }

        /// <summary>
        /// تطبيق تحويل الأرقام على مجموعة من TextBox
        /// </summary>
        public static void ApplyEnglishNumbersToTextBoxes(params System.Windows.Controls.TextBox[] textBoxes)
        {
            foreach (var textBox in textBoxes)
            {
                ApplyEnglishNumbersToTextBox(textBox);
            }
        }
    }
}
