using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class ExpenseRecordView : UserControl
    {
        private readonly ExpenseService _expenseService;
        private List<ExpenseCategory> _expenseCategories;
        private List<CompanyBranch> _companyBranches;
        private List<ResponsiblePerson> _responsiblePersons;

        public ExpenseRecordView()
        {
            InitializeComponent();
            _expenseService = new ExpenseService();
            _expenseCategories = new List<ExpenseCategory>();
            _companyBranches = new List<CompanyBranch>();
            _responsiblePersons = new List<ResponsiblePerson>();
            LoadData();
            InitializeForm();
        }

        private void LoadData()
        {
            try
            {
                _expenseCategories = _expenseService.GetAllExpenseCategories();
                _companyBranches = _expenseService.GetAllCompanyBranches();
                _responsiblePersons = _expenseService.GetAllResponsiblePersons();
                LoadMainExpenses();
                LoadBranches();
                LoadResponsiblePersons();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadMainExpenses()
        {
            var mainExpenses = _expenseService.GetDistinctMainExpenseNames();
            CmbMainExpense.ItemsSource = mainExpenses;
        }

        private void LoadBranches()
        {
            CmbBranch.ItemsSource = _companyBranches;
            CmbBranch.DisplayMemberPath = "Name";
            CmbBranch.SelectedValuePath = "Id";
        }

        private void LoadResponsiblePersons()
        {
            CmbResponsiblePerson.ItemsSource = _responsiblePersons;
            CmbResponsiblePerson.DisplayMemberPath = "Name";
            CmbResponsiblePerson.SelectedValuePath = "Id";
        }

        private void InitializeForm()
        {
            DpDateTime.SelectedDate = DateTime.Now;
            CmbPaymentMethod.SelectedIndex = 0; // كاش
            CmbExpenseStatus.SelectedIndex = 1; // مستحق
        }

        private void CmbMainExpense_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbMainExpense.SelectedItem is string selectedMainExpense)
            {
                var subExpenses = _expenseService.GetSubExpensesByMainExpense(selectedMainExpense);
                CmbSubExpense.ItemsSource = subExpenses;
                CmbSubExpense.DisplayMemberPath = "SubExpenseName";
                CmbSubExpense.SelectedValuePath = "Id";
            }
            else
            {
                CmbSubExpense.ItemsSource = null;
            }
        }

        private void BtnAddBranch_Click(object sender, RoutedEventArgs e)
        {
            var addBranchWindow = new AddBranchWindow();
            if (addBranchWindow.ShowDialog() == true)
            {
                LoadData(); // إعادة تحميل البيانات لتشمل الفرع الجديد

                // تحديد الفرع الجديد المضاف
                var newBranch = _companyBranches.LastOrDefault();
                if (newBranch != null)
                {
                    CmbBranch.SelectedValue = newBranch.Id;
                }
            }
        }

        private void BtnAddResponsiblePerson_Click(object sender, RoutedEventArgs e)
        {
            var addPersonWindow = new AddResponsiblePersonWindow();
            if (addPersonWindow.ShowDialog() == true)
            {
                LoadData(); // إعادة تحميل البيانات لتشمل المسؤول الجديد

                // تحديد المسؤول الجديد المضاف
                var newPerson = _responsiblePersons.LastOrDefault();
                if (newPerson != null)
                {
                    CmbResponsiblePerson.SelectedValue = newPerson.Id;
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedSubExpense = CmbSubExpense.SelectedItem as ExpenseCategory;
                var selectedBranch = CmbBranch.SelectedItem as CompanyBranch;
                var selectedResponsiblePerson = CmbResponsiblePerson.SelectedItem as ResponsiblePerson;

                var expenseRecord = new ExpenseRecord
                {
                    MainExpenseId = selectedSubExpense.Id,
                    MainExpenseName = CmbMainExpense.SelectedItem.ToString(),
                    SubExpenseId = selectedSubExpense.Id,
                    SubExpenseName = selectedSubExpense.SubExpenseName,
                    BranchId = selectedBranch.Id,
                    BranchName = selectedBranch.Name,
                    Amount = decimal.Parse(TxtAmount.Text),
                    InvoiceNumber = TxtInvoiceNumber.Text.Trim(),
                    DateTime = DpDateTime.SelectedDate.Value,
                    Notes = string.IsNullOrWhiteSpace(TxtNotes.Text) ? null : TxtNotes.Text.Trim(),
                    IsProjectExpense = selectedSubExpense.IsProjectRelated,
                    ProjectTitle = selectedSubExpense.IsProjectRelated ? selectedSubExpense.ProjectTitle : null,
                    ResponsiblePersonId = selectedResponsiblePerson.Id,
                    ResponsiblePersonName = selectedResponsiblePerson.Name,
                    PaymentMethod = ((ComboBoxItem)CmbPaymentMethod.SelectedItem).Content.ToString(),
                    ExpenseStatus = ((ComboBoxItem)CmbExpenseStatus.SelectedItem).Content.ToString(),
                    CreatedBy = "المستخدم الحالي"
                };

                var result = _expenseService.AddExpenseRecord(expenseRecord);

                if (result)
                {
                    MessageBox.Show("تم حفظ المصروف بنجاح!", "نجح الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ المصروف!", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المصروف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbMainExpense.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المصروف الرئيسي", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbMainExpense.Focus();
                return false;
            }

            if (CmbSubExpense.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المصروف الفرعي", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbSubExpense.Focus();
                return false;
            }

            if (CmbBranch.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار فرع الشركة", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbBranch.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtAmount.Text))
            {
                MessageBox.Show("يرجى إدخال قيمة المصروف", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للمصروف", "بيانات غير صحيحة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAmount.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtInvoiceNumber.Text))
            {
                MessageBox.Show("يرجى إدخال رقم الفاتورة", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtInvoiceNumber.Focus();
                return false;
            }

            if (DpDateTime.SelectedDate == null)
            {
                MessageBox.Show("يرجى اختيار التاريخ", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpDateTime.Focus();
                return false;
            }

            if (CmbResponsiblePerson.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المسؤول عن المصروف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbResponsiblePerson.Focus();
                return false;
            }

            if (CmbPaymentMethod.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار طريقة السداد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbPaymentMethod.Focus();
                return false;
            }

            if (CmbExpenseStatus.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار حالة المصروف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbExpenseStatus.Focus();
                return false;
            }

            return true;
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            CmbMainExpense.SelectedItem = null;
            CmbSubExpense.ItemsSource = null;
            CmbBranch.SelectedItem = null;
            CmbResponsiblePerson.SelectedItem = null;
            TxtAmount.Clear();
            TxtInvoiceNumber.Clear();
            TxtNotes.Clear();
            DpDateTime.SelectedDate = DateTime.Now;
            CmbPaymentMethod.SelectedIndex = 0; // كاش
            CmbExpenseStatus.SelectedIndex = 1; // مستحق
        }
    }

    // نافذة إضافة فرع جديد
    public partial class AddBranchWindow : Window
    {
        private readonly ExpenseService _expenseService;

        public AddBranchWindow()
        {
            InitializeComponent();
            _expenseService = new ExpenseService();
        }

        private void InitializeComponent()
        {
            Title = "إضافة فرع جديد";
            Width = 400;
            Height = 300;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            FlowDirection = FlowDirection.RightToLeft;

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.Margin = new Thickness(20);

            // اسم الفرع
            var lblName = new TextBlock { Text = "اسم الفرع:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblName, 0);
            grid.Children.Add(lblName);

            var txtName = new TextBox { Name = "TxtBranchName", Height = 35, Margin = new Thickness(0, 0, 0, 15) };
            Grid.SetRow(txtName, 1);
            grid.Children.Add(txtName);

            // العنوان
            var lblAddress = new TextBlock { Text = "العنوان:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblAddress, 2);
            grid.Children.Add(lblAddress);

            var txtAddress = new TextBox { Name = "TxtAddress", Height = 35, Margin = new Thickness(0, 0, 0, 15) };
            Grid.SetRow(txtAddress, 3);
            grid.Children.Add(txtAddress);

            // الأزرار
            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Center };
            var btnSave = new Button { Content = "حفظ", Width = 80, Height = 35, Margin = new Thickness(0, 0, 10, 0) };
            var btnCancel = new Button { Content = "إلغاء", Width = 80, Height = 35 };

            btnSave.Click += (s, e) => SaveBranch(txtName.Text, txtAddress.Text);
            btnCancel.Click += (s, e) => { DialogResult = false; Close(); };

            buttonPanel.Children.Add(btnSave);
            buttonPanel.Children.Add(btnCancel);
            Grid.SetRow(buttonPanel, 4);
            grid.Children.Add(buttonPanel);

            Content = grid;
        }

        private void SaveBranch(string name, string address)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                MessageBox.Show("يرجى إدخال اسم الفرع", "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var branch = new CompanyBranch
                {
                    Name = name.Trim(),
                    Address = string.IsNullOrWhiteSpace(address) ? null : address.Trim(),
                    CreatedBy = "المستخدم الحالي"
                };

                var result = _expenseService.AddCompanyBranch(branch);
                if (result)
                {
                    MessageBox.Show("تم حفظ الفرع بنجاح!", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الفرع!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الفرع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    // نافذة إضافة مسؤول جديد
    public partial class AddResponsiblePersonWindow : Window
    {
        private readonly ExpenseService _expenseService;

        public AddResponsiblePersonWindow()
        {
            InitializeComponent();
            _expenseService = new ExpenseService();
        }

        private void InitializeComponent()
        {
            Title = "إضافة مسؤول جديد";
            Width = 400;
            Height = 350;
            WindowStartupLocation = WindowStartupLocation.CenterOwner;
            FlowDirection = FlowDirection.RightToLeft;

            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.Margin = new Thickness(20);

            // الاسم
            var lblName = new TextBlock { Text = "الاسم:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblName, 0);
            grid.Children.Add(lblName);

            var txtName = new TextBox { Name = "TxtPersonName", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(txtName, 1);
            grid.Children.Add(txtName);

            // المنصب
            var lblPosition = new TextBlock { Text = "المنصب:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblPosition, 2);
            grid.Children.Add(lblPosition);

            var txtPosition = new TextBox { Name = "TxtPosition", Height = 35, Margin = new Thickness(0, 0, 0, 10) };
            Grid.SetRow(txtPosition, 3);
            grid.Children.Add(txtPosition);

            // القسم
            var lblDepartment = new TextBlock { Text = "القسم:", FontWeight = FontWeights.Bold, Margin = new Thickness(0, 0, 0, 5) };
            Grid.SetRow(lblDepartment, 4);
            grid.Children.Add(lblDepartment);

            var txtDepartment = new TextBox { Name = "TxtDepartment", Height = 35, Margin = new Thickness(0, 0, 0, 15) };
            Grid.SetRow(txtDepartment, 5);
            grid.Children.Add(txtDepartment);

            // الأزرار
            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Center };
            var btnSave = new Button { Content = "حفظ", Width = 80, Height = 35, Margin = new Thickness(0, 0, 10, 0) };
            var btnCancel = new Button { Content = "إلغاء", Width = 80, Height = 35 };

            btnSave.Click += (s, e) => SaveResponsiblePerson(txtName.Text, txtPosition.Text, txtDepartment.Text);
            btnCancel.Click += (s, e) => { DialogResult = false; Close(); };

            buttonPanel.Children.Add(btnSave);
            buttonPanel.Children.Add(btnCancel);
            Grid.SetRow(buttonPanel, 6);
            grid.Children.Add(buttonPanel);

            Content = grid;
        }

        private void SaveResponsiblePerson(string name, string position, string department)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                MessageBox.Show("يرجى إدخال اسم المسؤول", "بيانات ناقصة", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var person = new ResponsiblePerson
                {
                    Name = name.Trim(),
                    Position = string.IsNullOrWhiteSpace(position) ? null : position.Trim(),
                    Department = string.IsNullOrWhiteSpace(department) ? null : department.Trim(),
                    CreatedBy = "المستخدم الحالي"
                };

                var result = _expenseService.AddResponsiblePerson(person);
                if (result)
                {
                    MessageBox.Show("تم حفظ المسؤول بنجاح!", "نجح الحفظ", MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ المسؤول!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ المسؤول: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
