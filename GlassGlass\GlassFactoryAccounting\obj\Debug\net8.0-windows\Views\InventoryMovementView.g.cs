﻿#pragma checksum "..\..\..\..\Views\InventoryMovementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1F5B6CBC0E4D326C54E39F41F6E8F3F3D9F17C49"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// InventoryMovementView
    /// </summary>
    public partial class InventoryMovementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 86 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnReceive;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnIssue;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MovementFormPanel;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpDate;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtOrderNumber;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtInvoiceNumber;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbWarehouse;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CustomerSupplierPanel;
        
        #line default
        #line hidden
        
        
        #line 185 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblCustomerSupplier;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbCustomerSupplier;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbItem;
        
        #line default
        #line hidden
        
        
        #line 228 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblUnits;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtUnits;
        
        #line default
        #line hidden
        
        
        #line 244 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblQuantity;
        
        #line default
        #line hidden
        
        
        #line 247 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtQuantity;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtUnitPrice;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtTotalValue;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBoxCount;
        
        #line default
        #line hidden
        
        
        #line 318 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 329 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSaveMovement;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearForm;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\..\Views\InventoryMovementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid MovementsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/inventorymovementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\InventoryMovementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnReceive = ((System.Windows.Controls.Button)(target));
            
            #line 88 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.BtnReceive.Click += new System.Windows.RoutedEventHandler(this.BtnReceive_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.BtnIssue = ((System.Windows.Controls.Button)(target));
            
            #line 91 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.BtnIssue.Click += new System.Windows.RoutedEventHandler(this.BtnIssue_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.MovementFormPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 4:
            this.DpDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.TxtOrderNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtInvoiceNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.CmbWarehouse = ((System.Windows.Controls.ComboBox)(target));
            
            #line 174 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.CmbWarehouse.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbWarehouse_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 8:
            this.CustomerSupplierPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.LblCustomerSupplier = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.CmbCustomerSupplier = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 11:
            this.CmbItem = ((System.Windows.Controls.ComboBox)(target));
            
            #line 217 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.CmbItem.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbItem_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.LblUnits = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtUnits = ((System.Windows.Controls.TextBox)(target));
            
            #line 233 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.TxtUnits.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtUnits_TextChanged);
            
            #line default
            #line hidden
            return;
            case 14:
            this.LblQuantity = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TxtQuantity = ((System.Windows.Controls.TextBox)(target));
            
            #line 249 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.TxtQuantity.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtQuantity_TextChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TxtUnitPrice = ((System.Windows.Controls.TextBox)(target));
            
            #line 265 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.TxtUnitPrice.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtUnitPrice_TextChanged);
            
            #line default
            #line hidden
            return;
            case 17:
            this.TxtTotalValue = ((System.Windows.Controls.TextBox)(target));
            return;
            case 18:
            this.TxtBoxCount = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 20:
            this.BtnSaveMovement = ((System.Windows.Controls.Button)(target));
            
            #line 331 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.BtnSaveMovement.Click += new System.Windows.RoutedEventHandler(this.BtnSaveMovement_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.BtnClearForm = ((System.Windows.Controls.Button)(target));
            
            #line 334 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.BtnClearForm.Click += new System.Windows.RoutedEventHandler(this.BtnClearForm_Click);
            
            #line default
            #line hidden
            return;
            case 22:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 351 "..\..\..\..\Views\InventoryMovementView.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.MovementsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

