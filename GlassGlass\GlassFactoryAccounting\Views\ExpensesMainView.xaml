<UserControl x:Class="GlassFactoryAccounting.Views.ExpensesMainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="15"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="12"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="MenuButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="FontSize" Value="16"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Height" Value="120"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="10">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#F8F9FA"/>
                                <Setter Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="5" Opacity="0.4" BlurRadius="15"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20" Background="#F5F5F5">
        <StackPanel>
            <!-- العنوان الرئيسي -->
            <Border Style="{StaticResource CardStyle}">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                    <TextBlock Text="💰" FontSize="48" VerticalAlignment="Center" Margin="0,0,20,0"/>
                    <StackPanel>
                        <TextBlock Text="موديول المصروفات" FontSize="32" FontWeight="Bold" Foreground="White"/>
                        <TextBlock Text="منظومة ERP مرنة وقابلة للتوسع لإدارة المصروفات" FontSize="16" Foreground="White" Opacity="0.9"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- الأزرار الرئيسية -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="🎯 الوظائف الرئيسية" FontSize="20" FontWeight="Bold" 
                               Margin="0,0,0,25" HorizontalAlignment="Center" Foreground="#2C3E50"/>

                    <!-- الصف الأول من الأزرار -->
                    <Grid Grid.Row="1" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- إدارة المصروفات -->
                        <Button Grid.Column="0" x:Name="BtnExpenseManagement" 
                                Style="{StaticResource MenuButtonStyle}"
                                BorderBrush="#E74C3C" Foreground="#E74C3C"
                                Click="BtnExpenseManagement_Click">
                            <StackPanel>
                                <TextBlock Text="🏷️" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="إدارة المصروفات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="تسجيل المصروفات الأساسية والفرعية وربطها بالمشاريع" 
                                           FontSize="12" HorizontalAlignment="Center" TextWrapping="Wrap" 
                                           Margin="0,5,0,0" Opacity="0.7"/>
                            </StackPanel>
                        </Button>

                        <!-- تسجيل المصروفات -->
                        <Button Grid.Column="1" x:Name="BtnExpenseRecord" 
                                Style="{StaticResource MenuButtonStyle}"
                                BorderBrush="#27AE60" Foreground="#27AE60"
                                Click="BtnExpenseRecord_Click">
                            <StackPanel>
                                <TextBlock Text="💸" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تسجيل المصروفات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="تسجيل كل المصروفات الفعلية مع البيانات التفصيلية" 
                                           FontSize="12" HorizontalAlignment="Center" TextWrapping="Wrap" 
                                           Margin="0,5,0,0" Opacity="0.7"/>
                            </StackPanel>
                        </Button>
                    </Grid>

                    <!-- الصف الثاني من الأزرار -->
                    <Grid Grid.Row="2">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- تقرير المصروفات -->
                        <Button Grid.Column="0" x:Name="BtnExpenseReport" 
                                Style="{StaticResource MenuButtonStyle}"
                                BorderBrush="#3498DB" Foreground="#3498DB"
                                Click="BtnExpenseReport_Click">
                            <StackPanel>
                                <TextBlock Text="📊" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تقرير المصروفات" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="جميع المصروفات ما عدا المرتبطة بمشاريع مع فلاتر وتحليلات" 
                                           FontSize="12" HorizontalAlignment="Center" TextWrapping="Wrap" 
                                           Margin="0,5,0,0" Opacity="0.7"/>
                            </StackPanel>
                        </Button>

                        <!-- تقرير مصروفات المشاريع -->
                        <Button Grid.Column="1" x:Name="BtnProjectExpenseReport" 
                                Style="{StaticResource MenuButtonStyle}"
                                BorderBrush="#17A2B8" Foreground="#17A2B8"
                                Click="BtnProjectExpenseReport_Click">
                            <StackPanel>
                                <TextBlock Text="📈" FontSize="32" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                                <TextBlock Text="تقرير مصروفات المشاريع" FontSize="16" FontWeight="Bold" HorizontalAlignment="Center"/>
                                <TextBlock Text="المصروفات المرتبطة بالمشاريع مع تحليلات ورسومات بيانية" 
                                           FontSize="12" HorizontalAlignment="Center" TextWrapping="Wrap" 
                                           Margin="0,5,0,0" Opacity="0.7"/>
                            </StackPanel>
                        </Button>
                    </Grid>


                </Grid>
            </Border>

            <!-- معلومات إضافية -->
            <Border Style="{StaticResource CardStyle}" Background="#F8F9FA">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <!-- الميزات -->
                    <StackPanel Grid.Column="0" Margin="0,0,15,0">
                        <TextBlock Text="✨ الميزات" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" Foreground="#2C3E50"/>
                        <TextBlock Text="• إدارة المصروفات الرئيسية والفرعية" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• ربط المصروفات بالمشاريع" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• إدارة فروع الشركة" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• تقارير تفصيلية مع فلاتر" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• إحصائيات ورسومات بيانية" FontSize="12" Margin="0,0,0,5"/>
                    </StackPanel>

                    <!-- التقارير -->
                    <StackPanel Grid.Column="1" Margin="15,0,15,0">
                        <TextBlock Text="📋 التقارير" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" Foreground="#2C3E50"/>
                        <TextBlock Text="• تقرير المصروفات العادية" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• تقرير مصروفات المشاريع" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• فلاتر حسب التاريخ والنوع والفرع" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• إحصائيات مالية شاملة" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• إمكانية التعديل والحذف" FontSize="12" Margin="0,0,0,5"/>
                    </StackPanel>

                    <!-- الإدارة -->
                    <StackPanel Grid.Column="2" Margin="15,0,0,0">
                        <TextBlock Text="⚙️ الإدارة" FontSize="16" FontWeight="Bold" Margin="0,0,0,10" Foreground="#2C3E50"/>
                        <TextBlock Text="• إضافة وتعديل أنواع المصروفات" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• إدارة بيانات المشاريع" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• إضافة فروع جديدة للشركة" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• تتبع تواريخ الإنشاء والتعديل" FontSize="12" Margin="0,0,0,5"/>
                        <TextBlock Text="• حفظ البيانات بشكل آمن" FontSize="12" Margin="0,0,0,5"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- تعليمات الاستخدام -->
            <Border Style="{StaticResource CardStyle}">
                <Border.Background>
                    <LinearGradientBrush StartPoint="0,1" EndPoint="1,0">
                        <GradientStop Color="#667eea" Offset="0"/>
                        <GradientStop Color="#764ba2" Offset="1"/>
                    </LinearGradientBrush>
                </Border.Background>
                <StackPanel>
                    <TextBlock Text="📖 تعليمات الاستخدام" FontSize="18" FontWeight="Bold" 
                               Margin="0,0,0,15" HorizontalAlignment="Center" Foreground="White"/>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,15,0">
                            <TextBlock Text="1️⃣ ابدأ بإدارة المصروفات:" FontSize="14" FontWeight="Bold" 
                                       Margin="0,0,0,8" Foreground="White"/>
                            <TextBlock Text="• أضف المصروفات الرئيسية والفرعية" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• حدد إذا كان المصروف مرتبط بمشروع" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• أدخل بيانات المشروع إذا لزم الأمر" FontSize="12" 
                                       Margin="0,0,0,15" Foreground="White" Opacity="0.9"/>

                            <TextBlock Text="2️⃣ سجل المصروفات الفعلية:" FontSize="14" FontWeight="Bold" 
                                       Margin="0,0,0,8" Foreground="White"/>
                            <TextBlock Text="• اختر المصروف الرئيسي والفرعي" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• حدد فرع الشركة أو أضف فرع جديد" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• أدخل قيمة المصروف ورقم الفاتورة" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="15,0,0,0">
                            <TextBlock Text="3️⃣ راجع التقارير:" FontSize="14" FontWeight="Bold" 
                                       Margin="0,0,0,8" Foreground="White"/>
                            <TextBlock Text="• استخدم تقرير المصروفات للمصروفات العادية" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• استخدم تقرير مصروفات المشاريع للمشاريع" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• طبق الفلاتر للحصول على النتائج المطلوبة" FontSize="12" 
                                       Margin="0,0,0,15" Foreground="White" Opacity="0.9"/>

                            <TextBlock Text="4️⃣ استفد من الإحصائيات:" FontSize="14" FontWeight="Bold" 
                                       Margin="0,0,0,8" Foreground="White"/>
                            <TextBlock Text="• راجع إجمالي المصروفات والمتوسطات" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• تابع عدد المشاريع والمصروفات" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                            <TextBlock Text="• استخدم البيانات لاتخاذ قرارات مالية" FontSize="12" 
                                       Margin="0,0,0,5" Foreground="White" Opacity="0.9"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
