using System.IO;
using iTextSharp.text;
using iTextSharp.text.pdf;
using GlassFactoryAccounting.Models;
using System.Collections.ObjectModel;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إنشاء PDF للفواتير
    /// </summary>
    public class InvoicePdfService
    {
        public bool CreateInvoicePdf(string customerName, string invoiceNumber, DateTime invoiceDate,
            ObservableCollection<SaleItem> items, decimal subTotal, decimal discount, decimal finalTotal,
            string notes, string filePath)
        {
            try
            {
                // إنشاء المستند
                var document = new Document(PageSize.A4, 50, 50, 50, 50);
                var writer = PdfWriter.GetInstance(document, new FileStream(filePath, FileMode.Create));
                
                document.Open();

                // إنشاء خط يدعم العربية (استخدام خط افتراضي)
                var titleFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 18, BaseColor.BLACK);
                var headerFont = FontFactory.GetFont(FontFactory.HELVETICA_BOLD, 12, BaseColor.BLACK);
                var normalFont = FontFactory.GetFont(FontFactory.HELVETICA, 10, BaseColor.BLACK);

                // عنوان الفاتورة
                var title = new Paragraph("Glass Factory Invoice", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(title);

                // معلومات الشركة
                var companyInfo = new Paragraph("Glass Manufacturing Company\nAddress: Industrial Zone\nPhone: +*********", normalFont)
                {
                    Alignment = Element.ALIGN_LEFT,
                    SpacingAfter = 20
                };
                document.Add(companyInfo);

                // معلومات الفاتورة
                var invoiceInfo = new PdfPTable(2) { WidthPercentage = 100 };
                invoiceInfo.SetWidths(new float[] { 50, 50 });

                var customerCell = new PdfPCell(new Phrase($"Customer: {customerName}\nDate: {invoiceDate:yyyy/MM/dd}", normalFont))
                {
                    Border = Rectangle.NO_BORDER,
                    HorizontalAlignment = Element.ALIGN_LEFT
                };

                var invoiceCell = new PdfPCell(new Phrase($"Invoice No: {invoiceNumber}", normalFont))
                {
                    Border = Rectangle.NO_BORDER,
                    HorizontalAlignment = Element.ALIGN_RIGHT
                };

                invoiceInfo.AddCell(customerCell);
                invoiceInfo.AddCell(invoiceCell);
                document.Add(invoiceInfo);

                // مساحة فارغة
                document.Add(new Paragraph(" ", normalFont) { SpacingAfter = 10 });

                // جدول العناصر
                var itemsTable = new PdfPTable(8) { WidthPercentage = 100, SpacingBefore = 20 };
                itemsTable.SetWidths(new float[] { 8, 15, 12, 12, 12, 12, 12, 17 });

                // رؤوس الأعمدة
                string[] headers = { "#", "Service", "Thickness", "Length", "Width", "Quantity", "Price", "Total" };
                foreach (var header in headers)
                {
                    var cell = new PdfPCell(new Phrase(header, headerFont))
                    {
                        HorizontalAlignment = Element.ALIGN_CENTER,
                        BackgroundColor = BaseColor.LIGHT_GRAY,
                        Padding = 5
                    };
                    itemsTable.AddCell(cell);
                }

                // إضافة العناصر
                int rowNumber = 1;
                foreach (var item in items)
                {
                    itemsTable.AddCell(new PdfPCell(new Phrase(rowNumber.ToString(), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER, Padding = 3 });
                    itemsTable.AddCell(new PdfPCell(new Phrase(item.Service, normalFont)) { Padding = 3 });
                    itemsTable.AddCell(new PdfPCell(new Phrase($"{item.GlassThickness}mm", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER, Padding = 3 });
                    itemsTable.AddCell(new PdfPCell(new Phrase($"{item.Length}", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER, Padding = 3 });
                    itemsTable.AddCell(new PdfPCell(new Phrase($"{item.Width}", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER, Padding = 3 });
                    itemsTable.AddCell(new PdfPCell(new Phrase($"{item.TotalArea:F4}", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER, Padding = 3 });
                    itemsTable.AddCell(new PdfPCell(new Phrase($"{item.UnitPrice:F2}", normalFont)) { HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });
                    itemsTable.AddCell(new PdfPCell(new Phrase($"{item.TotalPrice:F2}", normalFont)) { HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });
                    rowNumber++;
                }

                document.Add(itemsTable);

                // الإجماليات
                var totalsTable = new PdfPTable(2) { WidthPercentage = 50, HorizontalAlignment = Element.ALIGN_RIGHT, SpacingBefore = 20 };
                totalsTable.SetWidths(new float[] { 60, 40 });

                // إجمالي فرعي
                totalsTable.AddCell(new PdfPCell(new Phrase("Subtotal:", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{subTotal:F2}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });

                // الخصم
                if (discount > 0)
                {
                    totalsTable.AddCell(new PdfPCell(new Phrase("Discount:", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });
                    totalsTable.AddCell(new PdfPCell(new Phrase($"-{discount:F2}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });
                }

                // خط فاصل
                var separatorCell1 = new PdfPCell(new Phrase("", normalFont)) { HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 };
                separatorCell1.BorderWidth = 0;
                separatorCell1.BorderWidthBottom = 1;
                totalsTable.AddCell(separatorCell1);

                var separatorCell2 = new PdfPCell(new Phrase("", normalFont)) { HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 };
                separatorCell2.BorderWidth = 0;
                separatorCell2.BorderWidthBottom = 1;
                totalsTable.AddCell(separatorCell2);

                // الإجمالي النهائي
                totalsTable.AddCell(new PdfPCell(new Phrase("Total:", titleFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });
                totalsTable.AddCell(new PdfPCell(new Phrase($"{finalTotal:F2}", titleFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_RIGHT, Padding = 3 });

                document.Add(totalsTable);

                // الملاحظات
                if (!string.IsNullOrWhiteSpace(notes))
                {
                    document.Add(new Paragraph(" ", normalFont) { SpacingAfter = 10 });
                    var notesTitle = new Paragraph("Notes:", headerFont) { SpacingAfter = 5 };
                    document.Add(notesTitle);
                    var notesParagraph = new Paragraph(notes, normalFont);
                    document.Add(notesParagraph);
                }

                // تذييل
                document.Add(new Paragraph(" ", normalFont) { SpacingAfter = 20 });
                var footer = new Paragraph("Thank you for your business!", normalFont)
                {
                    Alignment = Element.ALIGN_CENTER
                };
                document.Add(footer);

                document.Close();
                return true;
            }
            catch (Exception ex)
            {
                // يمكن إضافة تسجيل الأخطاء هنا
                System.Diagnostics.Debug.WriteLine($"Error creating PDF: {ex.Message}");
                return false;
            }
        }
    }
}
