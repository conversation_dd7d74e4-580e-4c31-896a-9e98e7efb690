<UserControl x:Class="GlassFactoryAccounting.Views.NewPurchaseInvoiceView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             xmlns:sys="clr-namespace:System;assembly=mscorlib"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان مع زر العودة -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="BtnBack" Content="⬅️ عودة" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,8" Margin="0,0,20,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnBack_Click"/>
                
                <TextBlock Text="🛒 فاتورة مشتريات جديدة" FontSize="24" FontWeight="Bold" 
                         Foreground="{StaticResource PrimaryBrush}" VerticalAlignment="Center"/>
                
                <Button x:Name="BtnManageServices" Content="إدارة الخدمات" 
                        Background="{StaticResource WarningBrush}" Foreground="White"
                        Padding="10,5" Margin="20,0,0,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnManageServices_Click"/>
            </StackPanel>
        </Border>
        
        <!-- بيانات الفاتورة الأساسية -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                    <TextBlock Text="اسم المورد:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <ComboBox x:Name="CmbSupplierName" Grid.Column="0" Height="35" 
                                  IsEditable="True" VerticalContentAlignment="Center"
                                  DisplayMemberPath="Name" SelectedValuePath="Name"/>
                        <Button x:Name="BtnAddSupplier" Grid.Column="1" Content="➕ إضافة مورد" 
                                Background="{StaticResource SuccessBrush}" Foreground="White"
                                Padding="8,5" Margin="5,0,0,0" FontWeight="Bold"
                                BorderThickness="0" Cursor="Hand" Click="BtnAddSupplier_Click"/>
                        <Button x:Name="BtnManageSuppliers" Grid.Column="2" Content="إدارة" 
                                Background="{StaticResource WarningBrush}" Foreground="White"
                                Padding="8,5" Margin="5,0,0,0" FontWeight="Bold"
                                BorderThickness="0" Cursor="Hand" Click="BtnManageSuppliers_Click"/>
                    </Grid>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Margin="5,0">
                    <TextBlock Text="التاريخ:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <DatePicker x:Name="DateInvoice" Height="35"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Margin="10,0,0,0">
                    <TextBlock Text="رقم الفاتورة:" FontWeight="Bold" Margin="0,0,0,5"/>
                    <TextBox x:Name="TxtInvoiceNumber" Height="35" VerticalContentAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- جدول الفاتورة -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- عنوان الجدول -->
                <TextBlock Grid.Row="0" Text="تفاصيل فاتورة المشتريات" FontSize="18" FontWeight="Bold" 
                         Margin="0,0,0,10" Foreground="{StaticResource PrimaryBrush}"/>
                
                <!-- أزرار إدارة الصفوف -->
                <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,10">
                    <Button x:Name="BtnAddNormalRow" Content="إضافة صف عادي" 
                            Background="{StaticResource SuccessBrush}" Foreground="White"
                            Padding="10,5" Margin="0,0,10,0" FontWeight="Bold"
                            BorderThickness="0" Cursor="Hand" Click="BtnAddNormalRow_Click"/>
                    
                    <Button x:Name="BtnAddManualRow" Content="إضافة صف يدوي" 
                            Background="{StaticResource PrimaryBrush}" Foreground="White"
                            Padding="10,5" Margin="0,0,10,0" FontWeight="Bold"
                            BorderThickness="0" Cursor="Hand" Click="BtnAddManualRow_Click"/>
                    
                    <Button x:Name="BtnDeleteRow" Content="حذف صف" 
                            Background="{StaticResource DangerBrush}" Foreground="White"
                            Padding="10,5" FontWeight="Bold"
                            BorderThickness="0" Cursor="Hand" Click="BtnDeleteRow_Click"/>
                </StackPanel>
                
                <!-- جدول البيانات -->
                <DataGrid x:Name="InvoiceDataGrid" Grid.Row="2"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          GridLinesVisibility="All"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="35"
                          FontSize="12">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="ت" Binding="{Binding Id}" Width="40" IsReadOnly="True"/>
                        
                        <DataGridTemplateColumn Header="الخدمة" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox x:Name="CmbService" ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=Services}"
                                              DisplayMemberPath="Name" SelectedValuePath="Name"
                                              SelectedValue="{Binding Service, UpdateSourceTrigger=PropertyChanged}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="نوع الزجاج" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox x:Name="CmbGlassType" ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=GlassTypes}"
                                              SelectedValue="{Binding GlassType, UpdateSourceTrigger=PropertyChanged}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <DataGridTemplateColumn Header="سمك الزجاج" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ComboBox x:Name="CmbThickness" ItemsSource="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=GlassThicknesses}"
                                              SelectedValue="{Binding GlassThickness, UpdateSourceTrigger=PropertyChanged}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                        
                        <DataGridTextColumn Header="تفاصيل" Binding="{Binding Details, UpdateSourceTrigger=PropertyChanged}" Width="120"/>
                        <DataGridTextColumn Header="الطول (ملم)" Binding="{Binding Length, UpdateSourceTrigger=PropertyChanged}" Width="100"/>
                        <DataGridTextColumn Header="العرض (ملم)" Binding="{Binding Width, UpdateSourceTrigger=PropertyChanged}" Width="100"/>
                        <DataGridTextColumn Header="الكمية" Binding="{Binding Area, StringFormat='{}{0:F4}', UpdateSourceTrigger=PropertyChanged}" Width="80"/>
                        <DataGridTextColumn Header="العدد" Binding="{Binding Count, UpdateSourceTrigger=PropertyChanged}" Width="60"/>
                        <DataGridTextColumn Header="إجمالي الكمية" Binding="{Binding TotalArea, StringFormat='{}{0:F4}', UpdateSourceTrigger=PropertyChanged}" Width="100"/>
                        <DataGridTextColumn Header="السعر" Binding="{Binding UnitPrice, UpdateSourceTrigger=PropertyChanged}" Width="80"/>
                        <DataGridTextColumn Header="إجمالي القيمة" Binding="{Binding TotalPrice, StringFormat='{}{0:F2}', UpdateSourceTrigger=PropertyChanged}" Width="100" IsReadOnly="True"/>

                        <DataGridTemplateColumn Header="ملاحظات" Width="120">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                             BorderThickness="0" Background="Transparent"
                                             VerticalContentAlignment="Center"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        
        <!-- إجماليات الفاتورة -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="300"/>
                </Grid.ColumnDefinitions>
                
                <!-- الخيارات الإضافية -->
                <StackPanel Grid.Column="0">
                    <CheckBox x:Name="ChkEnableDiscount" Content="تفعيل خصم" FontWeight="Bold" 
                              Margin="0,0,0,10" Checked="ChkEnableDiscount_Checked" Unchecked="ChkEnableDiscount_Unchecked"/>
                    
                    <StackPanel x:Name="PnlDiscount" Orientation="Horizontal" Margin="0,0,0,10" Visibility="Collapsed">
                        <TextBlock Text="قيمة الخصم:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <TextBox x:Name="TxtDiscountAmount" Width="100" Height="30" VerticalContentAlignment="Center"
                                 TextChanged="TxtDiscountAmount_TextChanged"/>
                    </StackPanel>
                    
                    <CheckBox x:Name="ChkEnableNotes" Content="إضافة ملاحظة" FontWeight="Bold" 
                              Margin="0,0,0,10" Checked="ChkEnableNotes_Checked" Unchecked="ChkEnableNotes_Unchecked"/>
                    
                    <TextBox x:Name="TxtNotes" Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                             Visibility="Collapsed"/>
                </StackPanel>
                
                <!-- الإجماليات -->
                <StackPanel Grid.Column="1">
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <TextBlock Text="إجمالي الفاتورة:" FontWeight="Bold" Width="120"/>
                        <TextBlock x:Name="TxtSubTotal" Text="0.00 ج.م" FontWeight="Bold" 
                                 Foreground="{StaticResource PrimaryBrush}"/>
                    </StackPanel>
                    
                    <StackPanel x:Name="PnlDiscountDisplay" Orientation="Horizontal" Margin="0,0,0,5" Visibility="Collapsed">
                        <TextBlock Text="الخصم:" FontWeight="Bold" Width="120"/>
                        <TextBlock x:Name="TxtDiscountDisplay" Text="0.00 ج.م" FontWeight="Bold" 
                                 Foreground="{StaticResource DangerBrush}"/>
                    </StackPanel>
                    
                    <Separator Margin="0,5"/>
                    
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="الإجمالي النهائي:" FontWeight="Bold" FontSize="16" Width="120"/>
                        <TextBlock x:Name="TxtFinalTotal" Text="0.00 ج.م" FontWeight="Bold" FontSize="16"
                                 Foreground="{StaticResource SuccessBrush}"/>
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- أزرار الحفظ والطباعة -->
        <Border Grid.Row="4" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ الفاتورة" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSave_Click"/>
                
                <Button x:Name="BtnSavePDF" Content="📄 حفظ PDF" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSavePDF_Click"/>
                
                <Button x:Name="BtnPrint" Content="🖨️ طباعة" 
                        Background="{StaticResource WarningBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnPrint_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</UserControl>
