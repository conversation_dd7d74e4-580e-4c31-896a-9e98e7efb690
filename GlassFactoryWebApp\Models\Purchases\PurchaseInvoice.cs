using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج فاتورة المشتريات
    /// </summary>
    public class PurchaseInvoice : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string InvoiceNumber { get; set; } = string.Empty;

        [Required]
        public DateTime InvoiceDate { get; set; } = DateTime.Now;

        [Required]
        public int SupplierId { get; set; }

        [MaxLength(100)]
        public string SupplierName { get; set; } = string.Empty;

        [MaxLength(20)]
        public string? SupplierInvoiceNumber { get; set; }

        public DateTime? SupplierInvoiceDate { get; set; }

        public decimal SubTotal { get; set; } = 0;

        public decimal DiscountPercentage { get; set; } = 0;

        public decimal DiscountAmount { get; set; } = 0;

        public decimal TaxPercentage { get; set; } = 0;

        public decimal TaxAmount { get; set; } = 0;

        public decimal TotalAmount { get; set; } = 0;

        public decimal PaidAmount { get; set; } = 0;

        public decimal RemainingAmount { get; set; } = 0;

        [MaxLength(50)]
        public string PaymentMethod { get; set; } = "آجل"; // نقدي، آجل، شيك، تحويل

        [MaxLength(50)]
        public string InvoiceStatus { get; set; } = "مفتوحة"; // مفتوحة، مدفوعة، ملغاة

        public DateTime? DueDate { get; set; }

        [MaxLength(100)]
        public string? ReferenceNumber { get; set; }

        [MaxLength(500)]
        public string? InvoiceNotes { get; set; }

        public bool IsReceived { get; set; } = false;

        public DateTime? ReceivedDate { get; set; }

        [MaxLength(100)]
        public string? ReceivedBy { get; set; }

        public bool IsPosted { get; set; } = false;

        public DateTime? PostedAt { get; set; }

        [MaxLength(100)]
        public string? PostedBy { get; set; }

        // Shipping and delivery
        [MaxLength(200)]
        public string? ShippingAddress { get; set; }

        [MaxLength(100)]
        public string? ShippingMethod { get; set; }

        public decimal ShippingCost { get; set; } = 0;

        [MaxLength(100)]
        public string? TrackingNumber { get; set; }

        // Foreign Keys
        [ForeignKey("SupplierId")]
        public virtual Supplier Supplier { get; set; } = null!;

        public virtual ApplicationUser CreatedByUser { get; set; } = null!;

        // Navigation Properties
        public virtual ICollection<PurchaseInvoiceItem> Items { get; set; } = new List<PurchaseInvoiceItem>();
        public virtual ICollection<SupplierPayment> Payments { get; set; } = new List<SupplierPayment>();
    }
}
