using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة الرواتب والأجور
/// </summary>
public partial class SalariesView : UserControl
{
    private ObservableCollection<Salary> _salaries;
    private ObservableCollection<Employee> _employees;

    public SalariesView()
    {
        InitializeComponent();
        _salaries = new ObservableCollection<Salary>();
        _employees = new ObservableCollection<Employee>();
        SalariesDataGrid.ItemsSource = _salaries;
        
        InitializePage();
        LoadSalariesData();
    }

    private void InitializePage()
    {
        // تعبئة قائمة الشهور
        CmbMonth.Items.Add("جميع الشهور");
        for (int i = 1; i <= 12; i++)
        {
            CmbMonth.Items.Add(new DateTime(2024, i, 1).ToString("MMMM"));
        }
        CmbMonth.SelectedIndex = DateTime.Now.Month; // الشهر الحالي

        // تعبئة قائمة السنوات
        CmbYear.Items.Add("جميع السنوات");
        for (int year = DateTime.Now.Year - 5; year <= DateTime.Now.Year + 1; year++)
        {
            CmbYear.Items.Add(year.ToString());
        }
        CmbYear.SelectedItem = DateTime.Now.Year.ToString(); // السنة الحالية
    }

    private void LoadSalariesData()
    {
        // سيتم تحميل البيانات الفعلية من قاعدة البيانات لاحقاً
        _salaries.Clear();
        _employees.Clear();

        // بيانات تجريبية للموظفين
        var sampleEmployees = new List<Employee>
        {
            new Employee
            {
                Id = 1,
                Name = "أحمد محمد علي",
                EmployeeCode = "EMP001",
                Position = "مدير انتاج",
                Branch = "الفرع الرئيسي",
                Department = "الإنتاج",
                BasicSalary = 8000.00m,
                WorkingHours = 8,
                HireDate = DateTime.Now.AddYears(-2)
            },
            new Employee
            {
                Id = 2,
                Name = "فاطمة أحمد حسن",
                EmployeeCode = "EMP002",
                Position = "محاسب",
                Branch = "الفرع الرئيسي",
                Department = "المحاسبة",
                BasicSalary = 6000.00m,
                WorkingHours = 8,
                HireDate = DateTime.Now.AddYears(-1)
            },
            new Employee
            {
                Id = 3,
                Name = "محمد عبد الله",
                EmployeeCode = "EMP003",
                Position = "فني انتاج",
                Branch = "الفرع الرئيسي",
                Department = "الإنتاج",
                BasicSalary = 4500.00m,
                WorkingHours = 8,
                HireDate = DateTime.Now.AddMonths(-6)
            }
        };

        foreach (var employee in sampleEmployees)
        {
            _employees.Add(employee);
        }

        // بيانات تجريبية للرواتب
        var sampleSalaries = new List<Salary>
        {
            new Salary
            {
                Id = 1,
                EmployeeId = 1,
                Employee = sampleEmployees[0],
                SalaryMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1),
                BasicSalary = 8000.00m,
                Allowances = 500.00m,
                Overtime = 300.00m,
                Bonus = 200.00m,
                Deductions = 100.00m,
                Taxes = 800.00m,
                IsPaid = true,
                PaidDate = DateTime.Now.AddDays(-5)
            },
            new Salary
            {
                Id = 2,
                EmployeeId = 2,
                Employee = sampleEmployees[1],
                SalaryMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1),
                BasicSalary = 6000.00m,
                Allowances = 300.00m,
                Overtime = 200.00m,
                Bonus = 150.00m,
                Deductions = 50.00m,
                Taxes = 600.00m,
                IsPaid = false
            },
            new Salary
            {
                Id = 3,
                EmployeeId = 3,
                Employee = sampleEmployees[2],
                SalaryMonth = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1),
                BasicSalary = 4500.00m,
                Allowances = 200.00m,
                Overtime = 150.00m,
                Bonus = 100.00m,
                Deductions = 25.00m,
                Taxes = 450.00m,
                IsPaid = false
            }
        };

        // حساب الراتب الصافي لكل راتب
        foreach (var salary in sampleSalaries)
        {
            salary.CalculateNetSalary();
            _salaries.Add(salary);
        }

        UpdateStatistics();
    }

    private void UpdateStatistics()
    {
        TxtTotalEmployees.Text = _employees.Count.ToString();
        
        var monthSalaries = _salaries.Where(s => s.SalaryMonth.Month == DateTime.Now.Month 
                                                && s.SalaryMonth.Year == DateTime.Now.Year);
        
        TxtMonthSalaries.Text = $"{monthSalaries.Sum(s => s.NetSalary):N2} ج.م";
        TxtPaidSalaries.Text = $"{monthSalaries.Where(s => s.IsPaid).Sum(s => s.NetSalary):N2} ج.م";
        TxtPendingSalaries.Text = $"{monthSalaries.Where(s => !s.IsPaid).Sum(s => s.NetSalary):N2} ج.م";
    }

    private void BtnNewEmployee_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح نافذة إضافة موظف جديد", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnNewSalary_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم فتح نافذة إضافة راتب جديد", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnRefresh_Click(object sender, RoutedEventArgs e)
    {
        LoadSalariesData();
        MessageBox.Show("تم تحديث البيانات", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnSearch_Click(object sender, RoutedEventArgs e)
    {
        // تطبيق فلاتر البحث حسب الشهر والسنة
        MessageBox.Show("سيتم تطبيق فلاتر البحث", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnViewSalary_Click(object sender, RoutedEventArgs e)
    {
        if (SalariesDataGrid.SelectedItem is Salary selectedSalary)
        {
            MessageBox.Show($"عرض تفاصيل راتب: {selectedSalary.Employee?.Name}", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void BtnPaySalary_Click(object sender, RoutedEventArgs e)
    {
        if (SalariesDataGrid.SelectedItem is Salary selectedSalary)
        {
            if (!selectedSalary.IsPaid)
            {
                var result = MessageBox.Show($"هل تريد تأكيد دفع راتب {selectedSalary.Employee?.Name}؟", 
                    "تأكيد الدفع", MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    selectedSalary.IsPaid = true;
                    selectedSalary.PaidDate = DateTime.Now;
                    UpdateStatistics();
                    MessageBox.Show("تم تأكيد دفع الراتب بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                MessageBox.Show("هذا الراتب مدفوع بالفعل", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
    }
}
