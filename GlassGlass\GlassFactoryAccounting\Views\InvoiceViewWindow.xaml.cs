using System.Collections.ObjectModel;
using System.Windows;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// نافذة عرض تفاصيل الفاتورة
/// </summary>
public partial class InvoiceViewWindow : Window
{
    private readonly Sale _invoice;

    public InvoiceViewWindow(Sale invoice)
    {
        InitializeComponent();
        _invoice = invoice;
        LoadInvoiceData();
    }

    private void LoadInvoiceData()
    {
        // البيانات الأساسية
        TxtTitle.Text = $"📄 عرض الفاتورة - {_invoice.InvoiceNumber}";
        TxtInvoiceNumber.Text = _invoice.InvoiceNumber;
        TxtCustomerName.Text = _invoice.Customer?.Name ?? "غير محدد";
        TxtInvoiceDate.Text = _invoice.SaleDate.ToString("yyyy/MM/dd");

        // تفاصيل العناصر
        ItemsDataGrid.ItemsSource = new ObservableCollection<SaleItem>(_invoice.SaleItems);

        // الإجماليات
        TxtSubTotal.Text = $"{_invoice.TotalAmount:N2} ج.م";
        
        if (_invoice.Discount > 0)
        {
            TxtDiscount.Text = $"{_invoice.Discount:N2} ج.م";
            PnlDiscount.Visibility = Visibility.Visible;
        }
        else
        {
            PnlDiscount.Visibility = Visibility.Collapsed;
        }
        
        TxtFinalTotal.Text = $"{_invoice.NetAmount:N2} ج.م";

        // الملاحظات
        TxtNotes.Text = string.IsNullOrWhiteSpace(_invoice.Notes) ? "لا توجد ملاحظات" : _invoice.Notes;
    }

    private void BtnClose_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
