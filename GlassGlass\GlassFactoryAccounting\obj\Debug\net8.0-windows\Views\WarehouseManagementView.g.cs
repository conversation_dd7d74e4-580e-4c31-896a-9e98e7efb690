﻿#pragma checksum "..\..\..\..\Views\WarehouseManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4FD9BBB2F8912D0DF072B8B3360AE1A0F57989AA"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// WarehouseManagementView
    /// </summary>
    public partial class WarehouseManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 101 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWarehouseCode;
        
        #line default
        #line hidden
        
        
        #line 116 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWarehouseName;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWarehouseLocation;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWarehouseDescription;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnGenerateCode;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddWarehouse;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearForm;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\WarehouseManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid WarehousesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/warehousemanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\WarehouseManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtWarehouseCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TxtWarehouseName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtWarehouseLocation = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TxtWarehouseDescription = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.BtnGenerateCode = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\..\Views\WarehouseManagementView.xaml"
            this.BtnGenerateCode.Click += new System.Windows.RoutedEventHandler(this.BtnGenerateCode_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnAddWarehouse = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\..\Views\WarehouseManagementView.xaml"
            this.BtnAddWarehouse.Click += new System.Windows.RoutedEventHandler(this.BtnAddWarehouse_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnClearForm = ((System.Windows.Controls.Button)(target));
            
            #line 175 "..\..\..\..\Views\WarehouseManagementView.xaml"
            this.BtnClearForm.Click += new System.Windows.RoutedEventHandler(this.BtnClearForm_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 193 "..\..\..\..\Views\WarehouseManagementView.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.WarehousesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 10:
            
            #line 222 "..\..\..\..\Views\WarehouseManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditWarehouse_Click);
            
            #line default
            #line hidden
            break;
            case 11:
            
            #line 227 "..\..\..\..\Views\WarehouseManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteWarehouse_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

