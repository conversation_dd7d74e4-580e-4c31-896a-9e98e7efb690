using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة مركزية لإدارة فروع الشركة والمسؤولين
    /// تستخدم من جميع الموديولات في النظام
    /// </summary>
    public class CompanyManagementService
    {
        private readonly DatabaseContext _context;

        public CompanyManagementService()
        {
            _context = new DatabaseContext();

            // تهيئة قاعدة البيانات بشكل متزامن
            try
            {
                var task = _context.InitializeDatabaseAsync();
                task.Wait();
                System.Diagnostics.Debug.WriteLine("Database initialized in CompanyManagementService");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing database in CompanyManagementService: {ex.Message}");
            }

            InitializeTables();
        }

        #region Database Initialization



        /// <summary>
        /// إنشاء الجداول المطلوبة
        /// </summary>
        private void InitializeTables()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // إنشاء جدول فروع الشركة
                var createCompanyBranchesTable = @"
                    CREATE TABLE IF NOT EXISTS CompanyBranches (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Address TEXT,
                        Phone TEXT,
                        Manager TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT
                    )";

                // إنشاء جدول المسؤولين
                var createResponsiblePersonsTable = @"
                    CREATE TABLE IF NOT EXISTS ResponsiblePersons (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Position TEXT,
                        Department TEXT,
                        Phone TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT
                    )";

                using var command1 = new SQLiteCommand(createCompanyBranchesTable, connection);
                command1.ExecuteNonQuery();

                // ملاحظة: جدول ResponsiblePersons يتم إنشاؤه بواسطة DatabaseContext
                // using var command2 = new SQLiteCommand(createResponsiblePersonsTable, connection);
                // command2.ExecuteNonQuery();

                // إدراج البيانات الافتراضية
                InsertDefaultData(connection);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing CompanyManagement tables: {ex.Message}");
            }
        }

        /// <summary>
        /// إدراج البيانات الافتراضية
        /// </summary>
        private void InsertDefaultData(SQLiteConnection connection)
        {
            try
            {
                // إدراج المسؤولين الافتراضيين
                var defaultResponsiblePersons = new[]
                {
                    new { Name = "مدير عام", Position = "مدير عام", Department = "الإدارة العليا" },
                    new { Name = "مدير مالي", Position = "مدير مالي", Department = "المالية" },
                    new { Name = "مدير مشتريات", Position = "مدير مشتريات", Department = "المشتريات" },
                    new { Name = "مدير مبيعات", Position = "مدير مبيعات", Department = "المبيعات" },
                    new { Name = "مدير إنتاج", Position = "مدير إنتاج", Department = "الإنتاج" }
                };

                foreach (var person in defaultResponsiblePersons)
                {
                    var checkQuery = "SELECT COUNT(*) FROM ResponsiblePersons WHERE Name = @name";
                    using var checkCommand = new SQLiteCommand(checkQuery, connection);
                    checkCommand.Parameters.AddWithValue("@name", person.Name);

                    var count = Convert.ToInt32(checkCommand.ExecuteScalar());
                    if (count == 0)
                    {
                        var insertQuery = @"
                            INSERT INTO ResponsiblePersons (Name, Position, Department, IsActive, CreatedDate, CreatedBy)
                            VALUES (@name, @position, @department, 1, @createdDate, @createdBy)";

                        using var insertCommand = new SQLiteCommand(insertQuery, connection);
                        insertCommand.Parameters.AddWithValue("@name", person.Name);
                        insertCommand.Parameters.AddWithValue("@position", person.Position);
                        insertCommand.Parameters.AddWithValue("@department", person.Department);
                        insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        insertCommand.Parameters.AddWithValue("@createdBy", "النظام");
                        insertCommand.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error inserting default data: {ex.Message}");
            }
        }

        #endregion

        #region Company Branches Management

        /// <summary>
        /// جلب جميع فروع الشركة
        /// </summary>
        public List<CompanyBranch> GetAllCompanyBranches()
        {
            var branches = new List<CompanyBranch>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var selectCommand = @"
                    SELECT Id, Name, Address, Phone, Manager, IsActive, 
                           CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                    FROM CompanyBranches 
                    WHERE IsActive = 1 
                    ORDER BY Name";

                using var command = new SQLiteCommand(selectCommand, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    branches.Add(new CompanyBranch
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"]?.ToString() ?? "",
                        Address = reader["Address"]?.ToString(),
                        Phone = reader["Phone"]?.ToString(),
                        Manager = reader["Manager"]?.ToString(),
                        IsActive = Convert.ToInt32(reader["IsActive"]) == 1,
                        CreatedDate = DateTime.Parse(reader["CreatedDate"]?.ToString() ?? DateTime.Now.ToString()),
                        CreatedBy = reader["CreatedBy"]?.ToString(),
                        ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? 
                            DateTime.Parse(reader["ModifiedDate"].ToString()) : null,
                        ModifiedBy = reader["ModifiedBy"]?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting company branches: {ex.Message}");
            }

            return branches;
        }

        /// <summary>
        /// إضافة فرع جديد
        /// </summary>
        public bool AddCompanyBranch(CompanyBranch branch)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var insertCommand = @"
                    INSERT INTO CompanyBranches (Name, Address, Phone, Manager, IsActive, CreatedDate, CreatedBy)
                    VALUES (@Name, @Address, @Phone, @Manager, @IsActive, @CreatedDate, @CreatedBy)";

                using var command = new SQLiteCommand(insertCommand, connection);
                command.Parameters.AddWithValue("@Name", branch.Name);
                command.Parameters.AddWithValue("@Address", branch.Address ?? "");
                command.Parameters.AddWithValue("@Phone", branch.Phone ?? "");
                command.Parameters.AddWithValue("@Manager", branch.Manager ?? "");
                command.Parameters.AddWithValue("@IsActive", branch.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@CreatedDate", branch.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@CreatedBy", branch.CreatedBy ?? "");

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error adding company branch: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحديث فرع موجود
        /// </summary>
        public bool UpdateCompanyBranch(CompanyBranch branch)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var updateCommand = @"
                    UPDATE CompanyBranches 
                    SET Name = @Name, Address = @Address, Phone = @Phone, Manager = @Manager,
                        IsActive = @IsActive, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(updateCommand, connection);
                command.Parameters.AddWithValue("@Id", branch.Id);
                command.Parameters.AddWithValue("@Name", branch.Name);
                command.Parameters.AddWithValue("@Address", branch.Address ?? "");
                command.Parameters.AddWithValue("@Phone", branch.Phone ?? "");
                command.Parameters.AddWithValue("@Manager", branch.Manager ?? "");
                command.Parameters.AddWithValue("@IsActive", branch.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@ModifiedBy", branch.ModifiedBy ?? "");

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating company branch: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف فرع (حذف منطقي)
        /// </summary>
        public bool DeleteCompanyBranch(int branchId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var deleteCommand = @"
                    UPDATE CompanyBranches 
                    SET IsActive = 0, ModifiedDate = @ModifiedDate 
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(deleteCommand, connection);
                command.Parameters.AddWithValue("@Id", branchId);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting company branch: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب أسماء الفروع فقط (للاستخدام في ComboBox)
        /// </summary>
        public List<string> GetCompanyBranchNames()
        {
            return GetAllCompanyBranches().Select(b => b.Name).ToList();
        }

        #endregion

        #region Responsible Persons Management

        /// <summary>
        /// جلب جميع المسؤولين
        /// </summary>
        public List<ResponsiblePerson> GetAllResponsiblePersons()
        {
            var persons = new List<ResponsiblePerson>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var selectCommand = @"
                    SELECT Id, Name, Position, Department, Phone, IsActive, 
                           CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
                    FROM ResponsiblePersons 
                    WHERE IsActive = 1 
                    ORDER BY Name";

                using var command = new SQLiteCommand(selectCommand, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var person = new ResponsiblePerson
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"]?.ToString() ?? "",
                        Position = reader["Position"]?.ToString() ?? "",
                        Department = reader["Department"]?.ToString() ?? "",
                        Phone = reader["Phone"]?.ToString() ?? "",
                        Email = "", // قاعدة البيانات لا تحتوي على Email
                        Notes = "", // قاعدة البيانات لا تحتوي على Notes
                        IsActive = Convert.ToInt32(reader["IsActive"]) == 1,
                        CreatedBy = reader["CreatedBy"]?.ToString(),
                        ModifiedBy = reader["ModifiedBy"]?.ToString()
                    };

                    // تحويل التواريخ بأمان
                    if (DateTime.TryParse(reader["CreatedDate"]?.ToString(), out DateTime createdDate))
                    {
                        person.CreatedDate = createdDate;
                    }
                    else
                    {
                        person.CreatedDate = DateTime.Now;
                    }

                    if (reader["ModifiedDate"] != DBNull.Value &&
                        DateTime.TryParse(reader["ModifiedDate"]?.ToString(), out DateTime modifiedDate))
                    {
                        person.ModifiedDate = modifiedDate;
                    }

                    persons.Add(person);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting responsible persons: {ex.Message}");
            }

            return persons;
        }

        /// <summary>
        /// إضافة مسؤول جديد
        /// </summary>
        public bool AddResponsiblePerson(ResponsiblePerson person)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(person.Name))
                {
                    System.Diagnostics.Debug.WriteLine("Error: ResponsiblePerson Name is required");
                    return false;
                }

                // التحقق من عدم تكرار الاسم
                if (IsPersonNameExists(person.Name))
                {
                    System.Diagnostics.Debug.WriteLine($"Error: ResponsiblePerson with name '{person.Name}' already exists");
                    return false;
                }

                using var connection = _context.GetConnection();
                connection.Open();

                var insertCommand = @"
                    INSERT INTO ResponsiblePersons (Name, Position, Department, Phone, IsActive, CreatedDate, CreatedBy)
                    VALUES (@Name, @Position, @Department, @Phone, @IsActive, @CreatedDate, @CreatedBy)";

                using var command = new SQLiteCommand(insertCommand, connection);
                command.Parameters.AddWithValue("@Name", person.Name.Trim());
                command.Parameters.AddWithValue("@Position", person.Position?.Trim() ?? "");
                command.Parameters.AddWithValue("@Department", person.Department?.Trim() ?? "");
                command.Parameters.AddWithValue("@Phone", person.Phone?.Trim() ?? "");
                command.Parameters.AddWithValue("@IsActive", person.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@CreatedDate", person.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@CreatedBy", person.CreatedBy?.Trim() ?? "");

                var result = command.ExecuteNonQuery();

                if (result > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"Successfully added responsible person: {person.Name}");
                    return true;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Failed to add responsible person: No rows affected");
                    return false;
                }
            }
            catch (SQLiteException sqlEx)
            {
                System.Diagnostics.Debug.WriteLine($"SQLite Error adding responsible person: {sqlEx.Message}");
                System.Diagnostics.Debug.WriteLine($"SQLite Error Code: {sqlEx.ErrorCode}");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"General Error adding responsible person: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack Trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// تحديث مسؤول موجود
        /// </summary>
        public bool UpdateResponsiblePerson(ResponsiblePerson person)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var updateCommand = @"
                    UPDATE ResponsiblePersons
                    SET Name = @Name, Position = @Position, Department = @Department, Phone = @Phone,
                        IsActive = @IsActive, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(updateCommand, connection);
                command.Parameters.AddWithValue("@Id", person.Id);
                command.Parameters.AddWithValue("@Name", person.Name);
                command.Parameters.AddWithValue("@Position", person.Position ?? "");
                command.Parameters.AddWithValue("@Department", person.Department ?? "");
                command.Parameters.AddWithValue("@Phone", person.Phone ?? "");
                command.Parameters.AddWithValue("@IsActive", person.IsActive ? 1 : 0);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@ModifiedBy", person.ModifiedBy ?? "");

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating responsible person: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حذف مسؤول (حذف منطقي)
        /// </summary>
        public bool DeleteResponsiblePerson(int personId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var deleteCommand = @"
                    UPDATE ResponsiblePersons
                    SET IsActive = 0, ModifiedDate = @ModifiedDate
                    WHERE Id = @Id";

                using var command = new SQLiteCommand(deleteCommand, connection);
                command.Parameters.AddWithValue("@Id", personId);
                command.Parameters.AddWithValue("@ModifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting responsible person: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب أسماء المسؤولين فقط (للاستخدام في ComboBox)
        /// </summary>
        public List<string> GetResponsiblePersonNames()
        {
            return GetAllResponsiblePersons().Select(p => p.Name).ToList();
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// التحقق من وجود فرع بنفس الاسم
        /// </summary>
        public bool IsBranchNameExists(string branchName, int excludeId = 0)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var checkCommand = @"
                    SELECT COUNT(*) FROM CompanyBranches
                    WHERE Name = @Name AND IsActive = 1 AND Id != @ExcludeId";

                using var command = new SQLiteCommand(checkCommand, connection);
                command.Parameters.AddWithValue("@Name", branchName);
                command.Parameters.AddWithValue("@ExcludeId", excludeId);

                return Convert.ToInt32(command.ExecuteScalar()) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking branch name: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// التحقق من وجود مسؤول بنفس الاسم
        /// </summary>
        public bool IsPersonNameExists(string personName, int excludeId = 0)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var checkCommand = @"
                    SELECT COUNT(*) FROM ResponsiblePersons
                    WHERE Name = @Name AND IsActive = 1 AND Id != @ExcludeId";

                using var command = new SQLiteCommand(checkCommand, connection);
                command.Parameters.AddWithValue("@Name", personName);
                command.Parameters.AddWithValue("@ExcludeId", excludeId);

                return Convert.ToInt32(command.ExecuteScalar()) > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error checking person name: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
