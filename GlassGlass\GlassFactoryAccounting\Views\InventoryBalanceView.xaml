<UserControl x:Class="GlassFactoryAccounting.Views.InventoryBalanceView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="📈" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                <StackPanel>
                    <TextBlock Text="الأصناف المتوفرة بالمخازن" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                    <TextBlock Text="أرصدة المخزون والأسعار بنظام المتوسط المتحرك" FontSize="14" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- جدول الأرصدة -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,15">
                    <TextBlock Text="📋 أرصدة المخزون" FontSize="18" FontWeight="Bold" VerticalAlignment="Center"/>
                    <Button x:Name="BtnRefresh" Content="🔄 تحديث" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Margin="20,0,0,0" Click="BtnRefresh_Click"/>
                    <Button x:Name="BtnExportExcel" Content="📊 تصدير Excel" 
                            Style="{StaticResource PrimaryButtonStyle}"
                            Background="#4CAF50" Margin="10,0,0,0" Click="BtnExportExcel_Click"/>
                </StackPanel>

                <DataGrid Grid.Row="1" x:Name="BalancesDataGrid" 
                          AutoGenerateColumns="False" 
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          Background="White"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الصنف" Binding="{Binding ItemName}" Width="100"/>
                        <DataGridTextColumn Header="المخزن" Binding="{Binding WarehouseName}" Width="80"/>
                        <DataGridTextColumn Header="وحدة القياس" Binding="{Binding UnitOfMeasure}" Width="70"/>
                        <DataGridTextColumn Header="محتوى الصندوق" Binding="{Binding BoxContent, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="الطول (مم)" Binding="{Binding Length}" Width="70"/>
                        <DataGridTextColumn Header="العرض (مم)" Binding="{Binding Width}" Width="70"/>
                        <DataGridTextColumn Header="المتر المربع" Binding="{Binding Area, StringFormat=F4}" Width="80"/>
                        <DataGridTextColumn Header="العدد" Binding="{Binding Units, StringFormat=F2}" Width="60"/>
                        <DataGridTextColumn Header="كمية الوارد" Binding="{Binding TotalReceived, StringFormat=F2}" Width="70"/>
                        <DataGridTextColumn Header="كمية المنصرف" Binding="{Binding TotalIssued, StringFormat=F2}" Width="70"/>
                        <DataGridTextColumn Header="رصيد الكمية" Binding="{Binding CurrentBalance, StringFormat=F2}" Width="70"/>
                        <DataGridTextColumn Header="عدد الصناديق" Binding="{Binding BoxCount, StringFormat=F2}" Width="80"/>
                        <DataGridTextColumn Header="متوسط السعر" Binding="{Binding AveragePrice, StringFormat=F2}" Width="70"/>
                        <DataGridTextColumn Header="قيمة الرصيد" Binding="{Binding BalanceValue, StringFormat=F2}" Width="90"/>
                    </DataGrid.Columns>


                </DataGrid>
            </Grid>
        </Border>

        <!-- الإحصائيات الإجمالية -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Border Grid.Column="0" Background="#E3F2FD" CornerRadius="8" Padding="15" Margin="0,0,10,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="عدد الأصناف" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TxtTotalItems" Text="0" FontSize="20" Foreground="#1976D2" 
                                   FontWeight="Bold" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Grid.Column="1" Background="#E8F5E8" CornerRadius="8" Padding="15" Margin="5,0,5,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="إجمالي قيمة المخزون" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TxtTotalValue" Text="0.00" FontSize="20" Foreground="#388E3C" 
                                   FontWeight="Bold" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Grid.Column="2" Background="#FFF3E0" CornerRadius="8" Padding="15" Margin="5,0,5,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="متوسط قيمة الصنف" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TxtAverageValue" Text="0.00" FontSize="20" Foreground="#F57C00" 
                                   FontWeight="Bold" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <Border Grid.Column="3" Background="#FFEBEE" CornerRadius="8" Padding="15" Margin="10,0,0,0">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="أصناف منخفضة المخزون" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center"/>
                        <TextBlock x:Name="TxtLowStockItems" Text="0" FontSize="20" Foreground="#D32F2F" 
                                   FontWeight="Bold" HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>
            </Grid>
        </Border>
    </Grid>
</UserControl>
