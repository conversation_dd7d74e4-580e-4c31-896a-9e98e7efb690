using System;
using System.ComponentModel.DataAnnotations;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج التقرير الكامل للموظف
    /// </summary>
    public class EmployeeFullReport
    {
        public int EmployeeId { get; set; }
        public string EmployeeCode { get; set; } = string.Empty;
        public string EmployeeName { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public decimal BasicSalary { get; set; }
        public decimal TotalSalaryDue { get; set; }
        public decimal TotalAdvances { get; set; }
        public decimal TotalBonuses { get; set; }
        public decimal TotalDeductions { get; set; }
        public decimal TotalOvertime { get; set; }
        public decimal TotalPayments { get; set; }
        public decimal RemainingBalance { get; set; }
    }

    /// <summary>
    /// نموذج تفاصيل الشيك
    /// </summary>
    public class CheckDetails
    {
        public decimal SalaryPaid { get; set; }
        public decimal BonusAmount { get; set; }
        public decimal OvertimeAmount { get; set; }
        public decimal TotalPaid { get; set; }
        public decimal RemainingSalary { get; set; }
        public decimal RemainingAdvances { get; set; }
        public decimal RemainingBonuses { get; set; }
        public decimal TotalRemaining { get; set; }
    }

    /// <summary>
    /// نموذج المسؤولين عن السداد
    /// </summary>
    public class ResponsiblePerson
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Position { get; set; } = string.Empty;
        public string Department { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public string Notes { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; } = DateTime.Now;
        public string? CreatedBy { get; set; }
        public DateTime? ModifiedDate { get; set; }
        public string? ModifiedBy { get; set; }
    }
}
