using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة تسجيل الوقت الإضافي للموظف
    /// </summary>
    public partial class EmployeeOvertimeView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;
        private Employee? _selectedEmployee;

        public EmployeeOvertimeView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadOvertime();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ اليوم كافتراضي
            DpOvertimeDate.SelectedDate = DateTime.Now;
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.ItemsSource = employees;
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadOvertime()
        {
            try
            {
                var overtimes = _payrollService.GetAllEmployeeOvertime();
                OvertimeDataGrid.ItemsSource = overtimes;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الوقت الإضافي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbEmployee_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbEmployee.SelectedItem is Employee selectedEmployee)
            {
                _selectedEmployee = selectedEmployee;
                TxtEmployeeCode.Text = selectedEmployee.EmployeeCode;
                TxtPosition.Text = selectedEmployee.Position;
                TxtBranch.Text = selectedEmployee.Branch;
                TxtHourlyRate.Text = selectedEmployee.HourlyRate.ToString("N2");

                // إعادة حساب قيمة الوقت الإضافي إذا كان هناك ساعات مدخلة
                if (!string.IsNullOrEmpty(TxtOvertimeHours.Text))
                {
                    CalculateOvertimeAmount();
                }
            }
            else
            {
                _selectedEmployee = null;
                ClearEmployeeFields();
            }
        }

        private void ClearEmployeeFields()
        {
            TxtEmployeeCode.Clear();
            TxtPosition.Clear();
            TxtBranch.Clear();
            TxtHourlyRate.Clear();
        }

        private void TxtOvertimeHours_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التحقق من صحة عدد الساعات المدخلة
            if (sender is TextBox textBox)
            {
                if (!string.IsNullOrEmpty(textBox.Text))
                {
                    if (!decimal.TryParse(textBox.Text, out _))
                    {
                        // إزالة الأحرف غير الصحيحة
                        int caretIndex = textBox.CaretIndex;
                        textBox.Text = System.Text.RegularExpressions.Regex.Replace(textBox.Text, @"[^\d.]", "");
                        textBox.CaretIndex = Math.Min(caretIndex, textBox.Text.Length);
                    }
                    else
                    {
                        // حساب قيمة الوقت الإضافي تلقائياً
                        CalculateOvertimeAmount();
                    }
                }
            }
        }

        private void TxtOvertimeAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التحقق من صحة المبلغ المدخل
            if (sender is TextBox textBox)
            {
                if (!string.IsNullOrEmpty(textBox.Text))
                {
                    if (!decimal.TryParse(textBox.Text, out _))
                    {
                        // إزالة الأحرف غير الصحيحة
                        int caretIndex = textBox.CaretIndex;
                        textBox.Text = System.Text.RegularExpressions.Regex.Replace(textBox.Text, @"[^\d.]", "");
                        textBox.CaretIndex = Math.Min(caretIndex, textBox.Text.Length);
                    }
                }
            }
        }

        private void BtnCalculateAuto_Click(object sender, RoutedEventArgs e)
        {
            CalculateOvertimeAmount();
        }

        private void CalculateOvertimeAmount()
        {
            if (_selectedEmployee != null &&
                !string.IsNullOrEmpty(TxtOvertimeHours.Text) &&
                decimal.TryParse(TxtOvertimeHours.Text, out decimal hours))
            {
                // تطبيق المعادلة: (عدد الساعات × أجر الساعة) + (عدد الساعات × 0.5 × أجر الساعة)
                var calculatedAmount = _payrollService.CalculateOvertimeAmount(_selectedEmployee, hours);
                TxtOvertimeAmount.Text = calculatedAmount.ToString("N2");
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var selectedEmployee = CmbEmployee.SelectedItem as Employee;
                if (selectedEmployee == null)
                {
                    MessageBox.Show("يرجى اختيار موظف.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var overtime = new EmployeeOvertime
                {
                    EmployeeId = selectedEmployee.Id,
                    EmployeeName = selectedEmployee.Name,
                    EmployeeCode = selectedEmployee.EmployeeCode,
                    OvertimeHours = decimal.Parse(TxtOvertimeHours.Text),
                    OvertimeAmount = decimal.Parse(TxtOvertimeAmount.Text),
                    OvertimeDate = DpOvertimeDate.SelectedDate ?? DateTime.Now,
                    Notes = TxtNotes.Text.Trim()
                };

                if (_payrollService.AddEmployeeOvertime(overtime))
                {
                    MessageBox.Show("تم حفظ الوقت الإضافي بنجاح!", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    ClearForm();
                    LoadOvertime();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ الوقت الإضافي!", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الوقت الإضافي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbEmployee.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbEmployee.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtOvertimeHours.Text))
            {
                MessageBox.Show("يرجى إدخال عدد الساعات الإضافية", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtOvertimeHours.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtOvertimeHours.Text, out decimal hours) || hours <= 0)
            {
                MessageBox.Show("يرجى إدخال عدد صحيح للساعات الإضافية", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtOvertimeHours.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtOvertimeAmount.Text))
            {
                MessageBox.Show("يرجى إدخال قيمة الوقت الإضافي", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtOvertimeAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtOvertimeAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للوقت الإضافي", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtOvertimeAmount.Focus();
                return false;
            }

            if (!DpOvertimeDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ التسجيل", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpOvertimeDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            CmbEmployee.SelectedIndex = -1;
            _selectedEmployee = null;
            ClearEmployeeFields();
            TxtOvertimeHours.Clear();
            TxtOvertimeAmount.Clear();
            DpOvertimeDate.SelectedDate = DateTime.Now;
            TxtNotes.Clear();
            CmbEmployee.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeleteOvertime_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int overtimeId)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذا الوقت الإضافي؟", "تأكيد الحذف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (_payrollService.DeleteEmployeeOvertime(overtimeId))
                        {
                            MessageBox.Show("تم حذف الوقت الإضافي بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadOvertime();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف الوقت الإضافي!", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الوقت الإضافي: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditOvertime_Click(object sender, RoutedEventArgs e)
        {
            // TODO: تنفيذ منطق التعديل هنا حسب الحاجة
            MessageBox.Show("ميزة التعديل غير مفعلة حالياً. يرجى استكمال منطق التعديل إذا لزم.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
