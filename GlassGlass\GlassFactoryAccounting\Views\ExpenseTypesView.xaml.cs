using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class ExpenseTypesView : UserControl
    {
        private readonly ExpenseService _expenseService;
        private List<ExpenseType> _expenseTypes;

        public ExpenseTypesView()
        {
            InitializeComponent();
            _expenseService = new ExpenseService();
            _expenseTypes = new List<ExpenseType>();
            LoadExpenseTypes();
        }

        private void LoadExpenseTypes()
        {
            try
            {
                var expenseCategories = _expenseService.GetAllExpenseCategories();

                // تحويل ExpenseCategory إلى ExpenseType للعرض
                _expenseTypes = expenseCategories.Select(ec => new ExpenseType
                {
                    Id = ec.Id,
                    Name = $"{ec.MainExpenseName} - {ec.SubExpenseName}",
                    Description = ec.Notes,
                    ParentType = ec.MainExpenseName,
                    IsActive = true,
                    CreatedDate = ec.CreatedDate,
                    CreatedBy = ec.CreatedBy
                }).ToList();

                DgExpenseTypes.ItemsSource = _expenseTypes;

                // تحديث قائمة الأنواع الأب
                var parentTypes = expenseCategories.Select(ec => ec.MainExpenseName).Distinct().ToList();
                parentTypes.Insert(0, ""); // إضافة خيار فارغ
                CmbParentType.ItemsSource = parentTypes;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أنواع المصروفات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnAddType_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var expenseCategory = new ExpenseCategory
                {
                    MainExpenseName = CmbParentType.SelectedItem?.ToString() ?? TxtTypeName.Text.Trim(),
                    SubExpenseName = TxtTypeName.Text.Trim(),
                    Notes = TxtDescription.Text.Trim(),
                    IsProjectRelated = false,
                    CreatedBy = "المستخدم الحالي"
                };

                var result = _expenseService.AddExpenseCategory(expenseCategory);

                if (result)
                {
                    MessageBox.Show("تم إضافة نوع المصروف بنجاح!", "نجح الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    ClearForm();
                    LoadExpenseTypes();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة نوع المصروف!", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة نوع المصروف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtTypeName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم النوع", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtTypeName.Focus();
                return false;
            }

            // التحقق من عدم تكرار الاسم
            if (_expenseTypes.Any(et => et.Name.Equals(TxtTypeName.Text.Trim(), StringComparison.OrdinalIgnoreCase)))
            {
                MessageBox.Show("اسم النوع موجود بالفعل", "بيانات مكررة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtTypeName.Focus();
                return false;
            }

            return true;
        }

        private void BtnClearForm_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            TxtTypeName.Clear();
            TxtDescription.Clear();
            CmbParentType.SelectedIndex = -1;
        }

        private void BtnEdit_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseType expenseType)
            {
                // تعبئة النموذج بالبيانات للتعديل
                TxtTypeName.Text = expenseType.Name;
                TxtDescription.Text = expenseType.Description ?? "";
                
                if (!string.IsNullOrEmpty(expenseType.ParentType))
                {
                    CmbParentType.SelectedItem = expenseType.ParentType;
                }
                else
                {
                    CmbParentType.SelectedIndex = 0; // الخيار الفارغ
                }

                // يمكن إضافة منطق التعديل هنا
                MessageBox.Show($"تم تحديد النوع: {expenseType.Name} للتعديل\nيمكنك الآن تعديل البيانات والضغط على إضافة النوع", 
                    "تعديل النوع", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is ExpenseType expenseType)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف نوع المصروف: {expenseType.Name}؟", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        var deleteResult = _expenseService.DeleteExpenseCategory(expenseType.Id);
                        
                        if (deleteResult)
                        {
                            MessageBox.Show("تم حذف نوع المصروف بنجاح!", "نجح الحذف", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadExpenseTypes();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف نوع المصروف!", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف نوع المصروف: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
