using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class WarehouseManagementView : UserControl
    {
        private readonly WarehouseService _warehouseService;
        private ObservableCollection<Warehouse> _warehouses;

        public WarehouseManagementView()
        {
            InitializeComponent();
            _warehouseService = new WarehouseService();
            _warehouses = new ObservableCollection<Warehouse>();
            WarehousesDataGrid.ItemsSource = _warehouses;
            LoadWarehouses();
            GenerateNewCode();
        }

        private async void LoadWarehouses()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                _warehouses.Clear();
                foreach (var warehouse in warehouses)
                {
                    _warehouses.Add(warehouse);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المخازن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void GenerateNewCode()
        {
            try
            {
                var warehouses = await _warehouseService.GetAllWarehousesAsync();
                var lastCode = warehouses
                    .Where(w => !string.IsNullOrEmpty(w.Code) && w.Code.StartsWith("WH"))
                    .Select(w => w.Code)
                    .OrderByDescending(c => c)
                    .FirstOrDefault();

                if (lastCode != null && lastCode.Length > 2)
                {
                    var numberPart = lastCode.Substring(2);
                    if (int.TryParse(numberPart, out int lastNumber))
                    {
                        TxtWarehouseCode.Text = $"WH{(lastNumber + 1):D4}";
                        return;
                    }
                }

                TxtWarehouseCode.Text = "WH0001";
            }
            catch
            {
                TxtWarehouseCode.Text = "WH0001";
            }
        }

        private async void BtnGenerateCode_Click(object sender, RoutedEventArgs e)
        {
            GenerateNewCode();
        }

        private void BtnClearForm_Click(object sender, RoutedEventArgs e)
        {
            TxtWarehouseName.Clear();
            TxtWarehouseLocation.Clear();
            TxtWarehouseDescription.Clear();
            GenerateNewCode();
        }

        private async void BtnAddWarehouse_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(TxtWarehouseName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المخزن", "تنبيه",
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var warehouse = new Warehouse
                {
                    Code = TxtWarehouseCode.Text.Trim(),
                    Name = TxtWarehouseName.Text.Trim(),
                    Location = TxtWarehouseLocation.Text.Trim(),
                    Description = TxtWarehouseDescription.Text.Trim()
                };

                var success = await _warehouseService.SaveWarehouseAsync(warehouse);
                if (success)
                {
                    MessageBox.Show("تم إضافة المخزن بنجاح", "نجح",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // مسح النموذج
                    TxtWarehouseName.Clear();
                    TxtWarehouseLocation.Clear();
                    TxtWarehouseDescription.Clear();
                    GenerateNewCode();

                    // تحديث القائمة
                    LoadWarehouses();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة المخزن", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المخزن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnEditWarehouse_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Warehouse warehouse)
                {
                    var editWindow = new WarehouseEditWindow(warehouse);
                    editWindow.Owner = Window.GetWindow(this);
                    
                    if (editWindow.ShowDialog() == true)
                    {
                        LoadWarehouses();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل المخزن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnDeleteWarehouse_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is Warehouse warehouse)
                {
                    var result = MessageBox.Show($"هل أنت متأكد من حذف المخزن '{warehouse.Name}'؟", 
                        "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);
                    
                    if (result == MessageBoxResult.Yes)
                    {
                        var success = await _warehouseService.DeleteWarehouseAsync(warehouse.Id);
                        if (success)
                        {
                            MessageBox.Show("تم حذف المخزن بنجاح", "نجح", 
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadWarehouses();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف المخزن", "خطأ", 
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المخزن: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadWarehouses();
        }
    }


}
