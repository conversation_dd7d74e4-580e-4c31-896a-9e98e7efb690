﻿#pragma checksum "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F9229836BAA7BD3ADEBF2426B79948D2ED559CC0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views.Accounting {
    
    
    /// <summary>
    /// ChartOfAccountsWindow
    /// </summary>
    public partial class ChartOfAccountsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 34 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnAddAccount;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnEditAccount;
        
        #line default
        #line hidden
        
        
        #line 44 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeleteAccount;
        
        #line default
        #line hidden
        
        
        #line 49 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefresh;
        
        #line default
        #line hidden
        
        
        #line 60 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TreeView tvAccounts;
        
        #line default
        #line hidden
        
        
        #line 91 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSelectedAccountCode;
        
        #line default
        #line hidden
        
        
        #line 96 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSelectedAccountName;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSelectedAccountType;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtSelectedAccountBalance;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/GlassFactoryAccounting;V1.0.0.0;component/views/accounting/chartofaccountswindow" +
                    ".xaml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnAddAccount = ((System.Windows.Controls.Button)(target));
            
            #line 37 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
            this.btnAddAccount.Click += new System.Windows.RoutedEventHandler(this.BtnAddAccount_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.btnEditAccount = ((System.Windows.Controls.Button)(target));
            
            #line 42 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
            this.btnEditAccount.Click += new System.Windows.RoutedEventHandler(this.BtnEditAccount_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.btnDeleteAccount = ((System.Windows.Controls.Button)(target));
            
            #line 47 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
            this.btnDeleteAccount.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteAccount_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\..\Views\Accounting\ChartOfAccountsWindow.xaml"
            this.btnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.tvAccounts = ((System.Windows.Controls.TreeView)(target));
            return;
            case 6:
            this.txtSelectedAccountCode = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.txtSelectedAccountName = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.txtSelectedAccountType = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.txtSelectedAccountBalance = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

