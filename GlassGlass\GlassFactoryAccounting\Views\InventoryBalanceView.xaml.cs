using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class InventoryBalanceView : UserControl
    {
        private readonly InventoryService _inventoryService;
        private ObservableCollection<InventoryBalance> _balances;

        public InventoryBalanceView()
        {
            InitializeComponent();
            _inventoryService = new InventoryService();
            _balances = new ObservableCollection<InventoryBalance>();
            
            BalancesDataGrid.ItemsSource = _balances;
            
            LoadBalances();
        }

        private async void LoadBalances()
        {
            try
            {
                var balances = await _inventoryService.GetInventoryBalancesAsync();
                _balances.Clear();
                foreach (var balance in balances)
                {
                    _balances.Add(balance);
                }

                UpdateStatistics(balances);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل أرصدة المخزون: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics(List<InventoryBalance> balances)
        {
            try
            {
                var totalItems = balances.Count;
                var totalValue = balances.Sum(b => b.BalanceValue);
                var averageValue = totalItems > 0 ? totalValue / totalItems : 0;
                var lowStockItems = balances.Count(b => b.CurrentBalance < 10); // أقل من 10 وحدات

                TxtTotalItems.Text = totalItems.ToString();
                TxtTotalValue.Text = totalValue.ToString("F2");
                TxtAverageValue.Text = averageValue.ToString("F2");
                TxtLowStockItems.Text = lowStockItems.ToString();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب الإحصائيات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BtnRefresh_Click(object sender, RoutedEventArgs e)
        {
            LoadBalances();
        }

        private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("ميزة التصدير إلى Excel ستكون متاحة قريباً", "معلومات",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
