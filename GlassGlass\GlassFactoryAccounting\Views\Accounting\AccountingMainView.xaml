<UserControl x:Class="GlassFactoryAccounting.Views.Accounting.AccountingMainView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1000"
             FontFamily="Segoe UI"
             FlowDirection="RightToLeft">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="🏦 النظام المحاسبي - اليومية الأمريكية" 
                   FontSize="28" FontWeight="Bold" 
                   Foreground="#2C3E50" 
                   HorizontalAlignment="Center" 
                   Margin="0,0,0,30"/>
        
        <!-- الوصف -->
        <TextBlock Grid.Row="1" 
                   Text="نظام محاسبي متكامل يعتمد على اليومية الأمريكية مع القيد المزدوج وشجرة الحسابات الهرمية"
                   FontSize="16" 
                   Foreground="#7F8C8D" 
                   HorizontalAlignment="Center" 
                   TextWrapping="Wrap"
                   Margin="0,0,0,30"/>
        
        <!-- الأزرار الرئيسية -->
        <ScrollViewer Grid.Row="2" VerticalScrollBarVisibility="Auto">
            <UniformGrid Columns="3" Margin="20">
                
                <!-- شجرة الحسابات -->
                <Border Background="#3498DB" CornerRadius="15" Margin="10"
                        Cursor="Hand" MouseLeftButtonUp="ChartOfAccounts_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="🌳" FontSize="48" HorizontalAlignment="Center" 
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="شجرة الحسابات" FontSize="18" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="إدارة الحسابات الرئيسية والفرعية" FontSize="12" 
                                   HorizontalAlignment="Center" Foreground="White" 
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>
                
                <!-- قيد اليومية -->
                <Border Background="#27AE60" CornerRadius="15" Margin="10" 
                        Cursor="Hand" MouseLeftButtonUp="JournalEntry_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="📝" FontSize="48" HorizontalAlignment="Center" 
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="قيد اليومية" FontSize="18" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="إنشاء وإدارة قيود اليومية" FontSize="12" 
                                   HorizontalAlignment="Center" Foreground="White" 
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>
                
                <!-- دفتر الأستاذ -->
                <Border Background="#E74C3C" CornerRadius="15" Margin="10" 
                        Cursor="Hand" MouseLeftButtonUp="GeneralLedger_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="📚" FontSize="48" HorizontalAlignment="Center"
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="دفتر الأستاذ العام" FontSize="18" FontWeight="Bold"
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="عرض حركة الحسابات والأرصدة" FontSize="12"
                                   HorizontalAlignment="Center" Foreground="White"
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- ميزان المراجعة -->
                <Border Background="#9B59B6" CornerRadius="15" Margin="10"
                        Cursor="Hand" MouseLeftButtonUp="TrialBalance_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="⚖️" FontSize="48" HorizontalAlignment="Center"
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="ميزان المراجعة" FontSize="18" FontWeight="Bold"
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="التحقق من توازن الحسابات" FontSize="12"
                                   HorizontalAlignment="Center" Foreground="White"
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- قائمة الدخل -->
                <Border Background="#F39C12" CornerRadius="15" Margin="10"
                        Cursor="Hand" MouseLeftButtonUp="IncomeStatement_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="📊" FontSize="48" HorizontalAlignment="Center"
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="قائمة الدخل" FontSize="18" FontWeight="Bold"
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="الإيرادات والمصروفات وصافي الربح" FontSize="12"
                                   HorizontalAlignment="Center" Foreground="White"
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- الميزانية العمومية -->
                <Border Background="#1ABC9C" CornerRadius="15" Margin="10"
                        Cursor="Hand" MouseLeftButtonUp="BalanceSheet_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="🏛️" FontSize="48" HorizontalAlignment="Center"
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="الميزانية العمومية" FontSize="18" FontWeight="Bold"
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="الأصول والالتزامات وحقوق الملكية" FontSize="12"
                                   HorizontalAlignment="Center" Foreground="White"
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- كشف حساب -->
                <Border Background="#34495E" CornerRadius="15" Margin="10"
                        Cursor="Hand" MouseLeftButtonUp="AccountStatement_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="📋" FontSize="48" HorizontalAlignment="Center"
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="كشف حساب" FontSize="18" FontWeight="Bold"
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="تفاصيل حركة حساب معين" FontSize="12"
                                   HorizontalAlignment="Center" Foreground="White"
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- التقارير المحاسبية -->
                <Border Background="#E67E22" CornerRadius="15" Margin="10"
                        Cursor="Hand" MouseLeftButtonUp="AccountingReports_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="📈" FontSize="48" HorizontalAlignment="Center"
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="التقارير المحاسبية" FontSize="18" FontWeight="Bold"
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="تقارير مالية شاملة ومفصلة" FontSize="12"
                                   HorizontalAlignment="Center" Foreground="White"
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- إعدادات النظام المحاسبي -->
                <Border Background="#95A5A6" CornerRadius="15" Margin="10"
                        Cursor="Hand" MouseLeftButtonUp="AccountingSettings_Click">
                    <StackPanel Margin="30" HorizontalAlignment="Center">
                        <TextBlock Text="⚙️" FontSize="48" HorizontalAlignment="Center" 
                                   Foreground="White" Margin="0,0,0,15"/>
                        <TextBlock Text="إعدادات النظام" FontSize="18" FontWeight="Bold" 
                                   HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,10"/>
                        <TextBlock Text="إعدادات الفترة المالية والعملة" FontSize="12" 
                                   HorizontalAlignment="Center" Foreground="White" 
                                   TextWrapping="Wrap" TextAlignment="Center"/>
                    </StackPanel>
                </Border>
                
            </UniformGrid>
        </ScrollViewer>
    </Grid>
</UserControl>
