using System;
using System.Drawing;
using System.Windows.Forms;
using System.Data.SQLite;
using System.IO;

namespace GlassFactoryWorking
{
    public partial class MainForm : Form
    {
        public MainForm()
        {
            InitializeComponent();
            SetupDatabase();
        }

        private void SetupDatabase()
        {
            try
            {
                string dbPath = "accounts.db";
                if (!File.Exists(dbPath))
                {
                    SQLiteConnection.CreateFile(dbPath);
                    using (var connection = new SQLiteConnection($"Data Source={dbPath}"))
                    {
                        connection.Open();
                        string createTable = @"
                            CREATE TABLE Accounts (
                                Id INTEGER PRIMARY KEY,
                                Name TEXT NOT NULL,
                                Code TEXT NOT NULL
                            )";
                        using (var command = new SQLiteCommand(createTable, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                        
                        string insertData = @"
                            INSERT INTO Accounts (Name, Code) VALUES 
                            ('النقدية', '1001'),
                            ('البنك', '1002'),
                            ('المبيعات', '4001')";
                        using (var command = new SQLiteCommand(insertData, connection))
                        {
                            command.ExecuteNonQuery();
                        }
                    }
                }
                lblStatus.Text = "✅ قاعدة البيانات جاهزة";
                lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                lblStatus.Text = "❌ خطأ في قاعدة البيانات";
                lblStatus.ForeColor = Color.Red;
                MessageBox.Show($"Database Error: {ex.Message}");
            }
        }

        private void btnTest_Click(object sender, EventArgs e)
        {
            MessageBox.Show("البرنامج يعمل بنجاح!\nThe program works successfully!", 
                "Success - نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void btnShowAccounts_Click(object sender, EventArgs e)
        {
            try
            {
                using (var connection = new SQLiteConnection("Data Source=accounts.db"))
                {
                    connection.Open();
                    string query = "SELECT Name, Code FROM Accounts";
                    using (var command = new SQLiteCommand(query, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        string accounts = "الحسابات المتاحة:\n\n";
                        while (reader.Read())
                        {
                            accounts += $"{reader["Code"]} - {reader["Name"]}\n";
                        }
                        MessageBox.Show(accounts, "Chart of Accounts - شجرة الحسابات", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل تريد إغلاق البرنامج؟\nDo you want to exit?", 
                "Exit - خروج", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                Application.Exit();
            }
        }
    }
}
