using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة تسجيل سلفة الموظف
    /// </summary>
    public partial class EmployeeAdvanceView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly ExpenseService _expenseService;
        private readonly MainWindow _mainWindow;

        // اجعل المتغير nullable
        private Employee? _selectedEmployee;

        public EmployeeAdvanceView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _expenseService = new ExpenseService();
            _mainWindow = mainWindow;

            InitializeForm();
            LoadEmployees();
            LoadAdvances();
        }

        private void InitializeForm()
        {
            // تعيين تاريخ اليوم كافتراضي
            DpAdvanceDate.SelectedDate = DateTime.Now;

            // تحميل قائمة المسؤولين من إدارة المصروفات
            LoadResponsiblePersons();

            // تحميل أنواع السداد
            CmbPaymentType.Items.Add("نقدي");
            CmbPaymentType.Items.Add("شيك");
            CmbPaymentType.Items.Add("تحويل بنكي");
            CmbPaymentType.Items.Add("خصم من الراتب");
            CmbPaymentType.SelectedIndex = 0;
        }

        private void LoadResponsiblePersons()
        {
            try
            {
                var responsiblePersons = _expenseService.GetAllResponsiblePersons();
                CmbResponsiblePerson.ItemsSource = responsiblePersons;
                CmbResponsiblePerson.DisplayMemberPath = "Name";
                CmbResponsiblePerson.SelectedValuePath = "Name";

                if (responsiblePersons.Any())
                {
                    CmbResponsiblePerson.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل قائمة المسؤولين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);

                // في حالة الخطأ، استخدم قائمة افتراضية
                CmbResponsiblePerson.Items.Add("مدير الموارد البشرية");
                CmbResponsiblePerson.Items.Add("المدير المالي");
                CmbResponsiblePerson.Items.Add("المدير العام");
                CmbResponsiblePerson.Items.Add("مدير الفرع");
                CmbResponsiblePerson.SelectedIndex = 0;
            }
        }

        private void LoadEmployees()
        {
            try
            {
                var employees = _payrollService.GetAllEmployees();
                CmbEmployee.ItemsSource = employees;
                CmbEmployee.DisplayMemberPath = "Name";
                CmbEmployee.SelectedValuePath = "Id";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات الموظفين: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadAdvances()
        {
            try
            {
                var advances = _payrollService.GetAllEmployeeAdvances();
                AdvancesDataGrid.ItemsSource = advances;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات السلف: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CmbEmployee_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (CmbEmployee.SelectedItem is Employee selectedEmployee)
            {
                TxtEmployeeCode.Text = selectedEmployee.EmployeeCode;
                TxtPosition.Text = selectedEmployee.Position;
                TxtBranch.Text = selectedEmployee.Branch;

                // عند التعيين إلى null، لا مشكلة الآن
                _selectedEmployee = selectedEmployee;
            }
            else
            {
                ClearEmployeeFields();

                // عند التعيين إلى null، لا مشكلة الآن
                _selectedEmployee = null;
            }
        }

        private void ClearEmployeeFields()
        {
            TxtEmployeeCode.Clear();
            TxtPosition.Clear();
            TxtBranch.Clear();
        }

        private void TxtAdvanceAmount_TextChanged(object sender, TextChangedEventArgs e)
        {
            // التحقق من صحة المبلغ المدخل
            if (sender is TextBox textBox)
            {
                if (!string.IsNullOrEmpty(textBox.Text))
                {
                    if (!decimal.TryParse(textBox.Text, out _))
                    {
                        // إزالة الأحرف غير الصحيحة
                        int caretIndex = textBox.CaretIndex;
                        textBox.Text = System.Text.RegularExpressions.Regex.Replace(textBox.Text, @"[^\d.]", "");
                        textBox.CaretIndex = Math.Min(caretIndex, textBox.Text.Length);
                    }
                }
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                // عند الاستخدام، تحقق من عدم null
                if (_selectedEmployee != null)
                {
                    // استخدم الخصائص بأمان
                    var id = _selectedEmployee.Id;

                    var advance = new EmployeeAdvance
                    {
                        EmployeeId = id,
                        EmployeeName = _selectedEmployee.Name,
                        EmployeeCode = _selectedEmployee.EmployeeCode,
                        AdvanceAmount = decimal.Parse(TxtAdvanceAmount.Text),
                        ResponsiblePerson = (CmbResponsiblePerson.SelectedItem as GlassFactoryAccounting.Models.ResponsiblePerson)?.Name ?? CmbResponsiblePerson.SelectedValue?.ToString() ?? "",
                        PaymentType = CmbPaymentType.SelectedItem?.ToString() ?? string.Empty,
                        AdvanceDate = DpAdvanceDate.SelectedDate ?? DateTime.Now,
                        Notes = TxtNotes.Text.Trim()
                    };

                    if (_payrollService.AddEmployeeAdvance(advance))
                    {
                        MessageBox.Show("تم حفظ السلفة بنجاح!", "نجح الحفظ",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        ClearForm();
                        LoadAdvances();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ السلفة!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    MessageBox.Show("يرجى اختيار موظف.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ السلفة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (CmbEmployee.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار الموظف", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbEmployee.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(TxtAdvanceAmount.Text))
            {
                MessageBox.Show("يرجى إدخال قيمة السلفة", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAdvanceAmount.Focus();
                return false;
            }

            if (!decimal.TryParse(TxtAdvanceAmount.Text, out decimal amount) || amount <= 0)
            {
                MessageBox.Show("يرجى إدخال قيمة صحيحة للسلفة", "بيانات غير صحيحة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtAdvanceAmount.Focus();
                return false;
            }

            if (CmbResponsiblePerson.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار المسؤول عن السداد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbResponsiblePerson.Focus();
                return false;
            }

            if (CmbPaymentType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع السداد", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbPaymentType.Focus();
                return false;
            }

            if (!DpAdvanceDate.SelectedDate.HasValue)
            {
                MessageBox.Show("يرجى اختيار تاريخ السلفة", "بيانات ناقصة",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                DpAdvanceDate.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            CmbEmployee.SelectedIndex = -1;
            ClearEmployeeFields();
            TxtAdvanceAmount.Clear();
            CmbResponsiblePerson.SelectedIndex = 0;
            CmbPaymentType.SelectedIndex = 0;
            DpAdvanceDate.SelectedDate = DateTime.Now;
            TxtNotes.Clear();
            CmbEmployee.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeleteAdvance_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int advanceId)
                {
                    var result = MessageBox.Show("هل أنت متأكد من حذف هذه السلفة؟", "تأكيد الحذف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        if (_payrollService.DeleteEmployeeAdvance(advanceId))
                        {
                            MessageBox.Show("تم حذف السلفة بنجاح!", "نجح الحذف",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                            LoadAdvances();
                        }
                        else
                        {
                            MessageBox.Show("فشل في حذف السلفة!", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السلفة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditAdvance_Click(object sender, RoutedEventArgs e)
        {
            // TODO: تنفيذ منطق التعديل هنا حسب الحاجة
            MessageBox.Show("ميزة التعديل غير مفعلة حالياً. يرجى استكمال منطق التعديل إذا لزم.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // محول لتحويل القيمة المنطقية إلى نص حالة الخصم
    public class BoolToDeductionStatusConverter : IValueConverter
    {
        public static readonly BoolToDeductionStatusConverter Instance = new BoolToDeductionStatusConverter();

        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is bool isDeducted)
            {
                return isDeducted ? "مخصومة" : "غير مخصومة";
            }
            return "غير محدد";
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
