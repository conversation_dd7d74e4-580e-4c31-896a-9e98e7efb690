using System.Collections.ObjectModel;
using System.IO;
using System.Text.Json;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة أنواع الزجاج مع الحفظ التلقائي
    /// </summary>
    public class GlassTypesService
    {
        private static readonly string _dataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "GlassFactory", "GlassTypes.json");
        private static List<string> _glassTypes = new List<string>
        {
            "زجاج شفاف",
            "زجاج ملون",
            "زجاج مقسى",
            "زجاج مضاد للكسر",
            "زجاج عاكس",
            "زجاج مزدوج",
            "زجاج أمان",
            "زجاج ديكوري",
            "زجاج مصنفر",
            "زجاج مطبوع",
            "زجاج مقاوم للحرارة",
            "زجاج مقاوم للصدمات",
            "زجاج عازل للصوت",
            "زجاج منخفض الانبعاث",
            "زجاج ذكي",
            "زجاج مضاد للوهج",
            "زجاج مقاوم للخدش",
            "زجاج مضاد للبكتيريا",
            "زجاج مقاوم للأشعة فوق البنفسجية",
            "زجاج مرن"
        };

        /// <summary>
        /// تهيئة أنواع الزجاج من الملف أو القيم الافتراضية
        /// </summary>
        static GlassTypesService()
        {
            LoadGlassTypes();
        }

        /// <summary>
        /// تحميل أنواع الزجاج من الملف
        /// </summary>
        private static void LoadGlassTypes()
        {
            try
            {
                if (File.Exists(_dataPath))
                {
                    var json = File.ReadAllText(_dataPath);
                    var loadedTypes = JsonSerializer.Deserialize<List<string>>(json);
                    if (loadedTypes != null && loadedTypes.Count > 0)
                    {
                        _glassTypes = loadedTypes;
                        return;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading glass types: {ex.Message}");
            }

            // إذا فشل التحميل، استخدم القيم الافتراضية
            _glassTypes = new List<string>
            {
                "زجاج شفاف",
                "زجاج ملون",
                "زجاج مقسى",
                "زجاج مضاد للكسر",
                "زجاج عاكس",
                "زجاج مزدوج",
                "زجاج أمان",
                "زجاج ديكوري",
                "زجاج مصنفر",
                "زجاج مطبوع",
                "زجاج مقاوم للحرارة",
                "زجاج مقاوم للصدمات",
                "زجاج عازل للصوت",
                "زجاج منخفض الانبعاث",
                "زجاج ذكي",
                "زجاج مضاد للوهج",
                "زجاج مقاوم للخدش",
                "زجاج مضاد للبكتيريا",
                "زجاج مقاوم للأشعة فوق البنفسجية",
                "زجاج مرن"
            };

            SaveGlassTypes();
        }

        /// <summary>
        /// حفظ أنواع الزجاج في الملف
        /// </summary>
        private static void SaveGlassTypes()
        {
            try
            {
                var directory = Path.GetDirectoryName(_dataPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(_glassTypes, new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

                File.WriteAllText(_dataPath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving glass types: {ex.Message}");
            }
        }

        /// <summary>
        /// جلب جميع أنواع الزجاج
        /// </summary>
        public static List<string> GetAllGlassTypes()
        {
            return new List<string>(_glassTypes);
        }

        /// <summary>
        /// جلب أنواع الزجاج كـ ObservableCollection
        /// </summary>
        public static ObservableCollection<string> GetGlassTypesCollection()
        {
            return new ObservableCollection<string>(_glassTypes);
        }

        /// <summary>
        /// إضافة نوع زجاج جديد مع الحفظ التلقائي
        /// </summary>
        public static bool AddGlassType(string glassType)
        {
            if (string.IsNullOrWhiteSpace(glassType))
                return false;

            if (_glassTypes.Contains(glassType, StringComparer.OrdinalIgnoreCase))
                return false;

            _glassTypes.Add(glassType);
            SaveGlassTypes();
            return true;
        }

        /// <summary>
        /// إضافة نوع زجاج جديد إذا لم يكن موجوداً (للاستخدام التلقائي)
        /// </summary>
        public static void EnsureGlassTypeExists(string glassType)
        {
            if (!string.IsNullOrWhiteSpace(glassType) && !_glassTypes.Contains(glassType, StringComparer.OrdinalIgnoreCase))
            {
                _glassTypes.Add(glassType);
                SaveGlassTypes();
            }
        }

        /// <summary>
        /// حذف نوع زجاج مع الحفظ التلقائي
        /// </summary>
        public static bool RemoveGlassType(string glassType)
        {
            bool removed = _glassTypes.Remove(glassType);
            if (removed)
            {
                SaveGlassTypes();
            }
            return removed;
        }

        /// <summary>
        /// البحث في أنواع الزجاج
        /// </summary>
        public static List<string> SearchGlassTypes(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
                return GetAllGlassTypes();

            return _glassTypes
                .Where(type => type.Contains(searchTerm, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }

        /// <summary>
        /// التحقق من وجود نوع زجاج
        /// </summary>
        public static bool GlassTypeExists(string glassType)
        {
            return _glassTypes.Contains(glassType, StringComparer.OrdinalIgnoreCase);
        }
    }
}
