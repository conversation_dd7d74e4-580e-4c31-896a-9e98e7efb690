﻿#pragma checksum "..\..\..\..\Views\ManufacturingView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "AB51546589F2C98FCCE48551033AA1EA20C24D8E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ManufacturingView
    /// </summary>
    public partial class ManufacturingView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 46 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnToggleNavigationTools;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border NavigationButtonsPanel;
        
        #line default
        #line hidden
        
        
        #line 71 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnNewManufacturingOrder;
        
        #line default
        #line hidden
        
        
        #line 78 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnViewOrders;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnDeliveryOrders;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnSizeTracking;
        
        #line default
        #line hidden
        
        
        #line 100 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnManufacturingReports;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnCostAnalysis;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatisticsPanel;
        
        #line default
        #line hidden
        
        
        #line 123 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtScheduledOrders;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtInProgressOrders;
        
        #line default
        #line hidden
        
        
        #line 139 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtCompletedOrders;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtStoppedOrders;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalCost;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\ManufacturingView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ContentControl MainContent;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/manufacturingview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ManufacturingView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.btnToggleNavigationTools = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\..\Views\ManufacturingView.xaml"
            this.btnToggleNavigationTools.Click += new System.Windows.RoutedEventHandler(this.BtnToggleNavigationTools_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.NavigationButtonsPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.btnNewManufacturingOrder = ((System.Windows.Controls.Button)(target));
            
            #line 76 "..\..\..\..\Views\ManufacturingView.xaml"
            this.btnNewManufacturingOrder.Click += new System.Windows.RoutedEventHandler(this.BtnNewManufacturingOrder_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.btnViewOrders = ((System.Windows.Controls.Button)(target));
            
            #line 83 "..\..\..\..\Views\ManufacturingView.xaml"
            this.btnViewOrders.Click += new System.Windows.RoutedEventHandler(this.BtnViewOrders_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.btnDeliveryOrders = ((System.Windows.Controls.Button)(target));
            
            #line 90 "..\..\..\..\Views\ManufacturingView.xaml"
            this.btnDeliveryOrders.Click += new System.Windows.RoutedEventHandler(this.BtnDeliveryOrders_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.btnSizeTracking = ((System.Windows.Controls.Button)(target));
            
            #line 98 "..\..\..\..\Views\ManufacturingView.xaml"
            this.btnSizeTracking.Click += new System.Windows.RoutedEventHandler(this.BtnSizeTracking_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnManufacturingReports = ((System.Windows.Controls.Button)(target));
            
            #line 105 "..\..\..\..\Views\ManufacturingView.xaml"
            this.btnManufacturingReports.Click += new System.Windows.RoutedEventHandler(this.BtnManufacturingReports_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btnCostAnalysis = ((System.Windows.Controls.Button)(target));
            
            #line 112 "..\..\..\..\Views\ManufacturingView.xaml"
            this.btnCostAnalysis.Click += new System.Windows.RoutedEventHandler(this.BtnCostAnalysis_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.StatisticsPanel = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.TxtScheduledOrders = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtInProgressOrders = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.TxtCompletedOrders = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtStoppedOrders = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtTotalCost = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.MainContent = ((System.Windows.Controls.ContentControl)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

