using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة تسجيل المسؤولين عن السداد
    /// </summary>
    public partial class ResponsiblePersonView : UserControl
    {
        private readonly PayrollService _payrollService;
        private readonly MainWindow _mainWindow;

        public ResponsiblePersonView(MainWindow mainWindow)
        {
            InitializeComponent();
            _payrollService = new PayrollService();
            _mainWindow = mainWindow;
            
            InitializeForm();
            LoadResponsiblePersons();
        }

        private void InitializeForm()
        {
            // تحميل المناصب الشائعة
            CmbPosition.Items.Add("مدير الموارد البشرية");
            CmbPosition.Items.Add("المدير المالي");
            CmbPosition.Items.Add("المدير العام");
            CmbPosition.Items.Add("مدير الفرع");
            CmbPosition.Items.Add("مدير الحسابات");
            CmbPosition.Items.Add("مسؤول الرواتب");
            CmbPosition.Items.Add("مسؤول الخزينة");
            CmbPosition.Items.Add("محاسب أول");

            // تحميل الأقسام الشائعة
            CmbDepartment.Items.Add("الموارد البشرية");
            CmbDepartment.Items.Add("الشؤون المالية");
            CmbDepartment.Items.Add("الإدارة العامة");
            CmbDepartment.Items.Add("المحاسبة");
            CmbDepartment.Items.Add("الخزينة");
            CmbDepartment.Items.Add("الرواتب والأجور");
        }

        private void LoadResponsiblePersons()
        {
            try
            {
                var persons = _payrollService.GetAllResponsiblePersons();
                ResponsiblePersonsDataGrid.ItemsSource = persons;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المسؤولين: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;

                var person = new ResponsiblePerson
                {
                    Name = TxtName.Text.Trim(),
                    Position = CmbPosition.Text.Trim(),
                    Department = CmbDepartment.Text.Trim(),
                    Phone = TxtPhone.Text.Trim(),
                    Email = TxtEmail.Text.Trim(),
                    IsActive = ChkIsActive.IsChecked ?? true,
                    Notes = TxtNotes.Text.Trim()
                };

                // التحقق من وضع التعديل
                if (BtnSave.Tag != null && int.TryParse(BtnSave.Tag.ToString(), out int personId))
                {
                    // وضع التعديل
                    person.Id = personId;
                    if (_payrollService.UpdateResponsiblePerson(person))
                    {
                        MessageBox.Show($"تم تحديث المسؤول بنجاح!\n\nالاسم: {person.Name}\nالمنصب: {person.Position}\nالقسم: {person.Department}",
                            "تم التحديث بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                        ClearForm();
                        LoadResponsiblePersons();

                        // إعادة تعيين الزر للوضع العادي
                        BtnSave.Content = "💾 حفظ المسؤول";
                        BtnSave.Tag = null;
                    }
                    else
                    {
                        MessageBox.Show("فشل في تحديث بيانات المسؤول!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                else
                {
                    // وضع الإضافة
                    if (_payrollService.AddResponsiblePerson(person))
                    {
                        MessageBox.Show($"تم حفظ المسؤول بنجاح!\n\nالاسم: {person.Name}\nالمنصب: {person.Position}\nالقسم: {person.Department}",
                            "تم الحفظ بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                        ClearForm();
                        LoadResponsiblePersons();
                    }
                    else
                    {
                        MessageBox.Show("فشل في حفظ بيانات المسؤول!", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ بيانات المسؤول: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(TxtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المسؤول", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                TxtName.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CmbPosition.Text))
            {
                MessageBox.Show("يرجى إدخال المنصب", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbPosition.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(CmbDepartment.Text))
            {
                MessageBox.Show("يرجى إدخال القسم", "بيانات ناقصة", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                CmbDepartment.Focus();
                return false;
            }

            return true;
        }

        private void ClearForm()
        {
            TxtName.Clear();
            CmbPosition.SelectedIndex = -1;
            CmbDepartment.SelectedIndex = -1;
            TxtPhone.Clear();
            TxtEmail.Clear();
            ChkIsActive.IsChecked = true;
            TxtNotes.Clear();
            TxtName.Focus();
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void BtnBack_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                _mainWindow.ShowSalaries();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnEditPerson_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int personId)
                {
                    var persons = _payrollService.GetAllResponsiblePersons();
                    var person = persons.FirstOrDefault(p => p.Id == personId);

                    if (person != null)
                    {
                        // تعبئة النموذج ببيانات المسؤول للتعديل
                        TxtName.Text = person.Name;
                        CmbPosition.Text = person.Position;
                        CmbDepartment.Text = person.Department;
                        TxtPhone.Text = person.Phone;
                        TxtEmail.Text = person.Email;
                        ChkIsActive.IsChecked = person.IsActive;
                        TxtNotes.Text = person.Notes;

                        // تغيير نص الزر إلى "تحديث"
                        BtnSave.Content = "🔄 تحديث المسؤول";
                        BtnSave.Tag = personId; // حفظ معرف المسؤول

                        MessageBox.Show("تم تحميل بيانات المسؤول للتعديل", "تعديل المسؤول",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المسؤول: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnDeletePerson_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is int personId)
                {
                    var persons = _payrollService.GetAllResponsiblePersons();
                    var person = persons.FirstOrDefault(p => p.Id == personId);

                    if (person != null)
                    {
                        var result = MessageBox.Show(
                            $"هل أنت متأكد من حذف المسؤول؟\n\nالاسم: {person.Name}\nالمنصب: {person.Position}\nالقسم: {person.Department}",
                            "تأكيد حذف المسؤول",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Warning);

                        if (result == MessageBoxResult.Yes)
                        {
                            if (_payrollService.DeleteResponsiblePerson(personId))
                            {
                                MessageBox.Show($"تم حذف المسؤول بنجاح!\n\nالمسؤول المحذوف: {person.Name}",
                                    "تم الحذف بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);

                                LoadResponsiblePersons();
                            }
                            else
                            {
                                MessageBox.Show("فشل في حذف المسؤول!", "خطأ",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف المسؤول: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
