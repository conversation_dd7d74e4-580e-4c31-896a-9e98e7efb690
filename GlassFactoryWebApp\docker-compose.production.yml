version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: glass-factory-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: glass_factory_db
      POSTGRES_USER: glass_factory_user
      POSTGRES_PASSWORD: GlassFactory2025!
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U glass_factory_user -d glass_factory_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    container_name: glass-factory-api
    restart: unless-stopped
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=glass_factory_db;Username=glass_factory_user;Password=GlassFactory2025!
      - JwtSettings__SecretKey=GlassFactorySecretKey2025VeryLongAndSecureForProduction!
      - JwtSettings__Issuer=GlassFactoryApp
      - JwtSettings__Audience=GlassFactoryUsers
      - JwtSettings__ExpiryInHours=24
    volumes:
      - ./uploads:/app/uploads
      - ./reports:/app/reports
      - ./logs:/app/logs
    ports:
      - "5000:80"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Web App
  web:
    build:
      context: ./client
      dockerfile: Dockerfile
      args:
        - REACT_APP_API_URL=https://glass-factory-demo.ddns.net/api
        - REACT_APP_VERSION=2.0.0
        - REACT_APP_ENVIRONMENT=production
    container_name: glass-factory-web
    restart: unless-stopped
    ports:
      - "3000:80"
    depends_on:
      - api
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: glass-factory-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./uploads:/var/www/uploads:ro
      - ./reports:/var/www/reports:ro
    depends_on:
      - api
      - web
    networks:
      - glass-factory-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
    driver: local

networks:
  glass-factory-network:
    driver: bridge
