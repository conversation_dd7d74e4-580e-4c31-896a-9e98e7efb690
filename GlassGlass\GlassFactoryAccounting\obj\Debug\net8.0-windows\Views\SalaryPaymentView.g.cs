﻿#pragma checksum "..\..\..\..\Views\SalaryPaymentView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "63341E27F00A98F32E8A03A00FAAA0D2C6EA3CD5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// SalaryPaymentView
    /// </summary>
    public partial class SalaryPaymentView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 176 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbEmployee;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtEmployeeCode;
        
        #line default
        #line hidden
        
        
        #line 186 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtPosition;
        
        #line default
        #line hidden
        
        
        #line 191 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBranch;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBasicSalary;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSalaryPaid;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkAddBonusOvertime;
        
        #line default
        #line hidden
        
        
        #line 210 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblBonus;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBonusAmount;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LblOvertime;
        
        #line default
        #line hidden
        
        
        #line 216 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtOvertimeAmount;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtAdvanceDeduction;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtTotalPaid;
        
        #line default
        #line hidden
        
        
        #line 231 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNetAmount;
        
        #line default
        #line hidden
        
        
        #line 236 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtAmountPaid;
        
        #line default
        #line hidden
        
        
        #line 241 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtRemainingAmount;
        
        #line default
        #line hidden
        
        
        #line 246 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbResponsiblePerson;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpPaymentDate;
        
        #line default
        #line hidden
        
        
        #line 254 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrintCheck;
        
        #line default
        #line hidden
        
        
        #line 260 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 270 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PaymentsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 312 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 313 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClear;
        
        #line default
        #line hidden
        
        
        #line 314 "..\..\..\..\Views\SalaryPaymentView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnBack;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/salarypaymentview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\SalaryPaymentView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CmbEmployee = ((System.Windows.Controls.ComboBox)(target));
            
            #line 177 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.CmbEmployee.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.CmbEmployee_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtEmployeeCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.TxtPosition = ((System.Windows.Controls.TextBox)(target));
            return;
            case 4:
            this.TxtBranch = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.TxtBasicSalary = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtSalaryPaid = ((System.Windows.Controls.TextBox)(target));
            
            #line 202 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.TxtSalaryPaid.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSalaryPaid_TextChanged);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ChkAddBonusOvertime = ((System.Windows.Controls.CheckBox)(target));
            
            #line 207 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.ChkAddBonusOvertime.Checked += new System.Windows.RoutedEventHandler(this.ChkAddBonusOvertime_Checked);
            
            #line default
            #line hidden
            
            #line 207 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.ChkAddBonusOvertime.Unchecked += new System.Windows.RoutedEventHandler(this.ChkAddBonusOvertime_Unchecked);
            
            #line default
            #line hidden
            return;
            case 8:
            this.LblBonus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtBonusAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 212 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.TxtBonusAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtBonusAmount_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.LblOvertime = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.TxtOvertimeAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 217 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.TxtOvertimeAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtOvertimeAmount_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.TxtAdvanceDeduction = ((System.Windows.Controls.TextBox)(target));
            
            #line 222 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.TxtAdvanceDeduction.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtAdvanceDeduction_TextChanged);
            
            #line default
            #line hidden
            return;
            case 13:
            this.TxtTotalPaid = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.TxtNetAmount = ((System.Windows.Controls.TextBox)(target));
            return;
            case 15:
            this.TxtAmountPaid = ((System.Windows.Controls.TextBox)(target));
            
            #line 237 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.TxtAmountPaid.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtAmountPaid_TextChanged);
            
            #line default
            #line hidden
            return;
            case 16:
            this.TxtRemainingAmount = ((System.Windows.Controls.TextBox)(target));
            return;
            case 17:
            this.CmbResponsiblePerson = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 18:
            this.DpPaymentDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 19:
            this.BtnPrintCheck = ((System.Windows.Controls.Button)(target));
            
            #line 255 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.BtnPrintCheck.Click += new System.Windows.RoutedEventHandler(this.BtnPrintCheck_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 21:
            this.PaymentsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 24:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 312 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 25:
            this.BtnClear = ((System.Windows.Controls.Button)(target));
            
            #line 313 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.BtnClear.Click += new System.Windows.RoutedEventHandler(this.BtnClear_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.BtnBack = ((System.Windows.Controls.Button)(target));
            
            #line 314 "..\..\..\..\Views\SalaryPaymentView.xaml"
            this.BtnBack.Click += new System.Windows.RoutedEventHandler(this.BtnBack_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 22:
            
            #line 294 "..\..\..\..\Views\SalaryPaymentView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditPayment_Click);
            
            #line default
            #line hidden
            break;
            case 23:
            
            #line 298 "..\..\..\..\Views\SalaryPaymentView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeletePayment_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

