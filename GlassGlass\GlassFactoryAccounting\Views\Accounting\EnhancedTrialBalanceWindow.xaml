<Window x:Class="GlassFactoryAccounting.Views.Accounting.EnhancedTrialBalanceWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        mc:Ignorable="d"
        Title="ميزان المراجعة المحسن" Height="700" Width="1200"
        WindowStartupLocation="CenterScreen"
        FontFamily="Segoe UI"
        FlowDirection="RightToLeft">
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Background="#2C3E50" CornerRadius="5" Padding="15" Margin="0,0,0,15">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <TextBlock Text="📊" FontSize="24" Margin="0,0,10,0"/>
                <TextBlock Text="ميزان المراجعة المحسن" 
                           FontSize="22" FontWeight="Bold" 
                           Foreground="White" VerticalAlignment="Center"/>
                <TextBlock Name="txtDateRange" Text="" 
                           FontSize="14" Foreground="#BDC3C7" 
                           VerticalAlignment="Center" Margin="20,0,0,0"/>
            </StackPanel>
        </Border>
        
        <!-- أدوات التحكم -->
        <Border Grid.Row="1" BorderBrush="#BDC3C7" BorderThickness="1"
                CornerRadius="5" Background="#F8F9FA" Padding="15" Margin="0,0,0,15">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <!-- فترة التقرير -->
                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,20,0">
                    <Label Content="من تاريخ:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <DatePicker Name="dpFromDate" Width="120" Margin="5,0"/>
                    <Label Content="إلى تاريخ:" FontWeight="Bold" VerticalAlignment="Center" Margin="15,0,0,0"/>
                    <DatePicker Name="dpToDate" Width="120" Margin="5,0"/>
                </StackPanel>
                
                <!-- فلتر نوع الحساب -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,20,0">
                    <Label Content="نوع الحساب:" FontWeight="Bold" VerticalAlignment="Center"/>
                    <ComboBox Name="cmbAccountType" Width="150" FontSize="14">
                        <ComboBoxItem Content="جميع الأنواع" IsSelected="True"/>
                        <ComboBoxItem Content="الأصول" Tag="Asset"/>
                        <ComboBoxItem Content="الالتزامات" Tag="Liability"/>
                        <ComboBoxItem Content="حقوق الملكية" Tag="Equity"/>
                        <ComboBoxItem Content="الإيرادات" Tag="Revenue"/>
                        <ComboBoxItem Content="المصروفات" Tag="Expense"/>
                    </ComboBox>
                </StackPanel>
                
                <!-- إخفاء الأرصدة الصفرية -->
                <CheckBox Grid.Column="2" Name="chkHideZeroBalances" 
                          Content="إخفاء الأرصدة الصفرية" 
                          FontWeight="Bold" VerticalAlignment="Center" 
                          IsChecked="True" Margin="0,0,20,0"/>
                
                <!-- أزرار التحكم -->
                <StackPanel Grid.Column="4" Orientation="Horizontal">
                    <Button Name="btnRefresh" Content="🔄 تحديث" 
                            Background="#3498DB" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnRefresh_Click"/>
                    
                    <Button Name="btnExport" Content="📤 تصدير" 
                            Background="#27AE60" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnExport_Click"/>
                    
                    <Button Name="btnPrint" Content="🖨️ طباعة" 
                            Background="#E67E22" Foreground="White" 
                            Padding="15,8" FontSize="14" FontWeight="Bold"
                            Margin="5,0" Click="BtnPrint_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- جدول ميزان المراجعة -->
        <Border Grid.Row="2" BorderBrush="#BDC3C7" BorderThickness="1" 
                CornerRadius="5" Background="White">
            <DataGrid Name="dgTrialBalance" 
                      AutoGenerateColumns="False"
                      CanUserAddRows="False"
                      CanUserDeleteRows="False"
                      IsReadOnly="True"
                      GridLinesVisibility="Horizontal"
                      HeadersVisibility="Column"
                      AlternatingRowBackground="#F8F9FA"
                      FontSize="13"
                      Margin="10">
                
                <DataGrid.Columns>
                    <!-- كود الحساب -->
                    <DataGridTextColumn Header="كود الحساب" 
                                        Binding="{Binding AccountCode}" 
                                        Width="120"
                                        HeaderStyle="{StaticResource DataGridHeaderStyle}"/>
                    
                    <!-- اسم الحساب -->
                    <DataGridTextColumn Header="اسم الحساب" 
                                        Binding="{Binding AccountName}" 
                                        Width="*"
                                        HeaderStyle="{StaticResource DataGridHeaderStyle}"/>
                    
                    <!-- نوع الحساب -->
                    <DataGridTextColumn Header="نوع الحساب" 
                                        Binding="{Binding AccountTypeDisplay}" 
                                        Width="120"
                                        HeaderStyle="{StaticResource DataGridHeaderStyle}"/>
                    
                    <!-- المدين -->
                    <DataGridTextColumn Header="المدين" 
                                        Binding="{Binding DebitBalance, StringFormat=N2}" 
                                        Width="120"
                                        HeaderStyle="{StaticResource DataGridHeaderStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#E74C3C"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <!-- الدائن -->
                    <DataGridTextColumn Header="الدائن" 
                                        Binding="{Binding CreditBalance, StringFormat=N2}" 
                                        Width="120"
                                        HeaderStyle="{StaticResource DataGridHeaderStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Setter Property="Foreground" Value="#27AE60"/>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                    
                    <!-- الرصيد -->
                    <DataGridTextColumn Header="الرصيد" 
                                        Binding="{Binding Balance, StringFormat=N2}" 
                                        Width="120"
                                        HeaderStyle="{StaticResource DataGridHeaderStyle}">
                        <DataGridTextColumn.ElementStyle>
                            <Style TargetType="TextBlock">
                                <Setter Property="TextAlignment" Value="Right"/>
                                <Setter Property="FontWeight" Value="Bold"/>
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Balance, Converter={StaticResource PositiveNegativeConverter}}" Value="Positive">
                                        <Setter Property="Foreground" Value="#27AE60"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Balance, Converter={StaticResource PositiveNegativeConverter}}" Value="Negative">
                                        <Setter Property="Foreground" Value="#E74C3C"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
        
        <!-- ملخص الأرصدة -->
        <Border Grid.Row="3" BorderBrush="#BDC3C7" BorderThickness="1"
                CornerRadius="5" Background="#ECF0F1" Padding="15" Margin="0,15,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي المدين" FontWeight="Bold" FontSize="14" 
                               HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock Name="txtTotalDebit" Text="0.00" FontSize="18" FontWeight="Bold" 
                               Foreground="#E74C3C" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الدائن" FontWeight="Bold" FontSize="14" 
                               HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock Name="txtTotalCredit" Text="0.00" FontSize="18" FontWeight="Bold" 
                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="الفرق" FontWeight="Bold" FontSize="14" 
                               HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock Name="txtDifference" Text="0.00" FontSize="18" FontWeight="Bold" 
                               HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="حالة التوازن" FontWeight="Bold" FontSize="14" 
                               HorizontalAlignment="Center" Margin="0,0,0,5"/>
                    <TextBlock Name="txtBalanceStatus" Text="متوازن" FontSize="16" FontWeight="Bold" 
                               Foreground="#27AE60" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
    
    <Window.Resources>
        <!-- تنسيق رأس الجدول -->
        <Style x:Key="DataGridHeaderStyle" TargetType="DataGridColumnHeader">
            <Setter Property="Background" Value="#34495E"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Padding" Value="10,8"/>
            <Setter Property="HorizontalContentAlignment" Value="Center"/>
        </Style>
        
        <!-- محول الأرقام الموجبة والسالبة -->
        <local:PositiveNegativeConverter x:Key="PositiveNegativeConverter"/>
    </Window.Resources>
</Window>
