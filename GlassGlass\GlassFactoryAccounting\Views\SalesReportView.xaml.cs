using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;
using System.Linq;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة تقرير المبيعات
/// </summary>
public partial class SalesReportView : UserControl
{
    private readonly MainWindow _mainWindow;
    private readonly SalesService _salesService;
    private ObservableCollection<Sale> _allSales;
    private ObservableCollection<Sale> _filteredSales;

    public SalesReportView(MainWindow mainWindow)
    {
        InitializeComponent();
        _mainWindow = mainWindow;
        _salesService = new SalesService();
        _allSales = new ObservableCollection<Sale>();
        _filteredSales = new ObservableCollection<Sale>();
        
        SalesReportDataGrid.ItemsSource = _filteredSales;
        
        InitializePage();
        LoadSalesData();
    }

    private void InitializePage()
    {
        try
        {
            // تعيين التواريخ الافتراضية
            DateFrom.SelectedDate = DateTime.Today.AddDays(-30);
            DateTo.SelectedDate = DateTime.Today;

            // تهيئة فلاتر الخدمات
            InitializeServiceFilter();

            // تهيئة فلاتر أنواع الزجاج
            InitializeGlassTypeFilter();

            // تهيئة فلاتر سماكة الزجاج
            InitializeGlassThicknessFilter();

            // تهيئة فلاتر حالة الدفع
            InitializePaymentStatusFilter();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تهيئة الصفحة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task InitializeServiceFilter()
    {
        try
        {
            CmbServiceFilter.Items.Clear();
            CmbServiceFilter.Items.Add(new Service { Name = "جميع الخدمات" });

            // تحميل الخدمات المستخدمة فعلياً في الفواتير
            var usedServices = await _salesService.GetUsedServicesAsync();
            foreach (var service in usedServices)
            {
                CmbServiceFilter.Items.Add(new Service { Name = service });
            }

            CmbServiceFilter.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error initializing service filter: {ex.Message}");
        }
    }

    private async Task InitializeGlassTypeFilter()
    {
        try
        {
            CmbGlassTypeFilter.Items.Clear();
            CmbGlassTypeFilter.Items.Add("جميع الأنواع");

            // تحميل أنواع الزجاج المستخدمة فعلياً في الفواتير
            var usedGlassTypes = await _salesService.GetUsedGlassTypesAsync();
            foreach (var glassType in usedGlassTypes)
            {
                CmbGlassTypeFilter.Items.Add(glassType);
            }

            CmbGlassTypeFilter.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error initializing glass type filter: {ex.Message}");
        }
    }

    private async Task InitializeGlassThicknessFilter()
    {
        try
        {
            CmbGlassThicknessFilter.Items.Clear();
            CmbGlassThicknessFilter.Items.Add("جميع السماكات");

            // تحميل سماكات الزجاج المستخدمة فعلياً في الفواتير
            var usedThicknesses = await _salesService.GetUsedGlassThicknessesAsync();
            foreach (var thickness in usedThicknesses)
            {
                CmbGlassThicknessFilter.Items.Add($"{thickness} ملم");
            }

            CmbGlassThicknessFilter.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error initializing glass thickness filter: {ex.Message}");
        }
    }

    private void InitializePaymentStatusFilter()
    {
        try
        {
            CmbPaymentStatusFilter.Items.Clear();
            CmbPaymentStatusFilter.Items.Add("جميع الحالات");
            CmbPaymentStatusFilter.Items.Add("مدفوع");
            CmbPaymentStatusFilter.Items.Add("غير مدفوع");
            CmbPaymentStatusFilter.Items.Add("مدفوع جزئياً");
            CmbPaymentStatusFilter.SelectedIndex = 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error initializing payment status filter: {ex.Message}");
        }
    }

    private void LoadSalesData()
    {
        try
        {
            Task.Run(async () =>
            {
                try
                {
                    var sales = await _salesService.GetAllSalesAsync();

                    Application.Current.Dispatcher.BeginInvoke(() =>
                    {
                        try
                        {
                            _allSales.Clear();
                            foreach (var sale in sales)
                            {
                                _allSales.Add(sale);
                            }

                            ApplyFilters();

                            // تحديث الفلاتر بالبيانات الجديدة
                            UpdateFiltersWithNewData();
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Error updating UI: {ex.Message}");
                            MessageBox.Show($"خطأ في تحديث الواجهة: {ex.Message}", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    });
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.BeginInvoke(() =>
                    {
                        System.Diagnostics.Debug.WriteLine($"Error loading sales data: {ex.Message}");
                        MessageBox.Show($"خطأ في تحميل بيانات المبيعات: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error starting load task: {ex.Message}");
            MessageBox.Show($"خطأ في بدء تحميل البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void UpdateFiltersWithNewData()
    {
        try
        {
            // تحديث فلتر الخدمات
            var currentServiceSelection = CmbServiceFilter.SelectedItem;
            await InitializeServiceFilter();

            // استعادة الاختيار السابق إن أمكن
            if (currentServiceSelection != null)
            {
                var serviceName = ((Service)currentServiceSelection).Name;
                for (int i = 0; i < CmbServiceFilter.Items.Count; i++)
                {
                    if (((Service)CmbServiceFilter.Items[i]).Name == serviceName)
                    {
                        CmbServiceFilter.SelectedIndex = i;
                        break;
                    }
                }
            }

            // تحديث فلتر أنواع الزجاج
            var currentGlassTypeSelection = CmbGlassTypeFilter.SelectedItem;
            await InitializeGlassTypeFilter();

            // استعادة الاختيار السابق إن أمكن
            if (currentGlassTypeSelection != null)
            {
                var glassType = currentGlassTypeSelection.ToString();
                for (int i = 0; i < CmbGlassTypeFilter.Items.Count; i++)
                {
                    if (CmbGlassTypeFilter.Items[i].ToString() == glassType)
                    {
                        CmbGlassTypeFilter.SelectedIndex = i;
                        break;
                    }
                }
            }

            // تحديث فلتر سماكات الزجاج
            var currentThicknessSelection = CmbGlassThicknessFilter.SelectedItem;
            await InitializeGlassThicknessFilter();

            // استعادة الاختيار السابق إن أمكن
            if (currentThicknessSelection != null)
            {
                var thickness = currentThicknessSelection.ToString();
                for (int i = 0; i < CmbGlassThicknessFilter.Items.Count; i++)
                {
                    if (CmbGlassThicknessFilter.Items[i].ToString() == thickness)
                    {
                        CmbGlassThicknessFilter.SelectedIndex = i;
                        break;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error updating filters: {ex.Message}");
        }
    }

    private void ApplyFilters()
    {
        try
        {
            // التأكد من وجود البيانات
            if (_allSales == null)
            {
                System.Diagnostics.Debug.WriteLine("_allSales is null in ApplyFilters");
                return;
            }

            if (_filteredSales == null)
            {
                System.Diagnostics.Debug.WriteLine("_filteredSales is null in ApplyFilters");
                return;
            }

            var filtered = _allSales.AsEnumerable();

            // فلتر التاريخ
            if (DateFrom?.SelectedDate.HasValue == true)
            {
                filtered = filtered.Where(s => s.SaleDate >= DateFrom.SelectedDate.Value);
            }

            if (DateTo?.SelectedDate.HasValue == true)
            {
                var endDate = DateTo.SelectedDate.Value.AddDays(1); // تضمين اليوم كاملاً
                filtered = filtered.Where(s => s.SaleDate < endDate);
            }

            // فلتر العميل
            var customerSearch = TxtCustomerSearch?.Text ?? "";
            if (!string.IsNullOrWhiteSpace(customerSearch) && customerSearch != "البحث في أسماء العملاء...")
            {
                filtered = filtered.Where(s => s.Customer?.Name?.Contains(customerSearch, StringComparison.OrdinalIgnoreCase) == true);
            }

            // فلتر الخدمة
            var selectedService = CmbServiceFilter?.SelectedItem as Service;
            if (selectedService != null && selectedService.Name != "جميع الخدمات")
            {
                filtered = filtered.Where(s => s.SaleItems?.Any(item => item.Service?.Contains(selectedService.Name, StringComparison.OrdinalIgnoreCase) == true) == true);
            }

            // فلتر نوع الزجاج
            var selectedGlassType = CmbGlassTypeFilter?.SelectedItem as string;
            if (!string.IsNullOrEmpty(selectedGlassType) && selectedGlassType != "جميع الأنواع")
            {
                filtered = filtered.Where(s => s.SaleItems?.Any(item => item.GlassType?.Contains(selectedGlassType, StringComparison.OrdinalIgnoreCase) == true) == true);
            }

            // فلتر سمك الزجاج
            var selectedThickness = CmbGlassThicknessFilter?.SelectedItem as string;
            if (!string.IsNullOrEmpty(selectedThickness) && selectedThickness != "جميع السماكات")
            {
                var thicknessValue = selectedThickness.Replace(" ملم", "");
                if (decimal.TryParse(thicknessValue, out decimal thickness))
                {
                    filtered = filtered.Where(s => s.SaleItems?.Any(item => item.GlassThickness == thickness) == true);
                }
            }

            // فلتر حالة الدفع
            var selectedPaymentStatus = CmbPaymentStatusFilter?.SelectedItem as string;
            if (!string.IsNullOrEmpty(selectedPaymentStatus) && selectedPaymentStatus != "جميع الحالات")
            {
                PaymentStatus status = selectedPaymentStatus switch
                {
                    "مدفوع" => PaymentStatus.مدفوع,
                    "غير مدفوع" => PaymentStatus.غير_مدفوع,
                    "مدفوع جزئياً" => PaymentStatus.مدفوع_جزئيا,
                    _ => PaymentStatus.غير_مدفوع
                };
                filtered = filtered.Where(s => s.PaymentStatus == status);
            }

            _filteredSales.Clear();
            foreach (var sale in filtered.OrderByDescending(s => s.SaleDate))
            {
                _filteredSales.Add(sale);
            }

            UpdateStatistics();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in ApplyFilters: {ex.Message}");
            // لا نعرض رسالة خطأ للمستخدم، فقط نسجل الخطأ
            // MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ",
            //     MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            var count = _filteredSales.Count;
            var totalAmount = _filteredSales.Sum(s => s.TotalAmount);
            var totalDiscount = _filteredSales.Sum(s => s.Discount);
            var netAmount = _filteredSales.Sum(s => s.NetAmount);

            TxtFilteredCount.Text = count.ToString();
            TxtFilteredTotal.Text = $"{totalAmount:N2} ج.م";
            TxtFilteredDiscount.Text = $"{totalDiscount:N2} ج.م";
            TxtFilteredNet.Text = $"{netAmount:N2} ج.م";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnBack_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _mainWindow.GoBack();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefreshData_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LoadSalesData();
            MessageBox.Show("تم تحديث البيانات بنجاح", "تحديث", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void DateFilter_Changed(object sender, SelectionChangedEventArgs e)
    {
        try
        {
            ApplyFilters();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق فلتر التاريخ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void TxtCustomerSearch_TextChanged(object sender, TextChangedEventArgs e)
    {
        try
        {
            ApplyFilters();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في البحث: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void TxtCustomerSearch_GotFocus(object sender, RoutedEventArgs e)
    {
        try
        {
            if (TxtCustomerSearch.Text == "البحث في أسماء العملاء...")
            {
                TxtCustomerSearch.Text = "";
                TxtCustomerSearch.Foreground = System.Windows.Media.Brushes.Black;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void TxtCustomerSearch_LostFocus(object sender, RoutedEventArgs e)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(TxtCustomerSearch.Text))
            {
                TxtCustomerSearch.Text = "البحث في أسماء العملاء...";
                TxtCustomerSearch.Foreground = System.Windows.Media.Brushes.Gray;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void ServiceFilter_Changed(object sender, SelectionChangedEventArgs e)
    {
        try
        {
            ApplyFilters();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق فلتر الخدمة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void GlassTypeFilter_Changed(object sender, SelectionChangedEventArgs e)
    {
        try
        {
            ApplyFilters();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق فلتر نوع الزجاج: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void GlassThicknessFilter_Changed(object sender, SelectionChangedEventArgs e)
    {
        try
        {
            ApplyFilters();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق فلتر سمك الزجاج: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void PaymentStatusFilter_Changed(object sender, SelectionChangedEventArgs e)
    {
        try
        {
            ApplyFilters();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تطبيق فلتر حالة الدفع: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnClearFilters_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            DateFrom.SelectedDate = DateTime.Today.AddDays(-30);
            DateTo.SelectedDate = DateTime.Today;
            TxtCustomerSearch.Text = "البحث في أسماء العملاء...";
            TxtCustomerSearch.Foreground = System.Windows.Media.Brushes.Gray;

            // مسح الفلاتر الجديدة
            CmbServiceFilter.SelectedIndex = 0;
            CmbGlassTypeFilter.SelectedIndex = 0;
            CmbGlassThicknessFilter.SelectedIndex = 0;
            CmbPaymentStatusFilter.SelectedIndex = 0;

            ApplyFilters();

            MessageBox.Show("تم مسح جميع الفلاتر", "مسح الفلاتر",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في مسح الفلاتر: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnViewInvoice_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (SalesReportDataGrid.SelectedItem is Sale selectedSale)
            {
                var viewWindow = new InvoiceViewWindow(selectedSale);
                viewWindow.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للعرض", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض الفاتورة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnEditInvoice_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (SalesReportDataGrid.SelectedItem is Sale selectedSale)
            {
                _mainWindow.ShowEditInvoice(selectedSale);
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للتعديل", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تعديل الفاتورة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnDeleteInvoice_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (SalesReportDataGrid.SelectedItem is Sale selectedSale)
            {
                var result = MessageBox.Show($"هل تريد حذف الفاتورة '{selectedSale.InvoiceNumber}'؟\nهذا الإجراء لا يمكن التراجع عنه.", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);
                
                if (result == MessageBoxResult.Yes)
                {
                    Task.Run(async () =>
                    {
                        try
                        {
                            bool success = await _salesService.DeleteSaleAsync(selectedSale.InvoiceNumber);
                            
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                if (success)
                                {
                                    _allSales.Remove(selectedSale);
                                    _filteredSales.Remove(selectedSale);
                                    UpdateStatistics();
                                    
                                    MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح", 
                                        MessageBoxButton.OK, MessageBoxImage.Information);
                                }
                                else
                                {
                                    MessageBox.Show("حدث خطأ أثناء حذف الفاتورة", "خطأ", 
                                        MessageBoxButton.OK, MessageBoxImage.Error);
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ", 
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                            });
                        }
                    });
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للحذف", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnPrintInvoice_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (SalesReportDataGrid.SelectedItem is Sale selectedSale)
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PDF Files|*.pdf",
                    FileName = $"Invoice_{selectedSale.InvoiceNumber}.pdf",
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var pdfService = new InvoicePdfService();
                    bool success = pdfService.CreateInvoicePdf(
                        selectedSale.Customer?.Name ?? "",
                        selectedSale.InvoiceNumber,
                        selectedSale.SaleDate,
                        new ObservableCollection<SaleItem>(selectedSale.SaleItems),
                        selectedSale.TotalAmount,
                        selectedSale.Discount,
                        selectedSale.NetAmount,
                        selectedSale.Notes,
                        saveDialog.FileName
                    );

                    if (success)
                    {
                        MessageBox.Show($"تم حفظ الفاتورة بنجاح في:\n{saveDialog.FileName}", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                    }
                    else
                    {
                        MessageBox.Show("حدث خطأ أثناء إنشاء ملف PDF", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للطباعة", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة الفاتورة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveDialog = new SaveFileDialog
            {
                Filter = "CSV Files|*.csv|Excel Files|*.xlsx",
                FileName = $"SalesReport_{DateTime.Now:yyyyMMdd}.csv",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
            };

            if (saveDialog.ShowDialog() == true)
            {
                var csvContent = "رقم الفاتورة,التاريخ,اسم العميل,المبلغ الإجمالي,الخصم,المبلغ الصافي,عدد العناصر,الخدمات\n";

                foreach (var sale in _filteredSales)
                {
                    csvContent += $"{sale.InvoiceNumber},{sale.SaleDate:yyyy/MM/dd},{sale.Customer?.Name ?? ""},{sale.TotalAmount:F2},{sale.Discount:F2},{sale.NetAmount:F2},{sale.SaleItems.Count},{sale.ServicesText}\n";
                }

                File.WriteAllText(saveDialog.FileName, csvContent, System.Text.Encoding.UTF8);

                MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{saveDialog.FileName}", "نجح",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تصدير التقرير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnPrintReport_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            MessageBox.Show("ميزة طباعة التقرير ستكون متاحة قريباً", "قريباً",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في طباعة التقرير: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
