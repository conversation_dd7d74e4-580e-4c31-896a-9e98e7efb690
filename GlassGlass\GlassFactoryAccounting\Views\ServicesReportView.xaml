<UserControl x:Class="GlassFactoryAccounting.Views.ServicesReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1000"
             FlowDirection="RightToLeft"
             FontFamily="Segoe UI"
             FontSize="14">
    
    <UserControl.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="InfoBrush" Color="#17A2B8"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </UserControl.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان مع زر العودة -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="BtnBack" Content="⬅️ عودة" 
                        Background="{StaticResource PrimaryBrush}" Foreground="White"
                        Padding="15,8" Margin="0,0,20,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnBack_Click"/>
                
                <TextBlock Text="🔧 تقرير خدمات المبيعات" FontSize="24" FontWeight="Bold" 
                         Foreground="{StaticResource InfoBrush}" VerticalAlignment="Center"/>
                
                <Button x:Name="BtnRefreshData" Content="🔄 تحديث" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="15,8" Margin="20,0,0,0" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnRefreshData_Click"/>
            </StackPanel>
        </Border>
        
        <!-- إحصائيات الخدمات -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي الخدمات" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtTotalServices" Text="0" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="أكثر خدمة استخداماً" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtTopService" Text="لا توجد" FontSize="16" FontWeight="Bold" 
                             Foreground="{StaticResource SuccessBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="إجمالي قيمة الخدمات" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtTotalValue" Text="0.00 ج.م" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource WarningBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" HorizontalAlignment="Center">
                    <TextBlock Text="متوسط قيمة الخدمة" FontWeight="Bold" HorizontalAlignment="Center"/>
                    <TextBlock x:Name="TxtAverageValue" Text="0.00 ج.م" FontSize="18" FontWeight="Bold" 
                             Foreground="{StaticResource DangerBrush}" HorizontalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- جدول تحليل الخدمات -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="📊 تحليل الخدمات المقدمة" FontSize="18" FontWeight="Bold" 
                         Margin="0,0,0,15" Foreground="{StaticResource InfoBrush}"/>
                
                <DataGrid x:Name="ServicesDataGrid" Grid.Row="1"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40"
                          FontSize="14">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الخدمة" Binding="{Binding ServiceName}" Width="200"/>
                        <DataGridTextColumn Header="عدد مرات الاستخدام" Binding="{Binding UsageCount}" Width="150"/>
                        <DataGridTextColumn Header="إجمالي الكمية" Binding="{Binding TotalQuantity, StringFormat='{}{0:F2}'}" Width="120"/>
                        <DataGridTextColumn Header="إجمالي القيمة" Binding="{Binding TotalValue, StringFormat='{}{0:N2} ج.م'}" Width="150"/>
                        <DataGridTextColumn Header="متوسط السعر" Binding="{Binding AveragePrice, StringFormat='{}{0:N2} ج.م'}" Width="120"/>
                        <DataGridTextColumn Header="النسبة من الإجمالي" Binding="{Binding Percentage, StringFormat='{}{0:F1}%'}" Width="150"/>
                        
                        <DataGridTemplateColumn Header="الرسم البياني" Width="150">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <ProgressBar Value="{Binding Percentage}" Maximum="100" Height="20" 
                                               Background="#E0E0E0" Foreground="{StaticResource InfoBrush}"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
    </Grid>
</UserControl>
