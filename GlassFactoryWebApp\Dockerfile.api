# Glass Factory Accounting System - Backend API Dockerfile
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

# Install curl for health checks
RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy csproj and restore dependencies
COPY ["GlassFactoryWebApp.csproj", "."]
RUN dotnet restore "GlassFactoryWebApp.csproj"

# Copy everything else and build
COPY . .
WORKDIR "/src"
RUN dotnet build "GlassFactoryWebApp.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "GlassFactoryWebApp.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app

# Create directories for uploads and reports
RUN mkdir -p /app/uploads /app/reports /app/logs

# Copy published app
COPY --from=publish /app/publish .

# Set environment variables
ENV ASPNETCORE_ENVIRONMENT=Production
ENV ASPNETCORE_URLS=http://+:80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

ENTRYPOINT ["dotnet", "GlassFactoryWebApp.dll"]
