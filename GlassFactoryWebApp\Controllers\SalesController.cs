using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using GlassFactoryWebApp.Services;
using GlassFactoryWebApp.DTOs;
using GlassFactoryWebApp.Models;

namespace GlassFactoryWebApp.Controllers
{
    /// <summary>
    /// تحكم في عمليات المبيعات
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SalesController : ControllerBase
    {
        private readonly ISalesService _salesService;
        private readonly ILogger<SalesController> _logger;

        public SalesController(ISalesService salesService, ILogger<SalesController> logger)
        {
            _salesService = salesService;
            _logger = logger;
        }

        /// <summary>
        /// الحصول على جميع فواتير المبيعات
        /// </summary>
        [HttpGet]
        public async Task<ActionResult<ApiResponse<PagedResult<SalesInvoiceDto>>>> GetSalesInvoices(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            [FromQuery] string? search = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] string? status = null)
        {
            try
            {
                var result = await _salesService.GetSalesInvoicesAsync(page, pageSize, search, fromDate, toDate, status);
                return Ok(ApiResponse<PagedResult<SalesInvoiceDto>>.Success(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales invoices");
                return StatusCode(500, ApiResponse<PagedResult<SalesInvoiceDto>>.Error("حدث خطأ أثناء جلب فواتير المبيعات"));
            }
        }

        /// <summary>
        /// الحصول على فاتورة مبيعات محددة
        /// </summary>
        [HttpGet("{id}")]
        public async Task<ActionResult<ApiResponse<SalesInvoiceDto>>> GetSalesInvoice(int id)
        {
            try
            {
                var invoice = await _salesService.GetSalesInvoiceByIdAsync(id);
                if (invoice == null)
                {
                    return NotFound(ApiResponse<SalesInvoiceDto>.Error("الفاتورة غير موجودة"));
                }

                return Ok(ApiResponse<SalesInvoiceDto>.Success(invoice));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales invoice {Id}", id);
                return StatusCode(500, ApiResponse<SalesInvoiceDto>.Error("حدث خطأ أثناء جلب الفاتورة"));
            }
        }

        /// <summary>
        /// إنشاء فاتورة مبيعات جديدة
        /// </summary>
        [HttpPost]
        public async Task<ActionResult<ApiResponse<SalesInvoiceDto>>> CreateSalesInvoice([FromBody] CreateSalesInvoiceDto createDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<SalesInvoiceDto>.Error("البيانات المدخلة غير صحيحة"));
                }

                var invoice = await _salesService.CreateSalesInvoiceAsync(createDto);
                return CreatedAtAction(nameof(GetSalesInvoice), new { id = invoice.Id }, 
                    ApiResponse<SalesInvoiceDto>.Success(invoice));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating sales invoice");
                return StatusCode(500, ApiResponse<SalesInvoiceDto>.Error("حدث خطأ أثناء إنشاء الفاتورة"));
            }
        }

        /// <summary>
        /// تحديث فاتورة مبيعات
        /// </summary>
        [HttpPut("{id}")]
        public async Task<ActionResult<ApiResponse<SalesInvoiceDto>>> UpdateSalesInvoice(int id, [FromBody] UpdateSalesInvoiceDto updateDto)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse<SalesInvoiceDto>.Error("البيانات المدخلة غير صحيحة"));
                }

                var invoice = await _salesService.UpdateSalesInvoiceAsync(id, updateDto);
                if (invoice == null)
                {
                    return NotFound(ApiResponse<SalesInvoiceDto>.Error("الفاتورة غير موجودة"));
                }

                return Ok(ApiResponse<SalesInvoiceDto>.Success(invoice));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating sales invoice {Id}", id);
                return StatusCode(500, ApiResponse<SalesInvoiceDto>.Error("حدث خطأ أثناء تحديث الفاتورة"));
            }
        }

        /// <summary>
        /// حذف فاتورة مبيعات
        /// </summary>
        [HttpDelete("{id}")]
        public async Task<ActionResult<ApiResponse<bool>>> DeleteSalesInvoice(int id)
        {
            try
            {
                var result = await _salesService.DeleteSalesInvoiceAsync(id);
                if (!result)
                {
                    return NotFound(ApiResponse<bool>.Error("الفاتورة غير موجودة"));
                }

                return Ok(ApiResponse<bool>.Success(true, "تم حذف الفاتورة بنجاح"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting sales invoice {Id}", id);
                return StatusCode(500, ApiResponse<bool>.Error("حدث خطأ أثناء حذف الفاتورة"));
            }
        }

        /// <summary>
        /// طباعة فاتورة مبيعات
        /// </summary>
        [HttpGet("{id}/print")]
        public async Task<ActionResult> PrintSalesInvoice(int id)
        {
            try
            {
                var pdfBytes = await _salesService.GenerateInvoicePdfAsync(id);
                if (pdfBytes == null)
                {
                    return NotFound(ApiResponse<object>.Error("الفاتورة غير موجودة"));
                }

                return File(pdfBytes, "application/pdf", $"sales-invoice-{id}.pdf");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error printing sales invoice {Id}", id);
                return StatusCode(500, ApiResponse<object>.Error("حدث خطأ أثناء طباعة الفاتورة"));
            }
        }

        /// <summary>
        /// ترحيل فاتورة مبيعات
        /// </summary>
        [HttpPost("{id}/post")]
        public async Task<ActionResult<ApiResponse<bool>>> PostSalesInvoice(int id)
        {
            try
            {
                var result = await _salesService.PostSalesInvoiceAsync(id);
                if (!result)
                {
                    return BadRequest(ApiResponse<bool>.Error("لا يمكن ترحيل الفاتورة"));
                }

                return Ok(ApiResponse<bool>.Success(true, "تم ترحيل الفاتورة بنجاح"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error posting sales invoice {Id}", id);
                return StatusCode(500, ApiResponse<bool>.Error("حدث خطأ أثناء ترحيل الفاتورة"));
            }
        }

        /// <summary>
        /// إلغاء ترحيل فاتورة مبيعات
        /// </summary>
        [HttpPost("{id}/unpost")]
        public async Task<ActionResult<ApiResponse<bool>>> UnpostSalesInvoice(int id)
        {
            try
            {
                var result = await _salesService.UnpostSalesInvoiceAsync(id);
                if (!result)
                {
                    return BadRequest(ApiResponse<bool>.Error("لا يمكن إلغاء ترحيل الفاتورة"));
                }

                return Ok(ApiResponse<bool>.Success(true, "تم إلغاء ترحيل الفاتورة بنجاح"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error unposting sales invoice {Id}", id);
                return StatusCode(500, ApiResponse<bool>.Error("حدث خطأ أثناء إلغاء ترحيل الفاتورة"));
            }
        }

        /// <summary>
        /// الحصول على إحصائيات المبيعات
        /// </summary>
        [HttpGet("statistics")]
        public async Task<ActionResult<ApiResponse<SalesStatisticsDto>>> GetSalesStatistics(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var statistics = await _salesService.GetSalesStatisticsAsync(fromDate, toDate);
                return Ok(ApiResponse<SalesStatisticsDto>.Success(statistics));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting sales statistics");
                return StatusCode(500, ApiResponse<SalesStatisticsDto>.Error("حدث خطأ أثناء جلب إحصائيات المبيعات"));
            }
        }

        /// <summary>
        /// الحصول على تقرير المبيعات
        /// </summary>
        [HttpGet("report")]
        public async Task<ActionResult> GetSalesReport(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null,
            [FromQuery] int? customerId = null,
            [FromQuery] string format = "pdf")
        {
            try
            {
                var reportBytes = await _salesService.GenerateSalesReportAsync(fromDate, toDate, customerId, format);
                var contentType = format.ToLower() == "excel" ? "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" : "application/pdf";
                var fileName = $"sales-report-{DateTime.Now:yyyyMMdd}.{(format.ToLower() == "excel" ? "xlsx" : "pdf")}";

                return File(reportBytes, contentType, fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating sales report");
                return StatusCode(500, ApiResponse<object>.Error("حدث خطأ أثناء إنشاء التقرير"));
            }
        }
    }
}
