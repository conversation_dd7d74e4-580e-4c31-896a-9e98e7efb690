<Window x:Class="GlassFactoryAccounting.Views.ItemEditWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="تعديل الصنف" Height="500" Width="600"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        ResizeMode="NoResize">

    <Window.Resources>
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#757575"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- العنوان -->
        <TextBlock Grid.Row="0" Text="تعديل بيانات الصنف" FontSize="18" FontWeight="Bold" 
                   HorizontalAlignment="Center" Margin="0,0,0,20"/>

        <!-- الصف الأول -->
        <Grid Grid.Row="1" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="اسم الصنف:" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtName" Height="35" VerticalContentAlignment="Center"
                         FontSize="14" Padding="10"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Margin="10,0,0,0">
                <TextBlock Text="المخزن:" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CmbWarehouse" Height="35" VerticalContentAlignment="Center"
                          FontSize="14" Padding="10" DisplayMemberPath="Name" SelectedValuePath="Id"/>
            </StackPanel>
        </Grid>

        <!-- الصف الثاني -->
        <Grid Grid.Row="2" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="وحدة القياس:" FontWeight="Bold" Margin="0,0,0,5"/>
                <ComboBox x:Name="CmbUnitOfMeasure" Height="35" VerticalContentAlignment="Center"
                          FontSize="14" Padding="10"/>
            </StackPanel>

            <StackPanel Grid.Column="1" VerticalAlignment="Bottom" Margin="10,0,0,0">
                <CheckBox x:Name="ChkHasDimensions" Content="له أبعاد؟" FontWeight="Bold"
                          Checked="ChkHasDimensions_Checked" Unchecked="ChkHasDimensions_Unchecked"/>
            </StackPanel>
        </Grid>

        <!-- الأبعاد -->
        <Grid Grid.Row="3" x:Name="DimensionsPanel" Visibility="Collapsed" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <TextBlock Text="الطول (ملم):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtLength" Height="35" VerticalContentAlignment="Center"
                         FontSize="14" Padding="10" TextChanged="Dimensions_TextChanged"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Margin="5,0,5,0">
                <TextBlock Text="العرض (ملم):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtWidth" Height="35" VerticalContentAlignment="Center"
                         FontSize="14" Padding="10" TextChanged="Dimensions_TextChanged"/>
            </StackPanel>

            <StackPanel Grid.Column="2" Margin="10,0,0,0">
                <TextBlock Text="المساحة (م²):" FontWeight="Bold" Margin="0,0,0,5"/>
                <TextBox x:Name="TxtArea" Height="35" VerticalContentAlignment="Center"
                         FontSize="14" Padding="10" IsReadOnly="True" Background="#F5F5F5"/>
            </StackPanel>
        </Grid>

        <!-- الوصف -->
        <StackPanel Grid.Row="4" Margin="0,0,0,15">
            <TextBlock Text="الوصف:" FontWeight="Bold" Margin="0,0,0,5"/>
            <TextBox x:Name="TxtDescription" Height="35" VerticalContentAlignment="Center"
                     FontSize="14" Padding="10"/>
        </StackPanel>

        <!-- الأزرار -->
        <StackPanel Grid.Row="7" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button x:Name="BtnSave" Content="💾 حفظ" 
                    Style="{StaticResource SuccessButtonStyle}"
                    Margin="0,0,10,0" Click="BtnSave_Click"/>
            <Button x:Name="BtnCancel" Content="❌ إلغاء" 
                    Style="{StaticResource SecondaryButtonStyle}"
                    Click="BtnCancel_Click"/>
        </StackPanel>
    </Grid>
</Window>
