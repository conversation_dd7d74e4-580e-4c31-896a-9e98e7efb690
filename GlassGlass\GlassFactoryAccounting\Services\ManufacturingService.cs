using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using System.Text.Json;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة موديول التصنيع
    /// </summary>
    public class ManufacturingService
    {
        private readonly DatabaseContext _context;

        public ManufacturingService()
        {
            _context = new DatabaseContext();
            
            // تهيئة قاعدة البيانات
            try
            {
                var task = _context.InitializeDatabaseAsync();
                task.Wait();
                System.Diagnostics.Debug.WriteLine("Database initialized in ManufacturingService");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing database in ManufacturingService: {ex.Message}");
            }
            
            InitializeManufacturingTables();
        }

        #region Database Initialization

        /// <summary>
        /// إنشاء جداول موديول التصنيع
        /// </summary>
        private void InitializeManufacturingTables()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var commands = new[]
                {
                    // جدول أوامر التصنيع
                    @"CREATE TABLE IF NOT EXISTS ManufacturingOrders (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        OrderNumber TEXT NOT NULL UNIQUE,
                        CustomerName TEXT NOT NULL,
                        InvoiceNumber TEXT,
                        OrderDate TEXT NOT NULL,
                        OrderStatus TEXT NOT NULL DEFAULT 'تحت التشغيل',
                        TotalServicesCost DECIMAL DEFAULT 0,
                        TotalAdditionalCosts DECIMAL DEFAULT 0,
                        TotalCost DECIMAL DEFAULT 0,
                        TotalSquareMeters DECIMAL DEFAULT 0,
                        PricePerMeter DECIMAL DEFAULT 0,
                        Notes TEXT,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1
                    )",

                    // جدول ألواح الزجاج
                    @"CREATE TABLE IF NOT EXISTS GlassPanels (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ManufacturingOrderId INTEGER NOT NULL,
                        GlassType TEXT NOT NULL,
                        Thickness TEXT NOT NULL,
                        Length DECIMAL NOT NULL,
                        Width DECIMAL NOT NULL,
                        SquareMeters DECIMAL NOT NULL,
                        Quantity INTEGER NOT NULL DEFAULT 1,
                        TotalSquareMeters DECIMAL NOT NULL,
                        Notes TEXT,
                        FOREIGN KEY (ManufacturingOrderId) REFERENCES ManufacturingOrders(Id)
                    )",

                    // جدول الخدمات
                    @"CREATE TABLE IF NOT EXISTS ManufacturingServices (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ManufacturingOrderId INTEGER NOT NULL,
                        ServiceType TEXT NOT NULL,
                        IsEnabled INTEGER NOT NULL DEFAULT 0,
                        ServiceData TEXT,
                        FOREIGN KEY (ManufacturingOrderId) REFERENCES ManufacturingOrders(Id)
                    )",

                    // جدول المقاسات المطلوبة
                    @"CREATE TABLE IF NOT EXISTS RequiredSizes (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ManufacturingOrderId INTEGER NOT NULL,
                        RefCode TEXT NOT NULL UNIQUE,
                        GlassType TEXT NOT NULL,
                        Thickness TEXT NOT NULL,
                        Length DECIMAL NOT NULL,
                        Width DECIMAL NOT NULL,
                        SquareMeters DECIMAL NOT NULL,
                        Quantity INTEGER NOT NULL DEFAULT 1,
                        TotalSquareMeters DECIMAL NOT NULL,
                        IsDelivered INTEGER NOT NULL DEFAULT 0,
                        FOREIGN KEY (ManufacturingOrderId) REFERENCES ManufacturingOrders(Id)
                    )",

                    // جدول تكلفة الخدمات
                    @"CREATE TABLE IF NOT EXISTS ServiceCosts (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ManufacturingOrderId INTEGER NOT NULL,
                        ServiceName TEXT NOT NULL,
                        Description TEXT,
                        Quantity DECIMAL NOT NULL DEFAULT 1,
                        Price DECIMAL NOT NULL DEFAULT 0,
                        Value DECIMAL NOT NULL DEFAULT 0,
                        FOREIGN KEY (ManufacturingOrderId) REFERENCES ManufacturingOrders(Id)
                    )",

                    // جدول التكاليف الإضافية
                    @"CREATE TABLE IF NOT EXISTS AdditionalCosts (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ManufacturingOrderId INTEGER NOT NULL,
                        Description TEXT NOT NULL,
                        Value DECIMAL NOT NULL DEFAULT 0,
                        Notes TEXT,
                        FOREIGN KEY (ManufacturingOrderId) REFERENCES ManufacturingOrders(Id)
                    )",

                    // جدول أوامر التسليم
                    @"CREATE TABLE IF NOT EXISTS DeliveryOrders (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        DeliveryOrderNumber TEXT NOT NULL UNIQUE,
                        ManufacturingOrderId INTEGER NOT NULL,
                        CustomerName TEXT NOT NULL,
                        InvoiceNumber TEXT,
                        WorkOrderNumber TEXT,
                        ProjectName TEXT,
                        TotalPieces INTEGER DEFAULT 0,
                        TotalSquareMeters DECIMAL DEFAULT 0,
                        DeliveryResponsible TEXT,
                        ReceiverSignature TEXT,
                        DeliveryDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        CreatedDate TEXT NOT NULL,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        FOREIGN KEY (ManufacturingOrderId) REFERENCES ManufacturingOrders(Id)
                    )",

                    // جدول المقاسات المسلمة
                    @"CREATE TABLE IF NOT EXISTS DeliveredSizes (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        DeliveryOrderId INTEGER NOT NULL,
                        RefCode TEXT NOT NULL,
                        GlassType TEXT NOT NULL,
                        Thickness TEXT NOT NULL,
                        Length DECIMAL NOT NULL,
                        Width DECIMAL NOT NULL,
                        Quantity INTEGER NOT NULL DEFAULT 1,
                        FOREIGN KEY (DeliveryOrderId) REFERENCES DeliveryOrders(Id)
                    )",

                    // جدول عداد كود المقاسات
                    @"CREATE TABLE IF NOT EXISTS SizeCodeCounters (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Prefix TEXT NOT NULL DEFAULT 'A',
                        CurrentNumber INTEGER NOT NULL DEFAULT 1,
                        LastUpdated TEXT NOT NULL
                    )"
                };

                foreach (var commandText in commands)
                {
                    using var command = new SQLiteCommand(commandText, connection);
                    command.ExecuteNonQuery();
                }

                // إنشاء عداد كود المقاسات الافتراضي
                InitializeSizeCodeCounter(connection);

                System.Diagnostics.Debug.WriteLine("Manufacturing tables initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing manufacturing tables: {ex.Message}");
            }
        }

        /// <summary>
        /// تهيئة عداد كود المقاسات
        /// </summary>
        private void InitializeSizeCodeCounter(SQLiteConnection connection)
        {
            try
            {
                var checkQuery = "SELECT COUNT(*) FROM SizeCodeCounters";
                using var checkCommand = new SQLiteCommand(checkQuery, connection);
                var count = Convert.ToInt32(checkCommand.ExecuteScalar());

                if (count == 0)
                {
                    var insertQuery = @"
                        INSERT INTO SizeCodeCounters (Prefix, CurrentNumber, LastUpdated)
                        VALUES ('A', 1, @LastUpdated)";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@LastUpdated", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    insertCommand.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing size code counter: {ex.Message}");
            }
        }

        #endregion

        #region Manufacturing Orders

        /// <summary>
        /// إنشاء رقم أمر تصنيع جديد
        /// </summary>
        public string GenerateOrderNumber()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT COUNT(*) FROM ManufacturingOrders WHERE IsActive = 1";
                using var command = new SQLiteCommand(query, connection);
                var count = Convert.ToInt32(command.ExecuteScalar());

                return $"MO{DateTime.Now:yyyyMM}{(count + 1):D4}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating order number: {ex.Message}");
                return $"MO{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// حفظ أمر تصنيع جديد
        /// </summary>
        public bool SaveManufacturingOrder(NewManufacturingOrder order)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var insertQuery = @"
                    INSERT INTO ManufacturingOrders (
                        OrderNumber, CustomerName, InvoiceNumber, OrderDate, OrderStatus,
                        TotalServicesCost, TotalAdditionalCosts, TotalCost, TotalSquareMeters, PricePerMeter,
                        Notes, CreatedDate, CreatedBy, IsActive
                    ) VALUES (
                        @OrderNumber, @CustomerName, @InvoiceNumber, @OrderDate, @OrderStatus,
                        @TotalServicesCost, @TotalAdditionalCosts, @TotalCost, @TotalSquareMeters, @PricePerMeter,
                        @Notes, @CreatedDate, @CreatedBy, @IsActive
                    )";

                using var command = new SQLiteCommand(insertQuery, connection);
                command.Parameters.AddWithValue("@OrderNumber", order.OrderNumber);
                command.Parameters.AddWithValue("@CustomerName", order.CustomerName);
                command.Parameters.AddWithValue("@InvoiceNumber", order.InvoiceNumber ?? "");
                command.Parameters.AddWithValue("@OrderDate", order.OrderDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@OrderStatus", order.OrderStatus);
                command.Parameters.AddWithValue("@TotalServicesCost", order.TotalServicesCost);
                command.Parameters.AddWithValue("@TotalAdditionalCosts", order.TotalAdditionalCosts);
                command.Parameters.AddWithValue("@TotalCost", order.TotalCost);
                command.Parameters.AddWithValue("@TotalSquareMeters", order.TotalSquareMeters);
                command.Parameters.AddWithValue("@PricePerMeter", order.PricePerMeter);
                command.Parameters.AddWithValue("@Notes", order.Notes ?? "");
                command.Parameters.AddWithValue("@CreatedDate", order.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@CreatedBy", order.CreatedBy);
                command.Parameters.AddWithValue("@IsActive", order.IsActive ? 1 : 0);

                var result = command.ExecuteNonQuery();
                
                if (result > 0)
                {
                    // الحصول على ID الأمر المحفوظ
                    var getIdQuery = "SELECT last_insert_rowid()";
                    using var getIdCommand = new SQLiteCommand(getIdQuery, connection);
                    order.Id = Convert.ToInt32(getIdCommand.ExecuteScalar());
                    
                    System.Diagnostics.Debug.WriteLine($"Manufacturing order saved successfully with ID: {order.Id}");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving manufacturing order: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على جميع أوامر التصنيع
        /// </summary>
        public List<NewManufacturingOrder> GetAllManufacturingOrders()
        {
            var orders = new List<NewManufacturingOrder>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    SELECT * FROM ManufacturingOrders 
                    WHERE IsActive = 1 
                    ORDER BY CreatedDate DESC";

                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var order = new NewManufacturingOrder
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        OrderNumber = reader["OrderNumber"]?.ToString() ?? "",
                        CustomerName = reader["CustomerName"]?.ToString() ?? "",
                        InvoiceNumber = reader["InvoiceNumber"]?.ToString() ?? "",
                        OrderStatus = reader["OrderStatus"]?.ToString() ?? "",
                        TotalServicesCost = Convert.ToDecimal(reader["TotalServicesCost"]),
                        TotalAdditionalCosts = Convert.ToDecimal(reader["TotalAdditionalCosts"]),
                        TotalCost = Convert.ToDecimal(reader["TotalCost"]),
                        TotalSquareMeters = Convert.ToDecimal(reader["TotalSquareMeters"]),
                        PricePerMeter = Convert.ToDecimal(reader["PricePerMeter"]),
                        Notes = reader["Notes"]?.ToString() ?? "",
                        CreatedBy = reader["CreatedBy"]?.ToString() ?? "",
                        IsActive = Convert.ToInt32(reader["IsActive"]) == 1
                    };

                    // تحويل التواريخ
                    if (DateTime.TryParse(reader["OrderDate"]?.ToString(), out DateTime orderDate))
                        order.OrderDate = orderDate;

                    if (DateTime.TryParse(reader["CreatedDate"]?.ToString(), out DateTime createdDate))
                        order.CreatedDate = createdDate;

                    if (reader["ModifiedDate"] != DBNull.Value && 
                        DateTime.TryParse(reader["ModifiedDate"]?.ToString(), out DateTime modifiedDate))
                        order.ModifiedDate = modifiedDate;

                    orders.Add(order);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting manufacturing orders: {ex.Message}");
            }

            return orders;
        }

        /// <summary>
        /// حذف أمر تصنيع
        /// </summary>
        public bool DeleteManufacturingOrder(int orderId)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // تحديث حالة الأمر إلى غير نشط بدلاً من الحذف الفعلي
                var updateQuery = "UPDATE ManufacturingOrders SET IsActive = 0 WHERE Id = @Id";
                using var command = new SQLiteCommand(updateQuery, connection);
                command.Parameters.AddWithValue("@Id", orderId);

                return command.ExecuteNonQuery() > 0;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting manufacturing order: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Size Code Generation

        /// <summary>
        /// توليد كود مقاس فريد
        /// </summary>
        public string GenerateUniqueRefCode()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // الحصول على العداد الحالي
                var getCounterQuery = "SELECT Prefix, CurrentNumber FROM SizeCodeCounters ORDER BY Id DESC LIMIT 1";
                using var getCounterCommand = new SQLiteCommand(getCounterQuery, connection);
                using var reader = getCounterCommand.ExecuteReader();

                string prefix = "A";
                int currentNumber = 1;

                if (reader.Read())
                {
                    prefix = reader["Prefix"]?.ToString() ?? "A";
                    currentNumber = Convert.ToInt32(reader["CurrentNumber"]);
                }
                reader.Close();

                // توليد الكود
                string refCode = $"{prefix}{currentNumber:D3}";

                // التحقق من عدم تكرار الكود
                var checkQuery = "SELECT COUNT(*) FROM RequiredSizes WHERE RefCode = @RefCode";
                using var checkCommand = new SQLiteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@RefCode", refCode);
                var exists = Convert.ToInt32(checkCommand.ExecuteScalar()) > 0;

                if (exists)
                {
                    // إذا كان الكود موجود، زيادة العداد
                    currentNumber++;
                    
                    // إذا وصل العداد إلى 999، انتقل للحرف التالي
                    if (currentNumber > 999)
                    {
                        prefix = GetNextPrefix(prefix);
                        currentNumber = 1;
                    }
                    
                    refCode = $"{prefix}{currentNumber:D3}";
                }

                // تحديث العداد
                var updateQuery = @"
                    UPDATE SizeCodeCounters 
                    SET Prefix = @Prefix, CurrentNumber = @CurrentNumber, LastUpdated = @LastUpdated
                    WHERE Id = (SELECT Id FROM SizeCodeCounters ORDER BY Id DESC LIMIT 1)";

                using var updateCommand = new SQLiteCommand(updateQuery, connection);
                updateCommand.Parameters.AddWithValue("@Prefix", prefix);
                updateCommand.Parameters.AddWithValue("@CurrentNumber", currentNumber + 1);
                updateCommand.Parameters.AddWithValue("@LastUpdated", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                updateCommand.ExecuteNonQuery();

                return refCode;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating unique ref code: {ex.Message}");
                return $"ERR{DateTime.Now:HHmmss}";
            }
        }

        /// <summary>
        /// الحصول على البادئة التالية
        /// </summary>
        private string GetNextPrefix(string currentPrefix)
        {
            if (string.IsNullOrEmpty(currentPrefix) || currentPrefix.Length == 0)
                return "A";

            char lastChar = currentPrefix[currentPrefix.Length - 1];
            
            if (lastChar == 'Z')
            {
                // إذا وصل إلى Z، أضف حرف جديد
                return currentPrefix + "A";
            }
            else
            {
                // انتقل للحرف التالي
                char nextChar = (char)(lastChar + 1);
                return currentPrefix.Substring(0, currentPrefix.Length - 1) + nextChar;
            }
        }

        #endregion

        #region Glass Panels

        /// <summary>
        /// حفظ ألواح الزجاج
        /// </summary>
        public bool SaveGlassPanels(int manufacturingOrderId, List<GlassPanel> panels)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // حذف الألواح الموجودة
                var deleteQuery = "DELETE FROM GlassPanels WHERE ManufacturingOrderId = @OrderId";
                using var deleteCommand = new SQLiteCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                deleteCommand.ExecuteNonQuery();

                // إضافة الألواح الجديدة
                foreach (var panel in panels)
                {
                    var insertQuery = @"
                        INSERT INTO GlassPanels (
                            ManufacturingOrderId, GlassType, Thickness, Length, Width,
                            SquareMeters, Quantity, TotalSquareMeters, Notes
                        ) VALUES (
                            @OrderId, @GlassType, @Thickness, @Length, @Width,
                            @SquareMeters, @Quantity, @TotalSquareMeters, @Notes
                        )";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                    insertCommand.Parameters.AddWithValue("@GlassType", panel.GlassType);
                    insertCommand.Parameters.AddWithValue("@Thickness", panel.Thickness);
                    insertCommand.Parameters.AddWithValue("@Length", panel.Length);
                    insertCommand.Parameters.AddWithValue("@Width", panel.Width);
                    insertCommand.Parameters.AddWithValue("@SquareMeters", panel.SquareMeters);
                    insertCommand.Parameters.AddWithValue("@Quantity", panel.Quantity);
                    insertCommand.Parameters.AddWithValue("@TotalSquareMeters", panel.TotalSquareMeters);
                    insertCommand.Parameters.AddWithValue("@Notes", panel.Notes ?? "");

                    insertCommand.ExecuteNonQuery();
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving glass panels: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على ألواح الزجاج لأمر تصنيع
        /// </summary>
        public List<GlassPanel> GetGlassPanels(int manufacturingOrderId)
        {
            var panels = new List<GlassPanel>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM GlassPanels WHERE ManufacturingOrderId = @OrderId";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var panel = new GlassPanel
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        ManufacturingOrderId = Convert.ToInt32(reader["ManufacturingOrderId"]),
                        GlassType = reader["GlassType"]?.ToString() ?? "",
                        Thickness = reader["Thickness"]?.ToString() ?? "",
                        Length = Convert.ToDecimal(reader["Length"]),
                        Width = Convert.ToDecimal(reader["Width"]),
                        SquareMeters = Convert.ToDecimal(reader["SquareMeters"]),
                        Quantity = Convert.ToInt32(reader["Quantity"]),
                        TotalSquareMeters = Convert.ToDecimal(reader["TotalSquareMeters"]),
                        Notes = reader["Notes"]?.ToString() ?? ""
                    };

                    panels.Add(panel);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting glass panels: {ex.Message}");
            }

            return panels;
        }

        #endregion

        #region Required Sizes

        /// <summary>
        /// حفظ المقاسات المطلوبة
        /// </summary>
        public bool SaveRequiredSizes(int manufacturingOrderId, List<RequiredSize> sizes)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // حذف المقاسات الموجودة
                var deleteQuery = "DELETE FROM RequiredSizes WHERE ManufacturingOrderId = @OrderId";
                using var deleteCommand = new SQLiteCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                deleteCommand.ExecuteNonQuery();

                // إضافة المقاسات الجديدة
                foreach (var size in sizes)
                {
                    var insertQuery = @"
                        INSERT INTO RequiredSizes (
                            ManufacturingOrderId, RefCode, GlassType, Thickness, Length, Width,
                            SquareMeters, Quantity, TotalSquareMeters, IsDelivered
                        ) VALUES (
                            @OrderId, @RefCode, @GlassType, @Thickness, @Length, @Width,
                            @SquareMeters, @Quantity, @TotalSquareMeters, @IsDelivered
                        )";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                    insertCommand.Parameters.AddWithValue("@RefCode", size.RefCode);
                    insertCommand.Parameters.AddWithValue("@GlassType", size.GlassType);
                    insertCommand.Parameters.AddWithValue("@Thickness", size.Thickness);
                    insertCommand.Parameters.AddWithValue("@Length", size.Length);
                    insertCommand.Parameters.AddWithValue("@Width", size.Width);
                    insertCommand.Parameters.AddWithValue("@SquareMeters", size.SquareMeters);
                    insertCommand.Parameters.AddWithValue("@Quantity", size.Quantity);
                    insertCommand.Parameters.AddWithValue("@TotalSquareMeters", size.TotalSquareMeters);
                    insertCommand.Parameters.AddWithValue("@IsDelivered", size.IsDelivered ? 1 : 0);

                    insertCommand.ExecuteNonQuery();
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving required sizes: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على المقاسات المطلوبة لأمر تصنيع
        /// </summary>
        public List<RequiredSize> GetRequiredSizes(int manufacturingOrderId)
        {
            var sizes = new List<RequiredSize>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM RequiredSizes WHERE ManufacturingOrderId = @OrderId";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var size = new RequiredSize
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        ManufacturingOrderId = Convert.ToInt32(reader["ManufacturingOrderId"]),
                        RefCode = reader["RefCode"]?.ToString() ?? "",
                        GlassType = reader["GlassType"]?.ToString() ?? "",
                        Thickness = reader["Thickness"]?.ToString() ?? "",
                        Length = Convert.ToDecimal(reader["Length"]),
                        Width = Convert.ToDecimal(reader["Width"]),
                        SquareMeters = Convert.ToDecimal(reader["SquareMeters"]),
                        Quantity = Convert.ToInt32(reader["Quantity"]),
                        TotalSquareMeters = Convert.ToDecimal(reader["TotalSquareMeters"]),
                        IsDelivered = Convert.ToInt32(reader["IsDelivered"]) == 1
                    };

                    sizes.Add(size);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting required sizes: {ex.Message}");
            }

            return sizes;
        }

        #endregion

        #region Service Costs

        /// <summary>
        /// حفظ تكاليف الخدمات
        /// </summary>
        public bool SaveServiceCosts(int manufacturingOrderId, List<ServiceCost> costs)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // حذف التكاليف الموجودة
                var deleteQuery = "DELETE FROM ServiceCosts WHERE ManufacturingOrderId = @OrderId";
                using var deleteCommand = new SQLiteCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                deleteCommand.ExecuteNonQuery();

                // إضافة التكاليف الجديدة
                foreach (var cost in costs)
                {
                    var insertQuery = @"
                        INSERT INTO ServiceCosts (
                            ManufacturingOrderId, ServiceName, Description, Quantity, Price, Value
                        ) VALUES (
                            @OrderId, @ServiceName, @Description, @Quantity, @Price, @Value
                        )";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                    insertCommand.Parameters.AddWithValue("@ServiceName", cost.ServiceName);
                    insertCommand.Parameters.AddWithValue("@Description", cost.Description ?? "");
                    insertCommand.Parameters.AddWithValue("@Quantity", cost.Quantity);
                    insertCommand.Parameters.AddWithValue("@Price", cost.Price);
                    insertCommand.Parameters.AddWithValue("@Value", cost.Value);

                    insertCommand.ExecuteNonQuery();
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving service costs: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على تكاليف الخدمات لأمر تصنيع
        /// </summary>
        public List<ServiceCost> GetServiceCosts(int manufacturingOrderId)
        {
            var costs = new List<ServiceCost>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM ServiceCosts WHERE ManufacturingOrderId = @OrderId";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var cost = new ServiceCost
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        ManufacturingOrderId = Convert.ToInt32(reader["ManufacturingOrderId"]),
                        ServiceName = reader["ServiceName"]?.ToString() ?? "",
                        Description = reader["Description"]?.ToString() ?? "",
                        Quantity = Convert.ToDecimal(reader["Quantity"]),
                        Price = Convert.ToDecimal(reader["Price"]),
                        Value = Convert.ToDecimal(reader["Value"])
                    };

                    costs.Add(cost);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting service costs: {ex.Message}");
            }

            return costs;
        }

        #endregion

        #region Additional Costs

        /// <summary>
        /// حفظ التكاليف الإضافية
        /// </summary>
        public bool SaveAdditionalCosts(int manufacturingOrderId, List<AdditionalCost> costs)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // حذف التكاليف الموجودة
                var deleteQuery = "DELETE FROM AdditionalCosts WHERE ManufacturingOrderId = @OrderId";
                using var deleteCommand = new SQLiteCommand(deleteQuery, connection);
                deleteCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                deleteCommand.ExecuteNonQuery();

                // إضافة التكاليف الجديدة
                foreach (var cost in costs)
                {
                    var insertQuery = @"
                        INSERT INTO AdditionalCosts (
                            ManufacturingOrderId, Description, Value, Notes
                        ) VALUES (
                            @OrderId, @Description, @Value, @Notes
                        )";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                    insertCommand.Parameters.AddWithValue("@Description", cost.Description);
                    insertCommand.Parameters.AddWithValue("@Value", cost.Value);
                    insertCommand.Parameters.AddWithValue("@Notes", cost.Notes ?? "");

                    insertCommand.ExecuteNonQuery();
                }

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving additional costs: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على التكاليف الإضافية لأمر تصنيع
        /// </summary>
        public List<AdditionalCost> GetAdditionalCosts(int manufacturingOrderId)
        {
            var costs = new List<AdditionalCost>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM AdditionalCosts WHERE ManufacturingOrderId = @OrderId";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@OrderId", manufacturingOrderId);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    var cost = new AdditionalCost
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        ManufacturingOrderId = Convert.ToInt32(reader["ManufacturingOrderId"]),
                        Description = reader["Description"]?.ToString() ?? "",
                        Value = Convert.ToDecimal(reader["Value"]),
                        Notes = reader["Notes"]?.ToString() ?? ""
                    };

                    costs.Add(cost);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting additional costs: {ex.Message}");
            }

            return costs;
        }

        #endregion

        #region Delivery Orders

        /// <summary>
        /// إنشاء رقم أمر تسليم جديد
        /// </summary>
        public string GenerateDeliveryOrderNumber()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT COUNT(*) FROM DeliveryOrders WHERE IsActive = 1";
                using var command = new SQLiteCommand(query, connection);
                var count = Convert.ToInt32(command.ExecuteScalar());

                return $"DO{DateTime.Now:yyyyMM}{(count + 1):D4}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error generating delivery order number: {ex.Message}");
                return $"DO{DateTime.Now:yyyyMMddHHmmss}";
            }
        }

        /// <summary>
        /// حفظ أمر تسليم
        /// </summary>
        public bool SaveDeliveryOrder(DeliveryOrder deliveryOrder)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var insertQuery = @"
                    INSERT INTO DeliveryOrders (
                        DeliveryOrderNumber, ManufacturingOrderId, CustomerName, InvoiceNumber,
                        WorkOrderNumber, ProjectName, TotalPieces, TotalSquareMeters,
                        DeliveryResponsible, ReceiverSignature, DeliveryDate, CreatedBy, CreatedDate, IsActive
                    ) VALUES (
                        @DeliveryOrderNumber, @ManufacturingOrderId, @CustomerName, @InvoiceNumber,
                        @WorkOrderNumber, @ProjectName, @TotalPieces, @TotalSquareMeters,
                        @DeliveryResponsible, @ReceiverSignature, @DeliveryDate, @CreatedBy, @CreatedDate, @IsActive
                    )";

                using var command = new SQLiteCommand(insertQuery, connection);
                command.Parameters.AddWithValue("@DeliveryOrderNumber", deliveryOrder.DeliveryOrderNumber);
                command.Parameters.AddWithValue("@ManufacturingOrderId", deliveryOrder.ManufacturingOrderId);
                command.Parameters.AddWithValue("@CustomerName", deliveryOrder.CustomerName);
                command.Parameters.AddWithValue("@InvoiceNumber", deliveryOrder.InvoiceNumber ?? "");
                command.Parameters.AddWithValue("@WorkOrderNumber", deliveryOrder.WorkOrderNumber ?? "");
                command.Parameters.AddWithValue("@ProjectName", deliveryOrder.ProjectName ?? "");
                command.Parameters.AddWithValue("@TotalPieces", deliveryOrder.TotalPieces);
                command.Parameters.AddWithValue("@TotalSquareMeters", deliveryOrder.TotalSquareMeters);
                command.Parameters.AddWithValue("@DeliveryResponsible", deliveryOrder.DeliveryResponsible ?? "");
                command.Parameters.AddWithValue("@ReceiverSignature", deliveryOrder.ReceiverSignature ?? "");
                command.Parameters.AddWithValue("@DeliveryDate", deliveryOrder.DeliveryDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@CreatedBy", deliveryOrder.CreatedBy);
                command.Parameters.AddWithValue("@CreatedDate", deliveryOrder.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@IsActive", deliveryOrder.IsActive ? 1 : 0);

                var result = command.ExecuteNonQuery();

                if (result > 0)
                {
                    // الحصول على ID أمر التسليم المحفوظ
                    var getIdQuery = "SELECT last_insert_rowid()";
                    using var getIdCommand = new SQLiteCommand(getIdQuery, connection);
                    deliveryOrder.Id = Convert.ToInt32(getIdCommand.ExecuteScalar());

                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving delivery order: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
