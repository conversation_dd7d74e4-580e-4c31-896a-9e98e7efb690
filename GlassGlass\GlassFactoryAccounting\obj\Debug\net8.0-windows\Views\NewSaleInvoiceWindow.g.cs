﻿#pragma checksum "..\..\..\..\Views\NewSaleInvoiceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "5E03F4D95311D7F6A66E561A75D5C29958EF3028"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// NewSaleInvoiceWindow
    /// </summary>
    public partial class NewSaleInvoiceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 49 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnManageServices;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtCustomerName;
        
        #line default
        #line hidden
        
        
        #line 72 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateInvoice;
        
        #line default
        #line hidden
        
        
        #line 77 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtInvoiceNumber;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddNormalRow;
        
        #line default
        #line hidden
        
        
        #line 102 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddManualRow;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnDeleteRow;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoiceDataGrid;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkEnableDiscount;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PnlDiscount;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDiscountAmount;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkEnableNotes;
        
        #line default
        #line hidden
        
        
        #line 182 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtNotes;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtSubTotal;
        
        #line default
        #line hidden
        
        
        #line 194 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel PnlDiscountDisplay;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtDiscountDisplay;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtFinalTotal;
        
        #line default
        #line hidden
        
        
        #line 214 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSave;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSavePDF;
        
        #line default
        #line hidden
        
        
        #line 224 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnPrint;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnCancel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/newsaleinvoicewindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.BtnManageServices = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnManageServices.Click += new System.Windows.RoutedEventHandler(this.BtnManageServices_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtCustomerName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.DateInvoice = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.TxtInvoiceNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 5:
            this.BtnAddNormalRow = ((System.Windows.Controls.Button)(target));
            
            #line 100 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnAddNormalRow.Click += new System.Windows.RoutedEventHandler(this.BtnAddNormalRow_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.BtnAddManualRow = ((System.Windows.Controls.Button)(target));
            
            #line 105 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnAddManualRow.Click += new System.Windows.RoutedEventHandler(this.BtnAddManualRow_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.BtnDeleteRow = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnDeleteRow.Click += new System.Windows.RoutedEventHandler(this.BtnDeleteRow_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.InvoiceDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 9:
            this.ChkEnableDiscount = ((System.Windows.Controls.CheckBox)(target));
            
            #line 171 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.ChkEnableDiscount.Checked += new System.Windows.RoutedEventHandler(this.ChkEnableDiscount_Checked);
            
            #line default
            #line hidden
            
            #line 171 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.ChkEnableDiscount.Unchecked += new System.Windows.RoutedEventHandler(this.ChkEnableDiscount_Unchecked);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PnlDiscount = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.TxtDiscountAmount = ((System.Windows.Controls.TextBox)(target));
            
            #line 176 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.TxtDiscountAmount.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtDiscountAmount_TextChanged);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ChkEnableNotes = ((System.Windows.Controls.CheckBox)(target));
            
            #line 180 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.ChkEnableNotes.Checked += new System.Windows.RoutedEventHandler(this.ChkEnableNotes_Checked);
            
            #line default
            #line hidden
            
            #line 180 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.ChkEnableNotes.Unchecked += new System.Windows.RoutedEventHandler(this.ChkEnableNotes_Unchecked);
            
            #line default
            #line hidden
            return;
            case 13:
            this.TxtNotes = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.TxtSubTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.PnlDiscountDisplay = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 16:
            this.TxtDiscountDisplay = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.TxtFinalTotal = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 18:
            this.BtnSave = ((System.Windows.Controls.Button)(target));
            
            #line 217 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnSave.Click += new System.Windows.RoutedEventHandler(this.BtnSave_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.BtnSavePDF = ((System.Windows.Controls.Button)(target));
            
            #line 222 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnSavePDF.Click += new System.Windows.RoutedEventHandler(this.BtnSavePDF_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.BtnPrint = ((System.Windows.Controls.Button)(target));
            
            #line 227 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.BtnCancel = ((System.Windows.Controls.Button)(target));
            
            #line 232 "..\..\..\..\Views\NewSaleInvoiceWindow.xaml"
            this.BtnCancel.Click += new System.Windows.RoutedEventHandler(this.BtnCancel_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

