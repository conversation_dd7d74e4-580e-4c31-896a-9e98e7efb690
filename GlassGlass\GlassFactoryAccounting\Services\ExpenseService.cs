using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Data;

namespace GlassFactoryAccounting.Services
{
    public class ExpenseService
    {
        private readonly DatabaseContext _context;
        private readonly CompanyManagementService _companyService;

        public ExpenseService()
        {
            _context = new DatabaseContext();
            _companyService = new CompanyManagementService();
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                // إنشاء جدول إدارة المصروفات (الرئيسية والفرعية)
                var createExpenseCategoriesTable = @"
                    CREATE TABLE IF NOT EXISTS ExpenseCategories (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        MainExpenseName TEXT NOT NULL,
                        SubExpenseName TEXT NOT NULL,
                        Notes TEXT,
                        IsProjectRelated INTEGER NOT NULL DEFAULT 0,
                        ProjectTitle TEXT,
                        ProjectStartDate TEXT,
                        ProjectDescription TEXT,
                        ProjectNotes TEXT,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT
                    )";

                // إنشاء جدول تسجيل المصروفات الفعلية
                var createExpenseRecordsTable = @"
                    CREATE TABLE IF NOT EXISTS ExpenseRecords (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        MainExpenseId INTEGER NOT NULL,
                        MainExpenseName TEXT NOT NULL,
                        SubExpenseId INTEGER NOT NULL,
                        SubExpenseName TEXT NOT NULL,
                        BranchId INTEGER NOT NULL,
                        BranchName TEXT NOT NULL,
                        Amount REAL NOT NULL,
                        InvoiceNumber TEXT NOT NULL,
                        DateTime TEXT NOT NULL,
                        Notes TEXT,
                        IsProjectExpense INTEGER NOT NULL DEFAULT 0,
                        ProjectTitle TEXT,
                        ResponsiblePersonId INTEGER NOT NULL DEFAULT 1,
                        ResponsiblePersonName TEXT NOT NULL DEFAULT 'مدير عام',
                        PaymentMethod TEXT NOT NULL DEFAULT 'كاش',
                        ExpenseStatus TEXT NOT NULL DEFAULT 'مستحق',
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT
                    )";

                // إنشاء جدول فروع الشركة
                var createCompanyBranchesTable = @"
                    CREATE TABLE IF NOT EXISTS CompanyBranches (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Address TEXT,
                        Phone TEXT,
                        Manager TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT
                    )";

                // ملاحظة: جدول ResponsiblePersons يتم إنشاؤه بواسطة CompanyManagementService

                // إنشاء جدول طرق السداد
                var createPaymentMethodsTable = @"
                    CREATE TABLE IF NOT EXISTS PaymentMethods (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Description TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT
                    )";

                using var command = new SQLiteCommand(createExpenseCategoriesTable, connection);
                command.ExecuteNonQuery();

                command.CommandText = createExpenseRecordsTable;
                command.ExecuteNonQuery();

                command.CommandText = createCompanyBranchesTable;
                command.ExecuteNonQuery();

                // ResponsiblePersons table is created by CompanyManagementService

                command.CommandText = createPaymentMethodsTable;
                command.ExecuteNonQuery();

                // تحديث الجدول الموجود لإضافة الأعمدة الجديدة
                UpdateExistingTables(connection);

                // إدراج البيانات الافتراضية
                InsertDefaultData(connection);
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تهيئة قاعدة بيانات المصروفات: {ex.Message}");
            }
        }

        private void UpdateExistingTables(SQLiteConnection connection)
        {
            try
            {
                // التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
                var checkColumns = @"PRAGMA table_info(ExpenseRecords)";
                using var command = new SQLiteCommand(checkColumns, connection);
                using var reader = command.ExecuteReader();

                var columns = new List<string>();
                while (reader.Read())
                {
                    columns.Add(reader["name"].ToString() ?? "");
                }
                reader.Close();

                // إضافة الأعمدة المفقودة
                if (!columns.Contains("ResponsiblePersonId"))
                {
                    var addColumn = "ALTER TABLE ExpenseRecords ADD COLUMN ResponsiblePersonId INTEGER NOT NULL DEFAULT 1";
                    using var addCommand = new SQLiteCommand(addColumn, connection);
                    addCommand.ExecuteNonQuery();
                }

                if (!columns.Contains("ResponsiblePersonName"))
                {
                    var addColumn = "ALTER TABLE ExpenseRecords ADD COLUMN ResponsiblePersonName TEXT NOT NULL DEFAULT 'مدير عام'";
                    using var addCommand = new SQLiteCommand(addColumn, connection);
                    addCommand.ExecuteNonQuery();
                }

                if (!columns.Contains("PaymentMethod"))
                {
                    var addColumn = "ALTER TABLE ExpenseRecords ADD COLUMN PaymentMethod TEXT NOT NULL DEFAULT 'كاش'";
                    using var addCommand = new SQLiteCommand(addColumn, connection);
                    addCommand.ExecuteNonQuery();
                }

                if (!columns.Contains("ExpenseStatus"))
                {
                    var addColumn = "ALTER TABLE ExpenseRecords ADD COLUMN ExpenseStatus TEXT NOT NULL DEFAULT 'مستحق'";
                    using var addCommand = new SQLiteCommand(addColumn, connection);
                    addCommand.ExecuteNonQuery();
                }
            }
            catch (Exception ex)
            {
                // تجاهل الأخطاء إذا كانت الأعمدة موجودة بالفعل
                System.Diagnostics.Debug.WriteLine($"تحديث الجداول: {ex.Message}");
            }
        }

        private void InsertDefaultData(SQLiteConnection connection)
        {
            // إدراج فروع الشركة الافتراضية
            var defaultBranches = new[]
            {
                new { Name = "الفرع الرئيسي", Address = "العنوان الرئيسي", Manager = "مدير عام" },
                new { Name = "فرع المصنع", Address = "منطقة المصنع", Manager = "مدير المصنع" },
                new { Name = "فرع المبيعات", Address = "منطقة المبيعات", Manager = "مدير المبيعات" }
            };

            foreach (var branch in defaultBranches)
            {
                var checkQuery = "SELECT COUNT(*) FROM CompanyBranches WHERE Name = @name";
                using var checkCommand = new SQLiteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@name", branch.Name);

                var count = Convert.ToInt32(checkCommand.ExecuteScalar());
                if (count == 0)
                {
                    var insertQuery = @"
                        INSERT INTO CompanyBranches (Name, Address, Manager, IsActive, CreatedDate)
                        VALUES (@name, @address, @manager, 1, @createdDate)";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@name", branch.Name);
                    insertCommand.Parameters.AddWithValue("@address", branch.Address);
                    insertCommand.Parameters.AddWithValue("@manager", branch.Manager);
                    insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    insertCommand.ExecuteNonQuery();
                }
            }

            // إدراج مصروفات افتراضية
            var defaultExpenses = new[]
            {
                new { Main = "مصروفات إدارية", Sub = "رواتب الموظفين" },
                new { Main = "مصروفات إدارية", Sub = "مصروفات مكتبية" },
                new { Main = "مصروفات تشغيلية", Sub = "كهرباء ومياه" },
                new { Main = "مصروفات تشغيلية", Sub = "صيانة المعدات" },
                new { Main = "مصروفات تسويقية", Sub = "دعاية وإعلان" },
                new { Main = "مصروفات تسويقية", Sub = "عمولات المبيعات" }
            };

            foreach (var expense in defaultExpenses)
            {
                var checkQuery = "SELECT COUNT(*) FROM ExpenseCategories WHERE MainExpenseName = @main AND SubExpenseName = @sub";
                using var checkCommand = new SQLiteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@main", expense.Main);
                checkCommand.Parameters.AddWithValue("@sub", expense.Sub);

                var count = Convert.ToInt32(checkCommand.ExecuteScalar());
                if (count == 0)
                {
                    var insertQuery = @"
                        INSERT INTO ExpenseCategories (MainExpenseName, SubExpenseName, IsProjectRelated, CreatedDate)
                        VALUES (@main, @sub, 0, @createdDate)";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@main", expense.Main);
                    insertCommand.Parameters.AddWithValue("@sub", expense.Sub);
                    insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    insertCommand.ExecuteNonQuery();
                }
            }

            // إدراج المسؤولين الافتراضيين
            var defaultResponsiblePersons = new[]
            {
                new { Name = "مدير عام", Position = "مدير عام", Department = "الإدارة العليا" },
                new { Name = "مدير مالي", Position = "مدير مالي", Department = "المالية" },
                new { Name = "مدير مشتريات", Position = "مدير مشتريات", Department = "المشتريات" },
                new { Name = "مدير مبيعات", Position = "مدير مبيعات", Department = "المبيعات" },
                new { Name = "مدير إنتاج", Position = "مدير إنتاج", Department = "الإنتاج" }
            };

            foreach (var person in defaultResponsiblePersons)
            {
                var checkQuery = "SELECT COUNT(*) FROM ResponsiblePersons WHERE Name = @name";
                using var checkCommand = new SQLiteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@name", person.Name);

                var count = Convert.ToInt32(checkCommand.ExecuteScalar());
                if (count == 0)
                {
                    var insertQuery = @"
                        INSERT INTO ResponsiblePersons (Name, Position, Department, IsActive, CreatedDate)
                        VALUES (@name, @position, @department, 1, @createdDate)";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@name", person.Name);
                    insertCommand.Parameters.AddWithValue("@position", person.Position);
                    insertCommand.Parameters.AddWithValue("@department", person.Department);
                    insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    insertCommand.ExecuteNonQuery();
                }
            }

            // إدراج طرق السداد الافتراضية
            var defaultPaymentMethods = new[]
            {
                new { Name = "كاش", Description = "دفع نقدي" },
                new { Name = "بنك", Description = "تحويل بنكي" },
                new { Name = "مستحق الدفع", Description = "دفع مؤجل" }
            };

            foreach (var method in defaultPaymentMethods)
            {
                var checkQuery = "SELECT COUNT(*) FROM PaymentMethods WHERE Name = @name";
                using var checkCommand = new SQLiteCommand(checkQuery, connection);
                checkCommand.Parameters.AddWithValue("@name", method.Name);

                var count = Convert.ToInt32(checkCommand.ExecuteScalar());
                if (count == 0)
                {
                    var insertQuery = @"
                        INSERT INTO PaymentMethods (Name, Description, IsActive, CreatedDate)
                        VALUES (@name, @description, 1, @createdDate)";

                    using var insertCommand = new SQLiteCommand(insertQuery, connection);
                    insertCommand.Parameters.AddWithValue("@name", method.Name);
                    insertCommand.Parameters.AddWithValue("@description", method.Description);
                    insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                    insertCommand.ExecuteNonQuery();
                }
            }
        }

        // إدارة المصروفات - الحصول على جميع فئات المصروفات
        public List<ExpenseCategory> GetAllExpenseCategories()
        {
            var categories = new List<ExpenseCategory>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM ExpenseCategories ORDER BY MainExpenseName, SubExpenseName";
                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    categories.Add(new ExpenseCategory
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        MainExpenseName = reader["MainExpenseName"].ToString() ?? "",
                        SubExpenseName = reader["SubExpenseName"].ToString() ?? "",
                        Notes = reader["Notes"]?.ToString(),
                        IsProjectRelated = Convert.ToBoolean(reader["IsProjectRelated"]),
                        ProjectTitle = reader["ProjectTitle"]?.ToString(),
                        ProjectStartDate = reader["ProjectStartDate"] != DBNull.Value ? DateTime.Parse(reader["ProjectStartDate"].ToString() ?? DateTime.Now.ToString()) : null,
                        ProjectDescription = reader["ProjectDescription"]?.ToString(),
                        ProjectNotes = reader["ProjectNotes"]?.ToString(),
                        CreatedDate = DateTime.Parse(reader["CreatedDate"].ToString() ?? DateTime.Now.ToString()),
                        CreatedBy = reader["CreatedBy"]?.ToString(),
                        ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? DateTime.Parse(reader["ModifiedDate"].ToString() ?? DateTime.Now.ToString()) : null,
                        ModifiedBy = reader["ModifiedBy"]?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع فئات المصروفات: {ex.Message}");
            }

            return categories;
        }

        // إضافة فئة مصروف جديدة
        public bool AddExpenseCategory(ExpenseCategory category)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO ExpenseCategories (
                        MainExpenseName, SubExpenseName, Notes, IsProjectRelated, ProjectTitle,
                        ProjectStartDate, ProjectDescription, ProjectNotes, CreatedDate, CreatedBy
                    ) VALUES (
                        @mainExpenseName, @subExpenseName, @notes, @isProjectRelated, @projectTitle,
                        @projectStartDate, @projectDescription, @projectNotes, @createdDate, @createdBy
                    )";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@mainExpenseName", category.MainExpenseName);
                command.Parameters.AddWithValue("@subExpenseName", category.SubExpenseName);
                command.Parameters.AddWithValue("@notes", category.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@isProjectRelated", category.IsProjectRelated ? 1 : 0);
                command.Parameters.AddWithValue("@projectTitle", category.ProjectTitle ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@projectStartDate", category.ProjectStartDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@projectDescription", category.ProjectDescription ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@projectNotes", category.ProjectNotes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@createdDate", category.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@createdBy", category.CreatedBy ?? (object)DBNull.Value);

                var result = command.ExecuteNonQuery();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة فئة المصروف: {ex.Message}");
            }
        }

        // تسجيل المصروفات - الحصول على جميع سجلات المصروفات
        public List<ExpenseRecord> GetAllExpenseRecords()
        {
            var records = new List<ExpenseRecord>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM ExpenseRecords ORDER BY DateTime DESC";
                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    records.Add(new ExpenseRecord
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        MainExpenseId = Convert.ToInt32(reader["MainExpenseId"]),
                        MainExpenseName = reader["MainExpenseName"].ToString() ?? "",
                        SubExpenseId = Convert.ToInt32(reader["SubExpenseId"]),
                        SubExpenseName = reader["SubExpenseName"].ToString() ?? "",
                        BranchId = Convert.ToInt32(reader["BranchId"]),
                        BranchName = reader["BranchName"].ToString() ?? "",
                        Amount = Convert.ToDecimal(reader["Amount"]),
                        InvoiceNumber = reader["InvoiceNumber"].ToString() ?? "",
                        DateTime = DateTime.Parse(reader["DateTime"].ToString() ?? DateTime.Now.ToString()),
                        Notes = reader["Notes"]?.ToString(),
                        IsProjectExpense = Convert.ToBoolean(reader["IsProjectExpense"]),
                        ProjectTitle = reader["ProjectTitle"]?.ToString(),
                        ResponsiblePersonId = Convert.ToInt32(reader["ResponsiblePersonId"]),
                        ResponsiblePersonName = reader["ResponsiblePersonName"].ToString() ?? "",
                        PaymentMethod = reader["PaymentMethod"].ToString() ?? "",
                        ExpenseStatus = reader["ExpenseStatus"].ToString() ?? "مستحق",
                        CreatedDate = DateTime.Parse(reader["CreatedDate"].ToString() ?? DateTime.Now.ToString()),
                        CreatedBy = reader["CreatedBy"]?.ToString(),
                        ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? DateTime.Parse(reader["ModifiedDate"].ToString() ?? DateTime.Now.ToString()) : null,
                        ModifiedBy = reader["ModifiedBy"]?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع سجلات المصروفات: {ex.Message}");
            }

            return records;
        }

        // إضافة سجل مصروف جديد
        public bool AddExpenseRecord(ExpenseRecord record)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO ExpenseRecords (
                        MainExpenseId, MainExpenseName, SubExpenseId, SubExpenseName, BranchId, BranchName,
                        Amount, InvoiceNumber, DateTime, Notes, IsProjectExpense, ProjectTitle,
                        ResponsiblePersonId, ResponsiblePersonName, PaymentMethod, ExpenseStatus, CreatedDate, CreatedBy
                    ) VALUES (
                        @mainExpenseId, @mainExpenseName, @subExpenseId, @subExpenseName, @branchId, @branchName,
                        @amount, @invoiceNumber, @dateTime, @notes, @isProjectExpense, @projectTitle,
                        @responsiblePersonId, @responsiblePersonName, @paymentMethod, @expenseStatus, @createdDate, @createdBy
                    )";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@mainExpenseId", record.MainExpenseId);
                command.Parameters.AddWithValue("@mainExpenseName", record.MainExpenseName);
                command.Parameters.AddWithValue("@subExpenseId", record.SubExpenseId);
                command.Parameters.AddWithValue("@subExpenseName", record.SubExpenseName);
                command.Parameters.AddWithValue("@branchId", record.BranchId);
                command.Parameters.AddWithValue("@branchName", record.BranchName);
                command.Parameters.AddWithValue("@amount", record.Amount);
                command.Parameters.AddWithValue("@invoiceNumber", record.InvoiceNumber);
                command.Parameters.AddWithValue("@dateTime", record.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@notes", record.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@isProjectExpense", record.IsProjectExpense ? 1 : 0);
                command.Parameters.AddWithValue("@projectTitle", record.ProjectTitle ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@responsiblePersonId", record.ResponsiblePersonId);
                command.Parameters.AddWithValue("@responsiblePersonName", record.ResponsiblePersonName);
                command.Parameters.AddWithValue("@paymentMethod", record.PaymentMethod);
                command.Parameters.AddWithValue("@expenseStatus", record.ExpenseStatus);
                command.Parameters.AddWithValue("@createdDate", record.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@createdBy", record.CreatedBy ?? (object)DBNull.Value);

                var result = command.ExecuteNonQuery();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة سجل المصروف: {ex.Message}");
            }
        }

        // إدارة فروع الشركة - الحصول على جميع الفروع (استخدام الخدمة المركزية)
        public List<CompanyBranch> GetAllCompanyBranches()
        {
            return _companyService.GetAllCompanyBranches();
        }

        // إضافة فرع جديد (استخدام الخدمة المركزية)
        public bool AddCompanyBranch(CompanyBranch branch)
        {
            return _companyService.AddCompanyBranch(branch);
        }

        // دوال مساعدة - الحصول على المصروفات الرئيسية المميزة
        public List<string> GetDistinctMainExpenseNames()
        {
            var mainExpenses = new List<string>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT DISTINCT MainExpenseName FROM ExpenseCategories ORDER BY MainExpenseName";
                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    mainExpenses.Add(reader["MainExpenseName"].ToString() ?? "");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع المصروفات الرئيسية: {ex.Message}");
            }

            return mainExpenses;
        }

        // الحصول على المصروفات الفرعية حسب المصروف الرئيسي
        public List<ExpenseCategory> GetSubExpensesByMainExpense(string mainExpenseName)
        {
            var subExpenses = new List<ExpenseCategory>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM ExpenseCategories WHERE MainExpenseName = @mainExpenseName ORDER BY SubExpenseName";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@mainExpenseName", mainExpenseName);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    subExpenses.Add(new ExpenseCategory
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        MainExpenseName = reader["MainExpenseName"].ToString() ?? "",
                        SubExpenseName = reader["SubExpenseName"].ToString() ?? "",
                        Notes = reader["Notes"]?.ToString(),
                        IsProjectRelated = Convert.ToBoolean(reader["IsProjectRelated"]),
                        ProjectTitle = reader["ProjectTitle"]?.ToString(),
                        ProjectStartDate = reader["ProjectStartDate"] != DBNull.Value ? DateTime.Parse(reader["ProjectStartDate"].ToString() ?? DateTime.Now.ToString()) : null,
                        ProjectDescription = reader["ProjectDescription"]?.ToString(),
                        ProjectNotes = reader["ProjectNotes"]?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع المصروفات الفرعية: {ex.Message}");
            }

            return subExpenses;
        }

        // دوال التقارير - الحصول على المصروفات العادية (غير مرتبطة بمشاريع)
        public List<ExpenseRecord> GetRegularExpenseRecords()
        {
            var records = new List<ExpenseRecord>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM ExpenseRecords WHERE IsProjectExpense = 0 ORDER BY DateTime DESC";
                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    records.Add(new ExpenseRecord
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        MainExpenseId = Convert.ToInt32(reader["MainExpenseId"]),
                        MainExpenseName = reader["MainExpenseName"].ToString() ?? "",
                        SubExpenseId = Convert.ToInt32(reader["SubExpenseId"]),
                        SubExpenseName = reader["SubExpenseName"].ToString() ?? "",
                        BranchId = Convert.ToInt32(reader["BranchId"]),
                        BranchName = reader["BranchName"].ToString() ?? "",
                        Amount = Convert.ToDecimal(reader["Amount"]),
                        InvoiceNumber = reader["InvoiceNumber"].ToString() ?? "",
                        DateTime = DateTime.Parse(reader["DateTime"].ToString() ?? DateTime.Now.ToString()),
                        Notes = reader["Notes"]?.ToString(),
                        IsProjectExpense = Convert.ToBoolean(reader["IsProjectExpense"]),
                        ProjectTitle = reader["ProjectTitle"]?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع المصروفات العادية: {ex.Message}");
            }

            return records;
        }

        // الحصول على مصروفات المشاريع
        public List<ExpenseRecord> GetProjectExpenseRecords()
        {
            var records = new List<ExpenseRecord>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM ExpenseRecords WHERE IsProjectExpense = 1 ORDER BY DateTime DESC";
                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    records.Add(new ExpenseRecord
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        MainExpenseId = Convert.ToInt32(reader["MainExpenseId"]),
                        MainExpenseName = reader["MainExpenseName"].ToString() ?? "",
                        SubExpenseId = Convert.ToInt32(reader["SubExpenseId"]),
                        SubExpenseName = reader["SubExpenseName"].ToString() ?? "",
                        BranchId = Convert.ToInt32(reader["BranchId"]),
                        BranchName = reader["BranchName"].ToString() ?? "",
                        Amount = Convert.ToDecimal(reader["Amount"]),
                        InvoiceNumber = reader["InvoiceNumber"].ToString() ?? "",
                        DateTime = DateTime.Parse(reader["DateTime"].ToString() ?? DateTime.Now.ToString()),
                        Notes = reader["Notes"]?.ToString(),
                        IsProjectExpense = Convert.ToBoolean(reader["IsProjectExpense"]),
                        ProjectTitle = reader["ProjectTitle"]?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع مصروفات المشاريع: {ex.Message}");
            }

            return records;
        }

        // حذف سجل مصروف
        public bool DeleteExpenseRecord(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "DELETE FROM ExpenseRecords WHERE Id = @id";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);

                var result = command.ExecuteNonQuery();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف سجل المصروف: {ex.Message}");
            }
        }

        // حذف فئة مصروف
        public bool DeleteExpenseCategory(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "DELETE FROM ExpenseCategories WHERE Id = @id";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);

                var result = command.ExecuteNonQuery();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في حذف فئة المصروف: {ex.Message}");
            }
        }

        // إدارة المسؤولين - الحصول على جميع المسؤولين (استخدام الخدمة المركزية)
        public List<ResponsiblePerson> GetAllResponsiblePersons()
        {
            return _companyService.GetAllResponsiblePersons();
        }

        // إضافة مسؤول جديد (استخدام الخدمة المركزية)
        public bool AddResponsiblePerson(ResponsiblePerson person)
        {
            return _companyService.AddResponsiblePerson(person);
        }

        // حذف مسؤول (استخدام الخدمة المركزية)
        public bool DeleteResponsiblePerson(int id)
        {
            return _companyService.DeleteResponsiblePerson(id);
        }

        // حذف فرع الشركة (استخدام الخدمة المركزية)
        public bool DeleteCompanyBranch(int id)
        {
            return _companyService.DeleteCompanyBranch(id);
        }

        // إدارة طرق السداد - الحصول على جميع طرق السداد
        public List<PaymentMethod> GetAllPaymentMethods()
        {
            var paymentMethods = new List<PaymentMethod>();

            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM PaymentMethods WHERE IsActive = 1 ORDER BY Name";
                using var command = new SQLiteCommand(query, connection);
                using var reader = command.ExecuteReader();

                while (reader.Read())
                {
                    paymentMethods.Add(new PaymentMethod
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        Name = reader["Name"].ToString() ?? "",
                        Description = reader["Description"]?.ToString(),
                        IsActive = Convert.ToBoolean(reader["IsActive"]),
                        CreatedDate = DateTime.Parse(reader["CreatedDate"].ToString() ?? DateTime.Now.ToString()),
                        CreatedBy = reader["CreatedBy"]?.ToString(),
                        ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? DateTime.Parse(reader["ModifiedDate"].ToString() ?? DateTime.Now.ToString()) : null,
                        ModifiedBy = reader["ModifiedBy"]?.ToString()
                    });
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع طرق السداد: {ex.Message}");
            }

            return paymentMethods;
        }

        // إضافة طريقة سداد جديدة
        public bool AddPaymentMethod(PaymentMethod method)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    INSERT INTO PaymentMethods (Name, Description, IsActive, CreatedDate, CreatedBy)
                    VALUES (@name, @description, 1, @createdDate, @createdBy)";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@name", method.Name);
                command.Parameters.AddWithValue("@description", method.Description ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@createdDate", method.CreatedDate.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@createdBy", method.CreatedBy ?? (object)DBNull.Value);

                var result = command.ExecuteNonQuery();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إضافة طريقة السداد: {ex.Message}");
            }
        }

        // تحديث سجل مصروف
        public bool UpdateExpenseRecord(ExpenseRecord record)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = @"
                    UPDATE ExpenseRecords SET
                        MainExpenseId = @mainExpenseId, MainExpenseName = @mainExpenseName,
                        SubExpenseId = @subExpenseId, SubExpenseName = @subExpenseName,
                        BranchId = @branchId, BranchName = @branchName,
                        Amount = @amount, InvoiceNumber = @invoiceNumber, DateTime = @dateTime,
                        Notes = @notes, ResponsiblePersonId = @responsiblePersonId,
                        ResponsiblePersonName = @responsiblePersonName, PaymentMethod = @paymentMethod,
                        ExpenseStatus = @expenseStatus, ModifiedDate = @modifiedDate, ModifiedBy = @modifiedBy
                    WHERE Id = @id";

                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@id", record.Id);
                command.Parameters.AddWithValue("@mainExpenseId", record.MainExpenseId);
                command.Parameters.AddWithValue("@mainExpenseName", record.MainExpenseName);
                command.Parameters.AddWithValue("@subExpenseId", record.SubExpenseId);
                command.Parameters.AddWithValue("@subExpenseName", record.SubExpenseName);
                command.Parameters.AddWithValue("@branchId", record.BranchId);
                command.Parameters.AddWithValue("@branchName", record.BranchName);
                command.Parameters.AddWithValue("@amount", record.Amount);
                command.Parameters.AddWithValue("@invoiceNumber", record.InvoiceNumber);
                command.Parameters.AddWithValue("@dateTime", record.DateTime.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@notes", record.Notes ?? (object)DBNull.Value);
                command.Parameters.AddWithValue("@responsiblePersonId", record.ResponsiblePersonId);
                command.Parameters.AddWithValue("@responsiblePersonName", record.ResponsiblePersonName);
                command.Parameters.AddWithValue("@paymentMethod", record.PaymentMethod);
                command.Parameters.AddWithValue("@expenseStatus", record.ExpenseStatus);
                command.Parameters.AddWithValue("@modifiedDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                command.Parameters.AddWithValue("@modifiedBy", record.ModifiedBy ?? (object)DBNull.Value);

                var result = command.ExecuteNonQuery();
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في تحديث سجل المصروف: {ex.Message}");
            }
        }

        // الحصول على سجل مصروف بالمعرف
        public ExpenseRecord? GetExpenseRecordById(int id)
        {
            try
            {
                using var connection = _context.GetConnection();
                connection.Open();

                var query = "SELECT * FROM ExpenseRecords WHERE Id = @id";
                using var command = new SQLiteCommand(query, connection);
                command.Parameters.AddWithValue("@id", id);
                using var reader = command.ExecuteReader();

                if (reader.Read())
                {
                    return new ExpenseRecord
                    {
                        Id = Convert.ToInt32(reader["Id"]),
                        MainExpenseId = Convert.ToInt32(reader["MainExpenseId"]),
                        MainExpenseName = reader["MainExpenseName"].ToString() ?? "",
                        SubExpenseId = Convert.ToInt32(reader["SubExpenseId"]),
                        SubExpenseName = reader["SubExpenseName"].ToString() ?? "",
                        BranchId = Convert.ToInt32(reader["BranchId"]),
                        BranchName = reader["BranchName"].ToString() ?? "",
                        Amount = Convert.ToDecimal(reader["Amount"]),
                        InvoiceNumber = reader["InvoiceNumber"].ToString() ?? "",
                        DateTime = DateTime.Parse(reader["DateTime"].ToString() ?? DateTime.Now.ToString()),
                        Notes = reader["Notes"]?.ToString(),
                        IsProjectExpense = Convert.ToBoolean(reader["IsProjectExpense"]),
                        ProjectTitle = reader["ProjectTitle"]?.ToString(),
                        ResponsiblePersonId = Convert.ToInt32(reader["ResponsiblePersonId"]),
                        ResponsiblePersonName = reader["ResponsiblePersonName"].ToString() ?? "",
                        PaymentMethod = reader["PaymentMethod"].ToString() ?? "",
                        ExpenseStatus = reader["ExpenseStatus"].ToString() ?? "مستحق",
                        CreatedDate = DateTime.Parse(reader["CreatedDate"].ToString() ?? DateTime.Now.ToString()),
                        CreatedBy = reader["CreatedBy"]?.ToString(),
                        ModifiedDate = reader["ModifiedDate"] != DBNull.Value ? DateTime.Parse(reader["ModifiedDate"].ToString() ?? DateTime.Now.ToString()) : null,
                        ModifiedBy = reader["ModifiedBy"]?.ToString()
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في استرجاع سجل المصروف: {ex.Message}");
            }

            return null;
        }
    }
}
