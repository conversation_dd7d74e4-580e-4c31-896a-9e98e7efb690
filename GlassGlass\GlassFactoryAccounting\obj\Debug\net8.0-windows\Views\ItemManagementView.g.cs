﻿#pragma checksum "..\..\..\..\Views\ItemManagementView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "49E50ACB686F212E9152555E2D9F294320C208F5"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ItemManagementView
    /// </summary>
    public partial class ItemManagementView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 100 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtItemCode;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtItemName;
        
        #line default
        #line hidden
        
        
        #line 131 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbWarehouse;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbUnitOfMeasure;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtBoxContent;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ChkHasDimensions;
        
        #line default
        #line hidden
        
        
        #line 187 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid DimensionsPanel;
        
        #line default
        #line hidden
        
        
        #line 203 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtLength;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtWidth;
        
        #line default
        #line hidden
        
        
        #line 235 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtArea;
        
        #line default
        #line hidden
        
        
        #line 250 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtDescription;
        
        #line default
        #line hidden
        
        
        #line 263 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnAddItem;
        
        #line default
        #line hidden
        
        
        #line 268 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearForm;
        
        #line default
        #line hidden
        
        
        #line 288 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 293 "..\..\..\..\Views\ItemManagementView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ItemsDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/itemmanagementview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ItemManagementView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtItemCode = ((System.Windows.Controls.TextBox)(target));
            return;
            case 2:
            this.TxtItemName = ((System.Windows.Controls.TextBox)(target));
            return;
            case 3:
            this.CmbWarehouse = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 4:
            this.CmbUnitOfMeasure = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.TxtBoxContent = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.ChkHasDimensions = ((System.Windows.Controls.CheckBox)(target));
            
            #line 181 "..\..\..\..\Views\ItemManagementView.xaml"
            this.ChkHasDimensions.Checked += new System.Windows.RoutedEventHandler(this.ChkHasDimensions_Checked);
            
            #line default
            #line hidden
            
            #line 181 "..\..\..\..\Views\ItemManagementView.xaml"
            this.ChkHasDimensions.Unchecked += new System.Windows.RoutedEventHandler(this.ChkHasDimensions_Unchecked);
            
            #line default
            #line hidden
            return;
            case 7:
            this.DimensionsPanel = ((System.Windows.Controls.Grid)(target));
            return;
            case 8:
            this.TxtLength = ((System.Windows.Controls.TextBox)(target));
            
            #line 205 "..\..\..\..\Views\ItemManagementView.xaml"
            this.TxtLength.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Dimensions_TextChanged);
            
            #line default
            #line hidden
            return;
            case 9:
            this.TxtWidth = ((System.Windows.Controls.TextBox)(target));
            
            #line 221 "..\..\..\..\Views\ItemManagementView.xaml"
            this.TxtWidth.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Dimensions_TextChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.TxtArea = ((System.Windows.Controls.TextBox)(target));
            return;
            case 11:
            this.TxtDescription = ((System.Windows.Controls.TextBox)(target));
            return;
            case 12:
            this.BtnAddItem = ((System.Windows.Controls.Button)(target));
            
            #line 266 "..\..\..\..\Views\ItemManagementView.xaml"
            this.BtnAddItem.Click += new System.Windows.RoutedEventHandler(this.BtnAddItem_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.BtnClearForm = ((System.Windows.Controls.Button)(target));
            
            #line 272 "..\..\..\..\Views\ItemManagementView.xaml"
            this.BtnClearForm.Click += new System.Windows.RoutedEventHandler(this.BtnClearForm_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 290 "..\..\..\..\Views\ItemManagementView.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.ItemsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 16:
            
            #line 325 "..\..\..\..\Views\ItemManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnEditItem_Click);
            
            #line default
            #line hidden
            break;
            case 17:
            
            #line 330 "..\..\..\..\Views\ItemManagementView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteItem_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

