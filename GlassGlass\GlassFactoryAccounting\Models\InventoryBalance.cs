using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج رصيد المخزون
    /// </summary>
    public class InventoryBalance : INotifyPropertyChanged
    {
        private int _itemId;
        private string _itemName = string.Empty;
        private string _warehouseName = string.Empty;
        private decimal _totalReceived;
        private decimal _totalIssued;
        private decimal _currentBalance;
        private decimal _averagePrice;
        private decimal _balanceValue;
        private UnitOfMeasure _unitOfMeasure;
        private decimal _boxContent;
        private decimal _boxCount;
        private decimal _length;
        private decimal _width;
        private decimal _area;
        private decimal _units;

        public int ItemId
        {
            get => _itemId;
            set => SetProperty(ref _itemId, value);
        }

        public string ItemName
        {
            get => _itemName;
            set => SetProperty(ref _itemName, value);
        }

        public string WarehouseName
        {
            get => _warehouseName;
            set => SetProperty(ref _warehouseName, value);
        }

        public decimal TotalReceived
        {
            get => _totalReceived;
            set
            {
                SetProperty(ref _totalReceived, value);
                CalculateCurrentBalance();
            }
        }

        public decimal TotalIssued
        {
            get => _totalIssued;
            set
            {
                SetProperty(ref _totalIssued, value);
                CalculateCurrentBalance();
            }
        }

        public decimal CurrentBalance
        {
            get => _currentBalance;
            private set
            {
                SetProperty(ref _currentBalance, value);
                CalculateBalanceValue();
            }
        }

        public decimal AveragePrice
        {
            get => _averagePrice;
            set
            {
                SetProperty(ref _averagePrice, value);
                CalculateBalanceValue();
            }
        }

        public decimal BalanceValue
        {
            get => _balanceValue;
            private set => SetProperty(ref _balanceValue, value);
        }

        public UnitOfMeasure UnitOfMeasure
        {
            get => _unitOfMeasure;
            set => SetProperty(ref _unitOfMeasure, value);
        }

        public decimal BoxContent
        {
            get => _boxContent;
            set
            {
                SetProperty(ref _boxContent, value);
                CalculateBoxCount();
            }
        }

        public decimal BoxCount
        {
            get => _boxCount;
            private set => SetProperty(ref _boxCount, value);
        }

        public decimal Length
        {
            get => _length;
            set => SetProperty(ref _length, value);
        }

        public decimal Width
        {
            get => _width;
            set => SetProperty(ref _width, value);
        }

        public decimal Area
        {
            get => _area;
            set => SetProperty(ref _area, value);
        }

        public decimal Units
        {
            get => _units;
            set => SetProperty(ref _units, value);
        }

        /// <summary>
        /// حساب الرصيد الحالي
        /// </summary>
        private void CalculateCurrentBalance()
        {
            CurrentBalance = TotalReceived - TotalIssued;
        }

        /// <summary>
        /// حساب قيمة الرصيد
        /// </summary>
        private void CalculateBalanceValue()
        {
            BalanceValue = CurrentBalance * AveragePrice;
        }

        /// <summary>
        /// حساب عدد الصناديق
        /// </summary>
        private void CalculateBoxCount()
        {
            if (BoxContent > 0)
            {
                BoxCount = CurrentBalance / BoxContent;
            }
            else
            {
                BoxCount = 0;
            }
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
