<Window x:Class="GlassFactoryAccounting.Views.CompanySettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
        mc:Ignorable="d"
        Title="إعدادات بيانات الشركة" Height="600" Width="500"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <TextBlock Text="🏢 إعدادات بيانات الشركة" FontSize="20" FontWeight="Bold" 
                     Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- بيانات الشركة -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <ScrollViewer VerticalScrollBarVisibility="Auto">
                <StackPanel>
                    <TextBlock Text="البيانات الأساسية:" FontSize="16" FontWeight="Bold" 
                             Margin="0,0,0,15" Foreground="{StaticResource PrimaryBrush}"/>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="اسم الشركة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCompanyName" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="عنوان الشركة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCompanyAddress" Height="60" TextWrapping="Wrap" AcceptsReturn="True"
                                 VerticalContentAlignment="Top" Padding="5"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="رقم الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCompanyPhone" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="البريد الإلكتروني:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCompanyEmail" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="الرقم الضريبي:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtTaxNumber" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <Separator Margin="0,10"/>
                    
                    <TextBlock Text="إعدادات الفواتير:" FontSize="16" FontWeight="Bold" 
                             Margin="0,15,0,15" Foreground="{StaticResource PrimaryBrush}"/>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="معدل الضريبة الافتراضي (%):" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtDefaultTaxRate" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="العملة:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCurrency" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <Separator Margin="0,10"/>
                    
                    <TextBlock Text="إعدادات النسخ الاحتياطي:" FontSize="16" FontWeight="Bold" 
                             Margin="0,15,0,15" Foreground="{StaticResource PrimaryBrush}"/>
                    
                    <CheckBox x:Name="ChkAutoBackup" Content="تفعيل النسخ الاحتياطي التلقائي" 
                              FontWeight="Bold" Margin="0,0,0,10"/>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="فترة النسخ الاحتياطي (أيام):" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtBackupInterval" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="مسار النسخ الاحتياطي:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>
                            <TextBox x:Name="TxtBackupPath" Grid.Column="0" Height="35" VerticalContentAlignment="Center"/>
                            <Button x:Name="BtnBrowseBackupPath" Grid.Column="1" Content="تصفح" 
                                    Background="{StaticResource PrimaryBrush}" Foreground="White"
                                    Padding="10,5" Margin="5,0,0,0" FontWeight="Bold"
                                    BorderThickness="0" Cursor="Hand" Click="BtnBrowseBackupPath_Click"/>
                        </Grid>
                    </StackPanel>
                </StackPanel>
            </ScrollViewer>
        </Border>
        
        <!-- أزرار الحفظ والإلغاء -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ الإعدادات" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSave_Click"/>
                
                <Button x:Name="BtnCancel" Content="❌ إلغاء" 
                        Background="{StaticResource DangerBrush}" Foreground="White"
                        Padding="20,10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
