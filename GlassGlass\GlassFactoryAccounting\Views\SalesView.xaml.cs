using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;
using Microsoft.Win32;
using System.IO;
using System.Diagnostics;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة المبيعات
/// </summary>
public partial class SalesView : UserControl
{
    private ObservableCollection<Sale> _sales;
    private readonly SalesService _salesService;

    public SalesView()
    {
        InitializeComponent();
        _sales = new ObservableCollection<Sale>();
        _salesService = new SalesService();
        SalesDataGrid.ItemsSource = _sales;

        InitializePage();
        LoadSalesData();
    }

    private void InitializePage()
    {
        // تهيئة الصفحة - لا حاجة لتعيين التواريخ الآن
    }

    private void LoadSalesData()
    {
        try
        {
            _sales.Clear();

            // تحميل البيانات بشكل متزامن لتجنب مشاكل async
            Task.Run(async () =>
            {
                try
                {
                    var sales = await _salesService.GetAllSalesAsync();

                    // التحديث في UI Thread
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        foreach (var sale in sales)
                        {
                            _sales.Add(sale);
                        }
                        UpdateTotals();
                    });
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات المبيعات: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات المبيعات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateTotals()
    {
        try
        {
            var totalCount = _sales.Count;
            var totalAmount = _sales.Sum(s => s.NetAmount);
            var uniqueCustomers = _sales.Select(s => s.Customer?.Name).Where(n => !string.IsNullOrEmpty(n)).Distinct().Count();

            TxtTotalSalesCount.Text = $"إجمالي المبيعات: {totalCount}";
            TxtTotalAmount.Text = $"إجمالي القيمة: {totalAmount:F2} ج.م";
            TxtTotalCustomers.Text = $"عدد العملاء: {uniqueCustomers}";
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error updating totals: {ex.Message}");
        }
    }

    private void BtnNewSale_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // البحث عن النافذة الرئيسية وتغيير المحتوى
            var mainWindow = Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.ShowNewSaleInvoice();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح فاتورة جديدة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefresh_Click(object sender, RoutedEventArgs e)
    {
        LoadSalesData();
        MessageBox.Show("تم تحديث البيانات", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnSearch_Click(object sender, RoutedEventArgs e)
    {
        // تطبيق فلاتر البحث
        MessageBox.Show("سيتم تطبيق فلاتر البحث", "معلومات", 
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void BtnViewSale_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var button = sender as Button;
            var selectedSale = button?.Tag as Sale ?? SalesDataGrid.SelectedItem as Sale;

            if (selectedSale != null)
            {
                var viewWindow = new InvoiceViewWindow(selectedSale);
                viewWindow.ShowDialog();
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للعرض", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في عرض الفاتورة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnPrintSale_Click(object sender, RoutedEventArgs e)
    {
        if (SalesDataGrid.SelectedItem is Sale selectedSale)
        {
            try
            {
                var saveDialog = new SaveFileDialog
                {
                    Filter = "PDF Files|*.pdf",
                    FileName = $"Invoice_{selectedSale.InvoiceNumber}.pdf",
                    InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop)
                };

                if (saveDialog.ShowDialog() == true)
                {
                    var pdfService = new InvoicePdfService();
                    bool success = pdfService.CreateInvoicePdf(
                        selectedSale.Customer?.Name ?? "",
                        selectedSale.InvoiceNumber,
                        selectedSale.SaleDate,
                        new ObservableCollection<SaleItem>(selectedSale.SaleItems),
                        selectedSale.TotalAmount,
                        selectedSale.Discount,
                        selectedSale.NetAmount,
                        selectedSale.Notes,
                        saveDialog.FileName
                    );

                    if (success)
                    {
                        MessageBox.Show($"تم حفظ الفاتورة بنجاح في:\n{saveDialog.FileName}", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);

                        Process.Start(new ProcessStartInfo(saveDialog.FileName) { UseShellExecute = true });
                    }
                    else
                    {
                        MessageBox.Show("حدث خطأ أثناء إنشاء ملف PDF", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }

    private void BtnEditSale_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var button = sender as Button;
            var selectedSale = button?.Tag as Sale ?? SalesDataGrid.SelectedItem as Sale;

            if (selectedSale != null)
            {
                // البحث عن النافذة الرئيسية وفتح صفحة تعديل الفاتورة
                var mainWindow = Application.Current.MainWindow as MainWindow;
                if (mainWindow != null)
                {
                    mainWindow.ShowEditSaleInvoice(selectedSale);
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للتعديل", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تعديل الفاتورة: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void BtnDeleteSale_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var button = sender as Button;
            var selectedSale = button?.Tag as Sale ?? SalesDataGrid.SelectedItem as Sale;

            if (selectedSale != null)
            {
                var result = MessageBox.Show($"هل تريد حذف الفاتورة '{selectedSale.InvoiceNumber}'؟\nهذا الإجراء لا يمكن التراجع عنه.",
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        bool success = await _salesService.DeleteSaleAsync(selectedSale.InvoiceNumber);

                        if (success)
                        {
                            // إزالة من القائمة
                            _sales.Remove(selectedSale);
                            UpdateTotals();

                            MessageBox.Show("تم حذف الفاتورة بنجاح", "نجح",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            MessageBox.Show("حدث خطأ أثناء حذف الفاتورة", "خطأ",
                                MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الفاتورة: {ex.Message}", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار فاتورة للحذف", "تنبيه",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefreshSales_Click(object sender, RoutedEventArgs e)
    {
        LoadSalesData();
    }



    private void BtnManageCustomers_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var customersWindow = new CustomersManagementWindow();
            customersWindow.Owner = Window.GetWindow(this);

            if (customersWindow.ShowDialog() == true && customersWindow.CustomersUpdated)
            {
                // تحديث البيانات إذا تم تعديل العملاء
                LoadSalesData();
                MessageBox.Show("تم تحديث قائمة العملاء بنجاح", "تحديث",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح إدارة العملاء: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnSalesReports_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var mainWindow = Application.Current.MainWindow as MainWindow;
            if (mainWindow != null)
            {
                mainWindow.ShowSalesReport();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تقارير المبيعات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
