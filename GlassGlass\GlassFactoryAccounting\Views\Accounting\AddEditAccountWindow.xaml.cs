using System;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models.Accounting;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views.Accounting
{
    /// <summary>
    /// نافذة إضافة/تعديل حساب
    /// </summary>
    public partial class AddEditAccountWindow : Window
    {
        private readonly AccountingService _accountingService;
        private readonly Account? _account;
        private readonly int? _parentAccountId;
        private bool _isEditMode;
        
        public AddEditAccountWindow(Account? account = null, int? parentAccountId = null)
        {
            InitializeComponent();
            _accountingService = new AccountingService();
            _account = account;
            _parentAccountId = parentAccountId;
            _isEditMode = account != null;
            
            InitializeWindow();
            LoadData();
        }
        
        /// <summary>
        /// تهيئة النافذة
        /// </summary>
        private void InitializeWindow()
        {
            if (_isEditMode)
            {
                txtTitle.Text = "تعديل حساب";
                Title = "تعديل حساب";
            }
            else
            {
                txtTitle.Text = "إضافة حساب جديد";
                Title = "إضافة حساب جديد";
            }
            
            // تحديد نوع الحساب
            cmbAccountType.SelectedIndex = 0; // افتراضي: أصل
            
            // إخفاء الحساب الأب افتراضياً
            UpdateParentAccountVisibility();
        }
        
        /// <summary>
        /// تحميل البيانات
        /// </summary>
        private void LoadData()
        {
            try
            {
                // تحميل الحسابات الرئيسية
                LoadParentAccounts();
                
                // تحميل بيانات الحساب للتعديل
                if (_isEditMode && _account != null)
                {
                    LoadAccountData();
                }
                else if (_parentAccountId.HasValue)
                {
                    // تحديد الحساب الأب المحدد مسبقاً
                    SetSelectedParentAccount(_parentAccountId.Value);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// تحميل الحسابات الرئيسية
        /// </summary>
        private void LoadParentAccounts()
        {
            var parentAccounts = _accountingService.GetAllAccounts()
                .Where(a => a.IsParent && (_account == null || a.Id != _account.Id))
                .OrderBy(a => a.AccountCode)
                .ToList();
            
            cmbParentAccount.Items.Clear();
            cmbParentAccount.Items.Add(new ComboBoxItem { Content = "-- اختر الحساب الأب --", Tag = null });
            
            foreach (var account in parentAccounts)
            {
                cmbParentAccount.Items.Add(new ComboBoxItem 
                { 
                    Content = $"{account.AccountCode} - {account.AccountName}", 
                    Tag = account.Id 
                });
            }
            
            cmbParentAccount.SelectedIndex = 0;
        }
        
        /// <summary>
        /// تحميل بيانات الحساب للتعديل
        /// </summary>
        private void LoadAccountData()
        {
            if (_account == null) return;
            
            txtAccountCode.Text = _account.AccountCode;
            txtAccountName.Text = _account.AccountName;
            txtNotes.Text = _account.Notes;
            chkIsParent.IsChecked = _account.IsParent;
            
            // تحديد نوع الحساب
            foreach (ComboBoxItem item in cmbAccountType.Items)
            {
                if (item.Tag?.ToString() == _account.AccountType.ToString())
                {
                    cmbAccountType.SelectedItem = item;
                    break;
                }
            }
            
            // تحديد الحساب الأب
            if (_account.ParentAccountId.HasValue)
            {
                SetSelectedParentAccount(_account.ParentAccountId.Value);
            }
        }
        
        /// <summary>
        /// تحديد الحساب الأب المحدد
        /// </summary>
        private void SetSelectedParentAccount(int parentAccountId)
        {
            foreach (ComboBoxItem item in cmbParentAccount.Items)
            {
                if (item.Tag != null && (int)item.Tag == parentAccountId)
                {
                    cmbParentAccount.SelectedItem = item;
                    break;
                }
            }
        }
        
        /// <summary>
        /// معالج تغيير حالة الحساب الرئيسي
        /// </summary>
        private void ChkIsParent_Checked(object sender, RoutedEventArgs e)
        {
            UpdateParentAccountVisibility();
        }
        
        private void ChkIsParent_Unchecked(object sender, RoutedEventArgs e)
        {
            UpdateParentAccountVisibility();
        }
        
        /// <summary>
        /// تحديث رؤية الحساب الأب
        /// </summary>
        private void UpdateParentAccountVisibility()
        {
            if (chkIsParent.IsChecked == true)
            {
                pnlParentAccount.Visibility = Visibility.Collapsed;
                lblParentAccount.Visibility = Visibility.Collapsed;
                cmbParentAccount.Visibility = Visibility.Collapsed;
            }
            else
            {
                pnlParentAccount.Visibility = Visibility.Visible;
                lblParentAccount.Visibility = Visibility.Visible;
                cmbParentAccount.Visibility = Visibility.Visible;
            }
        }
        
        /// <summary>
        /// حفظ الحساب
        /// </summary>
        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!ValidateInput())
                    return;
                
                var account = CreateAccountFromInput();
                
                bool success;
                if (_isEditMode)
                {
                    success = _accountingService.UpdateAccount(account);
                }
                else
                {
                    success = _accountingService.CreateAccount(account);
                }
                
                if (success)
                {
                    MessageBox.Show($"تم {(_isEditMode ? "تحديث" : "إضافة")} الحساب بنجاح", "نجح", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show($"فشل في {(_isEditMode ? "تحديث" : "إضافة")} الحساب", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الحساب: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// التحقق من صحة المدخلات
        /// </summary>
        private bool ValidateInput()
        {
            if (string.IsNullOrWhiteSpace(txtAccountCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود الحساب", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtAccountCode.Focus();
                return false;
            }
            
            if (string.IsNullOrWhiteSpace(txtAccountName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtAccountName.Focus();
                return false;
            }
            
            if (cmbAccountType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحساب", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                cmbAccountType.Focus();
                return false;
            }
            
            if (chkIsParent.IsChecked != true && cmbParentAccount.SelectedIndex <= 0)
            {
                MessageBox.Show("يرجى اختيار الحساب الأب للحساب الفرعي", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                cmbParentAccount.Focus();
                return false;
            }
            
            // التحقق من عدم تكرار كود الحساب
            var existingAccount = _accountingService.GetAllAccounts()
                .FirstOrDefault(a => a.AccountCode == txtAccountCode.Text.Trim() && 
                                   (_account == null || a.Id != _account.Id));
            
            if (existingAccount != null)
            {
                MessageBox.Show("كود الحساب موجود مسبقاً. يرجى اختيار كود آخر.", "تنبيه", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                txtAccountCode.Focus();
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// إنشاء كائن الحساب من المدخلات
        /// </summary>
        private Account CreateAccountFromInput()
        {
            var accountType = Enum.Parse<AccountType>(((ComboBoxItem)cmbAccountType.SelectedItem).Tag.ToString()!);
            
            int? parentAccountId = null;
            if (chkIsParent.IsChecked != true && cmbParentAccount.SelectedItem is ComboBoxItem selectedParent && selectedParent.Tag != null)
            {
                parentAccountId = (int)selectedParent.Tag;
            }
            
            var account = new Account
            {
                AccountCode = txtAccountCode.Text.Trim(),
                AccountName = txtAccountName.Text.Trim(),
                AccountType = accountType,
                IsParent = chkIsParent.IsChecked == true,
                ParentAccountId = parentAccountId,
                Notes = txtNotes.Text?.Trim() ?? "",
                IsActive = true,
                CreatedDate = DateTime.Now,
                ModifiedDate = DateTime.Now
            };
            
            if (_isEditMode && _account != null)
            {
                account.Id = _account.Id;
                account.CreatedDate = _account.CreatedDate;
            }
            
            return account;
        }
        
        /// <summary>
        /// إلغاء العملية
        /// </summary>
        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
