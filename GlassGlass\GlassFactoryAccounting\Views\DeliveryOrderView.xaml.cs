using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// واجهة أمر التسليم
    /// </summary>
    public partial class DeliveryOrderView : UserControl
    {
        private readonly Services.ManufacturingService _manufacturingService;
        private DeliveryOrder _currentDeliveryOrder;
        private NewManufacturingOrder _sourceManufacturingOrder;
        
        // مجموعة المقاسات المسلمة
        private ObservableCollection<DeliveredSizeViewModel> _deliveredSizes;

        public DeliveryOrderView()
        {
            InitializeComponent();
            _manufacturingService = new Services.ManufacturingService();
            
            InitializeCollections();
            InitializeControls();
        }

        /// <summary>
        /// تهيئة الواجهة مع أمر تصنيع محدد
        /// </summary>
        public DeliveryOrderView(NewManufacturingOrder manufacturingOrder) : this()
        {
            _sourceManufacturingOrder = manufacturingOrder;
            LoadFromManufacturingOrder();
        }

        #region Initialization

        /// <summary>
        /// تهيئة المجموعات
        /// </summary>
        private void InitializeCollections()
        {
            _deliveredSizes = new ObservableCollection<DeliveredSizeViewModel>();
            dgDeliveredSizes.ItemsSource = _deliveredSizes;
        }

        /// <summary>
        /// تهيئة العناصر
        /// </summary>
        private void InitializeControls()
        {
            // تعيين رقم أمر التسليم
            txtDeliveryOrderNumber.Text = _manufacturingService.GenerateDeliveryOrderNumber();
            
            // تعيين التاريخ الحالي
            dpDeliveryDate.SelectedDate = DateTime.Now;
            
            // إنشاء أمر تسليم جديد
            _currentDeliveryOrder = new DeliveryOrder
            {
                DeliveryOrderNumber = txtDeliveryOrderNumber.Text,
                DeliveryDate = DateTime.Now,
                CreatedBy = "النظام",
                CreatedDate = DateTime.Now
            };
        }

        /// <summary>
        /// تحميل البيانات من أمر التصنيع
        /// </summary>
        private void LoadFromManufacturingOrder()
        {
            if (_sourceManufacturingOrder == null) return;

            try
            {
                // تعبئة البيانات الأساسية
                txtCustomerName.Text = _sourceManufacturingOrder.CustomerName;
                txtWorkOrderNumber.Text = _sourceManufacturingOrder.OrderNumber;
                _currentDeliveryOrder.ManufacturingOrderId = _sourceManufacturingOrder.Id;
                _currentDeliveryOrder.CustomerName = _sourceManufacturingOrder.CustomerName;
                _currentDeliveryOrder.WorkOrderNumber = _sourceManufacturingOrder.OrderNumber;

                // تحميل المقاسات المطلوبة من أمر التصنيع
                LoadRequiredSizesFromManufacturingOrder();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات أمر التصنيع: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل المقاسات المطلوبة من أمر التصنيع
        /// </summary>
        private void LoadRequiredSizesFromManufacturingOrder()
        {
            if (_sourceManufacturingOrder == null) return;

            var requiredSizes = _manufacturingService.GetRequiredSizes(_sourceManufacturingOrder.Id);
            
            foreach (var size in requiredSizes.Where(s => !s.IsDelivered))
            {
                var deliveredSize = new DeliveredSizeViewModel
                {
                    RowNumber = _deliveredSizes.Count + 1,
                    RefCode = size.RefCode,
                    GlassType = size.GlassType,
                    Thickness = size.Thickness,
                    Length = size.Length,
                    Width = size.Width,
                    Quantity = size.Quantity
                };

                _deliveredSizes.Add(deliveredSize);
            }

            UpdateDeliverySummary();
        }

        #endregion

        #region Delivered Sizes

        /// <summary>
        /// إضافة مقاس مسلم جديد
        /// </summary>
        private void BtnAddDeliveredSize_Click(object sender, RoutedEventArgs e)
        {
            var newSize = new DeliveredSizeViewModel
            {
                RowNumber = _deliveredSizes.Count + 1,
                RefCode = "",
                GlassType = "",
                Thickness = "",
                Length = 0,
                Width = 0,
                Quantity = 1
            };

            _deliveredSizes.Add(newSize);
            UpdateDeliverySummary();
        }

        /// <summary>
        /// تحديث ملخص التسليم
        /// </summary>
        private void UpdateDeliverySummary()
        {
            int totalPieces = _deliveredSizes.Sum(s => s.Quantity);
            decimal totalSquareMeters = _deliveredSizes.Sum(s => (s.Length * s.Width * s.Quantity) / 1000000);

            txtTotalPieces.Text = $"{totalPieces} قطعة";
            txtTotalSquareMeters.Text = $"{totalSquareMeters:F2} م²";

            // تحديث أمر التسليم
            _currentDeliveryOrder.TotalPieces = totalPieces;
            _currentDeliveryOrder.TotalSquareMeters = totalSquareMeters;

            // تحديث العرض
            dgDeliveredSizes.Items.Refresh();
        }

        #endregion

        #region Main Actions

        /// <summary>
        /// حفظ أمر التسليم
        /// </summary>
        private void BtnSaveDeliveryOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(txtCustomerName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم العميل", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (_deliveredSizes.Count == 0)
                {
                    MessageBox.Show("يرجى إضافة مقاسات للتسليم", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // تحديث بيانات أمر التسليم
                _currentDeliveryOrder.CustomerName = txtCustomerName.Text;
                _currentDeliveryOrder.InvoiceNumber = txtInvoiceNumber.Text;
                _currentDeliveryOrder.ProjectName = txtProjectName.Text;
                _currentDeliveryOrder.DeliveryDate = dpDeliveryDate.SelectedDate ?? DateTime.Now;
                _currentDeliveryOrder.DeliveryResponsible = txtDeliveryResponsible.Text;
                _currentDeliveryOrder.ReceiverSignature = txtReceiverSignature.Text;

                // حفظ أمر التسليم
                bool success = _manufacturingService.SaveDeliveryOrder(_currentDeliveryOrder);

                if (success)
                {
                    // حفظ المقاسات المسلمة
                    SaveDeliveredSizes();

                    // تحديث حالة التسليم للمقاسات في أمر التصنيع
                    UpdateDeliveryStatus();

                    MessageBox.Show("تم حفظ أمر التسليم بنجاح!", "نجح الحفظ", 
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    // تحديث عرض التوقيعات
                    UpdateSignatureDisplay();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ أمر التسليم!", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ أمر التسليم: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ المقاسات المسلمة
        /// </summary>
        private void SaveDeliveredSizes()
        {
            try
            {
                var deliveredSizes = _deliveredSizes.Select(ds => new DeliveredSize
                {
                    DeliveryOrderId = _currentDeliveryOrder.Id,
                    RefCode = ds.RefCode,
                    GlassType = ds.GlassType,
                    Thickness = ds.Thickness,
                    Length = ds.Length,
                    Width = ds.Width,
                    Quantity = ds.Quantity
                }).ToList();

                // هنا يمكن إضافة دالة حفظ المقاسات المسلمة في الخدمة
                // _manufacturingService.SaveDeliveredSizes(_currentDeliveryOrder.Id, deliveredSizes);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving delivered sizes: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث حالة التسليم للمقاسات
        /// </summary>
        private void UpdateDeliveryStatus()
        {
            try
            {
                if (_sourceManufacturingOrder == null) return;

                var requiredSizes = _manufacturingService.GetRequiredSizes(_sourceManufacturingOrder.Id);
                
                foreach (var deliveredSize in _deliveredSizes)
                {
                    var requiredSize = requiredSizes.FirstOrDefault(rs => rs.RefCode == deliveredSize.RefCode);
                    if (requiredSize != null)
                    {
                        requiredSize.IsDelivered = true;
                    }
                }

                // حفظ التحديث
                _manufacturingService.SaveRequiredSizes(_sourceManufacturingOrder.Id, requiredSizes);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating delivery status: {ex.Message}");
            }
        }

        /// <summary>
        /// تحديث عرض التوقيعات
        /// </summary>
        private void UpdateSignatureDisplay()
        {
            txtDeliveryResponsibleDisplay.Text = txtDeliveryResponsible.Text;
            txtReceiverSignatureDisplay.Text = txtReceiverSignature.Text;
        }

        /// <summary>
        /// إنشاء أمر تسليم يدوي
        /// </summary>
        private void BtnCreateManualDelivery_Click(object sender, RoutedEventArgs e)
        {
            // إعادة تعيين الواجهة لأمر تسليم يدوي جديد
            _sourceManufacturingOrder = null;
            _currentDeliveryOrder = new DeliveryOrder
            {
                DeliveryOrderNumber = _manufacturingService.GenerateDeliveryOrderNumber(),
                DeliveryDate = DateTime.Now,
                CreatedBy = "النظام",
                CreatedDate = DateTime.Now
            };

            // مسح البيانات
            txtCustomerName.Text = "";
            txtCustomerName.IsReadOnly = false;
            txtWorkOrderNumber.Text = "";
            txtInvoiceNumber.Text = "";
            txtProjectName.Text = "";
            txtDeliveryResponsible.Text = "";
            txtReceiverSignature.Text = "";
            txtDeliveryOrderNumber.Text = _currentDeliveryOrder.DeliveryOrderNumber;
            dpDeliveryDate.SelectedDate = DateTime.Now;

            _deliveredSizes.Clear();
            UpdateDeliverySummary();

            MessageBox.Show("تم إنشاء أمر تسليم يدوي جديد", "أمر تسليم يدوي", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// طباعة أمر التسليم
        /// </summary>
        private void BtnPrintDeliveryOrder_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("سيتم تطوير طباعة أمر التسليم قريباً", "قيد التطوير", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// عرض المقاسات المسلمة
        /// </summary>
        private void BtnViewDeliveredSizes_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var deliveredSizesWindow = new DeliveredSizesReportView();
                deliveredSizesWindow.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح تقرير المقاسات المسلمة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion
    }

    #region ViewModels

    /// <summary>
    /// نموذج عرض المقاس المسلم
    /// </summary>
    public class DeliveredSizeViewModel
    {
        public int RowNumber { get; set; }
        public string RefCode { get; set; } = "";
        public string GlassType { get; set; } = "";
        public string Thickness { get; set; } = "";
        public decimal Length { get; set; }
        public decimal Width { get; set; }
        public int Quantity { get; set; }
    }

    #endregion
}
