﻿#pragma checksum "..\..\..\..\Views\ServicesView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "D27D8455486C123C7479ECC54188470052A531F7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ServicesView
    /// </summary>
    public partial class ServicesView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 113 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbCustomer;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CmbServiceType;
        
        #line default
        #line hidden
        
        
        #line 125 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpFromDate;
        
        #line default
        #line hidden
        
        
        #line 130 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DpToDate;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtInvoiceNumber;
        
        #line default
        #line hidden
        
        
        #line 141 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtGlassType;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnSearch;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearFilters;
        
        #line default
        #line hidden
        
        
        #line 155 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 157 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExport;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ServicesDataGrid;
        
        #line default
        #line hidden
        
        
        #line 206 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtRecordCount;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalArea;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalValue;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\..\Views\ServicesView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtLastUpdate;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/servicesview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ServicesView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CmbCustomer = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 2:
            this.CmbServiceType = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 3:
            this.DpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.DpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 5:
            this.TxtInvoiceNumber = ((System.Windows.Controls.TextBox)(target));
            return;
            case 6:
            this.TxtGlassType = ((System.Windows.Controls.TextBox)(target));
            return;
            case 7:
            this.BtnSearch = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\Views\ServicesView.xaml"
            this.BtnSearch.Click += new System.Windows.RoutedEventHandler(this.BtnSearch_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.BtnClearFilters = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\..\Views\ServicesView.xaml"
            this.BtnClearFilters.Click += new System.Windows.RoutedEventHandler(this.BtnClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 156 "..\..\..\..\Views\ServicesView.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.BtnExport = ((System.Windows.Controls.Button)(target));
            
            #line 158 "..\..\..\..\Views\ServicesView.xaml"
            this.BtnExport.Click += new System.Windows.RoutedEventHandler(this.BtnExport_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ServicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 12:
            this.TxtRecordCount = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.TxtTotalArea = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.TxtTotalValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TxtLastUpdate = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

