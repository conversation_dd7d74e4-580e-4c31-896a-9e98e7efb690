using System.Windows;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// نافذة تعديل المخزن
    /// </summary>
    public partial class WarehouseEditWindow : Window
    {
        private readonly WarehouseService _warehouseService;
        private readonly Warehouse _warehouse;

        public WarehouseEditWindow(Warehouse warehouse)
        {
            InitializeComponent();
            _warehouseService = new WarehouseService();
            _warehouse = warehouse;
            
            // تعبئة البيانات
            TxtCode.Text = warehouse.Code;
            TxtName.Text = warehouse.Name;
            TxtLocation.Text = warehouse.Location;
            TxtDescription.Text = warehouse.Description;
        }

        private async void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(TxtName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم المخزن", "تنبيه", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                _warehouse.Name = TxtName.Text.Trim();
                _warehouse.Location = TxtLocation.Text.Trim();
                _warehouse.Description = TxtDescription.Text.Trim();

                var success = await _warehouseService.SaveWarehouseAsync(_warehouse);
                if (success)
                {
                    DialogResult = true;
                    Close();
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التعديلات", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnCancel_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }
    }
}
