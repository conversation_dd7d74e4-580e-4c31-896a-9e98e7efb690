<UserControl x:Class="GlassFactoryAccounting.Views.ExpenseManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="800" d:DesignWidth="1200"
             FlowDirection="RightToLeft">

    <UserControl.Resources>
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="20"/>
            <Setter Property="Margin" Value="0,0,0,20"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2" BlurRadius="8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="5">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource PrimaryButtonStyle}">
            <Setter Property="Background" Value="#4CAF50"/>
        </Style>

        <DropShadowEffect x:Key="DropShadowEffect" Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="10"/>
    </UserControl.Resources>

    <ScrollViewer VerticalScrollBarVisibility="Auto" Margin="20" Background="#F5F5F5">
        <StackPanel>
            <!-- العنوان -->
            <Border Style="{StaticResource CardStyle}">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="🏷️" FontSize="32" VerticalAlignment="Center" Margin="0,0,15,0"/>
                    <StackPanel>
                        <TextBlock Text="إدارة المصروفات" FontSize="24" FontWeight="Bold" Foreground="#333"/>
                        <TextBlock Text="تسجيل المصروفات الأساسية والفرعية وربطها بالمشاريع" FontSize="14" Foreground="#666"/>
                    </StackPanel>
                </StackPanel>
            </Border>

            <!-- نموذج إضافة مصروف -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="➕ إضافة مصروف جديد" FontSize="18" FontWeight="Bold" 
                               Margin="0,0,0,20" Foreground="#2C3E50"/>

                    <!-- الصف الأول: المصروف الرئيسي والفرعي -->
                    <Grid Grid.Row="1" Margin="0,0,0,20">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <!-- المصروف الرئيسي -->
                        <Border Grid.Column="0" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="اسم المصروف الرئيسي:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <TextBox x:Name="TxtMainExpenseName" Height="50"
                                         VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                         FontSize="16" Padding="15,10"
                                         BorderBrush="#E74C3C" BorderThickness="3"
                                         Background="White"/>
                            </StackPanel>
                        </Border>

                        <!-- المصروف الفرعي -->
                        <Border Grid.Column="1" Margin="8" Padding="15" Background="White"
                                BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                                Effect="{StaticResource DropShadowEffect}">
                            <StackPanel>
                                <TextBlock Text="اسم المصروف الفرعي:" FontWeight="Bold" FontSize="15"
                                           Margin="0,0,0,12" HorizontalAlignment="Center"
                                           Foreground="#2C3E50"/>
                                <TextBox x:Name="TxtSubExpenseName" Height="50"
                                         VerticalContentAlignment="Center" HorizontalContentAlignment="Center"
                                         FontSize="16" Padding="15,10"
                                         BorderBrush="#27AE60" BorderThickness="3"
                                         Background="White"/>
                            </StackPanel>
                        </Border>
                    </Grid>

                    <!-- الصف الثاني: الملاحظات -->
                    <Border Grid.Row="2" Margin="8,0,8,20" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <StackPanel>
                            <TextBlock Text="ملاحظات:" FontWeight="Bold" FontSize="15"
                                       Margin="0,0,0,12" HorizontalAlignment="Center"
                                       Foreground="#2C3E50"/>
                            <TextBox x:Name="TxtNotes" Height="80"
                                     VerticalContentAlignment="Top" HorizontalContentAlignment="Right"
                                     FontSize="16" Padding="15,10"
                                     BorderBrush="#9B59B6" BorderThickness="3"
                                     Background="White" TextWrapping="Wrap"
                                     AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                        </StackPanel>
                    </Border>

                    <!-- الصف الثالث: خانة اختيار المشروع -->
                    <Border Grid.Row="3" Margin="8,0,8,20" Padding="15" Background="White"
                            BorderBrush="#E0E0E0" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}">
                        <CheckBox x:Name="ChkIsProjectRelated" Content="هذا المصروف خاص بمشروع" 
                                  FontSize="16" FontWeight="Bold" Foreground="#2C3E50"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"
                                  Checked="ChkIsProjectRelated_Checked" Unchecked="ChkIsProjectRelated_Unchecked"/>
                    </Border>

                    <!-- الصف الرابع: بيانات المشروع (مخفية افتراضياً) -->
                    <Border x:Name="ProjectDetailsPanel" Grid.Row="4" Margin="8,0,8,20" Padding="15" Background="#F8F9FA"
                            BorderBrush="#17A2B8" BorderThickness="2" CornerRadius="8"
                            Effect="{StaticResource DropShadowEffect}" Visibility="Collapsed">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="📋 بيانات المشروع" FontSize="16" FontWeight="Bold" 
                                       Margin="0,0,0,15" HorizontalAlignment="Center" Foreground="#17A2B8"/>

                            <!-- عنوان المشروع وتاريخ البدء -->
                            <Grid Grid.Row="1" Margin="0,0,0,15">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="2*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="0,0,10,0">
                                    <TextBlock Text="عنوان المشروع:" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                                    <TextBox x:Name="TxtProjectTitle" Height="45" VerticalContentAlignment="Center" 
                                             HorizontalContentAlignment="Center" FontSize="14" Padding="10"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="10,0,0,0">
                                    <TextBlock Text="تاريخ بدء المشروع:" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                                    <DatePicker x:Name="DpProjectStartDate" Height="45" VerticalContentAlignment="Center" 
                                                HorizontalContentAlignment="Center" FontSize="14"/>
                                </StackPanel>
                            </Grid>

                            <!-- وصف المشروع -->
                            <StackPanel Grid.Row="2" Margin="0,0,0,15">
                                <TextBlock Text="وصف المشروع:" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                                <TextBox x:Name="TxtProjectDescription" Height="60" VerticalContentAlignment="Top" 
                                         HorizontalContentAlignment="Right" FontSize="14" Padding="10"
                                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                            </StackPanel>

                            <!-- ملاحظات المشروع -->
                            <StackPanel Grid.Row="3">
                                <TextBlock Text="ملاحظات المشروع:" FontWeight="Bold" FontSize="14" Margin="0,0,0,8"/>
                                <TextBox x:Name="TxtProjectNotes" Height="60" VerticalContentAlignment="Top" 
                                         HorizontalContentAlignment="Right" FontSize="14" Padding="10"
                                         TextWrapping="Wrap" AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                            </StackPanel>
                        </Grid>
                    </Border>

                    <!-- الأزرار -->
                    <StackPanel Grid.Row="4" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,20,0,0">
                        <Button x:Name="BtnSave" Content="💾 حفظ المصروف" 
                                Style="{StaticResource SuccessButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Margin="0,0,15,0" Click="BtnSave_Click"/>
                        <Button x:Name="BtnClear" Content="🗑️ مسح النموذج" 
                                Style="{StaticResource PrimaryButtonStyle}"
                                Width="150" Height="45" FontSize="14" FontWeight="Bold"
                                Background="#95A5A6" Click="BtnClear_Click"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- قائمة المصروفات -->
            <Border Style="{StaticResource CardStyle}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <TextBlock Grid.Row="0" Text="📋 قائمة المصروفات المسجلة" FontSize="18" FontWeight="Bold" 
                               Margin="0,0,0,15" Foreground="#2C3E50"/>

                    <DataGrid Grid.Row="1" x:Name="DgExpenseCategories" AutoGenerateColumns="False"
                              CanUserAddRows="False" CanUserDeleteRows="False"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              Background="White" RowBackground="#FAFAFA" AlternatingRowBackground="#F0F0F0"
                              BorderBrush="#E0E0E0" BorderThickness="1"
                              FontSize="14" RowHeight="40" MinHeight="300">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="الرقم" Binding="{Binding Id}" Width="60" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="10"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المصروف الرئيسي" Binding="{Binding MainExpenseName}" Width="200" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="10"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="المصروف الفرعي" Binding="{Binding SubExpenseName}" Width="200" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="10"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="مرتبط بمشروع" Binding="{Binding IsProjectRelated}" Width="120" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="10"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTextColumn Header="عنوان المشروع" Binding="{Binding ProjectTitle}" Width="*" IsReadOnly="True">
                                <DataGridTextColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="10"/>
                                    </Style>
                                </DataGridTextColumn.HeaderStyle>
                                <DataGridTextColumn.ElementStyle>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                    </Style>
                                </DataGridTextColumn.ElementStyle>
                            </DataGridTextColumn>

                            <DataGridTemplateColumn Header="الإجراءات" Width="120">
                                <DataGridTemplateColumn.HeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#34495E"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="10"/>
                                    </Style>
                                </DataGridTemplateColumn.HeaderStyle>
                                <DataGridTemplateColumn.CellTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                            <Button Content="✏️" ToolTip="تعديل" Width="30" Height="30" 
                                                    Background="#3498DB" Foreground="White" BorderThickness="0"
                                                    Margin="2" Click="BtnEdit_Click"/>
                                            <Button Content="🗑️" ToolTip="حذف" Width="30" Height="30" 
                                                    Background="#E74C3C" Foreground="White" BorderThickness="0"
                                                    Margin="2" Click="BtnDelete_Click"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </DataGridTemplateColumn.CellTemplate>
                            </DataGridTemplateColumn>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </Border>
        </StackPanel>
    </ScrollViewer>
</UserControl>
