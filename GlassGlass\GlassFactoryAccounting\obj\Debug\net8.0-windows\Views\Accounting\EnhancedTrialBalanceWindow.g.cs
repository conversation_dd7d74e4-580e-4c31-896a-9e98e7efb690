﻿#pragma checksum "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F99D78BF336947FA9B7FEE70820A315851DE702B"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views.Accounting {
    
    
    /// <summary>
    /// EnhancedTrialBalanceWindow
    /// </summary>
    public partial class EnhancedTrialBalanceWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 27 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtDateRange;
        
        #line default
        #line hidden
        
        
        #line 48 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpFromDate;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker dpToDate;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox cmbAccountType;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox chkHideZeroBalances;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnRefresh;
        
        #line default
        #line hidden
        
        
        #line 79 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnExport;
        
        #line default
        #line hidden
        
        
        #line 84 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button btnPrint;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid dgTrialBalance;
        
        #line default
        #line hidden
        
        
        #line 188 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalDebit;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtTotalCredit;
        
        #line default
        #line hidden
        
        
        #line 202 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtDifference;
        
        #line default
        #line hidden
        
        
        #line 209 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock txtBalanceStatus;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/GlassFactoryAccounting;V1.0.0.0;component/views/accounting/enhancedtrialbalancew" +
                    "indow.xaml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.txtDateRange = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.dpFromDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 3:
            this.dpToDate = ((System.Windows.Controls.DatePicker)(target));
            return;
            case 4:
            this.cmbAccountType = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 5:
            this.chkHideZeroBalances = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 6:
            this.btnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 77 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
            this.btnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.btnExport = ((System.Windows.Controls.Button)(target));
            
            #line 82 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
            this.btnExport.Click += new System.Windows.RoutedEventHandler(this.BtnExport_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.btnPrint = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\..\Views\Accounting\EnhancedTrialBalanceWindow.xaml"
            this.btnPrint.Click += new System.Windows.RoutedEventHandler(this.BtnPrint_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.dgTrialBalance = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 10:
            this.txtTotalDebit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.txtTotalCredit = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.txtDifference = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.txtBalanceStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

