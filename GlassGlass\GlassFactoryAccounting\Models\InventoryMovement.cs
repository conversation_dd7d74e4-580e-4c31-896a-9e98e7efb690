using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نوع حركة المخزون
    /// </summary>
    public enum MovementType
    {
        استلام,
        تسليم
    }

    /// <summary>
    /// نموذج حركة المخزون
    /// </summary>
    public class InventoryMovement : INotifyPropertyChanged
    {
        private int _id;
        private string _orderNumber = string.Empty;
        private string _invoiceNumber = string.Empty;
        private DateTime _date;
        private MovementType _movementType;
        private int _customerId;
        private string _customerName = string.Empty;
        private int _supplierId;
        private string _supplierName = string.Empty;
        private int _itemId;
        private string _itemName = string.Empty;
        private int _warehouseId;
        private string _warehouseName = string.Empty;
        private decimal _units; // العدد
        private decimal _quantity;
        private decimal _unitPrice;
        private decimal _totalValue;
        private decimal _boxCount; // عدد الصناديق
        private UnitOfMeasure _unitOfMeasure;
        private decimal _boxContent;
        private decimal _length;
        private decimal _width;
        private decimal _area;
        private string _notes = string.Empty;
        private DateTime _createdDate;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string OrderNumber
        {
            get => _orderNumber;
            set => SetProperty(ref _orderNumber, value);
        }

        public string InvoiceNumber
        {
            get => _invoiceNumber;
            set => SetProperty(ref _invoiceNumber, value);
        }

        public DateTime Date
        {
            get => _date;
            set => SetProperty(ref _date, value);
        }

        public MovementType MovementType
        {
            get => _movementType;
            set => SetProperty(ref _movementType, value);
        }

        public int CustomerId
        {
            get => _customerId;
            set => SetProperty(ref _customerId, value);
        }

        public string CustomerName
        {
            get => _customerName;
            set => SetProperty(ref _customerName, value);
        }

        public int SupplierId
        {
            get => _supplierId;
            set => SetProperty(ref _supplierId, value);
        }

        public string SupplierName
        {
            get => _supplierName;
            set => SetProperty(ref _supplierName, value);
        }

        public int ItemId
        {
            get => _itemId;
            set => SetProperty(ref _itemId, value);
        }

        public string ItemName
        {
            get => _itemName;
            set => SetProperty(ref _itemName, value);
        }

        public int WarehouseId
        {
            get => _warehouseId;
            set => SetProperty(ref _warehouseId, value);
        }

        public string WarehouseName
        {
            get => _warehouseName;
            set => SetProperty(ref _warehouseName, value);
        }

        public decimal Units
        {
            get => _units;
            set
            {
                SetProperty(ref _units, value);
                CalculateTotalValue();
            }
        }

        public decimal Quantity
        {
            get => _quantity;
            set
            {
                SetProperty(ref _quantity, value);
                CalculateTotalValue();
            }
        }

        public decimal UnitPrice
        {
            get => _unitPrice;
            set
            {
                SetProperty(ref _unitPrice, value);
                CalculateTotalValue();
            }
        }

        public decimal TotalValue
        {
            get => _totalValue;
            private set => SetProperty(ref _totalValue, value);
        }

        public decimal BoxCount
        {
            get => _boxCount;
            set => SetProperty(ref _boxCount, value);
        }

        public UnitOfMeasure UnitOfMeasure
        {
            get => _unitOfMeasure;
            set => SetProperty(ref _unitOfMeasure, value);
        }

        public decimal BoxContent
        {
            get => _boxContent;
            set => SetProperty(ref _boxContent, value);
        }

        public decimal Length
        {
            get => _length;
            set => SetProperty(ref _length, value);
        }

        public decimal Width
        {
            get => _width;
            set => SetProperty(ref _width, value);
        }

        public decimal Area
        {
            get => _area;
            set => SetProperty(ref _area, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }

        /// <summary>
        /// حساب إجمالي القيمة
        /// </summary>
        private void CalculateTotalValue()
        {
            TotalValue = UnitPrice * Quantity;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        public InventoryMovement()
        {
            Date = DateTime.Now;
            CreatedDate = DateTime.Now;
        }
    }
}
