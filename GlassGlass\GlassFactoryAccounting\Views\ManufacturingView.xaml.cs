using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة التصنيع
/// </summary>
public partial class ManufacturingView : UserControl
{
    private readonly Services.ManufacturingService _manufacturingService;
    private ObservableCollection<NewManufacturingOrder> _manufacturingOrders;
    private bool _areNavigationToolsVisible = true;

    public ManufacturingView()
    {
        InitializeComponent();
        _manufacturingService = new Services.ManufacturingService();
        _manufacturingOrders = new ObservableCollection<NewManufacturingOrder>();

        LoadManufacturingData();
    }



    private void LoadManufacturingData()
    {
        try
        {
            _manufacturingOrders.Clear();
            var orders = _manufacturingService.GetAllManufacturingOrders();

            foreach (var order in orders)
            {
                _manufacturingOrders.Add(order);
            }

            UpdateStatistics();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            var totalOrders = _manufacturingOrders.Count;
            var activeOrders = _manufacturingOrders.Count(o => o.OrderStatus == "تحت التشغيل");
            var completedOrders = _manufacturingOrders.Count(o => o.OrderStatus == "مكتمل");
            var totalCost = _manufacturingOrders.Sum(o => o.TotalCost);

            TxtScheduledOrders.Text = totalOrders.ToString();
            TxtInProgressOrders.Text = activeOrders.ToString();
            TxtCompletedOrders.Text = completedOrders.ToString();
            TxtStoppedOrders.Text = _manufacturingOrders.Count(o => o.OrderStatus == "مؤجل").ToString();
            TxtTotalCost.Text = $"{totalCost:F2} ج.م";
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error updating statistics: {ex.Message}");
        }
    }

    #region Button Events

    /// <summary>
    /// أمر تصنيع جديد - يعرض في نفس النافذة
    /// </summary>
    private void BtnNewManufacturingOrder_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var newOrderView = new ManufacturingOrderView();

            // عرض في نفس النافذة بدل النافذة المنبثقة
            MainContent.Content = newOrderView;

            LoadManufacturingData();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح أمر تصنيع جديد: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    /// <summary>
    /// عرض أوامر التصنيع - يعرض في نفس النافذة
    /// </summary>
    private void BtnViewOrders_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var ordersListView = new ManufacturingOrdersListView();

            // عرض في نفس النافذة بدل النافذة المنبثقة
            MainContent.Content = ordersListView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح صفحة أوامر التصنيع: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// أوامر التسليم - يعرض في نفس النافذة
    /// </summary>
    private void BtnDeliveryOrders_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var deliveryOrderView = new DeliveryOrderView();

            // عرض في نفس النافذة بدل النافذة المنبثقة
            MainContent.Content = deliveryOrderView;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح أوامر التسليم: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تتبع المقاسات
    /// </summary>
    private void BtnSizeTracking_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var deliveredSizesReport = new DeliveredSizesReportView();
            deliveredSizesReport.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فتح تتبع المقاسات: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    /// <summary>
    /// تقارير التصنيع
    /// </summary>
    private void BtnManufacturingReports_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم تطوير تقارير التصنيع قريباً", "قيد التطوير",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    /// <summary>
    /// تحليل التكاليف
    /// </summary>
    private void BtnCostAnalysis_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("سيتم تطوير تحليل التكاليف قريباً", "قيد التطوير",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    /// <summary>
    /// إظهار/إخفاء أدوات التنقل
    /// </summary>
    private void BtnToggleNavigationTools_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            if (_areNavigationToolsVisible)
            {
                // إخفاء أدوات التنقل
                NavigationButtonsPanel.Visibility = Visibility.Collapsed;
                StatisticsPanel.Visibility = Visibility.Collapsed;
                btnToggleNavigationTools.Content = "✅ إظهار أدوات التنقل";
                btnToggleNavigationTools.Background = new SolidColorBrush(Color.FromRgb(40, 167, 69)); // أخضر
                btnToggleNavigationTools.ToolTip = "إظهار شريط الأزرار وشريط الإحصائيات";
                _areNavigationToolsVisible = false;
            }
            else
            {
                // إظهار أدوات التنقل
                NavigationButtonsPanel.Visibility = Visibility.Visible;
                StatisticsPanel.Visibility = Visibility.Visible;
                btnToggleNavigationTools.Content = "❌ إخفاء أدوات التنقل";
                btnToggleNavigationTools.Background = new SolidColorBrush(Color.FromRgb(108, 117, 125)); // رمادي
                btnToggleNavigationTools.ToolTip = "إخفاء شريط الأزرار وشريط الإحصائيات لتوسيع مساحة العمل";
                _areNavigationToolsVisible = true;
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في إظهار/إخفاء أدوات التنقل: {ex.Message}", "خطأ",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    #endregion
}
