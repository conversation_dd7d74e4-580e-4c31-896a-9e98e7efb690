import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  TextField,
  Grid,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Add,
  Search,
  FilterList,
  MoreVert,
  Edit,
  Delete,
  Print,
  Visibility,
  PostAdd,
  RemoveCircle,
  Download,
  Refresh,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Helmet } from 'react-helmet-async';
import { salesService } from '../../../services/salesService';
import LoadingSpinner from '../../../components/Common/LoadingSpinner';
import ErrorMessage from '../../../components/Common/ErrorMessage';
import { formatCurrency, formatDate } from '../../../utils/formatters';

interface SalesInvoice {
  id: number;
  invoiceNumber: string;
  invoiceDate: string;
  customerName: string;
  totalAmount: number;
  paidAmount: number;
  remainingAmount: number;
  invoiceStatus: string;
  isPosted: boolean;
  createdBy: string;
  createdAt: string;
}

const SalesInvoicesList: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // State للفلاتر
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [search, setSearch] = useState('');
  const [fromDate, setFromDate] = useState<Date | null>(null);
  const [toDate, setToDate] = useState<Date | null>(null);
  const [status, setStatus] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // State للحوارات
  const [deleteDialog, setDeleteDialog] = useState<{ open: boolean; invoice: SalesInvoice | null }>({
    open: false,
    invoice: null,
  });

  // جلب بيانات الفواتير
  const { data: invoicesData, isLoading, error, refetch } = useQuery(
    ['sales-invoices', page + 1, pageSize, search, fromDate, toDate, status],
    () => salesService.getSalesInvoices({
      page: page + 1,
      pageSize,
      search: search || undefined,
      fromDate: fromDate || undefined,
      toDate: toDate || undefined,
      status: status || undefined,
    }),
    {
      keepPreviousData: true,
      staleTime: 30000, // 30 seconds
    }
  );

  // Mutation للحذف
  const deleteMutation = useMutation(salesService.deleteSalesInvoice, {
    onSuccess: () => {
      toast.success('تم حذف الفاتورة بنجاح');
      queryClient.invalidateQueries('sales-invoices');
      setDeleteDialog({ open: false, invoice: null });
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'حدث خطأ أثناء حذف الفاتورة');
    },
  });

  // Mutation للترحيل
  const postMutation = useMutation(salesService.postSalesInvoice, {
    onSuccess: () => {
      toast.success('تم ترحيل الفاتورة بنجاح');
      queryClient.invalidateQueries('sales-invoices');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'حدث خطأ أثناء ترحيل الفاتورة');
    },
  });

  // Mutation لإلغاء الترحيل
  const unpostMutation = useMutation(salesService.unpostSalesInvoice, {
    onSuccess: () => {
      toast.success('تم إلغاء ترحيل الفاتورة بنجاح');
      queryClient.invalidateQueries('sales-invoices');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'حدث خطأ أثناء إلغاء ترحيل الفاتورة');
    },
  });

  // تعريف الأعمدة
  const columns: GridColDef[] = [
    {
      field: 'invoiceNumber',
      headerName: 'رقم الفاتورة',
      width: 150,
      renderCell: (params) => (
        <Button
          variant="text"
          color="primary"
          onClick={() => navigate(`/sales/invoices/${params.row.id}`)}
        >
          {params.value}
        </Button>
      ),
    },
    {
      field: 'invoiceDate',
      headerName: 'التاريخ',
      width: 120,
      valueFormatter: (params) => formatDate(params.value),
    },
    {
      field: 'customerName',
      headerName: 'العميل',
      width: 200,
      flex: 1,
    },
    {
      field: 'totalAmount',
      headerName: 'إجمالي الفاتورة',
      width: 150,
      type: 'number',
      valueFormatter: (params) => formatCurrency(params.value),
    },
    {
      field: 'paidAmount',
      headerName: 'المدفوع',
      width: 120,
      type: 'number',
      valueFormatter: (params) => formatCurrency(params.value),
    },
    {
      field: 'remainingAmount',
      headerName: 'المتبقي',
      width: 120,
      type: 'number',
      valueFormatter: (params) => formatCurrency(params.value),
      renderCell: (params) => (
        <Typography
          variant="body2"
          color={params.value > 0 ? 'error' : 'success'}
          fontWeight="bold"
        >
          {formatCurrency(params.value)}
        </Typography>
      ),
    },
    {
      field: 'invoiceStatus',
      headerName: 'الحالة',
      width: 120,
      renderCell: (params) => {
        const getStatusColor = (status: string) => {
          switch (status) {
            case 'مدفوعة': return 'success';
            case 'مفتوحة': return 'warning';
            case 'ملغاة': return 'error';
            default: return 'default';
          }
        };

        return (
          <Chip
            label={params.value}
            color={getStatusColor(params.value)}
            size="small"
          />
        );
      },
    },
    {
      field: 'isPosted',
      headerName: 'مرحلة',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'مرحلة' : 'غير مرحلة'}
          color={params.value ? 'success' : 'default'}
          size="small"
          variant={params.value ? 'filled' : 'outlined'}
        />
      ),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'الإجراءات',
      width: 120,
      getActions: (params) => {
        const invoice = params.row as SalesInvoice;
        const actions = [
          <GridActionsCellItem
            icon={<Visibility />}
            label="عرض"
            onClick={() => navigate(`/sales/invoices/${invoice.id}`)}
          />,
          <GridActionsCellItem
            icon={<Print />}
            label="طباعة"
            onClick={() => handlePrint(invoice.id)}
          />,
        ];

        if (!invoice.isPosted) {
          actions.push(
            <GridActionsCellItem
              icon={<Edit />}
              label="تعديل"
              onClick={() => navigate(`/sales/invoices/${invoice.id}/edit`)}
            />,
            <GridActionsCellItem
              icon={<PostAdd />}
              label="ترحيل"
              onClick={() => handlePost(invoice.id)}
            />,
            <GridActionsCellItem
              icon={<Delete />}
              label="حذف"
              onClick={() => setDeleteDialog({ open: true, invoice })}
            />
          );
        } else {
          actions.push(
            <GridActionsCellItem
              icon={<RemoveCircle />}
              label="إلغاء ترحيل"
              onClick={() => handleUnpost(invoice.id)}
            />
          );
        }

        return actions;
      },
    },
  ];

  // معالجات الأحداث
  const handlePrint = async (invoiceId: number) => {
    try {
      const pdfBlob = await salesService.printSalesInvoice(invoiceId);
      const url = window.URL.createObjectURL(pdfBlob);
      window.open(url, '_blank');
    } catch (error) {
      toast.error('حدث خطأ أثناء طباعة الفاتورة');
    }
  };

  const handlePost = (invoiceId: number) => {
    postMutation.mutate(invoiceId);
  };

  const handleUnpost = (invoiceId: number) => {
    unpostMutation.mutate(invoiceId);
  };

  const handleDelete = () => {
    if (deleteDialog.invoice) {
      deleteMutation.mutate(deleteDialog.invoice.id);
    }
  };

  const handleExport = async () => {
    try {
      const reportBlob = await salesService.generateSalesReport({
        fromDate,
        toDate,
        format: 'excel',
      });
      const url = window.URL.createObjectURL(reportBlob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `sales-report-${new Date().toISOString().split('T')[0]}.xlsx`;
      a.click();
    } catch (error) {
      toast.error('حدث خطأ أثناء تصدير التقرير');
    }
  };

  const resetFilters = () => {
    setSearch('');
    setFromDate(null);
    setToDate(null);
    setStatus('');
    setPage(0);
  };

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message="حدث خطأ في تحميل فواتير المبيعات" />;

  const invoices = invoicesData?.data?.items || [];
  const totalCount = invoicesData?.data?.totalCount || 0;

  return (
    <>
      <Helmet>
        <title>فواتير المبيعات - نظام حسابات مصنع الزجاج</title>
      </Helmet>

      <Box sx={{ p: 3 }}>
        {/* العنوان والأزرار */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h4" component="h1">
            📋 فواتير المبيعات
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<Download />}
              onClick={handleExport}
            >
              تصدير
            </Button>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={() => refetch()}
            >
              تحديث
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
              onClick={() => navigate('/sales/invoices/new')}
            >
              فاتورة جديدة
            </Button>
          </Box>
        </Box>

        {/* شريط البحث والفلاتر */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="البحث في الفواتير..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  InputProps={{
                    startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
                  }}
                />
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<FilterList />}
                  onClick={() => setShowFilters(!showFilters)}
                >
                  فلاتر
                </Button>
              </Grid>
              {showFilters && (
                <>
                  <Grid item xs={12} md={2}>
                    <DatePicker
                      label="من تاريخ"
                      value={fromDate}
                      onChange={setFromDate}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <DatePicker
                      label="إلى تاريخ"
                      value={toDate}
                      onChange={setToDate}
                      renderInput={(params) => <TextField {...params} fullWidth />}
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <FormControl fullWidth>
                      <InputLabel>الحالة</InputLabel>
                      <Select
                        value={status}
                        onChange={(e) => setStatus(e.target.value)}
                        label="الحالة"
                      >
                        <MenuItem value="">الكل</MenuItem>
                        <MenuItem value="مفتوحة">مفتوحة</MenuItem>
                        <MenuItem value="مدفوعة">مدفوعة</MenuItem>
                        <MenuItem value="ملغاة">ملغاة</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </>
              )}
              {showFilters && (
                <Grid item xs={12}>
                  <Button onClick={resetFilters} variant="text">
                    إعادة تعيين الفلاتر
                  </Button>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>

        {/* جدول البيانات */}
        <Card>
          <DataGrid
            rows={invoices}
            columns={columns}
            paginationMode="server"
            rowCount={totalCount}
            page={page}
            pageSize={pageSize}
            onPageChange={setPage}
            onPageSizeChange={setPageSize}
            rowsPerPageOptions={[5, 10, 25, 50]}
            loading={isLoading}
            disableSelectionOnClick
            autoHeight
            sx={{
              '& .MuiDataGrid-root': {
                border: 'none',
              },
              '& .MuiDataGrid-cell': {
                borderBottom: '1px solid #f0f0f0',
              },
              '& .MuiDataGrid-columnHeaders': {
                backgroundColor: '#fafafa',
                borderBottom: '2px solid #e0e0e0',
              },
            }}
          />
        </Card>

        {/* حوار تأكيد الحذف */}
        <Dialog
          open={deleteDialog.open}
          onClose={() => setDeleteDialog({ open: false, invoice: null })}
        >
          <DialogTitle>تأكيد الحذف</DialogTitle>
          <DialogContent>
            <Alert severity="warning" sx={{ mb: 2 }}>
              هل أنت متأكد من حذف الفاتورة رقم "{deleteDialog.invoice?.invoiceNumber}"؟
            </Alert>
            <Typography>
              هذا الإجراء لا يمكن التراجع عنه.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDeleteDialog({ open: false, invoice: null })}>
              إلغاء
            </Button>
            <Button
              onClick={handleDelete}
              color="error"
              variant="contained"
              disabled={deleteMutation.isLoading}
            >
              حذف
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </>
  );
};

export default SalesInvoicesList;
