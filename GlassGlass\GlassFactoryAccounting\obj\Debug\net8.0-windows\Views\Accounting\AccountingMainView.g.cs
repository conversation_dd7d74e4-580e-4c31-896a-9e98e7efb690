﻿#pragma checksum "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "87FAD26030209A357125C2B0944462F04F21AE8A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views.Accounting {
    
    
    /// <summary>
    /// AccountingMainView
    /// </summary>
    public partial class AccountingMainView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri(("/GlassFactoryAccounting;V1.0.0.0;component/views/accounting/accountingmainview.xa" +
                    "ml"), System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 40 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.ChartOfAccounts_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 54 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.JournalEntry_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 68 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.GeneralLedger_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 82 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.TrialBalance_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 96 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.IncomeStatement_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 110 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.BalanceSheet_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            
            #line 124 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.AccountStatement_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            
            #line 138 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.AccountingReports_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 152 "..\..\..\..\..\Views\Accounting\AccountingMainView.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonUp += new System.Windows.Input.MouseButtonEventHandler(this.AccountingSettings_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

