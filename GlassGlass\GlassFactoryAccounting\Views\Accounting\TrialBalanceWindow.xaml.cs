using System;
using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models.Accounting;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views.Accounting
{
    /// <summary>
    /// نافذة ميزان المراجعة
    /// </summary>
    public partial class TrialBalanceWindow : Window
    {
        private readonly AccountingService _accountingService;
        private readonly ObservableCollection<TrialBalanceItemViewModel> _trialBalanceItems;
        
        public TrialBalanceWindow()
        {
            InitializeComponent();
            _accountingService = new AccountingService();
            _trialBalanceItems = new ObservableCollection<TrialBalanceItemViewModel>();
            
            InitializeWindow();
            GenerateTrialBalance();
        }
        
        /// <summary>
        /// تهيئة النافذة
        /// </summary>
        private void InitializeWindow()
        {
            // تعيين التاريخ الحالي
            dpAsOfDate.SelectedDate = DateTime.Now;
            
            // ربط البيانات
            dgTrialBalance.ItemsSource = _trialBalanceItems;
        }
        
        /// <summary>
        /// إنشاء ميزان المراجعة
        /// </summary>
        private void BtnGenerate_Click(object sender, RoutedEventArgs e)
        {
            GenerateTrialBalance();
        }
        
        /// <summary>
        /// إنشاء ميزان المراجعة
        /// </summary>
        private void GenerateTrialBalance()
        {
            try
            {
                _trialBalanceItems.Clear();
                
                // إضافة بيانات تجريبية لميزان المراجعة
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "1001",
                    AccountName = "النقدية في الصندوق",
                    AccountType = AccountType.Asset,
                    DebitBalance = 50000,
                    CreditBalance = 0
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "1002",
                    AccountName = "البنك - الحساب الجاري",
                    AccountType = AccountType.Asset,
                    DebitBalance = 150000,
                    CreditBalance = 0
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "1101",
                    AccountName = "العملاء",
                    AccountType = AccountType.Asset,
                    DebitBalance = 75000,
                    CreditBalance = 0
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "1201",
                    AccountName = "المخزون - زجاج خام",
                    AccountType = AccountType.Asset,
                    DebitBalance = 100000,
                    CreditBalance = 0
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "2001",
                    AccountName = "الموردين",
                    AccountType = AccountType.Liability,
                    DebitBalance = 0,
                    CreditBalance = 45000
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "3001",
                    AccountName = "رأس المال",
                    AccountType = AccountType.Equity,
                    DebitBalance = 0,
                    CreditBalance = 250000
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "4001",
                    AccountName = "إيرادات المبيعات",
                    AccountType = AccountType.Revenue,
                    DebitBalance = 0,
                    CreditBalance = 120000
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "5001",
                    AccountName = "تكلفة البضاعة المباعة",
                    AccountType = AccountType.Expense,
                    DebitBalance = 80000,
                    CreditBalance = 0
                });
                
                _trialBalanceItems.Add(new TrialBalanceItemViewModel
                {
                    AccountCode = "5101",
                    AccountName = "مصروفات التشغيل",
                    AccountType = AccountType.Expense,
                    DebitBalance = 40000,
                    CreditBalance = 0
                });
                
                UpdateTotals();
                
                MessageBox.Show("تم إنشاء ميزان المراجعة بنجاح!\n\nملاحظة: هذه بيانات تجريبية للعرض. في النسخة الكاملة سيتم استخراج البيانات من قاعدة البيانات.", 
                    "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء ميزان المراجعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// تحديث الإجماليات
        /// </summary>
        private void UpdateTotals()
        {
            decimal totalDebit = 0;
            decimal totalCredit = 0;
            
            foreach (var item in _trialBalanceItems)
            {
                totalDebit += item.DebitBalance;
                totalCredit += item.CreditBalance;
            }
            
            txtTotalDebit.Text = totalDebit.ToString("N2");
            txtTotalCredit.Text = totalCredit.ToString("N2");
            
            // تحديث حالة التوازن
            if (totalDebit == totalCredit)
            {
                txtBalanceStatus.Text = "متوازن ✅";
                borderBalanceStatus.Background = System.Windows.Media.Brushes.Green;
            }
            else
            {
                txtBalanceStatus.Text = "غير متوازن ⚠️";
                borderBalanceStatus.Background = System.Windows.Media.Brushes.Red;
            }
        }
        
        /// <summary>
        /// تصدير إلى Excel
        /// </summary>
        private void BtnExport_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("تصدير Excel - قيد التطوير", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        
        /// <summary>
        /// طباعة التقرير
        /// </summary>
        private void BtnPrint_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("الطباعة - قيد التطوير", "معلومات", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
    
    /// <summary>
    /// نموذج عرض عنصر ميزان المراجعة
    /// </summary>
    public class TrialBalanceItemViewModel
    {
        public string AccountCode { get; set; } = "";
        public string AccountName { get; set; } = "";
        public AccountType AccountType { get; set; }
        public decimal DebitBalance { get; set; }
        public decimal CreditBalance { get; set; }
        
        public string AccountTypeDisplay
        {
            get
            {
                return AccountType switch
                {
                    AccountType.Asset => "أصل",
                    AccountType.Liability => "التزام",
                    AccountType.Equity => "حقوق ملكية",
                    AccountType.Revenue => "إيراد",
                    AccountType.Expense => "مصروف",
                    _ => "غير محدد"
                };
            }
        }
    }
}
