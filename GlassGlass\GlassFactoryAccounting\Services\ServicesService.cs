using System.Data.SQLite;
using System.Text.Json;
using System.IO;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;

namespace GlassFactoryAccounting.Services
{
    /// <summary>
    /// خدمة إدارة الخدمات المفصلة
    /// </summary>
    public class ServicesService
    {
        public ServicesService()
        {
        }

        /// <summary>
        /// دالة مساعدة لتحويل بيانات قاعدة البيانات إلى ServiceRecord
        /// </summary>
        private ServiceRecord CreateServiceRecordFromReader(System.Data.Common.DbDataReader reader)
        {
            return new ServiceRecord
            {
                Id = Convert.ToInt32(reader["Id"]),
                InvoiceNumber = reader["InvoiceNumber"]?.ToString() ?? "",
                SaleDate = DateTime.Parse(reader["SaleDate"]?.ToString() ?? DateTime.Now.ToString()),
                CustomerId = Convert.ToInt32(reader["CustomerId"]),
                CustomerName = reader["CustomerName"]?.ToString() ?? "غير محدد",
                ServiceName = reader["ServiceName"]?.ToString() ?? "",
                GlassType = reader["GlassType"]?.ToString() ?? "",
                GlassThickness = reader["GlassThickness"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["GlassThickness"]),
                Details = reader["Details"]?.ToString() ?? "",
                Length = reader["Length"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["Length"]),
                Width = reader["Width"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["Width"]),
                Area = reader["Area"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["Area"]),
                Count = reader["Count"] == DBNull.Value ? 0 : Convert.ToInt32(reader["Count"]),
                TotalArea = reader["TotalArea"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalArea"]),
                UnitPrice = reader["UnitPrice"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["UnitPrice"]),
                TotalPrice = reader["TotalPrice"] == DBNull.Value ? 0 : Convert.ToDecimal(reader["TotalPrice"]),
                IsManualRow = reader["IsManualRow"] != DBNull.Value && Convert.ToInt32(reader["IsManualRow"]) == 1,
                CreatedDate = DateTime.Parse(reader["CreatedDate"]?.ToString() ?? DateTime.Now.ToString()),
                ModifiedDate = DateTime.Parse(reader["ModifiedDate"]?.ToString() ?? DateTime.Now.ToString()),
                IsActive = reader["IsActive"] != DBNull.Value && Convert.ToInt32(reader["IsActive"]) == 1
            };
        }

        /// <summary>
        /// حفظ خدمات الفاتورة في جدول الخدمات المفصل
        /// </summary>
        public async Task<bool> SaveInvoiceServicesAsync(Sale sale)
        {
            try
            {
                // مؤقتاً - نعتمد على ملفات JSON فقط
                await Task.Delay(1);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error saving invoice services: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// جلب جميع سجلات الخدمات من فواتير المبيعات الفعلية
        /// </summary>
        public async Task<List<ServiceRecord>> GetAllServiceRecordsAsync()
        {
            try
            {
                // جلب من الفواتير المحفوظة
                var salesService = new SalesService();
                var sales = await salesService.GetAllSalesAsync();

                // إذا لم توجد فواتير، أرجع بيانات تجريبية للاختبار
                if (sales == null || sales.Count == 0)
                {
                    return GetSampleServiceRecords();
                }

                var serviceRecords = new List<ServiceRecord>();

                // تحويل الفواتير إلى سجلات خدمات
                foreach (var sale in sales)
                {
                    if (sale.SaleItems != null && sale.SaleItems.Count > 0)
                    {
                        foreach (var saleItem in sale.SaleItems)
                        {
                            var serviceRecord = new ServiceRecord
                            {
                                Id = saleItem.Id,
                                InvoiceNumber = sale.InvoiceNumber,
                                SaleDate = sale.SaleDate,
                                CustomerId = sale.CustomerId,
                                CustomerName = sale.Customer?.Name ?? "غير محدد",
                                ServiceName = saleItem.Service ?? "",
                                GlassType = saleItem.GlassType ?? "",
                                GlassThickness = saleItem.GlassThickness,
                                Details = saleItem.Details ?? "",
                                Length = saleItem.Length,
                                Width = saleItem.Width,
                                Area = saleItem.Area,
                                Count = saleItem.Count,
                                TotalArea = saleItem.TotalArea,
                                UnitPrice = saleItem.UnitPrice,
                                TotalPrice = saleItem.TotalPrice,
                                IsManualRow = saleItem.IsManualRow,
                                CreatedDate = DateTime.Now,
                                ModifiedDate = DateTime.Now,
                                IsActive = true
                            };
                            serviceRecords.Add(serviceRecord);
                        }
                    }
                }

                // ترتيب النتائج حسب التاريخ
                return serviceRecords.OrderByDescending(r => r.SaleDate)
                                   .ThenByDescending(r => r.InvoiceNumber)
                                   .ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting service records: {ex.Message}");
                // في حالة الخطأ، أرجع بيانات تجريبية
                return GetSampleServiceRecords();
            }
        }

        /// <summary>
        /// فلترة سجلات الخدمات حسب العميل
        /// </summary>
        public async Task<List<ServiceRecord>> GetServiceRecordsByCustomerAsync(string customerName)
        {
            var allRecords = await GetAllServiceRecordsAsync();
            return allRecords.Where(r => r.CustomerName.Contains(customerName, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// فلترة سجلات الخدمات حسب نوع الخدمة
        /// </summary>
        public async Task<List<ServiceRecord>> GetServiceRecordsByServiceTypeAsync(string serviceName)
        {
            var allRecords = await GetAllServiceRecordsAsync();
            return allRecords.Where(r => r.ServiceName.Contains(serviceName, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// فلترة سجلات الخدمات حسب التاريخ
        /// </summary>
        public async Task<List<ServiceRecord>> GetServiceRecordsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var allRecords = await GetAllServiceRecordsAsync();
            return allRecords.Where(r => r.SaleDate.Date >= fromDate.Date && r.SaleDate.Date <= toDate.Date).ToList();
        }

        /// <summary>
        /// فلترة سجلات الخدمات حسب رقم الفاتورة
        /// </summary>
        public async Task<List<ServiceRecord>> GetServiceRecordsByInvoiceNumberAsync(string invoiceNumber)
        {
            var allRecords = await GetAllServiceRecordsAsync();
            return allRecords.Where(r => r.InvoiceNumber.Contains(invoiceNumber, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        /// <summary>
        /// جلب قائمة بأسماء العملاء المتاحة في سجلات الخدمات
        /// </summary>
        public async Task<List<string>> GetDistinctCustomerNamesAsync()
        {
            var allRecords = await GetAllServiceRecordsAsync();
            return allRecords.Select(r => r.CustomerName)
                           .Where(name => !string.IsNullOrEmpty(name))
                           .Distinct()
                           .OrderBy(name => name)
                           .ToList();
        }

        /// <summary>
        /// جلب قائمة بأنواع الخدمات المتاحة في سجلات الخدمات
        /// </summary>
        public async Task<List<string>> GetDistinctServiceNamesAsync()
        {
            var allRecords = await GetAllServiceRecordsAsync();
            return allRecords.Select(r => r.ServiceName)
                           .Where(service => !string.IsNullOrEmpty(service))
                           .Distinct()
                           .OrderBy(service => service)
                           .ToList();
        }



        /// <summary>
        /// حذف سجلات الخدمات المرتبطة بفاتورة معينة
        /// </summary>
        public async Task<bool> DeleteServiceRecordsByInvoiceAsync(string invoiceNumber)
        {
            try
            {
                // مؤقتاً - نعتمد على ملفات JSON فقط
                await Task.Delay(1);
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error deleting service records: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إنشاء بيانات تجريبية للاختبار عندما لا توجد فواتير محفوظة
        /// </summary>
        private List<ServiceRecord> GetSampleServiceRecords()
        {
            return new List<ServiceRecord>
            {
                new ServiceRecord
                {
                    Id = 1,
                    InvoiceNumber = "INV-2024-001",
                    SaleDate = DateTime.Now.AddDays(-5),
                    CustomerId = 1,
                    CustomerName = "شركة الزجاج المتقدم",
                    ServiceName = "تقطيع زجاج",
                    GlassType = "زجاج شفاف",
                    GlassThickness = 6,
                    Length = 120,
                    Width = 80,
                    Area = 0.96m,
                    Count = 3,
                    TotalArea = 2.88m,
                    UnitPrice = 45,
                    TotalPrice = 129.6m,
                    Details = "تقطيع زجاج شفاف بسماكة 6 مم حسب المقاسات المطلوبة",
                    IsManualRow = false,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                    IsActive = true
                },
                new ServiceRecord
                {
                    Id = 2,
                    InvoiceNumber = "INV-2024-002",
                    SaleDate = DateTime.Now.AddDays(-3),
                    CustomerId = 2,
                    CustomerName = "مؤسسة البناء الحديث",
                    ServiceName = "تركيب زجاج",
                    GlassType = "زجاج ملون",
                    GlassThickness = 8,
                    Length = 150,
                    Width = 100,
                    Area = 1.5m,
                    Count = 2,
                    TotalArea = 3.0m,
                    UnitPrice = 65,
                    TotalPrice = 195,
                    Details = "تركيب زجاج ملون في الواجهة الرئيسية للمبنى",
                    IsManualRow = false,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                    IsActive = true
                },
                new ServiceRecord
                {
                    Id = 3,
                    InvoiceNumber = "INV-2024-003",
                    SaleDate = DateTime.Now.AddDays(-1),
                    CustomerId = 3,
                    CustomerName = "معرض الأثاث الفاخر",
                    ServiceName = "صيانة وتلميع",
                    GlassType = "زجاج مقسى",
                    GlassThickness = 10,
                    Length = 200,
                    Width = 120,
                    Area = 2.4m,
                    Count = 1,
                    TotalArea = 2.4m,
                    UnitPrice = 80,
                    TotalPrice = 192,
                    Details = "صيانة وتلميع زجاج المعرض الرئيسي",
                    IsManualRow = false,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                    IsActive = true
                },
                new ServiceRecord
                {
                    Id = 4,
                    InvoiceNumber = "INV-2024-004",
                    SaleDate = DateTime.Now.AddDays(-7),
                    CustomerId = 1,
                    CustomerName = "شركة الزجاج المتقدم",
                    ServiceName = "استبدال زجاج",
                    GlassType = "زجاج عاكس",
                    GlassThickness = 12,
                    Length = 180,
                    Width = 90,
                    Area = 1.62m,
                    Count = 4,
                    TotalArea = 6.48m,
                    UnitPrice = 95,
                    TotalPrice = 615.6m,
                    Details = "استبدال زجاج النوافذ القديمة بزجاج عاكس حديث",
                    IsManualRow = false,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                    IsActive = true
                },
                new ServiceRecord
                {
                    Id = 5,
                    InvoiceNumber = "INV-2024-005",
                    SaleDate = DateTime.Now,
                    CustomerId = 4,
                    CustomerName = "فندق النخيل الذهبي",
                    ServiceName = "تركيب زجاج",
                    GlassType = "زجاج أمان",
                    GlassThickness = 15,
                    Length = 250,
                    Width = 150,
                    Area = 3.75m,
                    Count = 2,
                    TotalArea = 7.5m,
                    UnitPrice = 120,
                    TotalPrice = 900,
                    Details = "تركيب زجاج أمان في مدخل الفندق الرئيسي",
                    IsManualRow = false,
                    CreatedDate = DateTime.Now,
                    ModifiedDate = DateTime.Now,
                    IsActive = true
                }
            };
        }

        /// <summary>
        /// الحصول على جميع الخدمات المتاحة
        /// </summary>
        public List<ServiceRecord> GetAllServices()
        {
            try
            {
                var task = GetAllServiceRecordsAsync();
                task.Wait();
                return task.Result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting services sync: {ex.Message}");
                return GetSampleServiceRecords();
            }
        }
    }
}
