using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using GlassFactoryWebApp.Data;
using GlassFactoryWebApp.Models;
using GlassFactoryWebApp.Services;
using GlassFactoryWebApp.Mappings;
using Microsoft.OpenApi.Models;
using Serilog;

var builder = WebApplication.CreateBuilder(args);

// إعداد Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .CreateLogger();

builder.Host.UseSerilog();

// تحديد البيئة
var isRailway = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("RAILWAY_STATIC_URL"));
var connectionString = builder.Configuration.GetConnectionString("DefaultConnection");

// إعداد PostgreSQL للـ Railway
if (isRailway)
{
    var databaseUrl = Environment.GetEnvironmentVariable("DATABASE_URL");
    if (!string.IsNullOrEmpty(databaseUrl))
    {
        var uri = new Uri(databaseUrl);
        var host = uri.Host;
        var port = uri.Port;
        var database = uri.LocalPath.TrimStart('/');
        var userInfo = uri.UserInfo.Split(':');
        var username = userInfo[0];
        var password = userInfo.Length > 1 ? userInfo[1] : "";
        
        connectionString = $"Host={host};Port={port};Database={database};Username={username};Password={password};SSL Mode=Require;Trust Server Certificate=true";
    }
}

// إضافة Entity Framework
builder.Services.AddDbContext<ApplicationDbContext>(options =>
    options.UseNpgsql(connectionString));

// إضافة Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options =>
{
    options.Password.RequireDigit = false;
    options.Password.RequireLowercase = false;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = false;
    options.Password.RequiredLength = 6;
})
.AddEntityFrameworkStores<ApplicationDbContext>()
.AddDefaultTokenProviders();

// JWT Configuration
var jwtSecretKey = Environment.GetEnvironmentVariable("JWT_SECRET_KEY") ?? 
                   "GlassFactorySecretKey2025VeryLongAndSecureForProduction!";

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = "GlassFactoryApp",
        ValidAudience = "GlassFactoryUsers",
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSecretKey))
    };
});

// إضافة AutoMapper
builder.Services.AddAutoMapper(typeof(SalesMappingProfile));

// إضافة خدمات التطبيق
builder.Services.AddScoped<ISalesService, SalesService>();

// إضافة Controllers
builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// إضافة Swagger
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Glass Factory Accounting System API",
        Version = "v2.0",
        Description = "نظام حسابات مصنع الزجاج - واجهة برمجة التطبيقات"
    });
});

// إضافة CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

// إضافة Health Checks
builder.Services.AddHealthChecks()
    .AddDbContext<ApplicationDbContext>();

var app = builder.Build();

// Configure the HTTP request pipeline
app.UseSwagger();
app.UseSwaggerUI(c =>
{
    c.SwaggerEndpoint("/swagger/v1/swagger.json", "Glass Factory API v2.0");
    c.RoutePrefix = "swagger";
});

app.UseCors();
app.UseStaticFiles();

app.UseAuthentication();
app.UseRouting();
app.UseAuthorization();

app.MapControllers();
app.MapHealthChecks("/health");

// Serve React app for all other routes
app.MapFallbackToFile("index.html");

// Database initialization
using (var scope = app.Services.CreateScope())
{
    try
    {
        var context = scope.ServiceProvider.GetRequiredService<ApplicationDbContext>();
        var userManager = scope.ServiceProvider.GetRequiredService<UserManager<ApplicationUser>>();
        var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

        // Ensure database is created
        await context.Database.EnsureCreatedAsync();

        // Run migrations if any
        if (context.Database.GetPendingMigrations().Any())
        {
            await context.Database.MigrateAsync();
        }

        // Seed initial data
        await SeedDataAsync(context, userManager, roleManager);
    }
    catch (Exception ex)
    {
        Log.Error(ex, "Database initialization failed");
    }
}

Log.Information("🚀 Glass Factory Accounting System started!");
Log.Information("🌐 Environment: {Environment}", isRailway ? "Railway" : "Development");

app.Run();

// Seed data method
static async Task SeedDataAsync(ApplicationDbContext context, UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager)
{
    // Create roles
    var roles = new[] { "Admin", "Manager", "User" };
    foreach (var role in roles)
    {
        if (!await roleManager.RoleExistsAsync(role))
        {
            await roleManager.CreateAsync(new IdentityRole(role));
        }
    }

    // Create admin user
    var adminEmail = "<EMAIL>";
    var adminUser = await userManager.FindByEmailAsync(adminEmail);
    if (adminUser == null)
    {
        adminUser = new ApplicationUser
        {
            UserName = adminEmail,
            Email = adminEmail,
            EmailConfirmed = true,
            FirstName = "مدير",
            LastName = "النظام",
            IsActive = true
        };

        var result = await userManager.CreateAsync(adminUser, "Admin123!");
        if (result.Succeeded)
        {
            await userManager.AddToRoleAsync(adminUser, "Admin");
        }
    }

    // Seed sample customers
    if (!context.Customers.Any())
    {
        var customers = new[]
        {
            new Customer
            {
                CustomerCode = "CUST-001",
                CustomerName = "شركة الزجاج المتطور",
                Phone = "0501234567",
                Email = "<EMAIL>",
                Address = "الرياض، المملكة العربية السعودية",
                CreditLimit = 50000,
                PaymentTermDays = 30,
                CustomerType = "شركة",
                IsActive = true
            },
            new Customer
            {
                CustomerCode = "CUST-002", 
                CustomerName = "مؤسسة البناء الحديث",
                Phone = "0507654321",
                Email = "<EMAIL>",
                Address = "جدة، المملكة العربية السعودية",
                CreditLimit = 75000,
                PaymentTermDays = 45,
                CustomerType = "مؤسسة",
                IsActive = true
            }
        };

        context.Customers.AddRange(customers);
        await context.SaveChangesAsync();
    }

    Log.Information("✅ Sample data seeded successfully");
}
