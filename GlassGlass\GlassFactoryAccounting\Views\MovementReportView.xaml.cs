using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class MovementReportView : UserControl
    {
        private readonly InventoryService _inventoryService;
        private readonly ItemService _itemService;
        private ObservableCollection<InventoryMovement> _movements;
        private ObservableCollection<Item> _items;
        private List<InventoryMovement> _allMovements;

        public MovementReportView()
        {
            InitializeComponent();
            _inventoryService = new InventoryService();
            _itemService = new ItemService();
            _movements = new ObservableCollection<InventoryMovement>();
            _items = new ObservableCollection<Item>();
            _allMovements = new List<InventoryMovement>();
            
            MovementsDataGrid.ItemsSource = _movements;
            
            LoadData();
            SetupFilters();
        }

        private void SetupFilters()
        {
            // إعداد فلتر نوع الحركة
            var movementTypes = new List<object>
            {
                new { Text = "جميع الحركات", Value = (MovementType?)null },
                new { Text = "استلام", Value = (MovementType?)MovementType.استلام },
                new { Text = "تسليم", Value = (MovementType?)MovementType.تسليم }
            };
            
            CmbMovementType.ItemsSource = movementTypes;
            CmbMovementType.DisplayMemberPath = "Text";
            CmbMovementType.SelectedValuePath = "Value";
            CmbMovementType.SelectedIndex = 0;

            // إعداد فلتر الأصناف
            CmbItem.ItemsSource = _items;
            
            // إعداد التواريخ الافتراضية
            DpFromDate.SelectedDate = DateTime.Now.AddMonths(-1);
            DpToDate.SelectedDate = DateTime.Now;
        }

        private async void LoadData()
        {
            await LoadItems();
            await LoadMovements();
        }

        private async Task LoadItems()
        {
            try
            {
                var items = await _itemService.GetAllItemsAsync();
                _items.Clear();
                _items.Add(new Item { Id = 0, Name = "جميع الأصناف" });
                foreach (var item in items)
                {
                    _items.Add(item);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الأصناف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async Task LoadMovements()
        {
            try
            {
                _allMovements = await _inventoryService.GetAllMovementsAsync();
                ApplyFilters();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحركات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnFilter_Click(object sender, RoutedEventArgs e)
        {
            ApplyFilters();
        }

        private void ApplyFilters()
        {
            try
            {
                var filteredMovements = _allMovements.AsEnumerable();

                // فلتر التاريخ
                if (DpFromDate.SelectedDate.HasValue)
                {
                    filteredMovements = filteredMovements.Where(m => m.Date >= DpFromDate.SelectedDate.Value);
                }

                if (DpToDate.SelectedDate.HasValue)
                {
                    filteredMovements = filteredMovements.Where(m => m.Date <= DpToDate.SelectedDate.Value);
                }

                // فلتر نوع الحركة
                if (CmbMovementType.SelectedValue is MovementType movementType)
                {
                    filteredMovements = filteredMovements.Where(m => m.MovementType == movementType);
                }

                // فلتر الصنف
                if (CmbItem.SelectedValue is int itemId && itemId > 0)
                {
                    filteredMovements = filteredMovements.Where(m => m.ItemId == itemId);
                }

                var result = filteredMovements.OrderByDescending(m => m.Date).ToList();

                _movements.Clear();
                foreach (var movement in result)
                {
                    _movements.Add(movement);
                }

                UpdateStatistics(result);
                TxtRecordCount.Text = $"({result.Count} حركة)";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تطبيق الفلاتر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void UpdateStatistics(List<InventoryMovement> movements)
        {
            var receivedMovements = movements.Where(m => m.MovementType == MovementType.استلام).ToList();
            var issuedMovements = movements.Where(m => m.MovementType == MovementType.تسليم).ToList();

            TxtTotalReceived.Text = receivedMovements.Sum(m => m.TotalValue).ToString("F2");
            TxtTotalIssued.Text = issuedMovements.Sum(m => m.TotalValue).ToString("F2");
            TxtReceiveCount.Text = receivedMovements.Count.ToString();
            TxtIssueCount.Text = issuedMovements.Count.ToString();
        }

        private void BtnClearFilter_Click(object sender, RoutedEventArgs e)
        {
            DpFromDate.SelectedDate = DateTime.Now.AddMonths(-1);
            DpToDate.SelectedDate = DateTime.Now;
            CmbMovementType.SelectedIndex = 0;
            CmbItem.SelectedIndex = 0;
            
            ApplyFilters();
        }

        private void BtnExportExcel_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                MessageBox.Show("ميزة التصدير إلى Excel ستكون متاحة قريباً", "معلومات", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في التصدير: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
