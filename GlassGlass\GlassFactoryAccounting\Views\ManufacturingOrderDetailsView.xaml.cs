using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    /// <summary>
    /// صفحة تفاصيل أمر التصنيع
    /// </summary>
    public partial class ManufacturingOrderDetailsView : UserControl
    {
        private readonly NewManufacturingOrder _order;
        private readonly ManufacturingService _manufacturingService;

        public ManufacturingOrderDetailsView(NewManufacturingOrder order)
        {
            InitializeComponent();
            _order = order;
            _manufacturingService = new ManufacturingService();
            
            LoadOrderDetails();
        }

        /// <summary>
        /// تحميل تفاصيل الأمر
        /// </summary>
        private void LoadOrderDetails()
        {
            try
            {
                if (_order != null)
                {
                    // العنوان
                    txtOrderTitle.Text = $"📋 تفاصيل أمر التصنيع الشامل";
                    txtOrderNumberHeader.Text = $"رقم الأمر: {_order.OrderNumber}";

                    // المعلومات الأساسية
                    txtCustomerName.Text = _order.CustomerName ?? "-";
                    txtInvoiceNumber.Text = _order.InvoiceNumber ?? "-";
                    txtOrderDate.Text = _order.OrderDate.ToString("dd/MM/yyyy");
                    txtOrderStatus.Text = _order.OrderStatus ?? "-";
                    txtGlassType.Text = "شفاف"; // مؤقتاً - يجب إضافة هذه الحقول لنموذج البيانات
                    txtThickness.Text = "6مم"; // مؤقتاً - يجب إضافة هذه الحقول لنموذج البيانات

                    // ملخص التكاليف سيتم تحديثه في LoadCostDetails()

                    // تحميل تفاصيل الجداول
                    LoadGlassPanelsDetails();
                    LoadRequiredSizesDetails();
                    LoadServiceDetails();
                    LoadCostDetails();
                    CalculateWasteDetails();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الأمر: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل تفاصيل ألواح الزجاج
        /// </summary>
        private void LoadGlassPanelsDetails()
        {
            try
            {
                // تحميل البيانات الفعلية من قاعدة البيانات
                var glassPanels = _manufacturingService.GetGlassPanels(_order.Id);

                if (glassPanels != null && glassPanels.Any())
                {
                    dgGlassPanelsDetails.ItemsSource = glassPanels.Select((panel, index) => new
                    {
                        RowNumber = index + 1,
                        GlassType = panel.GlassType ?? "غير محدد",
                        Thickness = panel.Thickness ?? "غير محدد",
                        Length = panel.Length,
                        Width = panel.Width,
                        SquareMeters = panel.SquareMeters,
                        Quantity = panel.Quantity,
                        TotalSquareMeters = panel.TotalSquareMeters,
                        Price = 0m, // السعر غير محفوظ في GlassPanel
                        TotalValue = 0m, // القيمة الإجمالية غير محفوظة في GlassPanel
                        Notes = panel.Notes ?? ""
                    }).ToList();

                    // تحديث المجاميع
                    decimal totalMeters = glassPanels.Sum(g => g.TotalSquareMeters);
                    decimal totalValue = 0m; // سيتم حسابها لاحقاً

                    txtTotalGlassMeters.Text = $"إجمالي الأمتار: {totalMeters:F2} م²";
                    txtTotalGlassValue.Text = $"إجمالي القيمة: {totalValue:F2} ج.م";
                }
                else
                {
                    dgGlassPanelsDetails.ItemsSource = null;
                    txtTotalGlassMeters.Text = "إجمالي الأمتار: 0.00 م²";
                    txtTotalGlassValue.Text = "إجمالي القيمة: 0.00 ج.م";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل ألواح الزجاج: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل تفاصيل المقاسات المطلوبة
        /// </summary>
        private void LoadRequiredSizesDetails()
        {
            try
            {
                // تحميل البيانات الفعلية من قاعدة البيانات
                var requiredSizes = _manufacturingService.GetRequiredSizes(_order.Id);

                if (requiredSizes != null && requiredSizes.Any())
                {
                    dgRequiredSizesDetails.ItemsSource = requiredSizes.Select((size, index) => new
                    {
                        RowNumber = index + 1,
                        RefCode = size.RefCode ?? $"R{index + 1:000}",
                        Length = size.Length,
                        Width = size.Width,
                        SquareMeters = size.SquareMeters,
                        Quantity = size.Quantity,
                        TotalSquareMeters = size.TotalSquareMeters,
                        LinearMeters = ((size.Length / 1000) * 2) + ((size.Width / 1000) * 2), // حساب المتر الطولي
                        TotalLinearMeters = (((size.Length / 1000) * 2) + ((size.Width / 1000) * 2)) * size.Quantity,
                        Notes = ""
                    }).ToList();

                    // تحديث المجاميع
                    decimal totalRequiredMeters = requiredSizes.Sum(r => r.TotalSquareMeters);
                    decimal totalLinearMeters = requiredSizes.Sum(r => (((r.Length / 1000) * 2) + ((r.Width / 1000) * 2)) * r.Quantity);

                    txtTotalRequiredMetersDetails.Text = $"إجمالي الأمتار المطلوبة فعلياً: {totalRequiredMeters:F2} م²";
                    txtTotalLinearMetersDetails.Text = $"إجمالي المتر الطولي: {totalLinearMeters:F2} م";
                }
                else
                {
                    dgRequiredSizesDetails.ItemsSource = null;
                    txtTotalRequiredMetersDetails.Text = "إجمالي الأمتار المطلوبة فعلياً: 0.00 م²";
                    txtTotalLinearMetersDetails.Text = "إجمالي المتر الطولي: 0.00 م";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل المقاسات المطلوبة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل تفاصيل الخدمات
        /// </summary>
        private void LoadServiceDetails()
        {
            try
            {
                // تحميل الخدمات المطبقة من قاعدة البيانات
                var serviceCosts = _manufacturingService.GetServiceCosts(_order.Id);

                // تحديث عرض الخدمات المطبقة
                if (serviceCosts != null && serviceCosts.Any())
                {
                    // البحث عن خدمات محددة وتحديث العرض
                    var filmService = serviceCosts.FirstOrDefault(s => s.ServiceName?.Contains("فيلم") == true);
                    if (filmService != null)
                    {
                        txtFilmService.Text = "مطبق ✓";
                        txtFilmService.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
                    }

                    var doubleService = serviceCosts.FirstOrDefault(s => s.ServiceName?.Contains("دبل") == true);
                    if (doubleService != null)
                    {
                        txtDoubleService.Text = "مطبق ✓";
                        txtDoubleService.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
                    }

                    var printService = serviceCosts.FirstOrDefault(s => s.ServiceName?.Contains("طباعة") == true);
                    if (printService != null)
                    {
                        txtPrintService.Text = "مطبق ✓";
                        txtPrintService.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green);
                    }

                    // عد الخدمات الأخرى
                    var otherServices = serviceCosts.Where(s =>
                        !s.ServiceName?.Contains("فيلم") == true &&
                        !s.ServiceName?.Contains("دبل") == true &&
                        !s.ServiceName?.Contains("طباعة") == true).Count();

                    if (otherServices > 0)
                    {
                        txtOtherServices.Text = $"{otherServices} خدمة";
                        txtOtherServices.Foreground = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Blue);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل الخدمات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحميل تفاصيل التكاليف
        /// </summary>
        private void LoadCostDetails()
        {
            try
            {
                // تحميل تكاليف الخدمات
                var serviceCosts = _manufacturingService.GetServiceCosts(_order.Id);
                if (serviceCosts != null && serviceCosts.Any())
                {
                    dgServiceCostsDetails.ItemsSource = serviceCosts.Select((cost, index) => new
                    {
                        RowNumber = index + 1,
                        ServiceName = cost.ServiceName ?? "خدمة غير محددة",
                        Cost = cost.Value,
                        Notes = cost.Description ?? ""
                    }).ToList();

                    decimal totalServiceCosts = serviceCosts.Sum(c => c.Value);
                    txtTotalServiceCostsDetails.Text = $"إجمالي تكلفة الخدمات: {totalServiceCosts:F2} ج.م";
                }
                else
                {
                    dgServiceCostsDetails.ItemsSource = null;
                    txtTotalServiceCostsDetails.Text = "إجمالي تكلفة الخدمات: 0.00 ج.م";
                }

                // تحميل التكاليف الإضافية
                var additionalCosts = _manufacturingService.GetAdditionalCosts(_order.Id);
                if (additionalCosts != null && additionalCosts.Any())
                {
                    dgAdditionalCostsDetails.ItemsSource = additionalCosts.Select((cost, index) => new
                    {
                        RowNumber = index + 1,
                        Description = cost.Description ?? "تكلفة إضافية",
                        Value = cost.Value,
                        Notes = cost.Notes ?? ""
                    }).ToList();

                    decimal totalAdditionalCosts = additionalCosts.Sum(c => c.Value);
                    txtTotalAdditionalCostsDetails.Text = $"إجمالي التكاليف الإضافية: {totalAdditionalCosts:F2} ج.م";
                }
                else
                {
                    dgAdditionalCostsDetails.ItemsSource = null;
                    txtTotalAdditionalCostsDetails.Text = "إجمالي التكاليف الإضافية: 0.00 ج.م";
                }

                // تحديث ملخص التكاليف النهائي
                txtSummaryServiceCosts.Text = $"{_order.TotalServicesCost:F2} ج.م";
                txtSummaryAdditionalCosts.Text = $"{_order.TotalAdditionalCosts:F2} ج.م";
                txtSummaryGlassValue.Text = $"{_order.TotalGlassCosts:F2} ج.م";
                txtSummaryTotalCost.Text = $"{_order.TotalCost:F2} ج.م";
                txtSummaryTotalMeters.Text = $"{_order.TotalSquareMeters:F2} م²";
                txtSummaryPricePerMeter.Text = $"{_order.PricePerMeter:F2} ج.م/م²";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل تفاصيل التكاليف: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حساب هالك الزجاج والفيلم
        /// </summary>
        private void CalculateWasteDetails()
        {
            try
            {
                // حسابات تجريبية - يجب استبدالها بالبيانات الفعلية
                decimal totalGlassMeters = 1.6m; // من ألواح الزجاج
                decimal totalRequiredMeters = 1.426m; // من المقاسات المطلوبة
                decimal totalFilmMeters = 1.5m; // من خدمة الفيلم

                // حساب هالك الزجاج
                decimal glassWaste = totalGlassMeters - totalRequiredMeters;
                decimal glassWastePercentage = totalGlassMeters > 0 ? (glassWaste / totalGlassMeters) * 100 : 0;

                // حساب هالك الفيلم
                decimal filmWaste = totalFilmMeters - totalRequiredMeters;
                decimal filmWastePercentage = totalFilmMeters > 0 ? (filmWaste / totalFilmMeters) * 100 : 0;

                // تحديث النصوص (إذا كانت موجودة في XAML)
                // txtGlassWasteDetails.Text = $"{glassWaste:F2} م²";
                // txtGlassWastePercentageDetails.Text = $"{glassWastePercentage:F2}%";
                // txtFilmWasteDetails.Text = $"{filmWaste:F2} م²";
                // txtFilmWastePercentageDetails.Text = $"{filmWastePercentage:F2}%";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حساب الهالك: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// طباعة التفاصيل (للفني - بدون القيم المالية)
        /// </summary>
        private void BtnPrintDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إخفاء العناصر المالية مؤقتاً للطباعة
                HideFinancialElements();

                // إنشاء مربع حوار الطباعة
                var printDialog = new PrintDialog();
                if (printDialog.ShowDialog() == true)
                {
                    // طباعة الصفحة
                    printDialog.PrintVisual(this, $"تفاصيل أمر التصنيع - {_order.OrderNumber} (للفني)");

                    MessageBox.Show("تم إرسال المستند للطباعة بنجاح", "نجحت الطباعة",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }

                // إظهار العناصر المالية مرة أخرى
                ShowFinancialElements();
            }
            catch (Exception ex)
            {
                ShowFinancialElements(); // التأكد من إظهار العناصر في حالة الخطأ
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// حفظ PDF (للمحاسب - مع جميع التفاصيل المالية)
        /// </summary>
        private void BtnSavePDFDetails_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // إنشاء مربع حوار حفظ الملف
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Filter = "PDF Files (*.pdf)|*.pdf",
                    DefaultExt = "pdf",
                    FileName = $"تفاصيل_أمر_التصنيع_{_order.OrderNumber}_{DateTime.Now:yyyyMMdd_HHmmss}.pdf"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    // إنشاء تقرير PDF شامل
                    CreateComprehensivePDFReport(saveDialog.FileName);

                    MessageBox.Show($"تم حفظ ملف PDF بنجاح في:\n{saveDialog.FileName}", "نجح الحفظ",
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إخفاء العناصر المالية للطباعة الفنية
        /// </summary>
        private void HideFinancialElements()
        {
            try
            {
                // إخفاء أعمدة الأسعار في جدول ألواح الزجاج
                if (dgGlassPanelsDetails.Columns.Count > 8)
                {
                    dgGlassPanelsDetails.Columns[8].Visibility = Visibility.Collapsed; // السعر
                    dgGlassPanelsDetails.Columns[9].Visibility = Visibility.Collapsed; // إجمالي القيمة
                }

                // إخفاء مجاميع القيم المالية
                txtTotalGlassValue.Visibility = Visibility.Collapsed;

                // إخفاء جداول التكاليف
                dgServiceCostsDetails.Visibility = Visibility.Collapsed;
                dgAdditionalCostsDetails.Visibility = Visibility.Collapsed;
                txtTotalServiceCostsDetails.Visibility = Visibility.Collapsed;
                txtTotalAdditionalCostsDetails.Visibility = Visibility.Collapsed;

                // إخفاء ملخص التكاليف المالي
                txtSummaryServiceCosts.Visibility = Visibility.Collapsed;
                txtSummaryAdditionalCosts.Visibility = Visibility.Collapsed;
                txtSummaryGlassValue.Visibility = Visibility.Collapsed;
                txtSummaryTotalCost.Visibility = Visibility.Collapsed;
                txtSummaryPricePerMeter.Visibility = Visibility.Collapsed;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error hiding financial elements: {ex.Message}");
            }
        }

        /// <summary>
        /// إظهار العناصر المالية بعد الطباعة
        /// </summary>
        private void ShowFinancialElements()
        {
            try
            {
                // إظهار أعمدة الأسعار في جدول ألواح الزجاج
                if (dgGlassPanelsDetails.Columns.Count > 8)
                {
                    dgGlassPanelsDetails.Columns[8].Visibility = Visibility.Visible; // السعر
                    dgGlassPanelsDetails.Columns[9].Visibility = Visibility.Visible; // إجمالي القيمة
                }

                // إظهار مجاميع القيم المالية
                txtTotalGlassValue.Visibility = Visibility.Visible;

                // إظهار جداول التكاليف
                dgServiceCostsDetails.Visibility = Visibility.Visible;
                dgAdditionalCostsDetails.Visibility = Visibility.Visible;
                txtTotalServiceCostsDetails.Visibility = Visibility.Visible;
                txtTotalAdditionalCostsDetails.Visibility = Visibility.Visible;

                // إظهار ملخص التكاليف المالي
                txtSummaryServiceCosts.Visibility = Visibility.Visible;
                txtSummaryAdditionalCosts.Visibility = Visibility.Visible;
                txtSummaryGlassValue.Visibility = Visibility.Visible;
                txtSummaryTotalCost.Visibility = Visibility.Visible;
                txtSummaryPricePerMeter.Visibility = Visibility.Visible;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing financial elements: {ex.Message}");
            }
        }

        /// <summary>
        /// إغلاق الصفحة والعودة لقائمة الأوامر
        /// </summary>
        private void BtnClose_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // العودة لصفحة قائمة الأوامر
                var ordersListView = new ManufacturingOrdersListView();
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.ShowView(ordersListView, "عرض أوامر التصنيع");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إغلاق الصفحة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تعديل الأمر
        /// </summary>
        private void BtnEditOrder_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editView = new ManufacturingOrderView(_order);
                var mainWindow = Application.Current.MainWindow as MainWindow;
                mainWindow?.ShowView(editView, $"تعديل أمر التصنيع - {_order.OrderNumber}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح صفحة التعديل: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء تقرير PDF شامل
        /// </summary>
        private void CreateComprehensivePDFReport(string fileName)
        {
            try
            {
                using (var document = new iTextSharp.text.Document(iTextSharp.text.PageSize.A4))
                {
                    using (var writer = iTextSharp.text.pdf.PdfWriter.GetInstance(document, new FileStream(fileName, FileMode.Create)))
                    {
                        document.Open();

                        // إضافة الخط العربي
                        var arabicFont = iTextSharp.text.pdf.BaseFont.CreateFont("c:\\windows\\fonts\\arial.ttf",
                            iTextSharp.text.pdf.BaseFont.IDENTITY_H, iTextSharp.text.pdf.BaseFont.EMBEDDED);
                        var titleFont = new iTextSharp.text.Font(arabicFont, 18, iTextSharp.text.Font.BOLD);
                        var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
                        var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);

                        // العنوان الرئيسي
                        var title = new iTextSharp.text.Paragraph($"تفاصيل أمر التصنيع - {_order.OrderNumber}", titleFont);
                        title.Alignment = iTextSharp.text.Element.ALIGN_CENTER;
                        title.SpacingAfter = 20f;
                        document.Add(title);

                        // معلومات الأمر الأساسية
                        AddBasicOrderInfo(document, headerFont, normalFont);

                        // ألواح الزجاج
                        AddGlassPanelsTable(document, headerFont, normalFont);

                        // المقاسات المطلوبة
                        AddRequiredSizesTable(document, headerFont, normalFont);

                        // تكاليف الخدمات
                        AddServiceCostsTable(document, headerFont, normalFont);

                        // التكاليف الإضافية
                        AddAdditionalCostsTable(document, headerFont, normalFont);

                        // ملخص التكاليف النهائي
                        AddCostSummary(document, headerFont, normalFont);

                        document.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء تقرير PDF: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إضافة معلومات الأمر الأساسية للتقرير
        /// </summary>
        private void AddBasicOrderInfo(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var infoTable = new iTextSharp.text.pdf.PdfPTable(2);
            infoTable.WidthPercentage = 100;
            infoTable.SpacingAfter = 15f;

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("اسم العميل:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_order.CustomerName, normalFont)) { Border = 0 });

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("رقم الفاتورة:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_order.InvoiceNumber ?? "-", normalFont)) { Border = 0 });

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("التاريخ:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_order.OrderDate.ToString("dd/MM/yyyy"), normalFont)) { Border = 0 });

            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("حالة الطلب:", normalFont)) { Border = 0 });
            infoTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(_order.OrderStatus ?? "-", normalFont)) { Border = 0 });

            document.Add(infoTable);
        }

        /// <summary>
        /// إضافة جدول ألواح الزجاج للتقرير
        /// </summary>
        private void AddGlassPanelsTable(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var glassPanels = _manufacturingService.GetGlassPanels(_order.Id);
            if (glassPanels.Any())
            {
                var header = new iTextSharp.text.Paragraph("ألواح الزجاج المستخدمة", headerFont);
                header.SpacingBefore = 10f;
                header.SpacingAfter = 10f;
                document.Add(header);

                var table = new iTextSharp.text.pdf.PdfPTable(7);
                table.WidthPercentage = 100;
                table.SpacingAfter = 15f;

                // رؤوس الأعمدة
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("ت", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("نوع الزجاج", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("السمك", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الطول", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العرض", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العدد", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي م²", headerFont)));

                // البيانات
                for (int i = 0; i < glassPanels.Count; i++)
                {
                    var panel = glassPanels[i];
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase((i + 1).ToString(), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.GlassType, normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Thickness, normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Length.ToString("F0"), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Width.ToString("F0"), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.Quantity.ToString(), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(panel.TotalSquareMeters.ToString("F2"), normalFont)));
                }

                document.Add(table);
            }
        }

        /// <summary>
        /// إضافة جدول المقاسات المطلوبة للتقرير
        /// </summary>
        private void AddRequiredSizesTable(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var requiredSizes = _manufacturingService.GetRequiredSizes(_order.Id);
            if (requiredSizes.Any())
            {
                var header = new iTextSharp.text.Paragraph("المقاسات المطلوبة للقص", headerFont);
                header.SpacingBefore = 10f;
                header.SpacingAfter = 10f;
                document.Add(header);

                var table = new iTextSharp.text.pdf.PdfPTable(7);
                table.WidthPercentage = 100;
                table.SpacingAfter = 15f;

                // رؤوس الأعمدة
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("ت", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("كود المرجع", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الطول", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العرض", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("العدد", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي م²", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("المتر الطولي", headerFont)));

                // البيانات
                for (int i = 0; i < requiredSizes.Count; i++)
                {
                    var size = requiredSizes[i];
                    var linearMeters = ((size.Length / 1000) * 2) + ((size.Width / 1000) * 2);

                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase((i + 1).ToString(), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.RefCode, normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.Length.ToString("F0"), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.Width.ToString("F0"), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.Quantity.ToString(), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(size.TotalSquareMeters.ToString("F2"), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase((linearMeters * size.Quantity).ToString("F2"), normalFont)));
                }

                document.Add(table);
            }
        }

        /// <summary>
        /// إضافة جدول تكاليف الخدمات للتقرير
        /// </summary>
        private void AddServiceCostsTable(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var serviceCosts = _manufacturingService.GetServiceCosts(_order.Id);
            if (serviceCosts.Any())
            {
                var header = new iTextSharp.text.Paragraph("تكاليف الخدمات", headerFont);
                header.SpacingBefore = 10f;
                header.SpacingAfter = 10f;
                document.Add(header);

                var table = new iTextSharp.text.pdf.PdfPTable(4);
                table.WidthPercentage = 100;
                table.SpacingAfter = 15f;

                // رؤوس الأعمدة
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("ت", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("اسم الخدمة", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الوصف", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("القيمة", headerFont)));

                // البيانات
                for (int i = 0; i < serviceCosts.Count; i++)
                {
                    var cost = serviceCosts[i];
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase((i + 1).ToString(), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.ServiceName, normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Description, normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Value.ToString("F2") + " ج.م", normalFont)));
                }

                document.Add(table);
            }
        }

        /// <summary>
        /// إضافة جدول التكاليف الإضافية للتقرير
        /// </summary>
        private void AddAdditionalCostsTable(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var additionalCosts = _manufacturingService.GetAdditionalCosts(_order.Id);
            if (additionalCosts.Any())
            {
                var header = new iTextSharp.text.Paragraph("التكاليف الإضافية", headerFont);
                header.SpacingBefore = 10f;
                header.SpacingAfter = 10f;
                document.Add(header);

                var table = new iTextSharp.text.pdf.PdfPTable(4);
                table.WidthPercentage = 100;
                table.SpacingAfter = 15f;

                // رؤوس الأعمدة
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("ت", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("الوصف", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("القيمة", headerFont)));
                table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("ملاحظات", headerFont)));

                // البيانات
                for (int i = 0; i < additionalCosts.Count; i++)
                {
                    var cost = additionalCosts[i];
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase((i + 1).ToString(), normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Description, normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Value.ToString("F2") + " ج.م", normalFont)));
                    table.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(cost.Notes, normalFont)));
                }

                document.Add(table);
            }
        }

        /// <summary>
        /// إضافة ملخص التكاليف للتقرير
        /// </summary>
        private void AddCostSummary(iTextSharp.text.Document document, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            var header = new iTextSharp.text.Paragraph("ملخص التكاليف النهائي", headerFont);
            header.SpacingBefore = 15f;
            header.SpacingAfter = 10f;
            document.Add(header);

            var summaryTable = new iTextSharp.text.pdf.PdfPTable(2);
            summaryTable.WidthPercentage = 100;

            var serviceCosts = _manufacturingService.GetServiceCosts(_order.Id);
            var additionalCosts = _manufacturingService.GetAdditionalCosts(_order.Id);
            var requiredSizes = _manufacturingService.GetRequiredSizes(_order.Id);

            decimal totalServiceCosts = serviceCosts.Sum(c => c.Value);
            decimal totalAdditionalCosts = additionalCosts.Sum(c => c.Value);
            decimal totalMeters = requiredSizes.Sum(s => s.TotalSquareMeters);
            decimal totalCost = totalServiceCosts + totalAdditionalCosts;
            decimal pricePerMeter = totalMeters > 0 ? totalCost / totalMeters : 0;

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي تكلفة الخدمات:", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalServiceCosts.ToString("F2") + " ج.م", normalFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي التكاليف الإضافية:", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalAdditionalCosts.ToString("F2") + " ج.م", normalFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("المجموع الكلي:", headerFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalCost.ToString("F2") + " ج.م", headerFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("إجمالي الأمتار:", normalFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(totalMeters.ToString("F2") + " م²", normalFont)) { Border = 0 });

            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase("سعر المتر:", headerFont)) { Border = 0 });
            summaryTable.AddCell(new iTextSharp.text.pdf.PdfPCell(new iTextSharp.text.Phrase(pricePerMeter.ToString("F2") + " ج.م/م²", headerFont)) { Border = 0 });

            document.Add(summaryTable);
        }


    }
}
