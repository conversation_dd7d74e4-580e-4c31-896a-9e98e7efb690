﻿#pragma checksum "..\..\..\..\Views\ArchiveView.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "EE44709A36B9FC63EAF0E165F13AD5FBB65D2A2A"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using GlassFactoryAccounting.Views;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace GlassFactoryAccounting.Views {
    
    
    /// <summary>
    /// ArchiveView
    /// </summary>
    public partial class ArchiveView : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector, System.Windows.Markup.IStyleConnector {
        
        
        #line 69 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearchInvoice;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox TxtSearchCustomer;
        
        #line default
        #line hidden
        
        
        #line 81 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateFrom;
        
        #line default
        #line hidden
        
        
        #line 86 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DatePicker DateTo;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnClearFilters;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalInvoices;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalSales;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtAverageInvoice;
        
        #line default
        #line hidden
        
        
        #line 143 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTopService;
        
        #line default
        #line hidden
        
        
        #line 150 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TxtTotalCustomers;
        
        #line default
        #line hidden
        
        
        #line 169 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnRefresh;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BtnExportExcel;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\..\Views\ArchiveView.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid InvoicesDataGrid;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/GlassFactoryAccounting;V1.0.0.0;component/views/archiveview.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\ArchiveView.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TxtSearchInvoice = ((System.Windows.Controls.TextBox)(target));
            
            #line 70 "..\..\..\..\Views\ArchiveView.xaml"
            this.TxtSearchInvoice.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearchInvoice_TextChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.TxtSearchCustomer = ((System.Windows.Controls.TextBox)(target));
            
            #line 76 "..\..\..\..\Views\ArchiveView.xaml"
            this.TxtSearchCustomer.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.TxtSearchCustomer_TextChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.DateFrom = ((System.Windows.Controls.DatePicker)(target));
            
            #line 81 "..\..\..\..\Views\ArchiveView.xaml"
            this.DateFrom.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateFrom_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 4:
            this.DateTo = ((System.Windows.Controls.DatePicker)(target));
            
            #line 86 "..\..\..\..\Views\ArchiveView.xaml"
            this.DateTo.SelectedDateChanged += new System.EventHandler<System.Windows.Controls.SelectionChangedEventArgs>(this.DateTo_SelectedDateChanged);
            
            #line default
            #line hidden
            return;
            case 5:
            this.BtnClearFilters = ((System.Windows.Controls.Button)(target));
            
            #line 93 "..\..\..\..\Views\ArchiveView.xaml"
            this.BtnClearFilters.Click += new System.Windows.RoutedEventHandler(this.BtnClearFilters_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TxtTotalInvoices = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.TxtTotalSales = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.TxtAverageInvoice = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.TxtTopService = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.TxtTotalCustomers = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.BtnRefresh = ((System.Windows.Controls.Button)(target));
            
            #line 172 "..\..\..\..\Views\ArchiveView.xaml"
            this.BtnRefresh.Click += new System.Windows.RoutedEventHandler(this.BtnRefresh_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.BtnExportExcel = ((System.Windows.Controls.Button)(target));
            
            #line 177 "..\..\..\..\Views\ArchiveView.xaml"
            this.BtnExportExcel.Click += new System.Windows.RoutedEventHandler(this.BtnExportExcel_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.InvoicesDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            }
            this._contentLoaded = true;
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "10.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        void System.Windows.Markup.IStyleConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 14:
            
            #line 209 "..\..\..\..\Views\ArchiveView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnViewInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 15:
            
            #line 214 "..\..\..\..\Views\ArchiveView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnPrintInvoice_Click);
            
            #line default
            #line hidden
            break;
            case 16:
            
            #line 219 "..\..\..\..\Views\ArchiveView.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.BtnDeleteInvoice_Click);
            
            #line default
            #line hidden
            break;
            }
        }
    }
}

