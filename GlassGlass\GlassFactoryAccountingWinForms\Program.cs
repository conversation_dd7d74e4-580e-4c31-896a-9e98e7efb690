using System;
using System.Windows.Forms;
using GlassFactoryAccountingWinForms.Forms;
using GlassFactoryAccountingWinForms.Services;

namespace GlassFactoryAccountingWinForms
{
    /// <summary>
    /// نقطة دخول التطبيق الرئيسية
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            // تهيئة قاعدة البيانات
            try
            {
                var dbService = new DatabaseService();
                dbService.InitializeDatabase();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            
            // تشغيل النموذج الرئيسي
            Application.Run(new MainForm());
        }
    }
}
