using System.ComponentModel.DataAnnotations;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج فئة المنتج
    /// </summary>
    public class ProductCategory : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string CategoryCode { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string CategoryName { get; set; } = string.Empty;

        [MaxLength(500)]
        public string? Description { get; set; }

        public int? ParentCategoryId { get; set; }

        public bool IsActive { get; set; } = true;

        [MaxLength(200)]
        public string? ImagePath { get; set; }

        public int SortOrder { get; set; } = 0;

        // Glass-specific categories
        [MaxLength(50)]
        public string CategoryType { get; set; } = "عام"; // زجاج خام، منتجات نهائية، خدمات، مواد خام

        // Navigation Properties
        public virtual ProductCategory? ParentCategory { get; set; }
        public virtual ICollection<ProductCategory> SubCategories { get; set; } = new List<ProductCategory>();
        public virtual ICollection<Product> Products { get; set; } = new List<Product>();
    }
}
