using System;
using System.Data.SQLite;
using System.IO;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting
{
    /// <summary>
    /// أداة تشخيص قاعدة البيانات لحل مشاكل المسؤولين
    /// </summary>
    public class DatabaseDiagnostic
    {
        private readonly DatabaseContext _context;

        public DatabaseDiagnostic()
        {
            _context = new DatabaseContext();
        }

        /// <summary>
        /// تشخيص شامل لقاعدة البيانات
        /// </summary>
        public void RunFullDiagnostic()
        {
            Console.WriteLine("=== تشخيص قاعدة البيانات ===");
            Console.WriteLine();

            // 1. فحص وجود قاعدة البيانات
            CheckDatabaseExists();

            // 2. فحص بنية جدول ResponsiblePersons
            CheckResponsiblePersonsTableStructure();

            // 3. فحص البيانات الموجودة
            CheckExistingData();

            // 4. اختبار إضافة مسؤول
            TestAddResponsiblePerson();

            // 5. إعادة إنشاء الجدول إذا لزم الأمر
            RecreateTableIfNeeded();

            Console.WriteLine();
            Console.WriteLine("=== انتهى التشخيص ===");
        }

        private void CheckDatabaseExists()
        {
            try
            {
                var dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
                var dbPath = Path.Combine(dataFolder, "GlassFactory.db");

                Console.WriteLine($"1. فحص وجود قاعدة البيانات:");
                Console.WriteLine($"   المسار: {dbPath}");
                Console.WriteLine($"   موجودة: {File.Exists(dbPath)}");

                if (File.Exists(dbPath))
                {
                    var fileInfo = new FileInfo(dbPath);
                    Console.WriteLine($"   الحجم: {fileInfo.Length} بايت");
                    Console.WriteLine($"   تاريخ التعديل: {fileInfo.LastWriteTime}");
                }
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   خطأ: {ex.Message}");
            }
        }

        private void CheckResponsiblePersonsTableStructure()
        {
            try
            {
                Console.WriteLine("2. فحص بنية جدول ResponsiblePersons:");

                using var connection = _context.GetConnection();
                connection.Open();

                // فحص وجود الجدول
                var checkTableQuery = @"
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='ResponsiblePersons'";

                using var checkCommand = new SQLiteCommand(checkTableQuery, connection);
                var tableName = checkCommand.ExecuteScalar()?.ToString();

                if (string.IsNullOrEmpty(tableName))
                {
                    Console.WriteLine("   ❌ الجدول غير موجود!");
                    return;
                }

                Console.WriteLine("   ✅ الجدول موجود");

                // فحص بنية الجدول
                var structureQuery = "PRAGMA table_info(ResponsiblePersons)";
                using var structureCommand = new SQLiteCommand(structureQuery, connection);
                using var reader = structureCommand.ExecuteReader();

                Console.WriteLine("   الحقول:");
                while (reader.Read())
                {
                    var columnName = reader["name"].ToString();
                    var columnType = reader["type"].ToString();
                    var notNull = Convert.ToInt32(reader["notnull"]) == 1;
                    var defaultValue = reader["dflt_value"]?.ToString();

                    Console.WriteLine($"     - {columnName}: {columnType}" +
                        (notNull ? " NOT NULL" : "") +
                        (!string.IsNullOrEmpty(defaultValue) ? $" DEFAULT {defaultValue}" : ""));
                }
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   خطأ: {ex.Message}");
            }
        }

        private void CheckExistingData()
        {
            try
            {
                Console.WriteLine("3. فحص البيانات الموجودة:");

                using var connection = _context.GetConnection();
                connection.Open();

                // عدد السجلات الإجمالي
                var countQuery = "SELECT COUNT(*) FROM ResponsiblePersons";
                using var countCommand = new SQLiteCommand(countQuery, connection);
                var totalCount = Convert.ToInt32(countCommand.ExecuteScalar());

                Console.WriteLine($"   إجمالي السجلات: {totalCount}");

                // عدد السجلات النشطة
                var activeCountQuery = "SELECT COUNT(*) FROM ResponsiblePersons WHERE IsActive = 1";
                using var activeCountCommand = new SQLiteCommand(activeCountQuery, connection);
                var activeCount = Convert.ToInt32(activeCountCommand.ExecuteScalar());

                Console.WriteLine($"   السجلات النشطة: {activeCount}");

                // عرض البيانات
                if (totalCount > 0)
                {
                    var dataQuery = "SELECT Id, Name, Position, Department, IsActive FROM ResponsiblePersons ORDER BY Id";
                    using var dataCommand = new SQLiteCommand(dataQuery, connection);
                    using var reader = dataCommand.ExecuteReader();

                    Console.WriteLine("   البيانات:");
                    while (reader.Read())
                    {
                        var id = reader["Id"];
                        var name = reader["Name"];
                        var position = reader["Position"];
                        var department = reader["Department"];
                        var isActive = Convert.ToInt32(reader["IsActive"]) == 1;

                        Console.WriteLine($"     {id}: {name} - {position} - {department} - {(isActive ? "نشط" : "غير نشط")}");
                    }
                }
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   خطأ: {ex.Message}");
            }
        }

        private void TestAddResponsiblePerson()
        {
            try
            {
                Console.WriteLine("4. اختبار إضافة مسؤول:");

                var companyService = new CompanyManagementService();
                var testPerson = new ResponsiblePerson
                {
                    Name = $"مسؤول تجريبي {DateTime.Now:HHmmss}",
                    Position = "منصب تجريبي",
                    Department = "قسم تجريبي",
                    Phone = "*********",
                    CreatedBy = "نظام التشخيص",
                    CreatedDate = DateTime.Now
                };

                Console.WriteLine($"   محاولة إضافة: {testPerson.Name}");

                var result = companyService.AddResponsiblePerson(testPerson);

                if (result)
                {
                    Console.WriteLine("   ✅ تم إضافة المسؤول بنجاح!");
                    
                    // التحقق من الإضافة
                    var persons = companyService.GetAllResponsiblePersons();
                    var addedPerson = persons.Find(p => p.Name == testPerson.Name);
                    
                    if (addedPerson != null)
                    {
                        Console.WriteLine($"   ✅ تم العثور على المسؤول في قاعدة البيانات (ID: {addedPerson.Id})");
                    }
                    else
                    {
                        Console.WriteLine("   ❌ لم يتم العثور على المسؤول في قاعدة البيانات!");
                    }
                }
                else
                {
                    Console.WriteLine("   ❌ فشل في إضافة المسؤول!");
                }
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   خطأ: {ex.Message}");
                Console.WriteLine($"   تفاصيل الخطأ: {ex.StackTrace}");
            }
        }

        private void RecreateTableIfNeeded()
        {
            try
            {
                Console.WriteLine("5. إعادة إنشاء الجدول (إذا لزم الأمر):");

                using var connection = _context.GetConnection();
                connection.Open();

                // حفظ البيانات الموجودة
                var backupData = new List<dynamic>();
                try
                {
                    var backupQuery = "SELECT * FROM ResponsiblePersons";
                    using var backupCommand = new SQLiteCommand(backupQuery, connection);
                    using var reader = backupCommand.ExecuteReader();

                    while (reader.Read())
                    {
                        backupData.Add(new
                        {
                            Id = reader["Id"],
                            Name = reader["Name"]?.ToString(),
                            Position = reader["Position"]?.ToString(),
                            Department = reader["Department"]?.ToString(),
                            Phone = reader["Phone"]?.ToString(),
                            IsActive = reader["IsActive"],
                            CreatedDate = reader["CreatedDate"]?.ToString(),
                            CreatedBy = reader["CreatedBy"]?.ToString(),
                            ModifiedDate = reader["ModifiedDate"]?.ToString(),
                            ModifiedBy = reader["ModifiedBy"]?.ToString()
                        });
                    }
                    Console.WriteLine($"   تم حفظ {backupData.Count} سجل");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   تحذير: لم يتم حفظ البيانات - {ex.Message}");
                }

                // حذف الجدول القديم
                var dropQuery = "DROP TABLE IF EXISTS ResponsiblePersons";
                using var dropCommand = new SQLiteCommand(dropQuery, connection);
                dropCommand.ExecuteNonQuery();
                Console.WriteLine("   تم حذف الجدول القديم");

                // إنشاء الجدول الجديد
                var createQuery = @"
                    CREATE TABLE ResponsiblePersons (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Name TEXT NOT NULL UNIQUE,
                        Position TEXT,
                        Department TEXT,
                        Phone TEXT,
                        IsActive INTEGER NOT NULL DEFAULT 1,
                        CreatedDate TEXT NOT NULL,
                        CreatedBy TEXT,
                        ModifiedDate TEXT,
                        ModifiedBy TEXT
                    )";

                using var createCommand = new SQLiteCommand(createQuery, connection);
                createCommand.ExecuteNonQuery();
                Console.WriteLine("   تم إنشاء الجدول الجديد");

                // استعادة البيانات
                foreach (var record in backupData)
                {
                    try
                    {
                        var insertQuery = @"
                            INSERT INTO ResponsiblePersons (Name, Position, Department, Phone, IsActive, CreatedDate, CreatedBy, ModifiedDate, ModifiedBy)
                            VALUES (@Name, @Position, @Department, @Phone, @IsActive, @CreatedDate, @CreatedBy, @ModifiedDate, @ModifiedBy)";

                        using var insertCommand = new SQLiteCommand(insertQuery, connection);
                        insertCommand.Parameters.AddWithValue("@Name", record.Name ?? "");
                        insertCommand.Parameters.AddWithValue("@Position", record.Position ?? "");
                        insertCommand.Parameters.AddWithValue("@Department", record.Department ?? "");
                        insertCommand.Parameters.AddWithValue("@Phone", record.Phone ?? "");
                        insertCommand.Parameters.AddWithValue("@IsActive", record.IsActive ?? 1);
                        insertCommand.Parameters.AddWithValue("@CreatedDate", record.CreatedDate ?? DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                        insertCommand.Parameters.AddWithValue("@CreatedBy", record.CreatedBy ?? "");
                        insertCommand.Parameters.AddWithValue("@ModifiedDate", record.ModifiedDate ?? (object)DBNull.Value);
                        insertCommand.Parameters.AddWithValue("@ModifiedBy", record.ModifiedBy ?? (object)DBNull.Value);

                        insertCommand.ExecuteNonQuery();
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   تحذير: فشل في استعادة السجل {record.Name} - {ex.Message}");
                    }
                }

                Console.WriteLine($"   تم استعادة البيانات");
                Console.WriteLine("   ✅ تم إعادة إنشاء الجدول بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   خطأ: {ex.Message}");
            }
        }
    }
}
