<Window x:Class="GlassFactoryAccounting.Views.CustomersManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:GlassFactoryAccounting.Views"
        mc:Ignorable="d"
        Title="إدارة العملاء" Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        FlowDirection="RightToLeft"
        FontFamily="Segoe UI"
        FontSize="14">
    
    <Window.Resources>
        <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
        <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
        <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
        <SolidColorBrush x:Key="CardBrush" Color="#FFFFFF"/>
        
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardBrush}"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="5"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F5F5F5">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان -->
        <Border Grid.Row="0" Style="{StaticResource CardStyle}">
            <TextBlock Text="👥 إدارة العملاء" FontSize="20" FontWeight="Bold" 
                     Foreground="{StaticResource PrimaryBrush}" HorizontalAlignment="Center"/>
        </Border>
        
        <!-- إضافة عميل جديد -->
        <Border Grid.Row="1" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="إضافة عميل جديد:" FontSize="16" FontWeight="Bold" 
                         Margin="0,0,0,10" Foreground="{StaticResource PrimaryBrush}"/>
                
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0" Margin="0,0,10,0">
                        <TextBlock Text="اسم العميل:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCustomerName" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" Margin="5,0">
                        <TextBlock Text="رقم الهاتف:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCustomerPhone" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="2" Margin="10,0,10,0">
                        <TextBlock Text="العنوان:" FontWeight="Bold" Margin="0,0,0,5"/>
                        <TextBox x:Name="TxtCustomerAddress" Height="35" VerticalContentAlignment="Center"/>
                    </StackPanel>
                    
                    <Button Grid.Column="3" x:Name="BtnAddCustomer" Content="إضافة" 
                            Background="{StaticResource SuccessBrush}" Foreground="White"
                            Padding="15,8" FontWeight="Bold" VerticalAlignment="Bottom"
                            BorderThickness="0" Cursor="Hand" Click="BtnAddCustomer_Click"/>
                </Grid>
            </Grid>
        </Border>
        
        <!-- قائمة العملاء -->
        <Border Grid.Row="2" Style="{StaticResource CardStyle}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <TextBlock Grid.Row="0" Text="قائمة العملاء:" FontSize="16" FontWeight="Bold" 
                         Margin="0,0,0,10" Foreground="{StaticResource PrimaryBrush}"/>
                
                <DataGrid Grid.Row="1" x:Name="CustomersDataGrid"
                          AutoGenerateColumns="False"
                          CanUserAddRows="False"
                          CanUserDeleteRows="False"
                          IsReadOnly="True"
                          GridLinesVisibility="Horizontal"
                          HeadersVisibility="Column"
                          SelectionMode="Single"
                          AlternatingRowBackground="#F9F9F9"
                          RowHeight="40"
                          FontSize="14">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم العميل" Binding="{Binding Name}" Width="200"/>
                        <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding Phone}" Width="150"/>
                        <DataGridTextColumn Header="العنوان" Binding="{Binding Address}" Width="200"/>
                        <DataGridTextColumn Header="البريد الإلكتروني" Binding="{Binding Email}" Width="150"/>
                        <DataGridTextColumn Header="الرصيد" Binding="{Binding Balance, StringFormat='{}{0:N2} ج.م'}" Width="100"/>
                        
                        <DataGridTemplateColumn Header="الإجراءات" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Button Content="حذف" Background="{StaticResource DangerBrush}" 
                                            Foreground="White" Padding="8,4"
                                            BorderThickness="0" Cursor="Hand"
                                            Click="BtnDeleteCustomer_Click"/>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>
        
        <!-- أزرار الإغلاق -->
        <Border Grid.Row="3" Style="{StaticResource CardStyle}">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button x:Name="BtnSave" Content="💾 حفظ وإغلاق" 
                        Background="{StaticResource SuccessBrush}" Foreground="White"
                        Padding="20,10" Margin="0,0,10,0" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnSave_Click"/>
                
                <Button x:Name="BtnCancel" Content="❌ إلغاء" 
                        Background="{StaticResource DangerBrush}" Foreground="White"
                        Padding="20,10" FontSize="16" FontWeight="Bold"
                        BorderThickness="0" Cursor="Hand" Click="BtnCancel_Click"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
