using System.Collections.ObjectModel;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Services;
using System.Linq;

namespace GlassFactoryAccounting.Views;

/// <summary>
/// صفحة تقرير خدمات المبيعات
/// </summary>
public partial class ServicesReportView : UserControl
{
    private readonly MainWindow _mainWindow;
    private readonly SalesService _salesService;
    private ObservableCollection<ServiceAnalysis> _servicesAnalysis;

    public ServicesReportView(MainWindow mainWindow)
    {
        InitializeComponent();
        _mainWindow = mainWindow;
        _salesService = new SalesService();
        _servicesAnalysis = new ObservableCollection<ServiceAnalysis>();
        
        ServicesDataGrid.ItemsSource = _servicesAnalysis;
        
        LoadServicesData();
    }

    private void LoadServicesData()
    {
        try
        {
            Task.Run(async () =>
            {
                try
                {
                    var sales = await _salesService.GetAllSalesAsync();
                    
                    // تحليل الخدمات
                    var servicesData = sales
                        .SelectMany(s => s.SaleItems)
                        .GroupBy(item => item.Service)
                        .Select(g => new ServiceAnalysis
                        {
                            ServiceName = g.Key,
                            UsageCount = g.Count(),
                            TotalQuantity = g.Sum(item => item.TotalArea),
                            TotalValue = g.Sum(item => item.TotalPrice),
                            AveragePrice = g.Average(item => item.UnitPrice)
                        })
                        .OrderByDescending(s => s.UsageCount)
                        .ToList();

                    // حساب النسب المئوية
                    var totalValue = servicesData.Sum(s => s.TotalValue);
                    foreach (var service in servicesData)
                    {
                        service.Percentage = totalValue > 0 ? (service.TotalValue / totalValue) * 100 : 0;
                    }

                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        _servicesAnalysis.Clear();
                        foreach (var service in servicesData)
                        {
                            _servicesAnalysis.Add(service);
                        }
                        
                        UpdateStatistics(servicesData, totalValue);
                    });
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                    {
                        MessageBox.Show($"خطأ في تحميل بيانات الخدمات: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    });
                }
            });
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحميل بيانات الخدمات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void UpdateStatistics(List<ServiceAnalysis> servicesData, decimal totalValue)
    {
        try
        {
            TxtTotalServices.Text = servicesData.Count.ToString();
            TxtTotalValue.Text = $"{totalValue:N2} ج.م";
            
            if (servicesData.Count > 0)
            {
                TxtTopService.Text = servicesData.First().ServiceName;
                TxtAverageValue.Text = $"{servicesData.Average(s => s.TotalValue):N2} ج.م";
            }
            else
            {
                TxtTopService.Text = "لا توجد خدمات";
                TxtAverageValue.Text = "0.00 ج.م";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث الإحصائيات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnBack_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            _mainWindow.GoBack();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في العودة: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void BtnRefreshData_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            LoadServicesData();
            MessageBox.Show("تم تحديث بيانات الخدمات بنجاح", "تحديث", 
                MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في تحديث البيانات: {ex.Message}", "خطأ", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}

/// <summary>
/// نموذج تحليل الخدمات
/// </summary>
public class ServiceAnalysis
{
    public string ServiceName { get; set; } = "";
    public int UsageCount { get; set; }
    public decimal TotalQuantity { get; set; }
    public decimal TotalValue { get; set; }
    public decimal AveragePrice { get; set; }
    public decimal Percentage { get; set; }
}
