using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace GlassFactoryAccounting.Models
{
    /// <summary>
    /// نموذج المخزن
    /// </summary>
    public class Warehouse : INotifyPropertyChanged
    {
        private int _id;
        private string _code = string.Empty;
        private string _name = string.Empty;
        private string _location = string.Empty;
        private DateTime _createdDate;
        private string _description = string.Empty;

        public int Id
        {
            get => _id;
            set => SetProperty(ref _id, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        public string Name
        {
            get => _name;
            set => SetProperty(ref _name, value);
        }

        public string Location
        {
            get => _location;
            set => SetProperty(ref _location, value);
        }

        public DateTime CreatedDate
        {
            get => _createdDate;
            set => SetProperty(ref _createdDate, value);
        }

        public string Description
        {
            get => _description;
            set => SetProperty(ref _description, value);
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        public Warehouse()
        {
            CreatedDate = DateTime.Now;
        }
    }
}
