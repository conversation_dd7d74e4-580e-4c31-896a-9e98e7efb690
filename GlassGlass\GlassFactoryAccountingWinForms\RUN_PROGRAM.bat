@echo off
title Glass Factory Accounting System - Windows Forms
color 0A

echo.
echo ===============================================
echo   Glass Factory Accounting System
echo   Windows Forms Edition
echo ===============================================
echo.

echo [1] Stopping any running instances...
taskkill /f /im "GlassFactoryAccountingWinForms.exe" 2>nul >nul

echo [2] Creating data directories...
if not exist "Data" mkdir "Data" 2>nul
if not exist "bin\Release\Data" mkdir "bin\Release\Data" 2>nul
if not exist "bin\Debug\Data" mkdir "bin\Debug\Data" 2>nul

echo [3] Looking for executable...

if exist "bin\Release\GlassFactoryAccountingWinForms.exe" (
    echo Found Release version
    cd "bin\Release"
    start "" "GlassFactoryAccountingWinForms.exe"
    cd ..\..
    goto :success
)

if exist "bin\Debug\GlassFactoryAccountingWinForms.exe" (
    echo Found Debug version
    cd "bin\Debug"
    start "" "GlassFactoryAccountingWinForms.exe"
    cd ..\..
    goto :success
)

if exist "GlassFactoryAccountingWinForms.exe" (
    echo Found in current directory
    start "" "GlassFactoryAccountingWinForms.exe"
    goto :success
)

echo ERROR: Executable not found!
echo.
echo Please build the project first:
echo 1. Open GlassFactoryAccountingWinForms.sln in Visual Studio
echo 2. Press Ctrl+Shift+B to build
echo 3. Run this file again
echo.
pause
exit /b 1

:success
echo.
echo SUCCESS: Program started!
echo.
echo Features available:
echo - Chart of Accounts management
echo - Account creation and editing
echo - Account details viewing
echo - Comprehensive statistics
echo.
echo To access: Click "Chart of Accounts" in the main interface
echo.
timeout /t 3 /nobreak >nul
