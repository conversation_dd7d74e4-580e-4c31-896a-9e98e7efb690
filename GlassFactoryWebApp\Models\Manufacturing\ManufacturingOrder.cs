using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GlassFactoryWebApp.Models
{
    /// <summary>
    /// نموذج أمر التصنيع
    /// </summary>
    public class ManufacturingOrder : BaseEntity
    {
        [Required]
        [MaxLength(20)]
        public string OrderNumber { get; set; } = string.Empty;

        [Required]
        public DateTime OrderDate { get; set; } = DateTime.Now;

        [Required]
        public int ProductId { get; set; }

        [MaxLength(200)]
        public string ProductName { get; set; } = string.Empty;

        [Column(TypeName = "decimal(18,3)")]
        public decimal OrderedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal ProducedQuantity { get; set; } = 0;

        [Column(TypeName = "decimal(18,3)")]
        public decimal RemainingQuantity { get; set; } = 0;

        [MaxLength(50)]
        public string Unit { get; set; } = "قطعة";

        public DateTime? StartDate { get; set; }

        public DateTime? EndDate { get; set; }

        public DateTime? PlannedStartDate { get; set; }

        public DateTime? PlannedEndDate { get; set; }

        [MaxLength(50)]
        public string Status { get; set; } = "مجدول"; // مجدول، قيد التنفيذ، مكتمل، ملغي، معلق

        [MaxLength(50)]
        public string Priority { get; set; } = "عادي"; // عالي، عادي، منخفض

        [Column(TypeName = "decimal(18,2)")]
        public decimal EstimatedCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal ActualCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal MaterialCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal LaborCost { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        public decimal OverheadCost { get; set; } = 0;

        [MaxLength(100)]
        public string? WorkCenter { get; set; }

        [MaxLength(100)]
        public string? Supervisor { get; set; }

        [MaxLength(500)]
        public string? OrderNotes { get; set; }

        [MaxLength(500)]
        public string? ProductionNotes { get; set; }

        [MaxLength(500)]
        public string? QualityNotes { get; set; }

        // Glass-specific manufacturing details
        [Column(TypeName = "decimal(18,2)")]
        public decimal? GlassWidth { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? GlassHeight { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        public decimal? GlassThickness { get; set; }

        [MaxLength(50)]
        public string? GlassType { get; set; }

        [MaxLength(50)]
        public string? GlassColor { get; set; }

        [MaxLength(100)]
        public string? FinishingType { get; set; }

        [MaxLength(100)]
        public string? EdgeWork { get; set; } // صقل، حفر، تشطيب

        [Column(TypeName = "decimal(18,2)")]
        public decimal? TemperingTemperature { get; set; }

        [MaxLength(100)]
        public string? QualityStandard { get; set; }

        public bool RequiresQualityCheck { get; set; } = true;

        public bool IsQualityApproved { get; set; } = false;

        public DateTime? QualityCheckDate { get; set; }

        [MaxLength(100)]
        public string? QualityCheckedBy { get; set; }

        // Customer order reference
        public int? CustomerOrderId { get; set; }

        [MaxLength(100)]
        public string? CustomerName { get; set; }

        [MaxLength(100)]
        public string? CustomerOrderNumber { get; set; }

        // Foreign Keys
        [ForeignKey("ProductId")]
        public virtual Product Product { get; set; } = null!;

        public virtual ApplicationUser CreatedByUser { get; set; } = null!;

        // Navigation Properties
        public virtual ICollection<ManufacturingOrderItem> Materials { get; set; } = new List<ManufacturingOrderItem>();
        public virtual ICollection<ProductionOperation> Operations { get; set; } = new List<ProductionOperation>();
        public virtual ICollection<QualityCheck> QualityChecks { get; set; } = new List<QualityCheck>();
        public virtual ICollection<ProductionCost> ProductionCosts { get; set; } = new List<ProductionCost>();
    }
}
