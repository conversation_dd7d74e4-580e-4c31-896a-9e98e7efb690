using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using GlassFactoryAccounting.Data;
using GlassFactoryAccounting.Models;
using GlassFactoryAccounting.Services;

namespace GlassFactoryAccounting.Views
{
    public partial class EmployeeManagementView : UserControl
    {
        // private readonly PayrollService _payrollService;
        private readonly ExpenseService _expenseService = new ExpenseService();

        public EmployeeManagementView()
        {
            InitializeComponent();
            // var context = new DatabaseContext();
            // _payrollService = new PayrollService(context);
            // _expenseService = new ExpenseService(context);
            LoadData();
        }

        private void LoadData()
        {
            try
            {
                // تحميل الفروع
                var branches = _expenseService.GetAllCompanyBranches();
                CmbBranch.ItemsSource = branches;

                // تحميل الموظفين
                // var employees = _payrollService.GetAllEmployees();
                // DgEmployees.ItemsSource = employees;

                // تعيين التاريخ الافتراضي
                DpStartDate.SelectedDate = DateTime.Now;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnSave_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (string.IsNullOrWhiteSpace(TxtEmployeeName.Text))
                {
                    MessageBox.Show("يرجى إدخال اسم الموظف", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtEmployeeName.Focus();
                    return;
                }

                if (CmbBranch.SelectedItem == null)
                {
                    MessageBox.Show("يرجى اختيار الفرع", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    CmbBranch.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(TxtJobTitle.Text))
                {
                    MessageBox.Show("يرجى إدخال المسمى الوظيفي", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    TxtJobTitle.Focus();
                    return;
                }

                if (!DpStartDate.SelectedDate.HasValue)
                {
                    MessageBox.Show("يرجى اختيار تاريخ بدء العمل", "بيانات ناقصة", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    DpStartDate.Focus();
                    return;
                }

                var selectedBranch = CmbBranch.SelectedItem as CompanyBranch;
                var employee = new Employee
                {
                    Name = TxtEmployeeName.Text.Trim(),
                    Phone = TxtPhone.Text?.Trim() ?? string.Empty,
                    Address = TxtAddress.Text?.Trim() ?? string.Empty
                };

                MessageBox.Show("تم تعطيل إضافة الموظفين مؤقتاً بعد حذف موديول الرواتب.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الموظف: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void BtnClear_Click(object sender, RoutedEventArgs e)
        {
            ClearForm();
        }

        private void ClearForm()
        {
            TxtEmployeeName.Clear();
            CmbBranch.SelectedIndex = -1;
            TxtJobTitle.Clear();
            DpStartDate.SelectedDate = DateTime.Now;
            TxtPhone.Clear();
            TxtAddress.Clear();
            TxtEmployeeName.Focus();
        }

        private void BtnDelete_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.DataContext is Employee employee)
            {
                var result = MessageBox.Show($"هل أنت متأكد من حذف الموظف '{employee.Name}'؟\n\nملاحظة: سيتم الحذف المنطقي وليس الحذف الفعلي من قاعدة البيانات.", 
                    "تأكيد الحذف", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    try
                    {
                        // var deleteResult = _payrollService.DeleteEmployee(employee.Id);
                        MessageBox.Show("تم تعطيل حذف الموظفين مؤقتاً بعد حذف موديول الرواتب.", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في حذف الموظف: {ex.Message}", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }
    }
}
